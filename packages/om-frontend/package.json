{"name": "om-frontend", "version": "1.0.0", "description": "new frontend provider", "main": "index.js", "private": true, "installConfig": {"hoistingLimits": "workspaces"}, "scripts": {"test:unit": "NODE_ENV=test jest --testPathPattern='src/.*\\.(test|spec)?\\.ts$'", "test:unit:watch": "yarn run test:unit --watch", "test:integration": "NODE_ENV=test jest --testPathPattern='tests/.*\\.test\\.ts'", "test:coverage": "yarn run test:unit --coverage", "test:client:unit": "NODE_ENV=test jest --config src/client/jest.config.js --testPathPattern='src/client/.*\\.(test|spec)?\\.(ts|js)$'", "test:client:coverage": "yarn run test:client:unit --coverage", "test:client:watch": "yarn run test:client:unit --watch", "test:playwright": "playwright test --grep @visitorSide", "compile:ts": "tsc", "compile:asset": "yarn run wpNoWach && yarn run gulpProd", "cleanup:asset": "yarn rimraf -g resources/js resources/css src/client/compiled-wp", "cleanup": "yarn run cleanup:asset", "build": "yarn run cleanup && yarn run compile:ts && yarn run compile:asset", "start": "node --enable-source-maps ./dist/index.js", "start-mock-server": "ts-node ./playwright/mock-server.ts", "nodemon": "nodemon --watch src --watch resources/twig", "wp": "webpack -p --entry \"./src/client/webpack.js\" --config \"./src/client/webpack.config.js\"", "gulp": "gulp dev -f src/client/gulpfile.js", "dev": "concurrently --prefix-colors=black.bgWhite --names=GULP,WEBPACK,NODEMON \"yarn run gulp\" \"yarn run wp\" \"yarn run nodemon\"", "wpNoWach": "webpack -p --env.DISABLE_WATCH=1 --entry \"./src/client/webpack.js\" --config \"./src/client/webpack.config.js\"", "gulpProd": "gulp -f src/client/gulpfile.js", "watchTests": "yarn run test:unit --watchAll", "watchIntegrationTests": "yarn run test:integration --watchAll", "watchOneTest": "yarn run test -- -t calculate --watch", "lint": "eslint --ext .js,.ts ."}, "author": "OptiMonk", "dependencies": {"@bull-board/express": "^3.2.11", "@google-cloud/storage": "^7.7.0", "@om/change-builder": "workspace:^", "@om/common": "workspace:^", "@om/integrations": "workspace:^", "@om/jetfabric-sdk": "workspace:^", "@om/payment": "workspace:^", "@om/queues": "workspace:^", "@types/ua-parser-js": "^0.7.36", "@zootools/email-spell-checker": "^1.12.0", "axios": "^0.18.1", "axios-retry": "^3.2.0", "body-parser": "^1.18.3", "bull": "^3.22.0", "compression": "^1.7.3", "cors": "^2.8.5", "css-minimizer-webpack-plugin": "1.1", "dotenv": "^6.1.0", "dotenv-flow": "^3.1.0", "email-existence": "^0.1.6", "express": "^4.16.4", "express-async-errors": "^3.1.1", "express-rate-limit": "^6.7.0", "express-useragent": "^1.0.12", "fast-xml-parser": "^4.4.0", "fastest-levenshtein": "^1.0.16", "geoip-country": "^4.1.45", "helmet": "^3.15.0", "ioredis": "^4.3.0", "ioredis-lock": "^4.0.0", "ioredis-mock": "^5", "jsdom": "^19.0.0", "jsonwebtoken": "^9.0.2", "jstat": "^1.9.0", "lodash.get": "^4.4.2", "loglevel": "^1.6.1", "logrocket": "^3.0.1", "memoize-utils": "^1.0.1", "mobile-detect": "^1.4.3", "moment": "^2.22.2", "mongodb": "4.17.2", "node-fetch": "2", "node-mailwizz": "^1.0.0", "pino": "^5.11.1", "qs": "^6.9.1", "rate-limit-redis": "^3.0.2", "sib-api-v3-sdk": "^8.4.0", "stopcock": "^1.1.0", "supertest": "^4.0.2", "swiper": "^6.6.2", "twig": "^1.12.0", "ua-parser-js": "^1.0.2", "uuid": "^3.3.2", "winston": "^3.1.0"}, "devDependencies": {"@babel/core": "^7.13.15", "@babel/plugin-transform-runtime": "^7.13.15", "@babel/preset-env": "^7.13.15", "@om/jest-num-failed": "workspace:^", "@playwright/test": "^1.35.1", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^18.0.0", "@rollup/plugin-node-resolve": "^11.2.1", "@rollup/plugin-replace": "^2.4.2", "@types/cors": "^2.8.4", "@types/dotenv": "^4.0.3", "@types/express": "^4.16.0", "@types/express-useragent": "^0.2.21", "@types/facebook-js-sdk": "^3.2.1", "@types/google.analytics": "0.0.39", "@types/ioredis": "^4.0.4", "@types/jest": "^28.1.7", "@types/jquery": "^3.3.29", "@types/mobile-detect": "^1.3.4", "@types/node": "^14", "@types/youtube": "0.0.35", "@typescript-eslint/eslint-plugin": "^4.26.1", "@typescript-eslint/parser": "^4.26.1", "autoprefixer": "^9.4.9", "clean-webpack-plugin": "^1.0.0", "concurrently": "^5.3.0", "css-loader": "^2.1.0", "eslint": "*", "gridlex": "^2.7.1", "gulp": "^4.0.0", "gulp-clean-css": "^4.0.0", "gulp-cli": "^2.0.1", "gulp-concat": "^2.6.1", "gulp-eol": "^0.2.0", "gulp-plumber": "^1.2.1", "gulp-typescript": "^5.0.0", "gulp-uglify": "^3.0.1", "gulp-uglify-es": "^3.0.0", "gulp-uglifycss": "^1.1.0", "jest": "^28.1.3", "jest-environment-jsdom": "^28.1.3", "jest-junit": "^14.0.0", "mini-css-extract-plugin": "^0.5.0", "mongodb-memory-server": "^8.0.2", "nodemon": "^1.18.7", "pino-pretty": "^2.5.0", "postcss-loader": "^3.0.0", "rimraf": "^3.0.2", "rollup": "^2.45.2", "rollup-plugin-output-manifest": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.49.7", "sass-loader": "^10", "through2": "^4.0.2", "ts-jest": "^28.0.8", "ts-node": "^10.9.1", "typescript": "~4.1", "webpack": "^4.28.2", "webpack-cli": "^3.1.2"}}