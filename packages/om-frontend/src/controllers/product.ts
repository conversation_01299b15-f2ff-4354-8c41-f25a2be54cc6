import { Request, Response, Router } from 'express';
import { pino as log } from '../helpers/logger';
import { getProductDetails } from '../helpers/productHelper';
import { getMostPopularProducts } from '../helpers/unifiedProductsAdapter';

const router = Router();

router.post(
  '/details',
  async ({ body: { account, campaign, products, type } }: Request, res: Response) => {
    log.info(
      'fetchProduct: %d account, %d campaign, %s type, products: %o',
      account,
      campaign,
      type,
      products,
    );
    try {
      const webshopProducts = await getProductDetails({ account, campaign, products, type });
      return res.send(webshopProducts);
    } catch (e: any) {
      log.warn(
        `product details error: ${account}, campaign: ${campaign}, message: ${e.message}, stack: ${e.stack}`,
      );
      return res.json(false);
    }
  },
);

router.post(
  '/popular',
  async ({ body: { databaseId, providerServiceId } }: Request, res: Response) => {
    log.info({
      message: 'fetchPopularProducts',
      databaseId,
      providerServiceId,
    });
    try {
      const webshopProducts = await getMostPopularProducts(databaseId, providerServiceId);
      return res.send(webshopProducts);
    } catch (e: any) {
      log.warn({
        message: 'fetchPopularProducts failed',
        error: e?.message,
        stack: e?.stack,
      });
      return res.json(false);
    }
  },
);

export { router };
