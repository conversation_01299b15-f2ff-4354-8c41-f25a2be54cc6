import { ObjectID } from 'bson';
import * as https from 'https';
import * as _get from 'lodash.get';
import HandlingResults from './conversions/HandlingResults';
import {
  getIntegrationSettings,
  upsertSubscriber,
  getCampaignVariantFields,
  getAccountSettings,
} from '../helpers/mongoHelper';
import { pino } from '../helpers/logger';
import { checkEmailInBody } from '../helpers/smtpEmailValidator';
import LegacyIntegrationHandler from './LegacyIntegrationHandler';
import IntegrationHandler from './IntegrationHandler';
import HandlingResult from './conversions/HandlingResult';
import { reportSubscribed as jfReportSubscribed } from '../helpers/jfHelper';
import { FEATURES, isFeatureEnabled } from '../helpers/featureFlags';

export interface ISubscriber {
  pageUserId: string;
  firstName: string;
  lastName: string;
  email: string;
  campaignId: ObjectID;
  variantId: ObjectID;
  syncStatus: 'unknown' | 'completed' | 'failed' | 'pending';
  customFields: Object | null;
  ip: string | null;
  __META__?: any;
  canAppearOnLeadsPage?: boolean;
}

export default abstract class AbstractConversionHandler {
  static HANDLED_HERE = [
    'activeCampaign',
    'automizy',
    'copernica',
    'klaviyo',
    'klaviyoOAuth',
    'mailChimp',
    'emarsys',
    'webhook',
    'moosend',
    'maileon',
    'conversio',
    'campaignMonitor',
    'smsBump',
    'miniCrm',
    'shopifyCustomer',
    'salesAutopilot',
    'shopRenter',
    'soundest',
    'omnisend',
    'marketo',
    'ontraport',
    'webGalamb4Plus',
    'slack',
    'mailerLite',
    'mailjet',
    'mailWizz',
    'keap',
    'attentive',
    'attentiveV2',
    'hubSpot',
    'dotmailer',
    'aWeber',
    'getResponse',
    'mailigen',
    'salesforce',
    'acerCCDB',
    'postscriptLegacy',
    'postscript',
    'sendGrid',
    'sendinblue',
    'hubSpotV2',
    'acerCCDBV2',
    'highLevel',
    'recart',
    'selzy',
    'unas',
    'theMarketer',
    'listamesterV2',
    'zapier',
    'convertKit',
  ];

  protected databaseId: number;
  protected preventStore = null;

  constructor(databaseId: number) {
    this.databaseId = databaseId;
  }

  public async hasNotifyOnErrorFlag() {
    return await isFeatureEnabled(this.databaseId, FEATURES.NOTIFY_ON_ERROR_SUB, true);
  }

  private async hasStopOnMissingReqField() {
    return await isFeatureEnabled(this.databaseId, FEATURES.STOP_ON_MISSING_REQUIRED_FIELD, true);
  }

  public async shouldPreventStore() {
    if (this.preventStore === null) {
      const settings = await getAccountSettings(this.databaseId);
      if (settings) {
        const {
          settings: { preventSubscribe },
        } = settings;
        this.preventStore = preventSubscribe?.storage;
      }
    }

    return !!this.preventStore;
  }

  public static getVisitorData(visitor): {
    firstName: string;
    lastName: string;
    email: string;
  } {
    return {
      firstName: visitor?.firstname || '',
      lastName: visitor?.lastname || '',
      email: visitor?.email ? visitor.email.trim().toLowerCase() : '',
    };
  }

  static getMetaFromBody(body) {
    if (body && body.__META__) {
      try {
        return JSON.parse(body.__META__);
      } catch (e) {} // eslint-disable-line
    }
    return null;
  }

  static createSubscriber(body, campaignId: ObjectID, variantId: ObjectID): ISubscriber {
    const visitor = AbstractConversionHandler.getVisitorData(body.visitor);
    const subscriber: ISubscriber = {
      pageUserId: body.pageUserId || '',
      ...visitor,
      campaignId,
      variantId,
      syncStatus: 'unknown',
      customFields: body.custom_fields || null,
      ip: body.ip || null,
    };
    subscriber.__META__ = this.getMetaFromBody(body);

    return subscriber;
  }

  protected hasInputField(body): boolean {
    const hasCustomFields = body.custom_fields && Object.values(body.custom_fields).some(Boolean);
    const hasVisitorFields = this.hasVisitorData(body);
    return hasCustomFields || hasVisitorFields;
  }

  protected hasVisitorData(body): boolean {
    return (
      body.visitor &&
      ((body.visitor.firstname && body.visitor.firstname.length > 0) ||
        (body.visitor.lastname && body.visitor.lastname.length > 0) ||
        (body.visitor.email && body.visitor.email.length > 0) != null)
    );
  }

  protected async upsertSubscriber(
    databaseId,
    filter,
    subscriber: ISubscriber,
    increaseFills?: boolean,
  ) {
    await upsertSubscriber(databaseId, filter, subscriber, increaseFills);
  }

  protected info(...args) {
    pino.info(...args);
  }

  protected log(...args) {
    pino.log(...args);
  }

  protected error(...args) {
    pino.error(...args);
  }

  static getIntegrationGlobalSettings(globalId: ObjectID, globals) {
    const globalSettings = globals as { integrations: Array<{ _id: ObjectID; type: string }> };
    return globalSettings.integrations.find(
      (integration) => integration._id.toString() === globalId.toString(),
    );
  }

  protected hasIntegrations(data): boolean {
    const accountIntegrations = _get(data, 'integration_settings.0.integrations', []);
    const campaignIntegrations = _get(data, 'integration_settings.1.integrations', []);

    return !!accountIntegrations.length && !!campaignIntegrations.length;
  }

  static hasToHandleHere(data): boolean {
    let result = false;
    const specificGlobalRefs = data.integration_settings[1].integrations.map((x) =>
      x.id.toString(),
    );
    data.integration_settings[0].integrations.forEach((global) => {
      if (
        AbstractConversionHandler.HANDLED_HERE.includes(global.type) &&
        specificGlobalRefs.includes(global._id.toString())
      )
        result = true;
    });

    return result;
  }

  static filterLegacyIntegrations(data) {
    const specificGlobalRefs = data.integration_settings[1].integrations.map((x) =>
      x.id.toString(),
    );

    data.integration_settings[0].integrations = data.integration_settings[0].integrations.filter(
      (global) => {
        return (
          !AbstractConversionHandler.HANDLED_HERE.includes(global.type) &&
          specificGlobalRefs.includes(global._id.toString())
        );
      },
    );

    return data;
  }

  async sendData(campaignId, variantId, body, subscriber: ISubscriber, filter) {
    // eslint-disable-next-line
    return new Promise(async (resolve, reject) => {
      try {
        // tslint:disable-next-line: no-floating-promises
        checkEmailInBody(body);
        const results = new HandlingResults(subscriber, filter);
        const data = await getIntegrationSettings(this.databaseId, variantId);

        jfReportSubscribed(
          this.databaseId,
          body,
          data.integration_settings[0] as any,
          data.integration_settings[1].integrations,
        );

        if (!this.hasIntegrations(data)) {
          if (await this.shouldPreventStore()) {
            const preventStoreLogger = pino.child({ service: 'prevent-store-error' });
            preventStoreLogger.error(
              `No integration for prevent store account ${this.databaseId}!`,
              body,
            );
          }

          resolve(results);
          return;
        }
        const campaignVariantFields = await getCampaignVariantFields(this.databaseId, campaignId);
        if (AbstractConversionHandler.hasToHandleHere(data)) {
          const specificIntegrations = data.integration_settings[1] as {
            integrations: Array<{
              id: ObjectID;
              settings: { bindings: Array<{ [key: string]: string | null }> };
            }>;
          };
          for (let i = 0; i < specificIntegrations.integrations.length; ++i) {
            const specificIntegrationSetting = specificIntegrations.integrations[i];
            const globalSettings = AbstractConversionHandler.getIntegrationGlobalSettings(
              specificIntegrationSetting.id,
              data.integration_settings[0],
            );
            if (globalSettings) {
              const hasStopOnMissingReqFieldFlag = await this.hasStopOnMissingReqField();
              const result = await IntegrationHandler.handle(
                this.databaseId,
                campaignId,
                variantId,
                subscriber,
                body,
                globalSettings,
                specificIntegrationSetting,
                campaignVariantFields,
                hasStopOnMissingReqFieldFlag,
              );

              if (result) {
                results.add(result);
              }
            }
          }
        }
        const notHandledHere = AbstractConversionHandler.filterLegacyIntegrations(data);
        if (notHandledHere.integration_settings[0].integrations.length) {
          const legacyResults = (await LegacyIntegrationHandler.handle(
            this.databaseId,
            campaignId,
            subscriber,
            body,
            variantId,
            notHandledHere,
          )) as HandlingResult[];
          results.merge(legacyResults);
          resolve(results);
        } else resolve(results);
      } catch (e) {
        reject(e);
      }
    });
  }

  public getRequestOpts() {
    const agent = new https.Agent({
      rejectUnauthorized: false,
    });

    return {
      httpsAgent: agent,
      header: {
        'Content-Type': 'application/json',
      },
    };
  }

  // @ts-ignore
  // eslint-disable-next-line
  abstract async handle(body: any, campaignId: any, variantId: any);
}
