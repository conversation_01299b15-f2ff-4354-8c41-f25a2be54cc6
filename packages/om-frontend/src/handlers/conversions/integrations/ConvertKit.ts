import { ObjectId } from 'mongodb';
import { AxiosResponse } from 'axios';
import { ISubscriber } from '../../AbstractConversionHandler';
import BindingHelper from '../binding/ConvertKit';
import AbstractIntegration from './AbstractIntegration';
import HandlingResult from '../HandlingResult';
import IIntegrationAdapter from './IIntegrationAdapter';
import IntegrationResponse from '../IntegrationResponse';

export default class ConvertKit extends AbstractIntegration implements IIntegrationAdapter {
  getType() {
    return 'convertKit';
  }

  public delayEnabled = true;

  async handle(campaignId: ObjectId, subscriber: ISubscriber): Promise<HandlingResult> {
    this.campaignData = await this.getCampaignData(campaignId, subscriber.variantId);
    let response;
    if (this.settings.getSpecific('formId')) {
      response = await this.subscribeTo('form', subscriber);
    }
    if (this.settings.getSpecific('courseId')) {
      response = await this.subscribeTo('course', subscriber);
    }
    return new HandlingResult(this.settings, response, subscriber, this.buildError(response));
  }

  async subscribeTo(
    type: 'form' | 'course',
    subscriber: ISubscriber,
  ): Promise<IntegrationResponse> {
    const config =
      type === 'form'
        ? this.getSubscribeConfigForForm(subscriber)
        : this.getSubscribeConfigForCourse(subscriber);
    let response = {
      status: 0,
      statusText: 'No response',
      message: '',
      data: {},
      headers: {},
      config: {},
    } as AxiosResponse;

    try {
      response = await this.axiosInstance.request(config);
    } catch (e) {
      const err: any = e;
      this.logWarn('SUBSCRIBE EXCEPTION', err);
      if (err.response) {
        this.logWarn('SUBSCRIBE EXCEPTION', {
          stack: err.stack,
          errorMessage: err.message,
          code: err.code,
          response: err.response.data,
        });
        return new IntegrationResponse(
          err.response.status,
          err.response.statusText,
          config.data,
          err.response,
        );
      }
      return new IntegrationResponse(500, err.message, config.data, undefined);
    }

    return new IntegrationResponse(response.status, response.statusText, config.data, response);
  }

  buildPayload(subscriber: ISubscriber) {
    const payload: any = { api_key: this.settings.getGlobal('apiKey'), fields: {} };
    const bindings = this.settings.getSpecific('bindings');

    if (Array.isArray(bindings) && bindings.length) {
      BindingHelper.resolve({
        body: this.body,
        campaignData: this.campaignData,
        subscriber,
        bindings,
        properties: payload,
      });
    }

    const oldTagId = this.settings.getSpecific('tagId');

    if (oldTagId) {
      payload.tags = [oldTagId];
    } else {
      const tags = this.settings.getSpecific('tags');

      if (tags?.length) {
        payload.tags = tags.map((tag) => tag.key);
      }
    }

    return payload;
  }

  protected getAxiosConfig(): { [key: string]: any; baseURL: string } {
    const config = {
      baseURL: `https://api.convertkit.com/v3/`,
    };
    return config;
  }

  protected getSubscribeConfig(): {
    [key: string]: any;
    url: string;
    method: string;
  } {
    return {
      method: '',
      url: ``,
      data: {},
    };
  }

  protected getSubscribeConfigForForm(subscriber: any): {
    [key: string]: any;
    url: string;
    method: string;
  } {
    return {
      method: 'post',
      url: `/forms/${this.settings.getSpecific('formId')}/subscribe`,
      data: this.buildPayload(subscriber),
    };
  }

  protected getSubscribeConfigForCourse(subscriber: any): {
    [key: string]: any;
    url: string;
    method: string;
  } {
    return {
      method: 'post',
      url: `/sequences/${this.settings.getSpecific('courseId')}/subscribe`,
      data: this.buildPayload(subscriber),
    };
  }
}
