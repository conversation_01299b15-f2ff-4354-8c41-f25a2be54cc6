import BindableIntegrationSettings from './BindableIntegrationSettings';
import NormalIntegrationSettings from './NormalIntegrationSettings';

export default class IntegrationSettingsFactory {
  public static create(settings: {
    global: any;
    specific: any;
  }): NormalIntegrationSettings | BindableIntegrationSettings | null {
    switch (settings.global.type) {
      case 'automizy':
        return new NormalIntegrationSettings(settings);
      case 'activeCampaign':
      case 'klaviyo':
      case 'klaviyoOAuth':
      case 'mailChimp':
      case 'webhook':
      case 'slack':
      case 'conversio':
      case 'campaignMonitor':
      case 'shopifyCustomer':
      case 'smsBump':
      case 'miniCrm':
      case 'shopRenter':
      case 'salesAutopilot':
      case 'soundest':
      case 'omnisend':
      case 'webGalamb4Plus':
      case 'copernica':
      case 'marketo':
      case 'ontraport':
      case 'moosend':
      case 'maileon':
      case 'mailerLite':
      case 'mailjet':
      case 'mailWizz':
      case 'keap':
      case 'attentive':
      case 'attentiveV2':
      case 'dotmailer':
      case 'hubSpot':
      case 'aWeber':
      case 'getResponse':
      case 'mailigen':
      case 'salesforce':
      case 'acerCCDB':
      case 'emarsys':
      case 'postscriptLegacy':
      case 'postscript':
      case 'sendGrid':
      case 'sendinblue':
      case 'hubSpotV2':
      case 'acerCCDBV2':
      case 'highLevel':
      case 'recart':
      case 'selzy':
      case 'unas':
      case 'theMarketer':
      case 'listamesterV2':
      case 'zapier':
      case 'convertKit':
        return new BindableIntegrationSettings(settings);
    }
    return null;
  }
}
