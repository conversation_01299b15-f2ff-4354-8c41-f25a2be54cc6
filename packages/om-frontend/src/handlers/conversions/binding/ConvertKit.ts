import AbstractResolver from './AbstractResolver';

export default class ConvertKitBinding extends AbstractResolver {
  public static resolve({ body, campaignData, subscriber, bindings, properties }): void {
    bindings.forEach((binding) => {
      const { fixValue, isFix, fieldId, externalId } = binding;
      if (isFix) {
        if (externalId && !externalId.startsWith('__')) {
          properties.fields[externalId] = fixValue;
        }
      } else if (externalId != null) {
        if (fieldId === 'email') {
          properties[externalId] = subscriber.email;
          return;
        }

        if (fieldId === 'firstname') {
          properties[externalId] = subscriber.firstName;
          return;
        }

        let value = subscriber.customFields?.[fieldId];

        if (fieldId === 'url') {
          const url = body.visitor.url || '';
          value = decodeURIComponent(url);
        } else if (fieldId === 'campaign_name') {
          value = campaignData.name;
        } else if (fieldId === 'variant_name') {
          value = campaignData.variantName;
        } else if (fieldId === 'lastname') {
          value = subscriber.lastName;
        } else {
          value = Array.isArray(value) ? value.join(', ') : value;
        }

        properties.fields[externalId] = value;
      }
    });
  }
}
