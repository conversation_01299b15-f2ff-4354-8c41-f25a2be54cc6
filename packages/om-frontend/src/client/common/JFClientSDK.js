import { makeAsyncValue } from './asyncValue';
import { firstPartyDataFields } from '../preload/firstPartyData';
import { getViewedProductIds } from '../preload/shared/helpers';
import { Storage } from '../shared/Storage';
import { error } from '../shared/log';
import { isCheckoutPage, isShoprenterCheckoutPage, isUnasCheckoutPage } from './checkout';
import { processPreRegisteredJFEvents, reportEventJF } from '../shared/JFEventReport';
import { getDeviceType } from '../shared/JF/helpers';
import { getViewedProductAdapter } from '../shared/Cookie/ViewedProductAdapter';

let [isLoaded, setLoaded] = makeAsyncValue();

const extractJFClientSDK = () => window?.JFClientSDK;

export const jfSdkLoaded = () => {
  const jfSdk = extractJFClientSDK();
  try {
    if (jfSdk) {
      setLoaded(true);
    }
  } catch (_) {
    isLoaded = Promise.resolve(!!jfSdk);
  }
};

export const getJFClientSDK = async () => {
  if (extractJFClientSDK()) return extractJFClientSDK();

  await isLoaded;
  return extractJFClientSDK();
};

export const getJFClientSDKWithTimeout = (timeoutMs) =>
  Promise.race([
    getJFClientSDK(),
    new Promise((resolve) => setTimeout(() => resolve(), timeoutMs)),
  ]);

// implicitly initialize internal state if JFClientSDK is already loaded
if (extractJFClientSDK()) {
  jfSdkLoaded();
}

export function logFirstPartyData() {
  const visitorAttributes = Storage.local.getItem('OptiMonkVisitorAttributes');

  const visitorData = {};
  firstPartyDataFields.forEach((firstPartyDataField) => {
    if (visitorAttributes && typeof visitorAttributes[firstPartyDataField] !== 'undefined') {
      visitorData[firstPartyDataField] = visitorAttributes[firstPartyDataField];
    }
  });

  reportEventJF('visitorData', visitorData);
}
export function logPageView() {
  const canonicalUrl =
    document.querySelector("link[rel='canonical']")?.getAttribute('href') || null;

  try {
    reportEventJF('pageView', {
      title: document.title,
      referrer: document.referrer,
      userAgent: navigator.userAgent,
      url: window.location.href,
      host: window.location.host,
      path: window.location.pathname,
      query: window.location.search,
      hash: window.location.hash,
      viewportWidth: document.documentElement.clientWidth,
      viewportHeight: document.documentElement.clientHeight,
      canonicalUrl,
    });
  } catch (err) {
    console.log('unable to log page view:', err.message);
  }
}

export function logProductView(fire) {
  const ids = getViewedProductIds();
  if (ids) {
    window.JFClientSDK.v2.registerProductView(ids);
    if (fire) {
      return window.JFClientSDK.v2.go().catch(function (err) {
        console.log('An error occurred during JFClientSDK.v2.go():', err.message);
        console.log(err);
      });
    }
  }

  return null;
}

export function logCheckout() {
  if (window.Shopify.checkout) {
    const itemCount = window.Shopify.checkout.line_items.reduce(
      (sum, item) => sum + item.quantity,
      0,
    );
    const deviceType = getDeviceType();
    reportEventJF('eoo', {
      orderId: window.Shopify.checkout.order_id.toString(),
      total: parseFloat(window.Shopify.checkout.total_price_set.shop_money.amount),
      itemCount,
      currency: window.Shopify.checkout.total_price_set.shop_money.currency_code,
      shopifyCurrency: window.Shopify.currency.active,
      platform: 'shopify',
      deviceType,
      ...(window.Shopify.checkout.discount && {
        total_discount: parseFloat(window.Shopify.checkout.discount.amount),
      }),
      ...(window.Shopify.checkout.discount?.code && {
        discount_code: window.Shopify.checkout.discount.code,
      }),
    });
  }
}

export function logShoprenterCheckout() {
  if (window.ShopRenter.lastOrder) {
    const itemCount = window.ShopRenter.lastOrder.products.reduce(
      (sum, product) => sum + parseInt(product.quantity, 10),
      0,
    );
    reportEventJF('eoo', {
      orderId: window.ShopRenter.lastOrder.id,
      total: window.ShopRenter.lastOrder.total,
      itemCount,
      currency: window.ShopRenter.lastOrder.currency,
      platform: 'shoprenter',
      deviceType: OptiMonkRegistry.isMobile ? 'mobile' : 'desktop',
    });
  }
}

export function logUnasCheckout() {
  if (!window.UNAS?.getOrder) return;

  const lastOrderKey = window.UNAS?.shop?.last_order_key ?? null;
  if (!lastOrderKey) return;

  let visitorAttributes = Storage.local.getItem('OptiMonkVisitorAttributes');
  const reportedLastOrderKey = visitorAttributes?.unas_last_order_key ?? null;

  // Has it been reported yet?
  if (reportedLastOrderKey && reportedLastOrderKey === lastOrderKey) return;

  window.UNAS.getOrder((order) => {
    if (!order?.id || !order.items?.item) return;
    const orderItems = Array.isArray(order.items.item) ? order.items.item : [order.items.item];
    const itemCount = orderItems.reduce(
      (sum, product) => sum + parseInt(product?.quantity || 0, 10),
      0,
    );
    reportEventJF('eoo', {
      orderId: order.id,
      total: parseFloat(order.sum_price_gross) || 0,
      itemCount,
      currency: order.currency,
      platform: 'unas',
      deviceType: OptiMonkRegistry.isMobile ? 'mobile' : 'desktop',
    });
    visitorAttributes = Storage.local.getItem('OptiMonkVisitorAttributes');
    visitorAttributes.unas_last_order_key = lastOrderKey;
    Storage.local.setItem('OptiMonkVisitorAttributes', visitorAttributes);
  });
}

export function hasUnreportedWooOrder() {
  if (!window.woocommerce_params) return false;

  const visitorAttributes = Storage.local.getItem('OptiMonkVisitorAttributes');

  const orderId = visitorAttributes?.['wp_order.order_id'];
  // Is there order set by a plugin?
  if (!orderId) return false;

  const reportedLastOrderId = visitorAttributes?.woo_last_order_id;
  const hasReported = reportedLastOrderId && reportedLastOrderId === orderId;

  return !hasReported;
}

export function logWooOrder() {
  const visitorAttributes = Storage.local.getItem('OptiMonkVisitorAttributes');
  const orderId = visitorAttributes['wp_order.order_id'];

  reportEventJF('eoo', {
    orderId,
    total: parseFloat(visitorAttributes['wp_order.total']) || 0,
    itemCount: parseInt(visitorAttributes['wp_order.item_count'] || 0, 10),
    currency: visitorAttributes['wp_order.currency'],
    platform: 'woocommerce',
    deviceType: OptiMonkRegistry.isMobile ? 'mobile' : 'desktop',
  });

  visitorAttributes.woo_last_order_id = orderId;
  Storage.local.setItem('OptiMonkVisitorAttributes', visitorAttributes);
}

export function registerProductChangeListeners(ids) {
  if (OptiMonkRegistry.productChangeInterval) {
    clearInterval(OptiMonkRegistry.productChangeInterval);
  }

  ids = ids || getViewedProductIds();
  if (!ids) {
    return;
  }
  const lastIds = { productId: ids.productId, variantId: ids.variantId };
  if (window.ShopifyAnalytics) {
    OptiMonkRegistry.productChangeInterval = setInterval(function () {
      const currentIds = getViewedProductIds();
      if (currentIds && currentIds.variantId && lastIds.variantId !== currentIds.variantId) {
        lastIds.productId = currentIds.productId;
        lastIds.variantId = currentIds.variantId;
        console.log('Detected Shopify variant change.');

        logProductView(true);
      }
    }, 1000);
  }
}

export function registerWooAttributesChangeListeners() {
  document.querySelector('html').addEventListener('optimonk#wc-attributes-updated', () => {
    const visitorAttributes = Storage.local.getItem('OptiMonkVisitorAttributes');
    const wooProductId = visitorAttributes?.['wp_current_product.id'] ?? null;
    if (wooProductId) {
      window.JFClientSDK.v2.registerProductView({
        productId: wooProductId,
        variantId: wooProductId,
      });
    }

    if (hasUnreportedWooOrder()) {
      logWooOrder();
    }

    window.JFClientSDK.v2.go().catch(function (err) {
      console.error('An error occurred during initial JFClientSDK request:', err.message);
      console.error(err);
    });
  });
}

const registerDataLayerListener = (cb) => {
  window.dataLayer = window.dataLayer || [];

  const originalPush = window.dataLayer.push;
  window.dataLayer.push = function (...messages) {
    try {
      messages.forEach((message) => {
        cb(message);
      });
    } catch (err) {
      error(err);
    }

    return originalPush.apply(window.dataLayer, messages);
  };

  // replay all previous messages
  window.dataLayer.forEach((msg) => cb(msg));
};

export const registerGA4EventListener = () => {
  if (OptiMonkRegistry.ga4EventListenerRegistered) return;

  const deviceType = getDeviceType();

  registerDataLayerListener((message) => {
    const isPurchaseEvent =
      message?.event === 'purchase' || (message?.[0] === 'event' && message?.[1] === 'purchase');
    if (!isPurchaseEvent) return;

    const props = message.ecommerce || message[2];
    const { transaction_id: orderId, items, value, currency, coupon } = props;

    // if (!orderId || isNaN(parseFloat(value)) || !currency) return;
    const nonStandardOrderEvent = !orderId || isNaN(parseFloat(value)) || !currency;
    if (nonStandardOrderEvent) {
      reportEventJF('ga4:nonstandard-purchase', { data: JSON.stringify(props) });
      return;
    }

    const { totalDiscount, itemCount } = (items || []).reduce(
      ({ totalDiscount, itemCount }, item) => ({
        totalDiscount: totalDiscount + (item.discount ? parseFloat(item.discount) : 0),
        itemCount: itemCount + (item.quantity ?? 1),
      }),
      { totalDiscount: 0, itemCount: 0 },
    );

    reportEventJF('ga4:purchase', {
      orderId,
      total: parseFloat(value),
      itemCount,
      currency,
      platform: OptiMonkEmbedded.Engine.getInfo().type,
      deviceType,
      total_discount: totalDiscount,
      discount_code: coupon || undefined,
    });
  });

  OptiMonkRegistry.ga4EventListenerRegistered = true;
};

export const trackProductView = () => {
  const viewedProductAdapter = getViewedProductAdapter();
  viewedProductAdapter.track();
};

export const firePageTasks = () => {
  registerGA4EventListener();
  logProductView();
  trackProductView();
  logPageView();
  logFirstPartyData();

  if (isCheckoutPage()) {
    logCheckout();
  }

  if (isShoprenterCheckoutPage()) {
    logShoprenterCheckout();
  }

  if (isUnasCheckoutPage()) {
    logUnasCheckout();
  }

  if (hasUnreportedWooOrder()) {
    logWooOrder();
  }

  processPreRegisteredJFEvents();

  window.JFClientSDK.v2.go().catch(function (err) {
    console.error('An error occurred during initial JFClientSDK request:', err.message);
    console.error(err);
  });

  registerProductChangeListeners();
  registerWooAttributesChangeListeners();
};
