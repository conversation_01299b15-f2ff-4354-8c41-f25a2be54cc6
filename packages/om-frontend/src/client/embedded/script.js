import { OptiMonkEmbedded } from './OptiMonkEmbedded';
import { OMWebfont } from '../vendor/webfontloader';
import { prepare, start } from './init';
import { CampaignRegistry } from './CampaignRegistry';
import { RequestService } from './shared/RequestService';
import { AssetManager } from '../shared/AssetManager';
import { merge } from '../preload/Util/utils';
import { ViewedPageStorageHandler } from '../shared/ViewedPageStorageHandler';
import { NotViewedPageRecentStorageHandler } from '../shared/NotViewedPageRecentStorageHandler';
import { Engine } from '../preload/Engine/Engine';
import { native } from '../preload/shared/native';
import { Visitor } from '../shared/Visitor/index';
import { TYPES as CART_INTEGRATION_TYPES } from './CartIntegration/Types';
import PreviewService from './PreviewService';
import { listenEventQueue } from '../shared/CustomEventReporter/customEventReporter';

import { getAccountId, getCookie, getRequestService, isOmDiagSession } from '../shared/helpers';
import { SiteInfo } from '../preload/SiteInfo/SiteInfo';
import { PNC } from '../shared/pnc';
import { DiagnosticToolService } from '../shared/DiagnosticTool';
/**
 * @namespace OptiMonkEmbedded
 */
if (typeof window.OptiMonkEmbedded.CampaignRegistry !== 'object') {
  if (!OptiMonkEmbedded.EmbeddedCampaign) {
    merge(OptiMonkEmbedded, {
      prepare,
      start,
      CampaignRegistry,
      RequestService,
      AssetManager,
      ViewedPageStorageHandler,
      NotViewedPageRecentStorageHandler,
      Engine,
      native,
      Visitor,
      initWebfontLoader: OMWebfont,
    });
  }

  if (typeof window.OptiMonkEmbedded === 'undefined') window.OptiMonkEmbedded = OptiMonkEmbedded;

  window.OptiMonkEmbedded = {
    ...window.OptiMonkEmbedded,
    CampaignRegistry: new OptiMonkEmbedded.CampaignRegistry(),
    RequestService: new OptiMonkEmbedded.RequestService({
      accountId: OptiMonkRegistry.account,
      baseUrl: OptiMonkRegistry.baseUrl,
      embeddedContentUrl: OptiMonkRegistry.embeddedContentUrl,
      cdnUrl: OptiMonkRegistry.cdnUrl,
      aiCdnUrl: OptiMonkRegistry.aiCdnUrl,
      aiPPOCdnUrl: OptiMonkRegistry.aiPPOCdnUrl,
      accountDataUrl: OptiMonkRegistry.accountDataUrl,
      limitUrl: OptiMonkRegistry.limitUrl,
    }),
    PreviewService,
    AssetManager: new OptiMonkEmbedded.AssetManager(),
    loadEmbedded() {
      return OptiMonkEmbedded.prepare()
        .then((response) => {
          if (response) {
            // initEmbedded will not be awaited
            OptiMonkEmbedded.start(response);
          }
          return Promise.resolve();
        })
        .catch((err) => {
          console.error(err);
          return Promise.resolve();
        });
    },
    async loadPopups() {
      const { init } = await import('../v2/popup');
      init();
    },
  };

  const appEmbedStatusReport = async () => {
    if (!window.Shopify) return;
    if (
      document.querySelector(
        `script[src*="/script.js?account=${OptiMonkRegistry.account}&origin=shopify-app-embed-block"]`,
      ) === null
    )
      return;

    return getRequestService().sendAppEmbedStatus(window.Shopify.shop);
  };

  const insertTrackJsScript = () => {
    return new Promise((resolve) => {
      const { trackJsApiKey, account } = window.OptiMonkRegistry;
      if (!OptiMonkRegistry.features.TRACK_JS || !trackJsApiKey) return resolve(1);
      const r = document.getElementsByTagName('head')[0];
      const t = document.createElement('script');
      t.type = 'text/javascript';
      t.charset = 'utf-8';
      t.src = 'https://cdn.trackjs.com/agent/v3/latest/t.js';
      r.appendChild(t);

      const maxTry = 10;
      let tryCount = 0;
      const install = () => {
        if (tryCount >= maxTry) return resolve(1);
        if (!window.TrackJS) {
          tryCount++;
          setTimeout(install, 50);
        } else {
          window.TrackJS.install({
            token: trackJsApiKey,
          });
          window.TrackJS.addMetadata('account', account);
          return resolve(1);
        }
      };
      install();
    });
  };

  const { type } = OptiMonkEmbedded.Engine.getInfo();

  // region Platform specific scripts + cart integration
  const cartIntegrationPlatforms = Object.values(CART_INTEGRATION_TYPES);
  if (cartIntegrationPlatforms.includes(type)) {
    import('./CartIntegration/CartIntegration').then(({ CartIntegration }) => {
      window.OptiMonkEmbedded.CartIntegration = new CartIntegration(type);
      if (type === 'shopify') {
        import('../platform/shopify').then(({ initShopify }) => {
          initShopify(document, window.jQuery);
        });
      } else if (type === 'shoprenter') {
        import('../platform/shoprenter').then(({ initShoprenter }) => {
          initShoprenter();
        });
      } else if (type === 'unas') {
        import('../platform/unas').then(({ initUnas }) => {
          initUnas();
        });
      } else if (type === 'woocommerce') {
        import('../platform/woocommerce').then(({ initWooCommerce }) => {
          initWooCommerce();
        });
      }
    });
  }

  // endregion

  // region Platform specific features
  if (type === 'bigcommerce') {
    import('../platform/bigcommerce').then(({ initBigCommerce }) => {
      initBigCommerce(document, window.jQuery);
    });
  }
  // endregion

  window.OptiMonkRegistry.Cookie = getCookie()[getAccountId()];

  if (PNC.isWebSelectorEnabled()) {
    PNC.init();
  }

  PreviewService.setup();
  try {
    appEmbedStatusReport();
  } catch (e) {
    console.error('[OM] App embed status report error', e.message);
  }

  try {
    // SPA init also in SiteInfo.checkSiteStatus
    SiteInfo.checkSiteStatus();
  } catch (e) {
    console.error('[OM] Site status check error', e.message);
  }

  if (isOmDiagSession() || window.location.search.match(/om-.*[a-z]-diag-id\=.*[1-9]/)) {
    window.OptiMonk = window.OptiMonk || {};
    window.OptiMonk.diagnosticTool = new DiagnosticToolService();
  }

  window.OptiMonkEmbedded.loadEmbedded().then(() => {
    listenEventQueue();
    insertTrackJsScript();
  });
}
