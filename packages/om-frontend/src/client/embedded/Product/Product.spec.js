const { Product } = require('./Product');
const { ProductDOM } = require('./ProductDOM');
const { EmbeddedCampaignDOMHtml } = require('../EmbeddedCampaignDOMHtml');
const { extendOptiMonkEmbedded } = require('../../test/utils');

const mockOptiMonkEmbeddedAsDesired = (CartIntegration) => {
  extendOptiMonkEmbedded({
    CartIntegration: { ...CartIntegration },
  });
};
jest.mock('../EmbeddedCampaignDOMHtml');
jest.mock('../OptiMonkEmbedded');
jest.spyOn(global.console, 'warn');

const redirectToProductPageMock = jest
  .spyOn(ProductDOM.prototype, 'redirectToProductPage')
  .mockImplementation(() => {
    return true;
  });

describe('Product', () => {
  let dom;
  let settings;
  let mockedProduct;
  let product;
  beforeEach(() => {
    dom = new EmbeddedCampaignDOMHtml(55, 0);
    settings = {
      mode: 'manual',
      nrOfProducts: 3,
      storeType: 'shopify',
      productFilter: {
        type: 'none',
        categoryId: null,
      },
      clickAction: 'add-to-cart',
      openInNewTab: true,
      showOutOfStock: true,
      ctaOutOfStockText: 'outOfStock',
      products: [
        {
          type: 'shopify',
          id: '6679192174786',
          variant: '39812208689346',
          url: null,
        },
        {
          type: 'shopify',
          id: '6683554218178',
          variant: '39820708020418',
          url: null,
        },
      ],
    };
    mockedProduct = {
      outOfStock: false,
      id: 7501412008130,
      variant: 42380332335298,
      title: 'Asztal',
      price: 1500,
      originalPrice: null,
      sku: '100',
      url: '/products/asztal?variant=42380332335298',
      imgUrl:
        'https://cdn.shopify.com/s/files/1/0565/2027/8210/products/2465818_R_Z001A.webp?v=1661449145',
    };

    product = new Product(new ProductDOM(dom), settings, false);
    product.setDetails(mockedProduct);
    EmbeddedCampaignDOMHtml.mockClear();
  });

  describe('.handleClick', () => {
    test('should return undefined in case of product is out of stock', () => {
      product._setOutOfStock(true);

      expect(product.handleClick()).toBe(undefined);
    });
    describe('.add-to-cart', () => {
      test('in cas of on stock product it should call the addToCart method with the product variant', () => {
        const mockedCartIntegration = {
          addToCart: jest.fn().mockImplementation(() => Promise.resolve()),
        };
        mockOptiMonkEmbeddedAsDesired(mockedCartIntegration);
        product.handleClick();

        expect(mockedCartIntegration.addToCart).toHaveBeenCalledTimes(1);
        expect(mockedCartIntegration.addToCart).toHaveBeenCalledWith(
          product.getVariant(),
          product.getSku(),
        );
      });

      test('in case of converted product should logging', () => {
        product.setConverted();

        product.handleClick();

        expect(console.warn).toBeCalled();
      });
    });
    describe('.redirect', () => {
      test('in case of on stock product it should call the redirectToProductPage method with the product url', () => {
        settings.clickAction = 'redirect';

        product.handleClick();

        expect(redirectToProductPageMock).toHaveBeenCalledTimes(1);
        expect(redirectToProductPageMock).toHaveBeenCalledWith(
          product.isOpenToNewTab(),
          product.getUrl(),
        );
      });
    });
  });
});
