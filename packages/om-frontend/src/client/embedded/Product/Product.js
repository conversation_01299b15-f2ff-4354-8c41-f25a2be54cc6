import { CLICK_ACTION } from './constants';
import { ProductEvents } from './ProductEvents';

export class Product {
  constructor(productDOM, settings, isStatic) {
    this.productDOM = productDOM;
    this.settings = settings;

    this.id = null;
    this.variant = null;
    this.imgUrl = null;
    this.originalPrice = null;
    this.outOfStock = null;
    this.price = null;
    this.sku = null;
    this.title = null;
    this.url = null;
    this.converted = false;
    this.loading = !isStatic;

    if (isStatic) {
      this._setStaticData();
    }
  }

  setDetails(details) {
    const { id, imgUrl, originalPrice, outOfStock, price, sku, title, url, variant } = details;
    this._setId(id);
    this._setImageUrl(imgUrl);
    this._setOriginalPrice(originalPrice);
    this._setOutOfStock(outOfStock);
    this._setPrice(price);
    this._setSku(sku);
    this._setTitle(title);
    this._setUrl(url);
    this._setVariant(variant);

    this._replaceProductDetails();
  }

  getId() {
    return this.id;
  }

  getVariant() {
    return this.variant;
  }

  getImageUrl() {
    return this.imgUrl;
  }

  getOriginalPrice() {
    return this.originalPrice;
  }

  isOutOfStock() {
    return this.outOfStock;
  }

  getPrice() {
    return this.price;
  }

  getSku() {
    return this.sku;
  }

  getTitle() {
    return this.title;
  }

  getUrl() {
    return this.url;
  }

  getClickAction() {
    return this.settings.clickAction;
  }

  getOutOfStockText() {
    return this.settings.ctaOutOfStockText;
  }

  showOutOfStock() {
    return !!this.settings.showOutOfStock;
  }

  isOpenToNewTab() {
    return this.settings.openInNewTab;
  }

  isLoading() {
    return this.loading;
  }

  setLoading(value) {
    this.loading = value;
  }

  isConverted() {
    return this.converted;
  }

  getCTABtn() {
    return this.productDOM.getCTABtn();
  }

  handleClick() {
    if (this.isOutOfStock()) return;

    if (this._isAddToCartAndConverted()) {
      this._addToCart();
      return;
    }

    if (this._isRedirectAndHasProductUrl()) {
      this.productDOM.redirectToProductPage(this.isOpenToNewTab(), this.getUrl());
      return;
    }

    console.warn(`Unable to do any action on product click variant: ${this.getVariant()}`);
  }

  setConverted() {
    this.converted = true;
  }

  getProductNo() {
    return this.productDOM.getProductDataNo();
  }

  _setStaticData() {
    this._setTitle(this.productDOM.getTitle());
    this._setImageUrl(this.productDOM.getImageUrl());
    this._setUrl(this.productDOM.getUrl());
    this._setPrice(this.productDOM.getPrice());
    this._setOriginalPrice(this.productDOM.getOriginalPrice());
  }

  _setId(id) {
    this.id = parseInt(id, 10);
  }

  _setVariant(variant) {
    this.variant = variant;
  }

  _setImageUrl(imgUrl) {
    this.imgUrl = imgUrl;
  }

  _setOriginalPrice(originalPrice) {
    this.originalPrice = originalPrice;
  }

  _setOutOfStock(outOfStock) {
    this.outOfStock = outOfStock;
  }

  _setPrice(price) {
    this.price = price;
  }

  _setSku(sku) {
    this.sku = sku;
  }

  _setTitle(title) {
    this.title = title;
  }

  _setUrl(url) {
    this.url = url;
  }

  _addFAIcon(btn, type) {
    this.productDOM.addFAIcon(btn, type);
  }

  _addLoadIcon(btn, type) {
    this._addFAIcon(btn, type);
  }

  _addCheckedIcon(btn, type) {
    this._addFAIcon(btn, type);
  }

  _addToCart() {
    const ctaBtn = this.getCTABtn();
    this._addLoadIcon(ctaBtn, 'om-animate-spin fa-spinner');

    OptiMonkEmbedded.CartIntegration.addToCart(this.getVariant(), this.sku).then(() => {
      ProductEvents.addToCart(this.productDOM);
      this._addCheckedIcon(ctaBtn, 'fa-check');
    });
  }

  _isAddToCartAndConverted() {
    return this.getClickAction() === CLICK_ACTION.ADD_TO_CART && !this.isConverted();
  }

  _isRedirectAndHasProductUrl() {
    return this.getClickAction() === CLICK_ACTION.REDIRECT && this.getUrl();
  }

  _replaceProductDetails() {
    this.productDOM.replaceProductDetails(this);
  }
}
