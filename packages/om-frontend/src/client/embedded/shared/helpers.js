const decodeEntities = (function () {
  // this prevents any overhead from creating the object each time
  const element = document.createElement('div');
  function decodeHTMLEntities(str) {
    if (str && typeof str === 'string') {
      // strip script/html tags
      str = str.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim, '');
      str = str.replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gim, '');
      element.innerHTML = str;
      str = element.textContent;
      element.textContent = '';
    }
    return str;
  }
  return decodeHTMLEntities;
})();

export const formatMoney = (cents, format) => {
  if (typeof cents === 'string') {
    cents = cents.replace('.', '');
  }
  const placeholderRegex = /\{\{\s*(\w+)\s*\}\}/;
  const formatString = decodeEntities(format) || '{{amount}}';

  function defaultOption(opt, def) {
    return typeof opt === 'undefined' ? def : opt;
  }

  function formatWithDelimiters(number, precision, thousands, decimal) {
    precision = defaultOption(precision, 2);
    thousands = defaultOption(thousands, ',');
    decimal = defaultOption(decimal, '.');

    if (window.ShopRenter) {
      precision = window.ShopRenter.shop.currency.code === 'HUF' ? 0 : precision;
      thousands = '.';
      decimal = ',';
    }

    if (isNaN(number) || number == null) {
      return 0;
    }

    number = (number / 100).toFixed(precision);

    const parts = number.split('.');
    const dollars = parts[0].replace(/(\d)(?=(\d\d\d)+(?!\d))/g, `$1${thousands}`);
    const cents = parts[1] ? decimal + parts[1] : '';

    return dollars + cents;
  }

  const placeholder = formatString.match(placeholderRegex)[1];
  let value = '';
  switch (placeholder) {
    case 'amount':
      value = formatWithDelimiters(cents, 2);
      break;
    case 'amount_no_decimals':
      value = formatWithDelimiters(cents, 0);
      break;
    case 'amount_with_comma_separator':
      value = formatWithDelimiters(cents, 2, '.', ',');
      break;
    case 'amount_no_decimals_with_comma_separator':
      value = formatWithDelimiters(cents, 0, '.', ',');
      break;
    case 'amount_no_decimals_with_space_separator':
      value = formatWithDelimiters(cents, 0, ' ', ',');
      break;
  }

  return formatString.replace(placeholderRegex, value);
};

export const findDeepestChildText = (element) => {
  let ele = element;
  while (ele.hasChildNodes()) {
    ele = ele.firstChild;
  }
  return ele;
};

export const addFaIcon = (htmlElement, type) => {
  const button = htmlElement;
  const ele = findDeepestChildText(button);
  if (typeof ele.classList !== 'undefined') return;

  const target = ele ? ele.parentNode : button;
  const originalCTAText = target.innerHTML;
  target.innerHTML = `<span class='fa ${type}' style='font-family:OmCustom'></span>&nbsp;${originalCTAText}`;
};

export const removeFaIcon = (htmlElement) => {
  const button = htmlElement;
  const originalCTAText = button.children[0].innerText;
  const ele = findDeepestChildText(button);
  const target = ele ? ele.parentNode : button;
  target.innerHTML = originalCTAText;
};

export const slugifyUrlPath = (url) => {
  const pathIdentifier = new URL(url).pathname;
  return pathIdentifier
    .toLowerCase() // Convert to lowercase
    .trim() // Remove leading and trailing whitespace
    .replace(/(^\w+:|^)\/\//, '') // Remove protocol
    .replace(/[^a-z0-9 -]/g, '-') // Remove non-alphanumeric characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with a single hyphen
    .replace(/(?<=.)-$/, ''); // Replace traling slash, if there are other charecters before it (root filename will be -.json)
};

export const getPageIdentifier = () => {
  const fullIdentifier =
    document.querySelector('link[rel="canonical"]')?.href || window.location.href;
  return slugifyUrlPath(fullIdentifier);
};

export const getProductFromWindow = () => {
  return (
    window?.ShopRenter?.product?.id ||
    window?.ShopifyAnalytics?.meta?.product?.id ||
    window.UNAS?.shop?.product_id ||
    window.WooDataForOM?.product?.id?.toString()
  );
};
