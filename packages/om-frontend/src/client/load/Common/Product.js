/* eslint-disable consistent-return */
import { OptiMonk } from '../../OptiMonk';
import { getJFClientSDKWithTimeout } from '../../common/JFClientSDK';
import { ShopifyAdapter } from '../../shared/Platform/ShopifyAdapter';
import { ShopRenterAdapter } from '../../shared/Platform/ShopRenterAdapter';
import { reportUserAlert } from '../../shared/Utils/report/ReportUserAlert';
import { Cart } from '../../shared/Validator/helper/Cart';
import { isNewFrequencyRuleEnabled } from '../../shared/helpers';
import { getViewedProductAdapter } from '../../shared/Cookie/ViewedProductAdapter';
import { getProviderData } from '../../shared/JF/helpers';

const decodeEntities = (function () {
  // this prevents any overhead from creating the object each time
  const element = document.createElement('div');
  function decodeHTMLEntities(str) {
    if (str && typeof str === 'string') {
      // strip script/html tags
      str = str.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim, '');
      str = str.replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gim, '');
      element.innerHTML = str;
      str = element.textContent;
      element.textContent = '';
    }
    return str;
  }
  return decodeHTMLEntities;
})();

const formatMoney = (cents, format) => {
  if (typeof cents === 'string') {
    cents = cents.replace('.', '');
  }
  const placeholderRegex = /\{\{\s*(\w+)\s*\}\}/;
  const formatString = decodeEntities(format) || '{{amount}}';

  function defaultOption(opt, def) {
    return typeof opt === 'undefined' ? def : opt;
  }

  function formatWithDelimiters(number, precision, thousands, decimal) {
    precision = defaultOption(precision, 2);
    thousands = defaultOption(thousands, ',');
    decimal = defaultOption(decimal, '.');

    if (window.ShopRenter) {
      precision = window.ShopRenter.shop.currency.code === 'HUF' ? 0 : precision;
      thousands = '.';
      decimal = ',';
    }

    if (isNaN(number) || number == null) {
      return 0;
    }

    number = (number / 100).toFixed(precision);

    const parts = number.split('.');
    const dollars = parts[0].replace(/(\d)(?=(\d\d\d)+(?!\d))/g, `$1${thousands}`);
    const cents = parts[1] ? decimal + parts[1] : '';

    return dollars + cents;
  }

  const placeholder = formatString.match(placeholderRegex)[1];
  let value = '';
  switch (placeholder) {
    case 'amount':
      value = formatWithDelimiters(cents, 2);
      break;
    case 'amount_no_decimals':
      value = formatWithDelimiters(cents, 0);
      break;
    case 'amount_with_comma_separator':
      value = formatWithDelimiters(cents, 2, '.', ',');
      break;
    case 'amount_no_decimals_with_comma_separator':
      value = formatWithDelimiters(cents, 0, '.', ',');
      break;
    case 'amount_no_decimals_with_space_separator':
      value = formatWithDelimiters(cents, 0, ' ', ',');
      break;
  }

  return formatString.replace(placeholderRegex, value);
};

const getRandomElements = (arr, requestedItems) => {
  const arrCopy = [...arr];
  const result = [];

  const availableRequestedItems = Math.min(requestedItems, arr.length);

  for (let i = 0; i < availableRequestedItems; i++) {
    const randomIndex = Math.floor(Math.random() * arrCopy.length);
    result.push(arrCopy[randomIndex]);
    arrCopy.splice(randomIndex, 1);
  }

  return result;
};

export class Product {
  constructor(element, settings, campaign) {
    this.swiper = null;
    this.originalCTAText = null;
    this.added = [];
    this.productIdentifiers = null;
    this.productDetails = [];
    this.failedDisplayReported = false;
    this.element = element;
    this.settings = settings;
    this.layoutClass = `om-product-${this.settings?.layout ?? 'vertical'}`;
    this.mobileLayoutClass = `om-product-mobile-${this.settings?.mobileLayout ?? 'vertical'}`;

    this.campaign = campaign;
    if (this.settings.product) {
      this.settings.products = [this.settings.product];
    }
    this.handleMobile();
    this.initListeners();
    if (this.getMode() === 'manual' && this.settings.products && this.settings.products.length) {
      this.productIdentifiers = this.settings.products.map((product) => ({
        id: product.id,
        variant: product.variant,
      }));
    }
    if (
      this.getMode() === 'most-viewed' &&
      this.settings.productFilter &&
      this.settings.productFilter.categoryId
    ) {
      const { categoryId } = this.settings.productFilter;
      if (categoryId.startsWith('gid://')) {
        const match = categoryId.match(/\/(\d+)$/);
        if (match && match[1]) {
          this.settings.productFilter.categoryId = match[1];
        } else {
          console.log('Failed to resolve REST ID of collection from gid:', categoryId);
        }
      }
    }
    this.isDataLoading = !this.isStatic();
  }

  static fetchMostPopularProductsOfCategory(options) {
    const { storeType, categoryId, categoryIds, categoryHandle } = options;

    // For providers other than Shopify and Shoprenter, use the product-sync-service
    if (storeType !== 'shopify' && storeType !== 'shoprenter') {
      const databaseId = OptiMonkRegistry.account;
      const { providerServiceId } = getProviderData();

      if (!databaseId || !providerServiceId) {
        console.error('Missing databaseId or providerServiceId for fetching popular products');
        return Promise.resolve([]);
      }

      return fetch(`${OptiMonkRegistry.baseUrl}/product/popular`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          databaseId,
          providerServiceId,
        }),
      })
        .then((response) => {
          if (!response.ok) {
            throw new Error(`Failed to fetch popular products: ${response.status}`);
          }
          return response.json();
        })
        .then((data) => {
          return (data || []).map((product) => ({
            productId: product.id,
            variantId: product.variant,
          }));
        })
        .catch((error) => {
          console.error('Error fetching popular products:', error);
          return [];
        });
    }

    // Original implementation for Shopify and Shoprenter
    const values = {
      mergeVariants: true,
      filterCurrent: true,
      limit: 6,
    };
    if (categoryId) {
      values.categoryId = typeof categoryId !== 'number' ? parseFloat(categoryId) : categoryId;
    } else if (categoryIds) {
      if (Array.isArray(categoryIds)) {
        values.categoryIds = categoryIds.map((cid) =>
          typeof cid !== 'number' ? parseFloat(cid) : cid,
        );
      } else {
        values.categoryIds = categoryIds;
      }
    } else if (categoryHandle) {
      if (storeType !== 'shoprenter') {
        throw new Error(`MPP of category via categoryHandle is not supported in ${storeType}`);
      }
      values.categoryId = {
        path: '$.store.categoryHandle.categoryId',
        query: {
          type: 'qgql',
          values: { categoryHandle },
          gql: `
              query($categoryHandle: String!) {
                store: ${storeType} {
                  categoryHandle(categoryHandle: $categoryHandle) {
                    categoryId
                  }
                }
              }
            `,
        },
      };
    }
    return window.JFClientSDK.v2
      .getQueryResult({
        type: 'qgql',
        values,
        gql: `
            query(
              ${
                values.categoryId ? `$categoryId: ${storeType === 'shopify' ? 'Float' : 'Int'}` : ''
              }
              ${
                values.categoryIds
                  ? `$categoryIds: [${storeType === 'shopify' ? 'Float' : 'Int'}!]`
                  : ''
              }
              $filterCurrent: Boolean
              $mergeVariants: Boolean
              $limit: Int
            ) {
              store: ${storeType} {
                mpp: mostPopularProducts(
                  ${
                    values.categoryId
                      ? `${storeType === 'shopify' ? 'collectionId' : 'categoryId'}: $categoryId,`
                      : ''
                  }
                  ${
                    values.categoryIds
                      ? `${
                          storeType === 'shopify' ? 'collectionIds' : 'categoryIds'
                        }: $categoryIds,`
                      : ''
                  }
                  limit: $limit,
                  filterCurrent: $filterCurrent,
                  mergeVariants: $mergeVariants
                ) {
                  productId
                  variantId
                }
              }
            }`,
      })
      .then((resp) => (resp && resp.store && resp.store.mpp) || []);
  }

  static async getRecommendations() {
    const url = `${ShopifyAdapter.getRootURL()}recommendations/products.json?product_id=${ShopifyAdapter.getProductId()}&limit=3&intent=related`;
    const response = await fetch(url);
    const result = await response.json();
    const { products } = result;
    if (!products?.length) return null;

    return products.map(({ id, variants }) => ({ productId: id, variantId: variants?.[0].id }));
  }

  static async getShopRenterRecommendations(mode) {
    const selectorId = mode.includes('related') ? '#relatedproducts' : '#similar_products';
    const selector = `${selectorId} a[data-product-id]`;

    let productIds = Array.from(document.querySelectorAll(selector))
      .slice(0, 6)
      .map((e) => e.dataset.productId);

    // Try first fallback | vagyaim
    const fallbackSelector = `.similar .productItem input[name="product_id"]`;
    if (productIds.length === 0) {
      productIds = Array.from(document.querySelectorAll(fallbackSelector))
        .slice(0, 6)
        .map((e) => e.value);
    }

    // Try second fallback | bonlou
    if (productIds.length === 0) {
      productIds = Array.from(document.querySelectorAll('#similar_products [name=product_id]'))
        .slice(0, 6)
        .map((e) => e.value);
    }

    return productIds.map((e) => ({ productId: e, variantId: e }));
  }

  static fetchMostPopularProducts(product) {
    const storeType = product.getType();

    if (storeType && product.getMode() === 'most-viewed') {
      const { productFilter } = product.settings;
      if (productFilter && productFilter.type === 'category' && productFilter.categoryId) {
        return Product.fetchMostPopularProductsOfCategory({
          storeType,
          categoryId: productFilter.categoryId,
        });
      }
      if (productFilter && productFilter.type === 'viewed-category') {
        if (OptiMonk.isCollectionPage() && window.ShopifyAnalytics) {
          const viewedCategoryId = OptiMonk.getCollectionId();
          if (viewedCategoryId) {
            return Product.fetchMostPopularProductsOfCategory({
              storeType,
              categoryId: viewedCategoryId,
            });
          }
        } else {
          const categoryHandle = OptiMonk.getCollectionHandle();
          const viewedProduct = OptiMonk.getViewedProductIds();
          if (categoryHandle) {
            return Product.fetchMostPopularProductsOfCategory({ storeType, categoryHandle });
          }
          if (viewedProduct) {
            const productId =
              storeType === 'shoprenter' ? viewedProduct.variantId : viewedProduct.productId;
            return Product.fetchMostPopularProductsOfCategory({
              storeType,
              categoryIds: {
                path: '$.store.product.categoryIds',
                query: {
                  type: 'qgql',
                  values: {
                    productId: typeof productId === 'string' ? parseInt(productId, 10) : productId,
                  },
                  gql:
                    storeType === 'shoprenter'
                      ? `
                          query($productId: Int!) {
                            store: ${storeType} {
                              product(productId: $productId) {
                                categoryIds
                              }
                            }
                          }
                        `
                      : `
                          query($productId: Float!) {
                            store: ${storeType} {
                              product(productId: $productId) {
                                categoryIds: collectionIds
                              }
                            }
                          }
                        `,
                },
              },
            });
          }
        }
      } else {
        return Product.fetchMostPopularProductsOfCategory({ storeType });
      }
    }
    return null;
  }

  static loadSwiper() {
    if (OptiMonk.Swiper) return;
    return new Promise((resolve) => {
      OptiMonk.loadScript('/vendor/swiper.js', resolve);
    });
  }

  static onlyOmRecoComponents(products) {
    return products.every((product) => {
      const productMode = product.getMode();
      return productMode === 'optimonk-recommender';
    });
  }

  static async initProducts(campaign, { reInit = false } = {}) {
    let products = campaign.getProducts();
    if (products.length === 0) {
      return;
    }

    if (reInit) {
      products = products
        .filter((product) => product.getMode() === 'products-in-the-cart')
        .map((product) => {
          product.setProductIdentifiers(null);

          return product;
        });
    }

    try {
      await this.loadSwiper();
      Product._removeProductsSize();

      const { omRecoHasDetails } = await Product.loadDynamicProducts(products);

      if (reInit || !this.onlyOmRecoComponents(products) || !omRecoHasDetails) {
        return this.fetchProductDetails(campaign, reInit);
      }
    } catch (err) {
      products.forEach((p) => {
        p.isDataLoading = false;
      });
      console.log(err);
      throw new Error(`Failed to initialize product components of campaign: ${err.message}`);
    }
  }

  static needsJFSDK(products) {
    return products.some((product) =>
      [
        'last-visited',
        'most-viewed',
        'smart-recommendation',
        'smart-recommendation-related',
        'smart-recommendation-similar',
      ].includes(product.getMode()),
    );
  }

  static async getJFClientSDK(products) {
    const JFClientSDK = await getJFClientSDKWithTimeout(3000);

    if (!JFClientSDK) {
      console.warn('window.JFClientSDK is not loaded during Product component initialization.');
      products.forEach((p) => {
        p.isDataLoading = false;
      });
      throw new Error('JFClientSDK is not loaded.');
    }
    return JFClientSDK;
  }

  static async handleShopifySmartRecommender({ product }) {
    let result = null;
    if (ShopifyAdapter.isProductPage()) {
      result = await Product.getRecommendations();
    } else if (ShopifyAdapter.isCollectionPage()) {
      product.setProductFilter({ type: 'viewed-category' });
    }

    if (!result) {
      product.setMode('most-viewed');
      const { recommendations } = await Product.handleMostPopular({ product });
      result = recommendations;
    }

    return { recommendations: result };
  }

  static async handleShoprenterSmartRecommender({ product }) {
    let result = null;
    const isShoprenterProductPage = ShopRenterAdapter.getResourceType() === 'product';

    if (isShoprenterProductPage) {
      result = await Product.getShopRenterRecommendations(product.getMode());
    }

    if (!result) {
      product.setMode('most-viewed');
      const { recommendations } = await Product.handleMostPopular({ product });
      result = recommendations;
    }

    return { recommendations: result };
  }

  static async handleRecentlyViewed() {
    const viewedProductAdapter = getViewedProductAdapter();

    const products = viewedProductAdapter.getViewedProducts({
      limit: 6,
      filterCurrent: true,
    });

    return { recommendations: products };
  }

  static async handleMostPopular({ product }) {
    const recommendations = await Product.fetchMostPopularProducts(product);

    return { recommendations };
  }

  static async handleOptiMonkRecommender({ product }) {
    const { recommendations } =
      // eslint-disable-next-line no-undef
      await OptiMonkEmbedded?.RequestService?.loadOptimonkRecommenderProducts(product);

    if (!recommendations || recommendations.length < 2) {
      return { recommendations: [] };
    }

    const hasDetails = recommendations.every((recommendation) => recommendation.title);
    if (hasDetails) {
      product.setProductDetails(recommendations);
    }

    return { recommendations, hasDetails };
  }

  static removeCurrentlyViewed(result) {
    const viewedProduct = OptiMonk.getViewedProductIds();
    if (viewedProduct) {
      // Remove currently viewed product from product list
      return result.filter(
        ({ productId, variantId }) =>
          `${productId}${variantId}` !== `${viewedProduct.productId}${viewedProduct.variantId}`,
      );
    }

    return result;
  }

  static handleProductsInTheCartRecommender({ product }) {
    const items = Cart.getItems();
    let recommendations = Object.values(items)?.map((item) => {
      if (window.Shopify) {
        return {
          productId: item.product_id,
          variantId: item.id,
        };
      }
      return {
        productId: item.id,
        variantId: item.id,
      };
    });
    const nrOfProducts = product.getNrOfProducts();
    recommendations = getRandomElements(recommendations, nrOfProducts);

    return { recommendations };
  }

  static async loadDynamicProducts(products) {
    const { Promise } = window;
    const dynamicProductHandlers = {
      'smart-recommendation': this.handleShopifySmartRecommender,
      'smart-recommendation-similar': this.handleShoprenterSmartRecommender,
      'smart-recommendation-related': this.handleShoprenterSmartRecommender,
      'last-visited': this.handleRecentlyViewed,
      'most-viewed': this.handleMostPopular,
      'optimonk-recommender': this.handleOptiMonkRecommender,
      'products-in-the-cart': this.handleProductsInTheCartRecommender,
    };
    let omRecoHasDetails = true;
    let JFClientSDK;
    const needsJFSDK = this.needsJFSDK(products);

    if (needsJFSDK) {
      JFClientSDK = await this.getJFClientSDK(products);
    }

    const promises = products.map(async (product) => {
      let result = null;
      const productMode = product.getMode();
      if (productMode !== 'manual') {
        const handler = dynamicProductHandlers[productMode];
        const { recommendations, hasDetails } = await handler({ product, JFClientSDK });
        result = recommendations;
        if (hasDetails === false) {
          omRecoHasDetails = false;
        }

        if (result) {
          if (productMode !== 'products-in-the-cart') {
            result = this.removeCurrentlyViewed(result);
          }
          const productIdentifiers = result.map((product) => ({
            id: product.productId,
            variant: product.variantId,
          }));
          product.setProductIdentifiers(productIdentifiers);
        }
      }

      product.initSwiper();
      return product;
    });

    if (needsJFSDK) {
      // @ts-ignore
      await JFClientSDK.v2.go();
    }

    await Promise.all(promises);
    return { omRecoHasDetails };
  }

  static collectElements(campaign) {
    const rawProducts = campaign.getCampaignElement().querySelectorAll('.om-product');
    const products = [];
    for (let i = 0; i < rawProducts.length; i += 1) {
      const rawProductEl = rawProducts[i];
      const rawSettings = rawProductEl.getAttribute('data-settings');
      let settings;
      if (rawSettings) settings = JSON.parse(rawSettings);
      const product = new Product(rawProductEl, settings, campaign);
      rawProductEl.OMProduct = product;
      products.push(product);
    }
    return products;
  }

  static doResize() {
    Product._removeProductsSize();
    Product._setProductsSize();
  }

  static resize(tabbed) {
    if (tabbed && !Product.resizeListenerAdded) {
      const pages = document.querySelectorAll('.om-canvas.om-animated');
      // eslint-disable-next-line
      for (const page in pages) {
        if (pages.hasOwnProperty(page)) {
          const popupPage = pages[page];
          OptiMonk.addListener(popupPage, 'animationend', Product.doResize);
        }
      }
      Product.resizeListenerAdded = true;
    } else if (!tabbed || Product.resizeListenerAdded) {
      Product.doResize();
    }
  }

  static _getMaxHeight(items, parentSelector) {
    let max = Number.MIN_SAFE_INTEGER;
    let divider = 16;
    items.forEach((item) => {
      const h = item.offsetHeight;
      const holder = item.closest(parentSelector);
      if (holder) {
        const fontSize = parseInt(
          (getComputedStyle(holder).fontSize || '').replace('px', '') || '0',
          10,
        );
        if (fontSize) divider = fontSize;
      }
      const size = h / divider;
      if (max < size) max = size;
    });
    return max;
  }

  static _removeProductsSize() {
    const holders = document.querySelectorAll('.om-product-holder');
    const titles = document.querySelectorAll('.om-product-name');
    holders.forEach((item) => {
      if (item) item.style.height = '';
    });
    titles.forEach((item) => {
      if (item) item.style.height = '';
    });
  }

  static _setProductsSize() {
    Product._removeProductsSize();
    const holders = document.querySelectorAll('.om-product-holder');
    const maxHolder = Product._getMaxHeight(holders, '.om-product-holder');
    Product._setElementHeight(holders, `${maxHolder}em`);
  }

  static _setElementHeight(items, h) {
    items.forEach((item) => {
      item.style.height = h;
    });
  }

  static fetchProductDetails(campaign, reInit) {
    return new Promise((resolve, reject) => {
      let products = campaign.getProducts();
      if (reInit) {
        products = products.filter((product) => product.getMode() === 'products-in-the-cart');
      }

      const productIdsMap = {};
      let type = null;
      for (let i = 0; i < products.length; i += 1) {
        const product = products[i];
        if (!product.isStatic()) {
          if (!type) {
            // all product types should be the same, picking the first one.
            type = product.getType();
          }
          if (type !== product.getType()) {
            return reject(
              new Error(
                `Store type of #${product.getElementId()} product is other than "${type}": "${product.getType()}"`,
              ),
            );
          }
          if (!product.hasProductIdentifiers()) {
            products.forEach((p) => {
              p.isDataLoading = false;
            });
            // one of the product components does not have the needed amount of product identifiers
            // to be prepared for display, so we do not need to fetch any product details since
            // the campaign will never be displayed.
            reject(
              new Error(
                `#${product.getElementId()} does not have enough identifiers(${product.getNrOfProducts()}) to be displayed.`,
              ),
            );
            return;
          }
          const ids = product.getProductIdentifiers();
          ids.forEach((i) => {
            const { id, variant } = i;
            productIdsMap[`${id}${variant}`] = i;
          });
        }
      }

      const productIds = Object.values(productIdsMap);
      if (productIds.length > 0) {
        const xhr = new OptiMonk.native.XMLHttpRequest();
        const url = `${OptiMonkRegistry.baseUrl}/product/details`;
        xhr.open('POST', url, true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        const errorListener = function (err) {
          reject(err);
        };
        xhr.addEventListener('load', function () {
          let body;
          try {
            body = JSON.parse(this.responseText);
            if (!Array.isArray(body)) {
              throw new Error('Failed to retrieve product details.');
            }
          } catch (err) {
            reject(err);
            return;
          }
          products.forEach((product) => {
            if (!product.isStatic()) {
              const ids = product.getProductIdentifiers();
              if (ids) {
                const productDetails = ids.map(
                  ({ id, variant }) =>
                    body.find(
                      (details) =>
                        details &&
                        `${details.id}` === `${id}` &&
                        ((!details.variant && !variant) || `${details.variant}` === `${variant}`),
                    ) || null,
                );

                product.setProductDetails(productDetails);
              }
              product.isDataLoading = false;
            }
          });
          resolve();
        });
        xhr.addEventListener('error', errorListener);
        xhr.addEventListener('abort', errorListener);
        xhr.send(
          JSON.stringify({
            account: OptiMonkRegistry.account,
            campaign: campaign.getId(),
            type,
            products: productIds,
          }),
        );
      } else {
        resolve();
      }
    });
  }

  isConnectedToShop() {
    return (this.settings.products && this.settings.products.length) || this.getMode() !== 'manual';
  }

  isStatic() {
    return this.getMode() === 'manual' && !this.isConnectedToShop();
  }

  getPageNumber() {
    const page = this.element.closest('[id^="pge_"]');
    const step = page.getAttribute('data-om-step');
    return page && step ? parseInt(step, 10) : 0;
  }

  getProductFilterType() {
    if (this.settings.productFilter && this.settings.productFilter.type) {
      return this.settings.productFilter.type;
    }
    return 'none';
  }

  getProductIdentifiers() {
    return this.productIdentifiers;
  }

  setProductIdentifiers(ids) {
    this.productIdentifiers = ids;
  }

  hasProductIdentifiers() {
    return !!this.productIdentifiers && this.productIdentifiers.length >= this.getNrOfProducts();
  }

  showOutOfStock() {
    return !!this.settings.showOutOfStock;
  }

  replaceProductDetails() {
    if (this.productDetails.length > 0) {
      const holderElements = this.element.querySelectorAll('.om-product-holder');
      let detailsIndex = 0;
      const missingProducts = [];
      for (let i = 0; i < this.getNrOfProducts(); i += 1) {
        let details = null;
        do {
          // eslint-disable-next-line
          details = this.productDetails[detailsIndex++];

          if (details?.outOfStock && !this.showOutOfStock()) {
            const mode = this.getMode();
            const product = details;
            details = null;

            if (mode === 'manual') {
              missingProducts.push(product);
              OptiMonk.sendMetrics('product-outOfStock', {
                type: this.getType(),
                mode,
                filterType: this.getProductFilterType(),
                campaign: this.campaign.getId(),
              });
              console.warn(`no details for #${i + 1}th product in #${this.getElementId()} element`);
            }
          }
        } while (!details && detailsIndex < this.productDetails.length);

        const holderEl = holderElements[i];
        if (!holderEl) {
          console.warn(
            `holder element of ${
              i + 1
            }th product in #${this.getElementId()} element could not be found`,
          );
          return;
        }
        if (details) {
          holderEl.setAttribute('data-product-id', `${details.id}`);
          holderEl.setAttribute('data-product-variant', `${details.variant}`);
          holderEl.setAttribute('data-product-url', `${details.url}`);
          if (details?.sku) {
            holderEl.setAttribute('data-product-sku', `${details.sku}`);
          }
          const imageEl = holderEl.querySelector('[data-product-role=image]');
          if (imageEl) {
            if (details.imgUrl) {
              imageEl.src = details.imgUrl;
              imageEl.style.display = '';
            } else {
              imageEl.style.display = 'none';
            }
          }
          const titleEl = holderEl.querySelector('[data-product-role=title]');
          if (titleEl) {
            titleEl.innerHTML = details.title;
          }
          const skuEl = holderEl.querySelector('[data-product-role=sku]');
          if (skuEl) {
            const labelMatch = skuEl.innerText.match(/(SKU|Cikkszám):/i);
            let label = 'SKU:';
            if (labelMatch && labelMatch.length > 1) {
              label = labelMatch[1];
            }
            skuEl.innerHTML = `${label}: ${details.sku}`;
          }
          const priceEl = holderEl.querySelector('[data-product-role=price]');
          const btn = holderEl.querySelector('[data-product-role=cta]');
          if (priceEl) {
            const moneyFormat = priceEl.getAttribute('data-product-format');
            if (moneyFormat) {
              priceEl.innerHTML = formatMoney(details.price * 100, moneyFormat);
            } else {
              priceEl.innerHTML = details.price.toString();
            }
            if (details.outOfStock && !btn) {
              priceEl.innerHTML = this.settings.ctaOutOfStockText;
            }
            if (details.outOfStock && btn) {
              priceEl.style.textDecoration = 'line-through';
            }
          }
          const oldPrice = details.originalPrice;
          const oldPriceEl = holderEl.querySelector('[data-product-role=old-price]');
          if (oldPriceEl) {
            if (oldPrice && !details.outOfStock) {
              const moneyFormat = oldPriceEl.getAttribute('data-product-format');
              if (moneyFormat) {
                oldPriceEl.innerHTML = formatMoney(oldPrice * 100, moneyFormat);
              } else {
                oldPriceEl.innerHTML = oldPrice.toString();
              }
              oldPriceEl.style.display = '';
            } else {
              oldPriceEl.style.display = 'none';
            }
          }
          if (details.outOfStock) {
            holderEl.classList.add('out-of-stock');
            if (btn) {
              btn.innerHTML = this.settings.ctaOutOfStockText;
            }
          }
        } else {
          console.warn(
            `product details of ${detailsIndex}th product in #${this.getElementId()} element could not be found`,
          );
        }
      }
      this.sendUserAlert({ missingProducts });
    }
  }

  sendUserAlert({ missingProducts }) {
    if (missingProducts.length) {
      reportUserAlert({
        type: 'ProductOutOfStock',
        links: { campaign: this.campaign, component: this },
        context: {
          products: missingProducts,
          platform: this.getType() || null, // in case of manual mode this will be undefined, so assign null to keep it in context
          mode: this.getMode(),
        },
      });
    }
  }

  getType() {
    if (this.settings.storeType) {
      return this.settings.storeType;
    }
    return this.settings.products && this.settings.products[0] && this.settings.products[0].type;
  }

  getMode() {
    return this.settings.mode || 'manual';
  }

  setMode(mode) {
    this.settings.mode = mode;
  }

  setProductFilter(filter) {
    this.settings.productFilter = filter;
  }

  getNrOfProducts() {
    return this.settings.nrOfProducts || 1;
  }

  setProductDetails(productDetails) {
    this.productDetails = productDetails;
    this.replaceProductDetails();
  }

  hasEnoughProductDetails() {
    const detailsCount = this.productDetails
      ? this.productDetails.filter((p) => !!p && !p.outOfStock).length
      : 0;
    return detailsCount >= this.getNrOfProducts();
  }

  isReadyToDisplay() {
    if (
      this.getMode() === 'manual' &&
      this.productDetails?.filter((p) => !!p)?.every((p) => p?.outOfStock)
    ) {
      return false;
    }
    if (this.getMode() === 'manual' && this.productDetails?.some((p) => p?.outOfStock)) {
      return this.showOutOfStock();
    }
    return this.isStatic() || (!!this.getProductIdentifiers() && this.hasEnoughProductDetails());
  }

  getClickAction() {
    if (!this.getType() && this.settings.clickAction && this.settings.clickAction !== 'redirect') {
      return 'redirect';
    }

    if (this.settings.clickAction) {
      return this.settings.clickAction;
    }

    if (this.getType() === 'shopify') {
      return 'add-to-cart';
    }
    return 'redirect';
  }

  getOpenInNewTab() {
    return !!this.settings.openInNewTab;
  }

  addFAIcon(btn, type) {
    btn.innerHTML = `<span class='fa ${type}' style='font-family:OmCustom;margin-right:5px'></span>
    <div class="om-product-cta-text"><span data-product-role="cta">${btn.innerText.trim()}</div>`;
  }

  getElementId() {
    return (this.element.getAttribute('id') || '').replace('ele_', '');
  }

  isProductOutOfStock(productId, variantId) {
    return this.productDetails
      .filter((currentProduct) => currentProduct)
      .some(
        (currentProduct) =>
          currentProduct.id === parseInt(productId, 10) &&
          currentProduct.variant === parseInt(variantId, 10) &&
          currentProduct.outOfStock,
      );
  }

  handleCTAClick(e) {
    e.stopPropagation();
    e.preventDefault();
    const holder = e.target.closest('.om-product-holder');
    if (!holder) return;
    const productNo =
      (holder.getAttribute('data-product-no') &&
        parseInt(holder.getAttribute('data-product-no'), 10)) ||
      0;
    const product =
      typeof productNo === 'number' && this.settings.products && this.settings.products[productNo];
    let productUrl =
      holder.getAttribute('data-product-url') || (product && product.url) || this.settings.url;
    if (productUrl) {
      productUrl = this.campaign.replaceText(productUrl, true);
    }
    let cartUrl;
    if (window.Shopify || window.ShopRenter) {
      cartUrl = `${window.location.protocol}//${window.location.host}/cart`;
    }
    const productInfo = {};
    if (this.isStatic()) {
      productInfo.url = productUrl;
    } else {
      productInfo.id = holder.getAttribute('data-product-id');
      const sku = holder.getAttribute('data-product-sku');
      if (sku) {
        productInfo.sku = sku;
      }

      productInfo.variant = holder.getAttribute('data-product-variant');
    }
    const converted = this.added.includes(`${productNo}`);
    if (!converted) {
      this.added.push(`${productNo}`);
      const frequencyEnabled = isNewFrequencyRuleEnabled();
      // callback determines if campaign is set to filled | empth callback is not filled
      const campaign = this.campaign;
      const callback = frequencyEnabled
        ? () => {
            campaign.markFilled();
            campaign.setFilled();
          }
        : () => {};
      this.campaign.convert(
        Object.assign(this.campaign.collectValues(), {
          needSetConverted: true,
          final: true,
          [`custom_fields[product_${this.getElementId()}]`]: JSON.stringify(productInfo),
        }),
        callback,
      );
    }
    const btn = holder.querySelector('.om-product-cta');
    const hasButton = btn != null;
    OptiMonk.sendMetrics('productClick', {
      hasButton,
      type: this.getType(),
      mode: this.getMode(),
      filterType: this.getProductFilterType(),
      action: this.getClickAction(),
      campaign: this.campaign.getId(),
    });
    if (this.getClickAction() === 'add-to-cart' && !converted) {
      if (this.isProductOutOfStock(productInfo.id, productInfo.variant)) {
        return;
      }
      if (btn) {
        this.addFAIcon(btn, 'om-animate-spin fa-spinner');
      }

      OptiMonk.CartIntegration.addToCart(productInfo.variant, productInfo.sku).then(() => {
        OptiMonk.triggerEvent(document.querySelector('html'), 'optimonk#shopify-cart-added');
        if (btn) {
          this.addFAIcon(btn, 'fa-check');
        }
      });
    } else if (this.getClickAction() === 'redirect-to-cart' && cartUrl) {
      if (this.getOpenInNewTab()) {
        window.open(cartUrl);
      } else {
        window.location.href = cartUrl;
      }
    } else if (this.getClickAction() !== 'add-to-cart' && productUrl) {
      if (this.getOpenInNewTab()) {
        window.open(productUrl);
      } else {
        window.location.href = productUrl;
      }
    } else {
      console.warn(`Unable to do any action on product click #${this.getElementId()}`);
    }
  }

  getClickAreas() {
    return this.element.querySelectorAll('.om-product-holder');
  }

  // MOBIL
  onMqlMatch() {
    const holders = this.getClickAreas();

    this.updateAlignmentForMobile(holders);
    this.moveCtaBoxIfNecessary(holders);

    this.updateElementClass(this.mobileLayoutClass, this.layoutClass);
  }

  // DESKTOP
  onMqlMismatch() {
    const holders = this.getClickAreas();

    this.updateAlignmentForDesktop(holders);

    this.updateElementClass(this.layoutClass, this.mobileLayoutClass);
  }

  updateAlignmentForMobile(holders) {
    if (this.settings?.alignItems === 'flex-start' && this.settings?.layout === 'vertical') {
      this.addVerticalAlignment(holders);
      this.removeLeftAlignment(holders);
    }

    if (this.settings?.mobileLayout === 'vertical') {
      this.addVerticalAlignment(holders);
    } else if (this.settings?.mobileLayout === 'horizontal') {
      this.removeVerticalAndLeftAlignment(holders);
    }
  }

  updateAlignmentForDesktop(holders) {
    if (this.settings?.alignItems === 'flex-start' && this.settings?.layout === 'vertical') {
      this.addVerticalAlignment(holders);
      this.addLeftAlignment(holders);
    }

    if (this.settings?.alignItems === 'center' && this.settings?.layout === 'vertical') {
      this.addVerticalAlignment(holders);
    } else if (this.settings?.layout === 'horizontal') {
      this.removeVerticalAndLeftAlignment(holders);
    }
  }

  addVerticalAlignment(holders) {
    holders.forEach((holder) => holder.classList.add('align-vertical'));
  }

  removeVerticalAndLeftAlignment(holders) {
    holders.forEach((holder) => {
      holder.classList.remove('align-vertical');
      holder.classList.remove('align-vertical-left');
    });
  }

  addLeftAlignment(holders) {
    holders.forEach((holder) => holder.classList.add('align-vertical-left'));
  }

  removeLeftAlignment(holders) {
    holders.forEach((holder) => holder.classList.remove('align-vertical-left'));
  }

  moveCtaBoxIfNecessary(holders) {
    if (this.settings?.layout === 'vertical' && this.settings?.mobileLayout === 'horizontal') {
      holders.forEach((holder) => {
        const details = holder.childNodes[1];
        const ctaBox = holder.childNodes[2];
        details?.appendChild(ctaBox);
        ctaBox.classList.remove('om-product-vertical-action');
      });
    }
  }

  updateElementClass(addClass, removeClass) {
    this.element.classList.add(addClass);
    this.element.classList.remove(removeClass);
  }

  mqlHandler(e) {
    if (e.matches) {
      this.onMqlMatch();
    } else {
      this.onMqlMismatch();
    }
  }

  handleMobile() {
    const mql = window.matchMedia('(max-width: 576px)');

    if (mql.matches) {
      this.onMqlMatch();
    } else {
      this.onMqlMismatch();
    }

    mql.addEventListener('change', this.mqlHandler.bind(this));
  }

  initListeners() {
    const clickAreas = this.getClickAreas();
    if (clickAreas.length === 0) {
      console.log('Product click areas not found, campaign will not show up', {
        caId: this.campaign.getId(),
        caName: this.campaign.getName(),
      });
      this.campaign.setShowable(false);
      return;
    }
    clickAreas.forEach((btn) => {
      OptiMonk.addListener(btn, 'click', this.handleCTAClick.bind(this));
    });
    OptiMonk.addListener(
      document.querySelector('html'),
      'optimonk#campaign-popup-show',
      (event) => {
        if (this.campaign.getId() === event.parameters.campaignId && !this.campaign.minimized) {
          if (event.parameters.pageNum === this.getPageNumber()) {
            OptiMonk.sendMetrics('product-appeared', {
              type: this.getType(),
              mode: this.getMode(),
              filterType: this.getProductFilterType(),
              campaign: this.campaign.getId(),
            });
          }
        }
      },
    );
    const handleValidatorFailed = ({ parameters }) => {
      const { campaign } = parameters;
      if (campaign.getId() === this.campaign.getId()) {
        if (
          !this.failedDisplayReported &&
          !this.isReadyToDisplay() &&
          !this.failedDisplayReported
        ) {
          let reason = 'unknown';
          if (!this.hasProductIdentifiers()) {
            reason = 'not-enough-product-ids';
          } else if (!this.hasEnoughProductDetails()) {
            reason = 'not-enough-product-details';
          }
          OptiMonk.sendMetrics('frontend-product-display-failed', {
            type: this.getType(),
            mode: this.getMode(),
            filterType: this.getProductFilterType(),
            campaign: this.campaign.getId(),
            path: window.location.pathname,
            reason,
          });
          this.failedDisplayReported = true;
          OptiMonk.removeListener(
            document.querySelector('html'),
            'optimonk#campaign-product-validator-failed',
            handleValidatorFailed,
          );
        }
      }
    };
    OptiMonk.addListener(
      document.querySelector('html'),
      'optimonk#campaign-product-validator-failed',
      handleValidatorFailed,
    );
  }

  initSwiper() {
    if (this.swiper || !this.element) return;
    const parentEl = this.element.parentElement;
    if (!this.element.classList.contains('om-swiper-container')) {
      const wrapperEl = document.createElement('div');
      const scrollbarEl = document.createElement('div');
      const prevEl = document.createElement('div');
      const nextEl = document.createElement('div');
      this.element.classList.add('om-swiper-container');
      wrapperEl.classList.add('om-swiper-wrapper');
      scrollbarEl.classList.add('om-swiper-scrollbar');
      prevEl.classList.add('om-swiper-button-prev');
      nextEl.classList.add('om-swiper-button-next');
      this.element.appendChild(wrapperEl);
      this.element.appendChild(scrollbarEl);
      parentEl.insertBefore(prevEl, this.element);
      parentEl.insertBefore(nextEl, this.element);
      this.element.querySelectorAll('.om-product-holder').forEach((el) => {
        el.classList.add('om-swiper-slide');
        wrapperEl.appendChild(el);
      });
    }
    const disabled = this.element.querySelectorAll('.om-swiper-slide').length === 1;
    if (disabled) {
      this.element.classList.add('om-swiper-disabled');
    }

    this.swiper = new OptiMonk.Swiper(this.element, {
      enabled: !disabled,
      slidesPerView: 'auto',
      grabCursor: false,
      navigation: {
        nextEl: parentEl.querySelector('.om-swiper-button-next'),
        prevEl: parentEl.querySelector('.om-swiper-button-prev'),
        disabledClass: 'om-swiper-button-disabled',
        hiddenClass: 'om-swiper-button-hidden',
        lockClass: 'om-swiper-button-lock',
      },
      scrollbar: {
        el: '.om-swiper-scrollbar',
        lockClass: 'om-swiper-scrollbar-lock',
        dragClass: 'om-swiper-scrollbar-drag',
      },
      mousewheel: !disabled,
      observer: true,
      observeParents: true,
      resizeObserver: true,
      watchOverflow: true,

      containerModifierClass: 'om-swiper-container-',
      slideClass: 'om-swiper-slide',
      slideBlankClass: 'om-swiper-slide-invisible-blank',
      slideActiveClass: 'om-swiper-slide-active',
      slideDuplicateActiveClass: 'om-swiper-slide-duplicate-active',
      slideVisibleClass: 'om-swiper-slide-visible',
      slideDuplicateClass: 'om-swiper-slide-duplicate',
      slideNextClass: 'om-swiper-slide-next',
      slideDuplicateNextClass: 'om-swiper-slide-duplicate-next',
      slidePrevClass: 'om-swiper-slide-prev',
      slideDuplicatePrevClass: 'om-swiper-slide-duplicate-prev',
      wrapperClass: 'om-swiper-wrapper',
    });
  }

  getComponentId() {
    return this.element?.id;
  }
}
Product.resizeListenerAdded = false;
