/* eslint-disable */
import { getVisitorAdapter } from '../shared/helpers';
import { reportEventJF } from '../shared/JFEventReport';
import { Cart as sharedCart } from '../shared/Visitor/Cart';

export function initUnas() {
  const adapter = getVisitorAdapter().createAdapter();
  function reloadCart() {
    window.OptiMonkEmbedded?.CartIntegration?.updateVisitorCart();
  }
  function addListener(obj, type, fn) {
    if (obj.addEventListener) {
      obj.addEventListener(type, fn, false);
    } else if (obj.attachEvent) {
      obj.attachEvent('on' + type, function () {
        fn.apply(obj, new Array(window.event));
      });
    } else {
      obj['on' + type] = fn;
    }
  }

  function addListenerToHtml(name, cb) {
    addListener(document.querySelector('html'), name, cb);
  }

  const handleAddToCart = (_, product) => {
    const item = {
      sku: product.sku,
      quantity: parseInt(product.qty_all, 10),
      price: product.price,
      name: product.name,
      productId: product.master_key,
    };

    adapter.Cart.add(product.master_key, item);

    const cartItems = Object.values(sharedCart?.getItems() ?? {});
    const cartTotal = cartItems.reduce(
      (sum, item) => sum + Number(item.price) * (Number(item.quantity) || 1),
      0,
    );

    reportEventJF('addToCart', {
      price: item.price,
      quantity: item.quantity,
      productId: item.productId,
      cartTotal,
    });

    reloadCart();
  };

  const handleRemoveCart = (_, product) => {
    adapter.Cart.remove(product.master_key);

    reloadCart();
  };

  const updateOmCart = (unasCart) => {
    adapter.Cart.clear();

    for (let i = 0; i < unasCart.items.length; i++) {
      adapter.Cart.add(unasCart.items[i].id, {
        sku: unasCart.items[i].sku,
        quantity: parseInt(unasCart.items[i].qty, 10),
        price: unasCart.items[i].price_unit,
        name: unasCart.items[i].name,
        productId: unasCart.items[i].id,
      });
    }
  };

  addListenerToHtml('optimonk#ready', function () {
    reloadCart();
  });

  window.UNAS.onAddToCart(handleAddToCart);
  window.UNAS.onRemoveFromCart(handleRemoveCart);
  window.UNAS.getCart(updateOmCart);
  reloadCart();
}
