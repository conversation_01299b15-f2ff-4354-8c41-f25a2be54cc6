import { getVisitorAdapter } from '../shared/helpers';
import { reportEventJF } from '../shared/JFEventReport';

export function initWooCommerce() {
  const adapter = getVisitorAdapter().createAdapter();

  function reloadCart() {
    window.OptiMonkEmbedded?.CartIntegration?.updateVisitorCart();
  }

  function addListener(obj, type, fn) {
    if (obj.addEventListener) {
      obj.addEventListener(type, fn, false);
    } else if (obj.attachEvent) {
      obj.attachEvent(`on${type}`, function () {
        fn.apply(obj, new Array(window.event));
      });
    } else {
      obj[`on${type}`] = fn;
    }
  }

  function addListenerToHtml(name, cb) {
    addListener(document.querySelector('html'), name, cb);
  }

  function handleAddToCart(event, fragments, cartHash, button) {
    reloadCart();

    if (button && button.data('product_id')) {
      const productId = button.data('product_id');
      const sku = button.data('product_sku');
      const quantity = button.data('quantity') || 1;
      const productName = button.data('product_name') || '';
      const price = button.data('price') || 0;

      const item = {
        id: productId,
        sku,
        quantity: parseInt(quantity, 10),
        price,
        name: productName,
        productId,
      };

      adapter.Cart.add(productId, item);
      reportEventJF('addToCart', item);
    }
  }

  function handleRemoveFromCart() {
    reloadCart();
  }

  function handleCartUpdate() {
    reloadCart();
  }

  function initEventListeners() {
    addListener(document.body, 'added_to_cart', handleAddToCart);
    addListener(document.body, 'removed_from_cart', handleRemoveFromCart);
    addListener(document.body, 'updated_cart_totals', handleCartUpdate);

    addListenerToHtml('optimonk#ready', function () {
      reloadCart();
    });
  }

  initEventListeners();

  if (typeof jQuery !== 'undefined') {
    jQuery(document).ready(function () {
      reloadCart();
    });
  } else {
    document.addEventListener('DOMContentLoaded', function () {
      reloadCart();
    });
  }
}
