div[class*="om-template-version-2"]
  .om-inputs
    border: none
.om-inputs
  outline: none
  padding: .625em
  margin: 0
  line-height: 1.3
  -webkit-appearance: none
  -moz-appearance: none
  appearance: none
  input[type="radio"],
  input[type="checkbox"]
    margin: 0 5px
    align-self: center
select.om-inputs
  padding-right: 2.25em
.om-inputs-wrapper
  line-height: 0
  display: flex

/* placeholder color set */
::-webkit-input-placeholder /* Chrome/Opera/Safari */
  color: inherit !important
::-moz-placeholder /* Firefox 19+ */
  color: inherit !important
:-ms-input-placeholder /* IE 10+ */
  color: inherit !important
:-moz-placeholder  /* Firefox 18- */
  color: inherit !important
/* /placeholder color set */

label
  font-size: inherit
  color: inherit
  margin-bottom: 0
  font-weight: inherit
  display: flex
  align-items: center
  line-height: 1.2
  letter-spacing: 0
  text-transform: initial
  > span
    font-size: inherit
    font-weight: inherit
    color: inherit
    vertical-align: inherit
    letter-spacing: 0
    text-transform: initial
select
  background-repeat: no-repeat !important
  background-position-x: calc(100% - .625rem) !important
  background-position-y: 50% !important
  background-image: url("data:image/svg+xml,%3Csvg id='arrow_down' data-name='arrow down' xmlns='http://www.w3.org/2000/svg' width='16' viewBox='0 0 20 12'%3E%3Cpath d='M15.88,3.22l-.6-.6A.38.38,0,0,0,15,2.5a.37.37,0,0,0-.27.12L10,7.35,5.27,2.62A.37.37,0,0,0,5,2.5a.38.38,0,0,0-.28.12l-.6.6A.38.38,0,0,0,4,3.5a.39.39,0,0,0,.12.28l5.6,5.6a.39.39,0,0,0,.56,0l5.6-5.6A.39.39,0,0,0,16,3.5.38.38,0,0,0,15.88,3.22Z'/%3E%3C/svg%3E") !important
  option
    color: black

/* checkbox */
input[type="checkbox"]+label
  top: auto
input[type="checkbox"]
  display: block
  margin: 0 .625em
  opacity: 1
  position: relative

/* /checkbox */

/* radio */

input[type="radio"]
  display: block
  margin: 0 .625em
  opacity: 1
  position: relative
  &:focus
    outline: none

/* /radio */

/* phone input */
.om-overlay:not(#editor-mode)
  .intl-tel-input
    .selected-flag
      .iti-flag
        height: 9px
        width: 14px
        background: #DBDBDB
.intl-tel-input
  display: block
  margin: 0
  flex: none
  @include intlTelIti
  .in-editor-tel-input-flags
    &.iti.om-iti-container,
    &.iti.iti--container
      height: 100%
      position: absolute
  .iti
    width: 100%
    font-family: Roboto, sans-serif
  .iti__arrow
    display: block !important
    border-top: 4px solid currentColor
    border-left: 3px solid transparent
    border-right: 3px solid transparent
    &--up
      border-top: 0
      border-bottom: 4px solid currentColor
      border-left: 3px solid transparent
      border-right: 3px solid transparent
  &.full-width
    width: 100%
  &.manual-width
    max-width: 100%
    display: flex
  .intl-tel-input,
  .iti--allow-dropdown
    display: flex
    max-width: 100%
    flex: auto
  @media screen and (max-width: 576px)
    &.mobile-full-width
      max-width: 100%
      flex-grow: 1
    &.mobile-manual-width
      width: auto
      max-width: 100%
      display: flex
  .selected-flag
    .iti-arrow
      border-top: 4px solid currentColor
      border-left: 3px solid transparent
      border-right: 3px solid transparent
      &.up
        border-top: 0
        border-bottom: 4px solid currentColor
        border-left: 3px solid transparent
        border-right: 3px solid transparent
  .country-list .divider
    width: auto
    height: auto
    background: initial

  .om-phone-input
    outline: none
    border: none
    padding-left: 52px !important

/* /phone input */

.om-input-picker
  display: flex
  justify-content: flex-start
  padding: .3125em .625em
  align-items: center
  overflow: hidden
  line-height: 1.2 !important
  margin: .25em
  &-button
    cursor: pointer
    padding: 0

    label
      padding: .3125em .625em
      width: 100%
      height: 100%
      display: flex
      align-items: center
      justify-content: center

  &-stars, &-yesno, &-smiley
    cursor: pointer
    padding: 0

    label
      width: 100%
      height: 100%
      display: flex
      align-items: center
      justify-content: center

  label
    position: relative

  input[type="radio"]
    appearance: none
    width: 1em
    height: 1em
    min-width: 1em
    border-radius: 100%
    border: 0
    box-shadow: inset 0 0 0 1px darken(#eee, 10%)
    background: #fff
    cursor: pointer
    padding: 0
    margin-left: 0
    &:checked
      border: 0
      box-shadow: inset 0 0 0 3px #eee
      background: darken(#eee, 30%)
      outline: none
  input[type="checkbox"]
    appearance: none
    width: 1em
    height: 1em
    min-width: 1em
    cursor: pointer
    background: white
    border: 1px solid darken(#eee, 10%)
    outline: none
    padding: 0
    margin-left: 0
    border-radius: 3px
    &:checked + span:before,
    &:checked + a:before
      font-family: 'OmCustom'
      content: '\e910'
      font-size: 12px
      position: absolute
      left: 0
      top: 50%
      transform: translateY(-50%)
      display: flex
      justify-content: center
      align-items: center
      height: 16px
      width: 16px
      pointer-events: none

.om-feedback
  &-horizontal
    display: flex
    align-items: center
.om-feedback-picker-group
  display: flex
  align-items: center
  flex-wrap: nowrap

  .om-feedback-option

    // Reset custom radio css of sites
    &:before, &:after
      width: 0
      height: 0
      padding: 0
      margin: 0
      background: none
      content: none
      border: none
      display: none

    .om-feedback-image
      width: auto
      height: 1em
      object-fit: contain

    .fa
      font-family: OmCustom !important
      font-weight: normal
      font-style: normal

    &:last-of-type
      margin-right: 0 !important

  &.om-feedback-stars
    direction: rtl

    .om-feedback-option
      &:last-of-type
        padding-left: 0 !important

  &.om-feedback-yesno, &.om-feedback-smiley
    .om-feedback-option
      position: relative
      transform: scale(1)
      transition: 250ms transform

/* new radio, checkbox style */
.om-input-picker-v2
  input
    display: none !important
  input[type="radio"]:not(.om-feedback-button)
    &:checked
      ~ label:after
        position: absolute
        left: 0
        top: 50%
        transform: translateY(-50%)
        border-radius: 100%
        content: ''
        display: inline-block
        border: 0
        width: 16px
        height: 16px
        min-width: 16px
        box-shadow: inset 0 0 0 3px #eee
        background: darken(#eee, 30%)
        outline: none
        text-align: center
        line-height: 15px
        border: 1px solid transparent
    ~ label:before
      content: ''
      position: static
      left: auto
      margin-right: 10px
      display: inline-block
      vertical-align: text-top
      width: 16px
      height: 16px
      min-width: 16px
      background: white
      border: 1px solid #d5d5d5
      border-radius: 100%
  input[type="radio"]
    &.om-feedback-button
      ~ label
        cursor: pointer
        &:hover
          opacity: .7
  input[type="checkbox"]
    ~ label:before
      content: ''
      position: static
      left: auto
      margin-right: 10px
      display: inline-block
      vertical-align: text-top
      width: 16px
      height: 16px
      min-width: 16px
      background: white
      border: 1px solid #d5d5d5
      border-radius: 3px
      top: inherit
    &:checked ~ label:after
      font-family: 'OmCustom'
      content: '\e910'
      font-size: 12px
      color: #a2a2a2
      position: absolute
      left: 0
      top: 50%
      transform: translateY(-50%)
      width: 16px
      height: 16px
      line-height: 16px
      display: inline-block
      text-align: center
      vertical-align: middle
      font-style: normal
      border-width: inherit
      border-style: inherit
      border-color: inherit
      background-color: inherit
/* /new radio, checkbox style */

.om-picker-group
  flex-wrap: wrap
  justify-content: center
  &.vertical-alignment
    .om-input-picker
      margin: 4px 0


.om-feedback-picker-group:not(.edit-mode)
  &.om-feedback-stars
    > input:checked ~ label
      .fa:before
        content: '\e917' // full-star

  &.om-feedback-yesno, &.om-feedback-smiley
    > input:checked + .om-feedback-option
      transform: scale(1.3)

&:not(.om-lucky-wheel-spinning):not(.om-scratch-in-progress)
  .om-feedback-picker-group:not(.edit-mode)
    &.om-feedback-stars
        > label:hover,
        > label:hover ~ label
          .fa:before
            content: '\e917' // full-star

    &.om-feedback-yesno, &.om-feedback-smiley
      > .om-feedback-option:hover
        transform: scale(1.3)

/* in checkbox */
.privacy-policy-link
  text-decoration: none
  color: inherit
  font-size: inherit
  &:hover
    text-decoration: underline
