import { getViewedProductIds } from '../../preload/shared/helpers';
import { <PERSON><PERSON> } from '../Cookie';

const VIEWED_PRODUCT_COOKIE_KEY = 'optiMonkViewedProducts';
const MAX_PRODUCT_VIEWED = 10;

export class ViewedProductAdapter {
  track() {
    const currentView = getViewedProductIds();

    if (!currentView) return;

    const existingData = this.getFromCookie();
    const updatedData = [
      currentView,
      ...existingData.filter(
        (item) =>
          item.productId !== currentView.productId || item.variantId !== currentView.variantId,
      ),
    ];

    const limitedData = updatedData.slice(0, MAX_PRODUCT_VIEWED);
    Cookie.local.setItem(VIEWED_PRODUCT_COOKIE_KEY, JSON.stringify(limitedData));
  }

  getFromCookie() {
    const cookieData = Cookie.local.getItem(VIEWED_PRODUCT_COOKIE_KEY);
    try {
      return cookieData ? JSON.parse(cookieData) : [];
    } catch (e) {
      return [];
    }
  }

  getViewedProducts({ filterCurrent = true, limit = 10 }) {
    let recentlyViewed = this.getFromCookie();

    if (filterCurrent) {
      const currentView = getViewedProductIds();
      if (currentView) {
        recentlyViewed = recentlyViewed.filter(
          (item) =>
            item.productId !== currentView.productId || item.variantId !== currentView.variantId,
        );
      }
    }

    const products = recentlyViewed.slice(0, limit);

    return products;
  }
}

let viewedProductAdapter;

export const getViewedProductAdapter = () => {
  if (!viewedProductAdapter) {
    viewedProductAdapter = new ViewedProductAdapter();
  }

  return viewedProductAdapter;
};
