import { <PERSON><PERSON> } from './Cookie';
import { getCookie as _getCookie, uuid } from '../preload/shared/helpers';
import { CLIENT_ID } from './constants/cookie';
import { SESSION_KEY as OM_DIAG_SESSION_KEY } from './DiagnosticTool/constants';

export const getCookie = (account) => {
  return _getCookie(account);
};

export const getAccountId = () => {
  return OptiMonkRegistry.account;
};

export const getClientId = () => {
  return OptiMonkRegistry.clientId;
};

export const getUUId = () => OptiMonkRegistry.uuid;

export const getPageUserId = (variantId) => {
  return `${OptiMonkRegistry.uuid}-${variantId}`;
};

export const getFeatures = () => {
  return OptiMonkRegistry.features;
};
export const getCookieManager = () => {
  return OptiMonkEmbedded.CookieManager;
};

export const isDebug = () => {
  return OptiMonkRegistry.isDebug;
};

export const isPreview = () => {
  return OptiMonkRegistry.isPreview;
};

export const strToInt = (value, radix = 10) => {
  return parseInt(value, radix);
};

export const convertToString = (value) => {
  return `${value}`;
};

export const getSessionTime = () => {
  return strToInt(Cookie.session.getItem('optiMonkSession'));
};

export const getTime = () => {
  return Math.trunc(new Date().getTime() / 1000);
};

/**
 * @returns {RequestService}
 */
export const getRequestService = () => {
  return OptiMonkEmbedded.RequestService;
};

export const getVisitorData = () => {
  return OptiMonkEmbedded.VisitorData;
};

export const getVisitorAdapter = () => {
  return OptiMonkEmbedded.Visitor;
};

export const setSessionStart = () => {
  if (!Cookie.session.getItem('optiMonkSession')) {
    Cookie.session.setItem('optiMonkSession', getTime());
  }
};

export const hideElement = (element) => {
  if (element) {
    element.style.display = 'none';
  }
};

export const each = function (array, callback) {
  let key;
  // eslint-disable-next-line
  for (key in array) {
    if (array.hasOwnProperty(key) && key !== 'length') {
      callback(key, array[key]);
    }
  }
};

export const addedListeners = [];

export const addListener = function (obj, type, fn) {
  if (obj.addEventListener) {
    obj.addEventListener(type, fn, false);
    addedListeners.push({ obj, type, fn });
  } else if (obj.attachEvent) {
    obj.attachEvent(`on${type}`, function () {
      fn.apply(obj, new Array(window.event));
    });
    addedListeners.push({ obj, type, fn });
  } else {
    obj[`on${type}`] = fn;
    addedListeners.push({ obj, type, fn });
  }
};

export const removeListener = function (obj, type, fn) {
  if (obj.removeEventListener) {
    obj.removeEventListener(type, fn, false);
  } else if (obj.detachEvent) {
    obj.detachEvent(`on${type}`, fn);
  } else if (obj[`on${type}`]) {
    delete obj[`on${type}`];
  }
};

export const removeAllListeners = function () {
  for (let i = 0; i < addedListeners.length; ++i) {
    const one = addedListeners[i];
    removeListener(one.obj, one.type, one.fn);
  }
  addedListeners.splice(0, addedListeners.length);
};

export const getVariantsTypeByCampaign = () => {
  return OptiMonkRegistry.variantsTypeByCampaign;
};

export const setClientId = () => {
  let clientId = Cookie.local.getItem(CLIENT_ID);
  if (!clientId) {
    clientId = uuid();
    Cookie.local.setItem(CLIENT_ID, clientId);
  }

  OptiMonkRegistry.clientId = clientId;
};

export const triggerEvent = function (element, eventName, parameters) {
  let event;
  if (document.createEvent) {
    event = document.createEvent('HTMLEvents');
    event.initEvent(eventName, true, true);
  } else if (document.createEventObject) {
    event = document.createEventObject();
    event.eventType = eventName;
  }

  event.eventName = eventName;
  event.parameters = parameters || {};

  if (element.dispatchEvent) {
    element.dispatchEvent(event);
  } else if (element.fireEvent) {
    element.fireEvent(`on${event.eventType}`, event);
  } else if (element[eventName]) {
    element[eventName]();
  } else if (element[`on${eventName}`]) {
    element[`on${eventName}`]();
  }
};

export const hasNewJsScript = () =>
  performance
    .getEntriesByType('resource')
    .filter((entry) => entry.initiatorType === 'script')
    .some((entry) => entry.startTime > performance.timeOrigin);

export const stringify = function (e, t, n) {
  return JSON.stringify(e, t, n);
};

export const parse = function (text, reviver) {
  return JSON.parse(text, reviver);
};

export const decodeURIComponentSafe = function (uri, mod) {
  if (typeof uri !== 'string') {
    return decodeURIComponent(uri);
  }
  let out = '';
  let i = 0;
  let x;
  const arr = uri.split(/(%(?:d0|d1)%.{2})/);
  for (let l = arr.length; i < l; i++) {
    try {
      x = decodeURIComponent(arr[i]);
    } catch (e) {
      x = mod ? arr[i].replace(/%(?!\d+)/g, '%25') : arr[i];
    }
    out += x;
  }
  return out;
};

export const parseHelper = function (k, v) {
  return typeof v !== 'object' ? decodeURIComponentSafe(v) : v;
};

export const trim = function (text) {
  return text === null ? '' : text.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, '');
};
export const asyncFilter = async (arr, asyncFn) => {
  const results = await Promise.all(arr.map(asyncFn));

  return arr.filter((_v, index) => results[index]);
};

export const addScript = function (name, src, dispatch) {
  if (src) {
    const scriptElement = document.createElement('script');
    if (dispatch !== false) {
      scriptElement.onload = function () {
        const event = new CustomEvent(`${name}Loaded`, { bubbles: true, cancelable: true });

        document.dispatchEvent(event);
      };
    }
    if (OptiMonkRegistry.nonce) {
      scriptElement.setAttribute('nonce', OptiMonkRegistry.nonce);
    }
    scriptElement.src = src;
    scriptElement.async = true;
    document.getElementsByTagName('head')[0].appendChild(scriptElement);
  } else {
    console.log('no src for script', name);
  }
};

export const waitForElement = (selector, cb, alternativeSelectors) => {
  return new Promise((resolve) => {
    let counter = 0;
    const MAX_NUMBER_OF_RETRIES = 20;

    const checkElement = () => {
      if (counter >= MAX_NUMBER_OF_RETRIES) {
        if (OptiMonkRegistry.isDebug) {
          console.log('wait for element retries limit reached', selector);
        }
        resolve(false);
        return;
      }

      let element = document.querySelector(selector);
      if (!element && alternativeSelectors) {
        for (let i = 0; i < alternativeSelectors.length; i++) {
          element = document.querySelector(alternativeSelectors[i]);
          if (element) break;
        }
      }

      if (element) {
        const result = cb(element);
        resolve(result === true);
        return;
      }

      counter++;
      window.requestAnimationFrame(checkElement);
    };

    checkElement();
  });
};

const getProductsIds = ({ oldItems, newItems, idFieldName }) => {
  return {
    oldProductsIds: oldItems.map((item) => `${item[idFieldName]}`),
    newProductsIds: newItems.map((item) => `${item[idFieldName]}`),
  };
};

const areSameProductIds = ({ oldProductsIds, newProductsIds }) => {
  return oldProductsIds.every((oldId) => newProductsIds.some((newId) => `${newId}` === `${oldId}`));
};

const getIncreasedQuantityProduct = ({ oldItems, newItems, idFieldName }) => {
  return newItems.find((newItem) => {
    const oldQuantity = oldItems.find(
      (oldItem) => oldItem[idFieldName] === newItem[idFieldName],
    )?.quantity;

    return newItem.quantity !== oldQuantity && newItem.quantity > oldQuantity;
  });
};

const getIncreasedQuantity = ({ oldItems, idFieldName, increasedQuantityProduct }) => {
  const { price, quantity: newQuantity, [idFieldName]: productId } = increasedQuantityProduct;
  const oldItem = oldItems.find(
    (item) => `${item[idFieldName]}` === `${increasedQuantityProduct[idFieldName]}`,
  );

  return {
    price,
    productId,
    quantity: newQuantity - oldItem.quantity,
  };
};

const handleIncreasedQuantity = ({
  oldItems,
  newItems,
  oldProductsIds,
  newProductsIds,
  idFieldName,
}) => {
  // if productsIds are not the same, then it was not an addToCart event, somehow a product got changed entirely
  if (!areSameProductIds({ oldProductsIds, newProductsIds })) {
    return null;
  }

  // the quantity can be only increased for one product at once
  const increasedQuantityProduct = getIncreasedQuantityProduct({ oldItems, newItems, idFieldName });
  if (!increasedQuantityProduct) return null;
  return getIncreasedQuantity({ oldItems, newItems, idFieldName, increasedQuantityProduct });
};

const handleAddedProduct = ({ newItems, idFieldName, oldProductsIds }) => {
  const newProductAdded = newItems.find(
    (newItem) => !oldProductsIds.includes(`${newItem[idFieldName]}`),
  );

  const { price, quantity, [idFieldName]: productId, sku } = newProductAdded;
  return {
    sku,
    price,
    quantity,
    productId,
  };
};

export const itemTypesChanged = ({ type, oldItems, newItems }) => {
  const idFieldName =
    type === 'shoprenter' || type === 'unas' || type === 'woocommerce' ? 'id' : 'product_id';

  const { oldProductsIds, newProductsIds } = getProductsIds({ oldItems, newItems, idFieldName });

  const oldIds = new Set(oldProductsIds);
  const newIds = new Set(newProductsIds);

  return oldIds.size !== newIds.size || new Set([...oldIds, ...newIds]).size !== oldIds.size;
};

export const getNewProductsInCart = ({ type, oldItems, newItems }) => {
  const idFieldName =
    type === 'shoprenter' || type === 'unas' || type === 'woocommerce' ? 'id' : 'product_id';

  const { oldProductsIds, newProductsIds } = getProductsIds({ oldItems, newItems, idFieldName });

  // if same amount of different items are in cart, than only one item quantity could have changed
  if (oldItems.length === newItems.length) {
    return handleIncreasedQuantity({
      oldItems,
      newItems,
      oldProductsIds,
      newProductsIds,
      idFieldName,
    });
  }

  // if the items length is not the same we need to check if new product was added or something was removed from cart
  // if item was removed, not added, do nothing

  if (oldItems.length > newItems.length) return null;

  return handleAddedProduct({ newItems, idFieldName, oldProductsIds });
};

export const getUsedFontFamilies = () => {
  let fontFamilies = [];
  try {
    const { fonts } = document;
    const it = fonts.entries();

    const arr = [];
    let done = false;

    while (!done) {
      const font = it.next();
      if (!font.done) {
        arr.push(font.value[0].family);
      } else {
        done = font.done;
      }
    }

    // converted to set then arr to filter repetitive values
    fontFamilies = [...new Set(arr)];
  } catch {
    console.error('Error while getting used fonts');
  }

  return fontFamilies;
};

export const isNewFrequencyRuleEnabled = () => {
  return !!getFeatures().FREQUENCY_RULE_V2;
};

export const createCampaignsArray = (campaignMetaData) => {
  return campaignMetaData.reduce((previous, campaign) => {
    return {
      ...previous,
      [campaign.id]: campaign,
    };
  }, {});
};

export const filterCampaignMetaDataByAbTestManager = (campaignMetaData, abTestManager) => {
  return campaignMetaData.filter((campaignData) => {
    return abTestManager.isActiveVariant(campaignData.id, campaignData.variantId);
  });
};

export const isOmDiagSession = () => {
  if (typeof localStorage !== 'object') return false;
  return !!sessionStorage.getItem(OM_DIAG_SESSION_KEY);
};
