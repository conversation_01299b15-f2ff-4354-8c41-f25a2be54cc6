import {
  BUILD_ADD_2_CART_CONFIG,
  BUILD_FETCH_CART_CONFIG,
  HANDLE_ADD_2_CART_RESPONSE,
  HANDLE_FETCH_CART,
  LOG_LEVEL_INFO,
} from './constants';
import { BaseIntegration } from './BaseIntegration';
import { reportEventJF } from '../JFEventReport';
import { itemTypesChanged } from '../helpers';

const CART_URI = '/index.php?plugin=optimonk&action=cartData';
const ADD_2_CART_URI = '/index.php?plugin=optimonk&action=addToCart';
const CART_COUNTER_CLASS = '.omCartCount';

export class Woocommerce extends BaseIntegration {
  tryUpdateProductCount(count) {
    const countElements = document.querySelectorAll(CART_COUNTER_CLASS);
    for (let i = 0; i < countElements.length; i++) {
      countElements[i].textContent = count;
    }
  }

  [HANDLE_ADD_2_CART_RESPONSE]({ result, id }) {
    OptiMonkEmbedded.CartIntegration.updateVisitorCart();
    OptiMonkEmbedded.CartIntegration.dispatch(result, id);
    if (typeof jQuery !== 'undefined') {
      jQuery(document.body).trigger('added_to_cart');
    }
  }

  [BUILD_ADD_2_CART_CONFIG](data, isFetch) {
    const config = { url: ADD_2_CART_URI, method: 'POST' };

    const payload = new FormData();

    payload.append('id', data.id);
    payload.append('quantity', 1);

    if (isFetch) {
      const fetchConfig = {
        ...config,
        body: payload,
        headers: {},
      };

      return fetchConfig;
    }

    config.payload = payload;
    return config;
  }

  cartItemMapper(item) {
    return {
      id: item.id,
      name: item.name,
      quantity: item.quantity,
      price: item.price,
      sku: item.sku,
    };
  }

  dispatchProductTypeChangeEvent({ oldItems, newItems }) {
    const changed = itemTypesChanged({
      type: 'woocommerce',
      oldItems: Object.values(oldItems),
      newItems,
    });

    if (changed) {
      const event = new Event('optimonk#cart-product-types-changed');
      document.querySelector('html').dispatchEvent(event);
    }
  }

  [HANDLE_FETCH_CART]({ result, getAdapter }) {
    this.logger.log(LOG_LEVEL_INFO, HANDLE_FETCH_CART, ...arguments);

    const { cart, avs } = result;

    getAdapter().then((adapter) => {
      const oldItems = adapter.Cart.get();
      const newItems = [];

      adapter.Cart.clear();

      cart.forEach((item) => {
        const mapped = this.cartItemMapper(item);

        adapter.Cart.add(item.id, mapped);
        newItems.push(mapped);
      });

      this.dispatchProductTypeChangeEvent({ oldItems, newItems });
    });

    reportEventJF('cartContent', {
      itemCount: avs.total_number_of_cart_items,
      items: JSON.stringify(
        cart.map((item) => ({
          sku: item.sku,
          productId: item.id,
          quantity: item.quantity,
          price: item.price,
        })),
      ),
      totalPrice: avs.cart_tital,
    });

    this.tryUpdateProductCount(avs.total_number_of_cart_items);
  }

  [BUILD_FETCH_CART_CONFIG](_, isFetch) {
    this.logger.log(LOG_LEVEL_INFO, BUILD_FETCH_CART_CONFIG, ...arguments);

    const config = {
      url: `${CART_URI}`,
      method: 'GET',
    };

    this.logger.log(LOG_LEVEL_INFO, { config });
    return config;
  }
}
