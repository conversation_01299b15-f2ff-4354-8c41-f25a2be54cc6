import {
  BUILD_ADD_2_CART_CONFIG,
  BUILD_FETCH_CART_CONFIG,
  HANDLE_ADD_2_CART_RESPONSE,
  HANDLE_FETCH_CART,
  LOG_LEVEL_INFO,
} from './constants';
import { BaseIntegration } from './BaseIntegration';
import { reportEventJF } from '../JFEventReport';
import { itemTypesChanged } from '../helpers';

const CART_URI = '/shop_ajax/api.php';
const ADD_2_CART_URI = '/shop_ajax/ajax_cart.php';
const CART_COUNTER_CLASS = '.omCartCount';

export class UNAS extends BaseIntegration {
  tryUpdateProductCount(count) {
    const countElements = document.querySelectorAll(CART_COUNTER_CLASS);
    for (let i = 0; i < countElements.length; i++) {
      countElements[i].textContent = count;
    }
  }

  [HANDLE_ADD_2_CART_RESPONSE]({ result, id }) {
    OptiMonkEmbedded.CartIntegration.updateVisitorCart();
    OptiMonkEmbedded.CartIntegration.dispatch(result, id);

    if (typeof window.cart_refresh === 'function') {
      window.cart_refresh();
    }
  }

  [BUILD_ADD_2_CART_CONFIG](data, isFetch) {
    const config = { url: ADD_2_CART_URI, method: 'POST' };

    const payload = new FormData();

    payload.append('get_ajax', 1);
    payload.append('result_type', 'json');
    payload.append('lang_master', 'hu');
    payload.append('action', 'add');
    payload.append('sku', data.sku);
    payload.append('qty', 1);
    payload.append('api_auth', window.UNAS.api_auth);

    if (isFetch) {
      return {
        ...config,
        body: payload,
      };
    }

    config.payload = payload;
    return config;
  }

  cartItemMapper(item) {
    return {
      id: item.id,
      name: item.name,
      quantity: item.quantity,
      price: item.price,
      sku: item.sku,
    };
  }

  dispatchProductTypeChangeEvent({ oldItems, newItems }) {
    const changed = itemTypesChanged({
      type: 'unas',
      oldItems: Object.values(oldItems),
      newItems,
    });

    if (changed) {
      const event = new Event('optimonk#cart-product-types-changed');
      document.querySelector('html').dispatchEvent(event);
    }
  }

  [HANDLE_FETCH_CART]({ result, getAdapter }) {
    this.logger.log(LOG_LEVEL_INFO, HANDLE_FETCH_CART, ...arguments);

    const { items } = result;

    const itemCount = items.reduce((sum, item) => sum + Number(item.quantity), 0);
    const totalPrice = items.reduce((sum, item) => sum + Number(item.price), 0);

    getAdapter().then((adapter) => {
      const oldItems = adapter.Cart.get();
      const newItems = [];

      adapter.Cart.clear();

      items.forEach((item) => {
        const mapped = this.cartItemMapper(item);

        adapter.Cart.add(item.id, mapped);
        newItems.push(mapped);
      });

      this.dispatchProductTypeChangeEvent({ oldItems, newItems });
    });

    reportEventJF('cartContent', {
      itemCount,
      items: JSON.stringify(
        items.map((item) => ({
          sku: item.sku,
          productId: item.product_id,
          quantity: item.quantity,
          price: item.price,
        })),
      ),
      totalPrice,
    });

    this.tryUpdateProductCount(itemCount);
  }

  [BUILD_FETCH_CART_CONFIG](_, isFetch) {
    this.logger.log(LOG_LEVEL_INFO, BUILD_FETCH_CART_CONFIG, ...arguments);

    const params = new URLSearchParams({
      get_ajax: 1,
      action: 'getCart',
      api_auth: window.UNAS.api_auth,
    });

    const config = {
      url: `${CART_URI}?${params.toString()}`,
      method: 'GET',
    };

    this.logger.log(LOG_LEVEL_INFO, { config });
    return config;
  }
}
