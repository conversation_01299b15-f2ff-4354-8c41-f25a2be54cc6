import { Storage } from '../../Storage';
import { Cart as SharedCart } from '../../Visitor/Cart';

const readCartData = () => {
  return Storage.session.getItem('OptiMonkVisitorCart') || {};
};

export const Cart = {
  clear: () => {
    // this is a read-only Cart using only in Validators
  },
  getItems: () => {
    return readCartData();
  },
  totalItems: () => {
    const items = Object.values(readCartData());
    return items.reduce((prev, current) => {
      return prev + parseFloat(current.quantity);
    }, 0);
  },
  total: () => {
    // Using the Cart from shared/Visitor/Cart.js
    return SharedCart.total();
  },
  totalLinePrice: () => {
    const items = Object.values(readCartData());
    return items.reduce((prev, item) => {
      return prev + parseFloat(item.line_price);
    }, 0);
  },
};
