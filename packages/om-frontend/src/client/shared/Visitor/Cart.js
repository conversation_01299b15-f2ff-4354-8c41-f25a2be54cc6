import { each } from '../helpers';
import { Observable } from './Observable';
import { Observer } from './Observer/index';
import { Logger } from '../Logger';
import { Unas } from '../../preload/Engine/EngineDetectors/Unas';

const consoleError = function (message) {
  Logger.error(message);
};
const obs = new Observable();
let items = {};
const validateAddItem = function (id, item) {
  return (
    id !== undefined &&
    item !== undefined &&
    item.hasOwnProperty('quantity') &&
    item.hasOwnProperty('price')
  );
};
obs.attach(Observer.CartStorageHandler);
obs.attach(Observer.CartCacheClearer);
export const Cart = {
  addItem(id, item) {
    if (validateAddItem(id, item)) {
      item.id = id;
      items[id] = item;
      obs.notify(Cart, { id, data: item, event: 'addItem', items });
    } else {
      consoleError('Invalid cart item. You need to specify the id, quantity and price of it.');
    }
  },
  addItems(items) {
    each(items, function (i, item) {
      Cart.addItem(item.id, item);
    });
  },
  hasItem(id) {
    return items.hasOwnProperty(id);
  },
  getItem(id) {
    if (Cart.hasItem(id)) {
      return items[id];
    }
    return undefined;
  },
  getItems() {
    return items;
  },
  removeItem(id) {
    if (Cart.hasItem(id)) {
      delete items[id];
      obs.notify(Cart, { id, event: 'removeItem', items });
    }
  },
  clear() {
    items = {};
    obs.notify(Cart, { event: 'clear', items });
  },
  total() {
    if (new Unas().check()) {
      return Cart.unasTotal();
    }
    return Cart.defaultTotal();
  },
  defaultTotal() {
    let total = 0.0;
    each(items, function (i, item) {
      total += parseFloat(item.quantity) * parseFloat(item.price);
    });
    return total;
  },
  unasTotal() {
    let total = 0;
    each(items, function (i, item) {
      total += parseInt(item.price, 10);
    });
    return total;
  },
  totalLinePrice() {
    let total = 0.0;
    each(items, function (i, item) {
      total += parseFloat(item.line_price);
    });
    return total;
  },
  totalItems() {
    let total = 0.0;
    each(items, function (i, item) {
      total += parseFloat(item.quantity);
    });
    return total;
  },
  attach(observer) {
    obs.attach(observer);
  },
};
obs.notify(Cart, { event: 'construct', items });
