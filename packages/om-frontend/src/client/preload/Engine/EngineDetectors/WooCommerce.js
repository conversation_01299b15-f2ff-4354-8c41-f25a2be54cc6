import { Abstract } from './Abstract';

export class WooCommerce extends Abstract {
  constructor(...args) {
    super(...args);
    this.type = 'woocommerce';
  }

  check() {
    let hasWoocommerceClass = false;
    const body = document.querySelector('body');
    if (body) {
      hasWoocommerceClass = body.classList.contains('woocommerce');
    }
    return !!window.woocommerce_params || hasWoocommerceClass;
  }

  getShopId() {
    if (window.WooDataForOM?.shop?.shopId) {
      return window.WooDataForOM?.shop?.shopId;
    }
    return null;
  }
}
