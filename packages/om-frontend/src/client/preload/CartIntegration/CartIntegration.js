/* eslint-disable prefer-rest-params */
import { OptiMonk } from '../../OptiMonk';
import { native } from '../shared/native';
import { BaseIntegration } from './BaseIntegration';
import { Shoprenter } from './Shoprenter';
import {
  BUILD_ADD_2_CART_CONFIG,
  HANDLE_ADD_2_CART_RESPONSE,
  BUILD_FETCH_CART_CONFIG,
  HANDLE_FETCH_CART,
  LOG_CART_INTEGRATION_KEY,
  LOG_LEVEL_INFO,
  LOG_LEVEL_WARN,
  LOG_LEVEL_ERROR,
} from './constants';
import { DebugLogger } from './DebugLogger';
import { Shopify } from './Shopify';
import { UNAS } from '../../shared/CartIntegration/UNAS';
import { Woocommerce } from '../../shared/CartIntegration/Woocommerce';

const CART_UPDATE_EVENT = 'optimonk#cart-update';

const integrations = {
  woocommerce: Woocommerce,
  unas: UNAS,
  shoprenter: Shoprenter,
  shopify: Shopify,
  BaseIntegration,
};

const TYPE_ADD_2_CART = 'TYPE_ADD_2_CART';
const TYPE_FETCH_CART = 'TYPE_FETCH_CART';

const TYPE_METHODS = {
  [TYPE_ADD_2_CART]: {
    handler: HANDLE_ADD_2_CART_RESPONSE,
    requestConfigBuilder: BUILD_ADD_2_CART_CONFIG,
  },
  [TYPE_FETCH_CART]: {
    handler: HANDLE_FETCH_CART,
    requestConfigBuilder: BUILD_FETCH_CART_CONFIG,
  },
};

export class CartIntegration {
  constructor(debug = false) {
    this.adapter = null;
    this.logger = new DebugLogger(LOG_CART_INTEGRATION_KEY, debug);
    const engine = OptiMonk.Engine.getType();
    this.integration = integrations[engine]
      ? new integrations[engine](this.logger.child(engine))
      : null;
    if (!this.integration) {
      this.logger.log(LOG_LEVEL_WARN, 'Not supported site engine');
    }
  }

  createAdapter() {
    if (!this.adapter) {
      this.adapter = new Promise((resolve) => {
        if (OptiMonk?.Visitor?.createAdapter) {
          resolve(OptiMonk.Visitor.createAdapter());
        } else {
          document.querySelector('html').addEventListener('optimonk#ready', () => {
            resolve(OptiMonk.Visitor.createAdapter());
          });
        }
      });
    }
  }

  getAdapter() {
    return this.adapter;
  }

  makeFetch(config) {
    this.logger.log(LOG_LEVEL_INFO, 'makeFetch', ...arguments);
    const { url, ...extraConfigs } = config;
    return native.fetch(config.url, { ...extraConfigs });
  }

  makeXHR(config) {
    this.logger.log(LOG_LEVEL_INFO, 'makeXHR', ...arguments);
    return new Promise((resolve, reject) => {
      const xhr = new native.XMLHttpRequest();
      xhr.open(config.method, config.url, true);
      xhr.addEventListener('error', (e) => reject(e));
      xhr.addEventListener('load', () => resolve(xhr.responseText));
      xhr.send(config.payload);
    });
  }

  async handle(handler, data) {
    this.logger.log(LOG_LEVEL_INFO, 'handle', ...arguments);
    const response = await data.request;
    const result = data.isFetch ? await response.json() : JSON.parse(response);
    this.logger.log(LOG_LEVEL_INFO, { result });
    return this.integration[handler]({ result, ...data, getAdapter: () => this.adapter });
  }

  makeRequest(data) {
    const hasFetch = !!native.fetch;
    this.logger.log(LOG_LEVEL_INFO, 'makeRequest', ...arguments);
    const { type, ...usefulData } = data;
    const methods = TYPE_METHODS[data.type];
    this.logger.log(LOG_LEVEL_INFO, { methods, hasFetch });
    const config = this.integration[methods.requestConfigBuilder](usefulData, hasFetch);
    let request;
    if (hasFetch) {
      request = this.makeFetch(config);
    } else {
      request = this.makeXHR(config);
    }
    return this.handle(methods.handler, {
      request,
      isFetch: hasFetch,
      ...usefulData,
    });
  }

  checkIntegration() {
    if (!this.integration) {
      const message = 'Not supported site engine';
      this.logger.log(LOG_LEVEL_ERROR, message);
      throw new Error('Not supported site engine');
    }
  }

  addToCart(id, sku) {
    this.checkIntegration();
    this.logger.log(LOG_LEVEL_INFO, 'addToCart', ...arguments);
    return this.makeRequest({ type: TYPE_ADD_2_CART, id, sku });
  }

  updateCartWith(given) {
    this.logger.log(LOG_LEVEL_INFO, 'updateCartWith', ...arguments);
    const result = { added: [], failed: [] };
    this.getAdapter().then((adapter) => {
      adapter.Cart.clear();
      given.forEach((item) => {
        if (item.id && item.quantity && item.price) {
          const toAdd = this.integration ? this.integration.cartItemMapper(item) : item;
          adapter.Cart.add(item.id, toAdd);
          result.added.push(toAdd);
        } else {
          result.failed.push(item);
        }
      });
    });

    return result;
  }

  checkUpdateCartResult(result) {
    if (!result) return this.logger.log(LOG_LEVEL_ERROR, 'Result missing');
    if (result.failed.length) {
      this.logger.log(
        LOG_LEVEL_WARN,
        'One or all mandatory property missing. Required properties: id, quantity, price',
        result.failed,
      );
    }
  }

  async updateVisitorCart(given) {
    this.logger.log(LOG_LEVEL_INFO, 'updateVisitorCart', ...arguments);

    if (window.OptiMonkEmbedded?.CartIntegration) return;

    this.createAdapter();

    if (given) {
      const result = this.updateCartWith(given);
      this.checkUpdateCartResult(result);
      return result;
    }
    this.checkIntegration();
    const result = await this.makeRequest({ type: TYPE_FETCH_CART });
    OptiMonk.triggerEvent(document.querySelector('html'), 'optimonk#cart-update');
    return result;
  }

  dispatch(data, productId) {
    this.logger.log(LOG_LEVEL_INFO, 'dispatch', ...arguments);
    OptiMonk.triggerEvent(document.querySelector('html'), CART_UPDATE_EVENT, { data, productId });
  }
}
