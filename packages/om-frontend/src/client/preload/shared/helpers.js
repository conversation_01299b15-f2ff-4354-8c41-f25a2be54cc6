import { <PERSON><PERSON> } from '../../shared/Cookie';
import { LZString } from '../../shared/lz-string';
import { parse } from '../Util/utils';
import { OptiMonk } from '../../OptiMonk';
import { native } from './native';
import { _get } from '../../shared/Utils/object';
import { updateAccountCookie } from './cookieHelpers';
import { getAccountId } from '../../shared/helpers';

export const each = function (array, callback) {
  let key;
  // eslint-disable-next-line
  for (key in array) {
    if (array.hasOwnProperty(key) && key !== 'length') {
      callback(key, array[key]);
    }
  }
};

export const getCookie = function (account) {
  let cookies = Cookie.local.getItem('optiMonkClient') || '{}';
  let isNewCookie = cookies.indexOf('{') === -1;

  cookies = parse(isNewCookie ? LZString.decompressFromBase64(cookies) : cookies);
  account = account || getAccountId();

  if (!cookies[account]) {
    Object.keys(cookies).forEach(function (accountId) {
      const ONE_DAY_IN_SEC = 60 * 60 * 24;
      let shouldDeleteCookie = ['133806', '44'].indexOf(accountId) === -1;
      try {
        const diffInDays =
          (new Date().getTime() / 1000 - new Date(cookies[accountId].lv).getTime() || null) /
          ONE_DAY_IN_SEC;
        shouldDeleteCookie = shouldDeleteCookie && diffInDays > 30;
      } catch (e) {
        console.error('[OM] Multi account cookie deletion error', e);
      }

      if (shouldDeleteCookie) {
        delete cookies[accountId];
        console.log('delete account cookie', accountId);
      }
    });
    cookies[account] = {};
    return cookies;
  }

  if (Array.isArray(cookies[account].ca)) {
    const cookieSlices = Cookie.local.getItemSlicesRaw('optiMonkClient');
    const slices = cookies[account].ca;
    for (let index = 0, cookieLength = cookieSlices.length; index < cookieLength; index += 1) {
      const actual = cookieSlices[index];
      isNewCookie = actual.value.indexOf('{') === -1;
      let value = actual.value;

      if (isNewCookie) {
        value = LZString.decompressFromBase64(value);
      }

      Array.prototype.push.apply(slices, JSON.parse(decodeURIComponent(value)));
    }
    const cookieObject = {};

    slices.forEach(function (item) {
      const campaignId = item.caId;
      delete item.caId;

      cookieObject[campaignId] = item;
    });

    cookies[account].ca = cookieObject;

    return cookies;
  }
  return cookies;
};

export const updateCampaignCookie = (accountId, campaignId, campaignCookie) => {
  const browserCookie = getCookie();
  if (!browserCookie[accountId].ca) browserCookie[accountId].ca = {};
  browserCookie[accountId].ca[campaignId] = campaignCookie;
  updateAccountCookie(accountId, browserCookie);
  OptiMonkRegistry.Cookie.ca[campaignId] = campaignCookie;
};

export const updateCampaignCookies = (accountId, campaignCookiesFromServer) => {
  const browserCookie = getCookie();
  if (!browserCookie[accountId]?.ca) {
    browserCookie[accountId].ca = {};
  }

  Object.keys(campaignCookiesFromServer).forEach((campaignId) => {
    if (window.OptiMonkEmbedded.campaigns[campaignId]) return;
    browserCookie[accountId].ca[campaignId] = campaignCookiesFromServer[campaignId];
  });

  updateAccountCookie(accountId, browserCookie);
};

export const isCookieEnabled = function () {
  const navigatorCookie = navigator.cookieEnabled;
  let cookieEnabled = !!navigatorCookie;
  if (typeof navigatorCookie === 'undefined' && !cookieEnabled) {
    document.cookie = 'isCookie';
    // eslint-disable-next-line
    cookieEnabled = document.cookie.indexOf('isCookie') != -1;
  }
  return cookieEnabled;
};

export const isPreRender = function () {
  return !!document.webkitVisibilityState && document.webkitVisibilityState === 'prerender';
};

export const shouldContinueLoading = function () {
  return isCookieEnabled() && isPreRender() === false;
};

export const addResponseToHead = function (parsedResponse) {
  if (window.OMReloading) return; // if SPA loading can while want to add JS, error occur

  const scriptObject = document.createElement('script');
  const txtNode = document.createTextNode(parsedResponse);
  scriptObject.appendChild(txtNode);
  scriptObject.type = 'text/javascript';

  const preloadElement = document.querySelector(
    'script[src*="front.optimonk.com"][src$="preload.js"]',
  );
  if (preloadElement && preloadElement.nonce) {
    scriptObject.setAttribute('nonce', preloadElement.nonce);
  }
  try {
    const head = document.querySelector('head');
    head.appendChild(scriptObject);
  } catch (e) {
    console.warn(e);
  }
};

export const triggerEvent = function (element, eventName, parameters) {
  let event;
  if (document.createEvent) {
    event = document.createEvent('HTMLEvents');
    event.initEvent(eventName, true, true);
  } else if (document.createEventObject) {
    event = document.createEventObject();
    event.eventType = eventName;
  }

  event.eventName = eventName;
  event.parameters = parameters || {};

  if (element.dispatchEvent) {
    element.dispatchEvent(event);
  } else if (element.fireEvent) {
    element.fireEvent(`on${event.eventType}`, event);
  } else if (element[eventName]) {
    element[eventName]();
  } else if (element[`on${eventName}`]) {
    element[`on${eventName}`]();
  }
};

export const triggerConversion = (campaignId, details) => {
  const html = document.querySelector('html');
  triggerEvent(html, 'optimonk#campaign-conversion', {
    campaignId,
    elementDetails: details,
  });
};

export const uuid = function () {
  function s4() {
    return Math.floor((1 + Math.random()) * 0x10000)
      .toString(16)
      .substring(1);
  }
  return `${s4() + s4()}-${s4()}-${s4()}-${s4()}-${s4()}${s4()}${s4()}`;
};

export const requestIdleCallback = function (cb, options) {
  if (window.requestIdleCallback) {
    return window.requestIdleCallback(cb, options);
  }

  const start = Date.now();
  const timeout = options && typeof options.timeout === 'number' ? options.timeout : 50;

  return setTimeout(function () {
    cb({
      didTimeout: false,
      timeRemaining() {
        return Math.max(0, timeout - (Date.now() - start));
      },
    });
  }, 1);
};

// eslint-disable-next-line
export const cancelIdleCallback = function (id) {
  if (window.cancelIdleCallback) {
    return window.cancelIdleCallback(id);
  }

  clearTimeout(id);
};

export const sendMetrics = function (event, properties) {
  properties.frontend = true;
  properties.device = OptiMonkRegistry.isMobile ? 'mobile' : 'desktop';
  native.fetch(`${OptiMonkRegistry.baseUrl}/metrics`, {
    method: 'POST',
    mode: 'cors',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      account: getAccountId(),
      event: `frontend-${event}`,
      properties,
    }),
  });
};

export const getAddToCartForm = () => {
  const forms = document.querySelector(
    'form[action^="/cart/add"][data-delm-is-primary-form="true"]',
  )
    ? document.querySelectorAll('form[action*="/cart/add"][data-delm-is-primary-form="true"]')
    : document.querySelectorAll('form[action*="/cart/add"]');

  const correctForm = [...forms].find(
    (f) => f.getAttribute('data-product-id') || f.getAttribute('data-productid'),
  );
  return correctForm ?? forms?.[0];
};

export const getSelectedVariantId = function () {
  if (window.ShopifyAnalytics) {
    const addToCartForm = getAddToCartForm();

    if (addToCartForm) {
      const variantSelect = addToCartForm.querySelector('[name="id"], [name="variantId"][checked]');

      if (variantSelect) {
        return variantSelect.value;
      }
    }

    if (typeof URLSearchParams !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const urlVariantId = urlParams.get('variant');

      if (urlVariantId) {
        return urlVariantId;
      }
    }

    return _get(window.ShopifyAnalytics, 'meta.selectedVariantId') || null;
  }
  if (window.ShopRenter) {
    return _get(window.ShopRenter, 'product.id') || null;
  }

  return null;
};

export const getShopifyVariantsFromDOM = () => {
  const addToCartForm = getAddToCartForm();

  const variantsOptions =
    addToCartForm?.querySelectorAll?.('[name="id"] option, [name="variantId"]') || [];

  return Array.from(variantsOptions)?.map?.((option) => {
    return {
      id: option.value,
    };
  });
};

export const getViewedProductIds = function () {
  let productId = null;
  let variantId = getSelectedVariantId();

  if (window.ShopifyAnalytics) {
    productId = _get(window.ShopifyAnalytics, 'meta.product.id');

    const variantsFromDOM = getShopifyVariantsFromDOM();
    const variants = variantsFromDOM.length
      ? variantsFromDOM
      : _get(window.ShopifyAnalytics, 'meta.product.variants');

    if (variants) {
      const variant = variants.find(function (v) {
        return `${v.id}` === `${variantId}`;
      });

      if (!variant) {
        variantId = null;
      }
    }
  } else if (window.ShopRenter) {
    variantId = _get(window.ShopRenter, 'product.id');
    productId = _get(window.ShopRenter, 'product.parent.id') || variantId;
  } else if (window.UNAS) {
    productId = _get(window.UNAS, 'shop.product_id');
    variantId = productId;
  } else if (window.WooDataForOM) {
    productId = _get(window.WooDataForOM, 'product.id');
    variantId = productId;
  }

  if (productId && variantId) {
    return {
      productId: `${productId}`,
      variantId: `${variantId}`,
    };
  }

  return null;
};

export const getExperimentalSetting = function (key) {
  return OptiMonk.experimentalSettings?.[key];
};

OptiMonk.loadScript = function (path, cb) {
  if (path.charAt(0) !== '/') path = `/${path}`;
  OptiMonkEmbedded.AssetManager.loadAsset(OptiMonkRegistry.getAssetUrlFor(path), 'js', cb);
};
