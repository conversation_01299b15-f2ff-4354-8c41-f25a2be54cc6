import { ObjectId } from 'mongodb';
import { CampaignStatus, TeaserStatus, Device, FrontendRule, FrontendEvent } from './enums';

export interface ICampaignCookieShort {
  act?: 0 | 1;
  cr?: string;
  tua?: number;
  nod: number;
  app: number;
  s?: CampaignStatus;
  ts?: TeaserStatus;
  btc?: number;
}

export interface IAccountCookieShort {
  fv?: number;
  lv?: number;
  nopv?: number;
  ca: { [key: number]: ICampaignCookieShort };
}

export interface ICampaignCookie {
  activated?: boolean;
  variantId?: string;
  timeUntilAvailable?: number;
  numberOfDisplays: number;
  app: number;
  statusOfCampaign?: CampaignStatus;
  teaserStatus?: TeaserStatus;
  beforeTeaserClosed?: number;
}

export interface IAccountCookie {
  firstVisit?: number;
  lastVisit?: number;
  numberOfPageViews: number;
  campaigns: { [key: number]: ICampaignCookie };
}

export interface ICampaignInfo {
  AccountId: number;
  Device: Device;
}

export interface IFrontendRule {
  _id: ObjectId;
  type: FrontendRule | 'viewedPageV2' | 'visitorCartV3';
  options: any;
}

export interface IEvent {
  _id: ObjectId;
  type: FrontendEvent;
  device: Device | 'desktop_and_mobile';
  options: any;
}

export interface IPosition {
  path: string;
  selector: string;
  position: string;
}

export interface IIntegration {
  _id: ObjectId;
  id: number;
  settings: any;
  bindings: any;
}

export interface ICloseGestures {
  onEsc: boolean;
  onOverlayClick: boolean;
  onOverlayClickDevice: 'desktop' | 'mobile' | 'both';
}

interface ScheduleRepeatsOn {
  tz: String | null;
  daysOfWeek: number[];
  fromTime: string | null;
  toTime: string | null;
}

export interface IVariant {
  changes?: ObjectId;
  isActive?: boolean;
  deleted?: boolean;
  _id: ObjectId | string;
  id: number;
  name: string;
  lastActivatedDate: Date;
  previewURLs: string[];
  previewGeneratedAt?: Date;
  settings: object;
  status: string;
  confidence: number;
  winner: boolean;
  template?: any;
}

interface ICampaignSettings {
  stopTestRunning;
  notifyMe?: {
    status: boolean;
    emails: [{ status: 'accepted' | 'pending'; email }];
  };
}

export interface IExperience {
  _id: ObjectId;
  name: string;
  priority: number;
  variants: ObjectId[];
  frontendRules: IFrontendRule[];
}

export interface ICampaign {
  _id: ObjectId | string;
  id: number;
  templateName: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  analyticsType: string | null;
  events: IEvent[];
  rules: IFrontendRule[];
  frequency: object;
  experiences: IExperience[];
  positions: IPosition[];
  analytics: {
    type: string;
    status: 0 | 1;
  };
  status: 'active' | 'inactive' | 'deleted' | 'archived';
  priority: CampaignPriority;
  schedule: {
    status: 0 | 1;
    tz: string | null;
    from: Date | null;
    to: Date | null;
    repeatsOn: ScheduleRepeatsOn[];
  };

  // domain: string
  domainId: ObjectId | string;
  variants: IVariant[];
  mode: string | null;
  integrations: IIntegration[];
  settings: ICampaignSettings;
  currentExperimentId: string;
  isControlVariant: boolean;
  type?: string;
  locale: string;
}

export interface IProcessedCampaign extends ICampaign {
  selectedVariants: ISelectedVariant[];
}

export interface ISelectedVariant {
  // this is probably a campaign ObjectId
  _id: ObjectId;
  campaignId: number;
  variantId: ObjectId;
  lastActivatedDate: Date;
  experiences?: IExperience[];
  updatedAt: Date;
  variantName: string;
  previewGeneratedAt: Date;
  closeGestures: ICloseGestures;
  animationType: string;
  overlayPosition: number;
  mode: string | null;
  isControlVariant: boolean;
  hasElementsArray: Boolean;
  total?: number;
  hasCustomJS: boolean;
  hasCustomCSS: boolean;
  isPermanentTeaser: boolean;
}

export interface ICampaignSelectedVariant extends ICampaign {
  selectedVariant: ISelectedVariant;
}

export interface ICampaignHtmlParameters {
  campaignId: number;
  campaignName: string;
  creativeId: string;
  creativeUrl: string;
  domain: string;
  overlayColor: string;
  overlayOpacity: number;
  creativeEffectType: string;
  displayGroup: string;
  isIFrameless: boolean;
  campaignPriority: CampaignPriority;
}

export interface IDomain {
  _id: ObjectId;
  domain: string;
  lastRequestDate: Date;
  v3LastRequestDate?: Date;
  analytics: {
    enabled: boolean;
  };
}

export interface ZapierHook {
  url: string;
  campaigns: number[];
}

export interface IFailedSubscription {
  _id: ObjectId;
  databaseId: number;
  integrationId: string;
  variantId: string;
  data: {
    subscriber: object;
  };
  tryCount: number;
  reason: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICampaignProgressMessageTypes {
  state: string;
  campaignId?: number;
  messageType?: 'campaign' | 'popup' | 'sidebar' | 'nanobar';
  visitType?: 'visit' | 'ever';
  operator?: string;
  field?: {
    name: string;
    type: string;
    customId: string;
  };
  attributeValue?: string;
}

export type DiscountType = 'percentage' | 'fix';
export interface IDiscountExpiration {
  type: 'relative' | 'absolute';
  value: number;
}

export interface CouponConfig {
  _id: ObjectId;
  databaseId: number;
  variantId: string;
  elementId: string;
  automatic: {
    type: DiscountType;
    /** Value of the discount, if type is 'percentage' then value = 10 means 10% discount */
    value: number;
    /** If type is relative: number of seconds within a created coupon code will expire
     *  if type if absolute: UNIX timestamp when the coupon will expire
     */
    expiration: IDiscountExpiration;
    /** String prepended to the generated coupon codes. */
    prefix: string;
  };
}

export interface AssocArray {
  [key: string]: string | boolean | object;
}

export type AccountExperimentName =
  | 'CampaignsAbTest'
  | 'GlobalFrequencyCap'
  | 'BrowserTabNotification';
export type AccountExperimentSettings = Partial<Record<AccountExperimentName, unknown>>;

export type CampaignPriority = 'HIGH' | 'NORMAL' | 'LOW';

export type UserAlertLinkType = 'Campaign' | 'Variant' | 'Component' | 'Integration';

export interface UserAlertLink {
  id: string;
  type: UserAlertLinkType;
}

export type UserAlertLinks = Array<UserAlertLink>;
export type UserAlertType =
  | 'ProductOutOfStock'
  | 'InsertCodeMissing'
  | 'NoRequestFromDomain'
  | 'IntegrationError'
  | 'CampaignOutOfDiscountCodes'
  | 'CampaignAlmostOutOfDiscountCodes'
  | 'InsertCodeV3MissingWarning'
  | 'InsertCodeV3MissingError';

export interface UserAlertUpsertPayload {
  databaseId: number;
  links: UserAlertLinks;
  type: UserAlertType;
  createdAt: Date;
  expiresAt: Date;
  context: { [key: string]: any };
}

export type ShopifyProduct = {
  outOfStock: boolean;
  id: number;
  variant: number;
  title: string;
  price: number;
  originalPrice?: any;
  sku: string;
  url: string;
  imgUrl?: string | null;
} | null;

export type UnifiedProduct = {
  databaseId: number;
  providerServiceId: string;
  status: string;
  locale: string;
  productId: string;
  name: string;
  price: number;
  originalPrice: number;
  currency: string;
  imageUrl?: string;
  description: string;
  parentProductId?: number;
};

export type ShopRenterProduct = {
  id: number | string;
  variant: number | string;
  title: string;
  originalPrice: number | null;
  price: number | null;
  currency: number | string;
  sku: string;
  url: string;
  imgUrl: string;
} | null;

export interface ICheckout {
  databaseId: number;
  variantId: ObjectId;
  showedAt: Date;
  orderId: number;
  totalPrice: number;
  currency: string;
}

interface Embedded {
  domain: string;
  name: string;
  variantName: string;
  device: string;
  frequency: object | null;
  positions: object[];
  isControlVariant: boolean;
  schedule: object;
  currentExperimentId: string;
  analytics: string;
  coupons?: object[];
}

export interface EmbeddedV3 extends Embedded {
  variantId: string;
  _id: string;
  id: number;
  rules: AssocArray;
  customJS: object;
  ts: number;
  experiences: object[];
}

export interface ActiveEmbedded extends Embedded {
  customJsByEvents?: object[];
  _id: object;
  campaignId: number;
  variantId: object;
  rules: IFrontendRule[];
  experiences: any[];
  updatedAt: string;
  previewGeneratedAt: string;
  lastActivatedDate: string;
}
