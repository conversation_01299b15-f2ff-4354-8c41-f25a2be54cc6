import axios from 'axios';
import { sign as jwtSign } from 'jsonwebtoken';
import { pino } from './logger';
import ioRedisAdapter from './ioRedisAdapter';

const logger = pino.child({ helper: 'unifiedProductsAdapter' });
const PRODUCT_SYNC_SERVICE_URL = process.env.PRODUCT_SYNC_SERVICE_URL!;
const SHARED_KEY = process.env.OM_SHARED_KEY!;
const CACHE_PRODUCT_TTL = 120;
interface UnifiedProductDetailsInput {
  userId: number;
  providerServiceId: string;
  productIds: string[];
}

interface UnifiedProduct {
  id: string;
  variant: string | null;
  title: string;
  originalPrice: number | null;
  price: number;
  currency: string;
  sku: string | null;
  url: string | null;
  imgUrl: string | null;
}

interface UnifiedProductsResponse {
  products: UnifiedProduct[];
}

/**
 * Generate OM token for authentication
 */
const generateOmToken = (userId: number) => {
  // TODO: create token with valid payload
  const payload = {
    userId,
    accountType: 'normal',
    role: 'owner',
    iat: Math.floor(Date.now() / 1000),
  };

  return jwtSign(payload, SHARED_KEY, { expiresIn: '1h' });
};
console.log('generateOmToken', generateOmToken(44));

/**
 * Adapter for fetching unified product details directly from the product sync service
 */
export const getUnifiedProductsDetails = async (
  input: UnifiedProductDetailsInput,
): Promise<UnifiedProductsResponse> => {
  const token = generateOmToken(input.userId);

  try {
    const { data } = await axios.post(
      `${PRODUCT_SYNC_SERVICE_URL}/products`,
      {
        productIds: input.productIds,
        databaseId: input.userId,
        providerServiceId: input.providerServiceId,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'X-OM-TOKEN': token,
        },
        timeout: 10000, // Add a timeout to prevent hanging requests
      },
    );

    // Map the response to match our expected format
    const products = data.products.map((product: any) => ({
      id: product.productId,
      variant: product.productId,
      title: product.name || null,
      originalPrice: product.originalPrice || null,
      price: product.price || null,
      currency: product.currency || null,
      sku: product.sku,
      url: product.url || product.productUrl || null,
      imgUrl: product.imageUrl || null,
    }));

    return {
      products,
    };
  } catch (error: any) {
    throw new Error(`Failed to fetch unified product details: ${error.message}`);
  }
};

/**
 * Adapter for fetching most popular products from the product sync service
 */
export const getMostPopularProductsDetails = async (
  input: any,
): Promise<UnifiedProductsResponse> => {
  const cacheKey = `mostPopularProducts:${input.databaseId}:${input.providerServiceId}`;
  const cache = await ioRedisAdapter.get(cacheKey);

  if (cache) {
    return JSON.parse(cache);
  }

  const token = generateOmToken(input.databaseId);

  try {
    const { data } = await axios.post(
      `${PRODUCT_SYNC_SERVICE_URL}/products/popular`,
      {
        databaseId: input.databaseId,
        providerServiceId: input.providerServiceId,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'X-OM-TOKEN': token,
        },
        timeout: 10000,
      },
    );

    const products = data.products.map((product: any) => ({
      id: product.productId,
      variant: product.productId,
      title: product.name || null,
      originalPrice: product.originalPrice || null,
      price: product.price || null,
      currency: product.currency || null,
      sku: product.sku,
      url: product.url || product.productUrl || null,
      imgUrl: product.imageUrl || null,
    }));

    ioRedisAdapter
      .setex(cacheKey, JSON.stringify(products), CACHE_PRODUCT_TTL)
      .catch(() => logger.error('Failed to write most popular product details to redis'));

    return {
      products,
    };
  } catch (error: any) {
    throw new Error(`Failed to fetch most popular products: ${error.message}`);
  }
};

/**
 * Update the getProductDetails function to use the unified products adapter
 * when the shop type is 'unas', 'woo', or 'woocommerce'
 */
export const getUnifiedProducts = async (
  userId: number,
  productIds: string[],
  shopData: object,
): Promise<UnifiedProduct[]> => {
  try {
    interface ShopDataType {
      providerServiceIdOverride?: string;
      shopId?: string;
      // eslint-disable-next-line camelcase
      live_domain?: string;
    }
    const providerServiceId =
      (shopData as ShopDataType).providerServiceIdOverride ||
      (shopData as ShopDataType).shopId ||
      '';

    const response = await getUnifiedProductsDetails({
      userId,
      providerServiceId,
      productIds,
    });

    return response.products;
  } catch (error) {
    logger.error('Failed to get unified products for product helper:', error);
    throw error;
  }
};

export const getMostPopularProducts = async (
  databaseId: number,
  providerServiceId: string,
): Promise<UnifiedProduct[]> => {
  try {
    const response = await getMostPopularProductsDetails({
      databaseId,
      providerServiceId,
    });

    return response.products;
  } catch (error) {
    logger.error(error, 'Failed to get most popular products');
    throw error;
  }
};
