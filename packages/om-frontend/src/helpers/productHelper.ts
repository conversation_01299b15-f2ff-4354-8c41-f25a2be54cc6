import axios from 'axios';
import { getShopData } from './mongoHelper';
import ioRedisAdapter from './ioRedisAdapter';
import { pino } from './logger';
import { ShopifyProduct, ShopRenterProduct } from '../types/interfaces';
import { getUnifiedProducts } from './unifiedProductsAdapter';

const logger = pino.child({ helper: 'productHelper' });

const { SHOPIFY_API_VERSION } = process.env;
const CACHE_PRODUCT_TTL = 120;

export const getProductDetails = async ({ account, campaign, products, type }) => {
  const shopData = await getShopData(account, campaign, type);
  if (!shopData) {
    throw new Error(`failedToGetShopData, accountId: ${account}`);
  }

  const productIds = products.map((p) => p.variant);
  const cacheKeys = productIds.map((id) => `products:${account}:${campaign}:${id}`);
  const cache = (await ioRedisAdapter.mget(cacheKeys)).map((json) => JSON.parse(json));
  const hasMissingKey = cache.some((e) => !e);

  if (cache.length) {
    if (!hasMissingKey) {
      return cache;
    }

    // filter out items in cache
    products = products.filter((_, idx) => !cache[idx]);
  }

  let shopProducts;
  switch (type) {
    case 'shoprenter':
      shopProducts = await getShoprenterProducts(shopData, products);
      break;
    case 'shopify':
      shopProducts = await getShopifyProducts({ accountId: account, shopData }, products);
      break;
    case 'unas':
    case 'woo':
    case 'woocommerce':
      shopProducts = await getUnifiedProducts(account, productIds, shopData);
      break;
    default:
      throw Error(`unsupportedShopType ${type}`);
  }

  cache.forEach((cached, idx) => {
    if (cached === null) {
      const product = shopProducts.shift();

      cache[idx] = product;

      if (product) {
        ioRedisAdapter
          .setex(
            `products:${account}:${campaign}:${productIds[idx]}`,
            JSON.stringify(product),
            CACHE_PRODUCT_TTL,
          )
          .catch(() => logger.error('Failed to write product details to redis'));
      }
    }
  });

  return cache;
};

const parseShopRenterResponse = (
  resp,
  { noStockAddToCart, configLanguageId },
  productIds,
): ShopRenterProduct => {
  if (resp.items.length > 0) {
    const item = resp.items[0];
    const price = item.productPrices.find((e) => e.customerGroup.default === true);
    const idEntry = productIds.find(({ variant }) => `${variant}` === `${item.innerId}`);
    const parentId = idEntry && idEntry.id;
    const status = parseInt(item.status, 10);
    const onStock = noStockAddToCart || isShoprenterProductOnStock(item);

    if (status !== 1 || !onStock) {
      return null;
    }

    if (!item.urlAliases?.[0]?.urlAlias) {
      logger.warn({
        shoprenter: { product: resp },
        message: 'Unable to map SR product since unsupported structure passed',
      });
      return null;
    }

    let title = item.productDescriptions[0].name;
    if (configLanguageId != null) {
      const correctDescription = item.productDescriptions.find((desc) =>
        desc.language.href.endsWith(configLanguageId),
      );
      title = correctDescription ? correctDescription.name : item.productDescriptions[0].name;
    }

    return {
      id: parentId,
      variant: item.innerId,
      title,
      originalPrice:
        price.grossOriginal && price.grossOriginal !== price.gross
          ? parseFloat(price.grossOriginal)
          : null,
      price: parseFloat(price.gross),
      currency: price.currencyCode,
      sku: item.sku,
      url: `/${item.urlAliases[0].urlAlias}`,
      imgUrl: encodeURI(item.allImages.mainImage),
    };
  }
  logger.warn({
    shoprenter: { product: resp },
    message: 'Unable to map SR product since unsupported structure passed',
  });

  return null;
};
const getShoprenterProducts = async (shopData, ids): Promise<ShopRenterProduct[]> => {
  const { shopname, username, password } = shopData;
  const shopSettings = await getShoprenterShopSettings(shopData);

  const result: ShopRenterProduct[] = [];
  const srRequestOptions = {
    auth: { username, password },
    headers: { Accept: 'application/json' },
  };
  try {
    const { data } = await axios.post(
      `http://${shopname}.api.shoprenter.hu/batch`,
      {
        data: {
          requests: ids.map(({ variant }) => ({
            method: 'GET',
            uri: `http://${shopname}.api.shoprenter.hu/productExtend?innerId=${variant}&full=1`,
          })),
        },
      },
      srRequestOptions,
    );

    if (data.requests && Array.isArray(data.requests.request)) {
      data.requests.request.forEach((req) => {
        const { response } = req;

        if (response.header.statusCode === 200) {
          result.push(parseShopRenterResponse(response.body, shopSettings, ids));
        } else {
          logger.error(
            'Invalid ShopRenter product response: non 200 response code, shopname: %s, request: %o',
            req,
          );
          result.push(null);
        }
      });
    } else {
      throw new Error(`Invalid batch response body received: ${JSON.stringify(data)}`);
    }
  } catch (err: any) {
    if (err?.response?.status === 429) {
      logger.warn({
        message: 'ShopRenter product details batch failed, too many requests',
        errorMessage: err.message,
        stack: err.stack,
        shopName: shopname,
        data: err.response.data,
        status: err.response.status,
      });
      throw new Error('ShopRenter product details batch failed, too many requests.');
    }

    if (err.response) {
      logger.error(
        'ShopRenter product details batch request failed, error: %s, stack: %s, shopname: %s, resp: %o, status: %d',
        err.message,
        err.stack,
        shopname,
        err.response.data,
        err.response.status,
      );
    } else {
      logger.error(
        'ShopRenter product details batch request failed, error: %s, stack: %s, shopname: %s',
        err.message,
        err.stack,
        shopname,
      );
    }
    throw new Error('ShopRenter product details batch request failed.');
  }

  return result;
};

const isShopifyVariantOnStock = (product: any, variant: any) => {
  if (
    product.tracksInventory === false ||
    variant.inventoryQuantity > 0 ||
    variant.inventoryPolicy === 'CONTINUE'
  ) {
    return true;
  }
  return false;
};
const parseShopifyId = (id: string, type: string) =>
  parseInt(`${id}`.replace(`gid://shopify/${type}/`, ''), 10);

const getShopifyProducts = async ({ accountId, shopData }, products): Promise<ShopifyProduct[]> => {
  const { myshopify_domain: shopName, oauth_token: accessToken } = shopData;
  const endpoint = `https://${shopName}/admin/api/${SHOPIFY_API_VERSION}/graphql.json`;

  const uniqueProductIds = Array.from(new Set(products.map((product) => product.id)));
  const uniqueVariantIds = Array.from(new Set(products.map((product) => product.variant)));

  const variables = {
    queryString: `${uniqueProductIds.map((id) => `id:${id}`).join(' OR ')} AND ${uniqueVariantIds
      .map((id) => `variant_id:${id}`)
      .join(' OR ')}`,
  };

  const query = `
      query GetProducts($queryString: String!) {
        products(first: 250, query: $queryString) {
          edges {
            node {
              id
              title
              handle
              status
              tracksInventory
              variants(first: 250) {
                edges {
                  node {
                    id
                    price
                    compareAtPrice
                    sku
                    image {
                      id
                      url
                    }
                    inventoryQuantity
                    inventoryPolicy
                  }
                }
              }
              images(first: 1) {
                edges {
                  node {
                    id
                    url
                  }
                }
              }
            }
          }
        }
      }
  `;

  try {
    const response = await axios.post(
      endpoint,
      { query, variables },
      {
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Access-Token': accessToken,
        },
      },
    );

    const data = response.data.data;
    if (response.data?.errors?.length) {
      throw new Error(response.data.errors.map((e) => e?.message).join());
    }

    const processedProducts = products.map((product) => {
      const { id, variant: variantId } = product;

      const productDetail = data.products.edges.find(
        (edge) => edge?.node?.id && parseShopifyId(edge.node.id, 'Product') === parseInt(id, 10),
      )?.node;

      if (!productDetail || productDetail.status !== 'ACTIVE') return null;

      const variant = productDetail.variants.edges.find(
        (edge) =>
          edge?.node?.id &&
          parseShopifyId(edge.node.id, 'ProductVariant') === parseInt(variantId, 10),
      )?.node;

      if (!variant) {
        logger.warn({
          account: accountId,
          shopName,
          productId: id,
          variantId,
          message: `Shopify variant could not be found in product`,
        });
        return null;
      }

      let imgUrl = variant.image?.url ?? null;
      if (!imgUrl && productDetail.images.edges.length > 0) {
        imgUrl = productDetail.images.edges[0].node.url;
      }

      const parsedProductId = parseShopifyId(product.id, 'Product');
      const parsedVariantId = parseShopifyId(variant.id, 'ProductVariant');

      return {
        outOfStock: !isShopifyVariantOnStock(productDetail, variant),
        id: parsedProductId,
        variant: parsedVariantId,
        title: productDetail.title,
        price: parseFloat(variant.price),
        originalPrice: variant.compareAtPrice ? parseFloat(variant.compareAtPrice) : null,
        sku: variant.sku,
        url: `/products/${productDetail.handle}?variant=${parsedVariantId}`,
        imgUrl,
      };
    });

    return processedProducts.filter((item): item is ShopifyProduct => item !== null);
  } catch (error: any) {
    logger.error({
      account: accountId,
      shopName,
      products,
      message: `Failed to fetch Shopify products: ${error.message}`,
    });
    throw error;
  }
};

const isShoprenterProductOnStock = (product) => {
  const quantityInStock = Math.max(
    parseInt(product.stock1, 10),
    parseInt(product.stock2, 10),
    parseInt(product.stock3, 10),
    parseInt(product.stock4, 10),
  );

  return quantityInStock > 0;
};

const getShoprenterShopSettings = async ({ shopname, username, password }) => {
  const cacheKey = `shopSettings:shopRenter:${shopname}`;
  const cached = await ioRedisAdapter.get(cacheKey);

  if (cached) {
    return JSON.parse(cached);
  }

  const axiosConfig = {
    auth: { username, password },
    headers: { Accept: 'application/json' },
  };

  const [stockResponse, languageResponse, languagesResponse] = await Promise.all([
    axios.get(
      `https://${shopname}.api.shoprenter.hu/settings?full=1&key=config_disable_addtocart_no_quantity`,
      axiosConfig,
    ),
    axios.get(
      `https://${shopname}.api.shoprenter.hu/settings?full=1&key=config_language`,
      axiosConfig,
    ),
    axios.get(`https://${shopname}.api.shoprenter.hu/languages?full=1`, axiosConfig),
  ]);

  const settings = {
    noStockAddToCart: false,
    configLanguage: 'hu',
    configLanguageId: null,
  };

  if (
    !stockResponse.data?.items ||
    !languageResponse.data?.items ||
    !languagesResponse.data?.items
  ) {
    throw new Error('Failed to get shoprenter shop settings.');
  }

  stockResponse.data.items.forEach(({ key, value }) => {
    if (key === 'config_disable_addtocart_no_quantity') {
      // 0 -> allow customers to add products out of stock to cart
      // 1 -> forbid customers to add products out of stock to cart
      settings.noStockAddToCart = !parseInt(value, 10);
    }
  });

  // This code block retrieves the configured language for the shop
  // and sets it in the settings object. It also finds the corresponding
  // language ID from the languages response and stores it.
  // This information is used to ensure product titles are in the correct language.
  const responseConfigLanguage = languageResponse?.data?.items[0]?.value;
  if (responseConfigLanguage) {
    settings.configLanguage = responseConfigLanguage;

    languagesResponse?.data?.items.forEach((item) => {
      if (item.code === responseConfigLanguage) {
        settings.configLanguageId = item.id;
      }
    });
  }

  await ioRedisAdapter.setex(cacheKey, JSON.stringify(settings), 60);
  return settings;
};
