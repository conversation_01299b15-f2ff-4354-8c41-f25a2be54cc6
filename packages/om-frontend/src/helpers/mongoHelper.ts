import { arrayify } from '@om/common/src/utils';
import * as _get from 'lodash.get';
import * as moment from 'moment';
import {
  FindCursor,
  Db,
  InsertOneResult,
  MongoClient,
  ObjectId,
  UpdateResult,
  AnyBulkWriteOperation,
  Document,
} from 'mongodb';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { Readable } from 'stream';
import RedisLock = require('ioredis-lock');
import { isTest } from '../config';
import { CampaignVariantFields, VariantFields } from '../handlers/IntegrationHandler';
import { cacheKeyFirstConversion } from '../handlers/UserProfileHandler';
import { Device } from '../types/enums';
import {
  CouponConfig,
  ICampaign,
  IDomain,
  IFailedSubscription,
  ISelectedVariant,
  UserAlertUpsertPayload,
  ZapierHook,
  ICheckout,
} from '../types/interfaces';
import { FREEMIUM_PACKAGES } from './accountHelper';
import { getAccountDomains, purifyDomain } from './domainHelper';
import { reportIntegrationErrorAlert } from './failedSubscription';
import * as heapAnalytics from './heapAnalytics';
import redisAdapter from './ioRedisAdapter';
import * as campaignHelper from './mongodb/campaigns';
import { mongoHelperLog, pino as logger } from './logger';
import { getProcessedActiveVariantCacheKey, getVariantCacheKey } from './redisKeys';

import { QUERY_TIME_LIMIT, storeInRedisRandom } from './cacheHelper';
import { LimitManager } from '../services/LimitManager';
import { secondaryDbConnection } from './SecondaryMongoHelper';
import { IntegrationErrorParser } from './integrationErrorParser/IntegrationErrorParser';
import { FEATURES } from './featureFlags';

export let dbConnection: Db;
export const ACCOUNTS_COLLECTION_NAME = 'master_accounts';
export const LOGINS_COLLECTION_NAME = 'master_logins';
export const CONFIG_COLLECTION_NAME = 'master_config';
export const COUPON_CONFIG_COLLECTION_NAME = 'user_coupon_config';
export const USER_ALERTS_COLLECTION_NAME = 'user_alerts';
export const USER_EXPERIMENT_COLLECTION_NAME = 'user_experiments';
export const DYNAMIC_CONTENT_COLLECTION_NAME = 'dynamic_contents';
export const USER_SMART_PERSONALIZATION_COLLECTION_NAME = 'user_smart_personalization';
export const SUBSCRIPTION_FAILED_COLLECTION_NAME = 'subscription_failed';
export const USER_SPLIT_URL_AB_TEST_COLLECTION_NAME = 'user_split_url_tests';
const couponLogger = logger.child({ service: 'coupon' });
const integrationSettingLogger = logger.child({ service: 'integration-setting' });

const getVariantTypeFields = () => ({
  overlayPosition: '$variants.template.style.overlay.position',
  mode: '$variants.template.style.mode',
});

const getProjectionValueFromArray = (input, cond, path = '') => {
  if (path && path !== '') path = `.${path}`;
  return {
    $ifNull: [
      {
        $arrayElemAt: [
          {
            $map: {
              input: {
                $filter: {
                  input,
                  cond,
                },
              },
              as: 'filtered',
              in: `$$filtered${path}`,
            },
          },
          0,
        ],
      },
      null,
    ],
  };
};

const defaultDynamicContentVariantsProjections = [
  {
    $project: {
      _id: 1,
      campaignId: '$id',
      name: 1,
      domainId: 1,
      schedule: 1,
      createdAt: 1,
      currentExperimentId: 1,
      device: 1,
      rules: '$settings.frontendRules',
      frequency: '$settings.frequency',
      mode: '$variants.mode',
      variantId: '$variants._id',
      variantName: '$variants.name',
      lastActivatedDate: '$variants.lastActivatedDate',
      updatedAt: '$variants.updatedAt',
      changes: '$variants.changes',
      isControlVariant: '$variants.isControlVariant',
    },
  },
];

export const defaultVariantProjections = [
  {
    $project: {
      _id: 1,
      campaignId: '$id',
      name: 1,
      templateName: 1,
      domainId: 1,
      device: 1,
      schedule: 1,
      currentExperimentId: 1,
      isControlVariant: '$variants.isControlVariant',
      rules: '$settings.frontendRules',
      frequency: '$settings.frequency',
      mode: {
        $ifNull: ['$mode', '$variants.template.style.mode'],
      },
      positions: '$settings.positions',
      overlayPosition: {
        $ifNull: ['$overlayPosition', '$variants.template.style.overlay.position'],
      },
      variantId: '$variants._id',
      variantName: '$variants.name',
      closeGestures: '$variants.template.data.closeGestures',
      animationType: '$variants.template.style.animation.type',
      lastActivatedDate: '$variants.lastActivatedDate',
      isPermanentTeaser: '$variants.template.data.isPermanentTeaser',
      previewGeneratedAt: '$variants.previewGeneratedAt',
      updatedAt: '$variants.updatedAt',
      hasElementsArray: {
        $cond: [{ $isArray: '$variants.template.elements' }, true, false],
      },
      elementTypes: { $ifNull: ['$variants.template.elements.type', []] },
      customJs: '$variants.template.customJs',
      customJsByEvents: { $objectToArray: '$variants.template.customJsByEvents' },
      customCss: '$variants.template.customCss',
    },
  },
];

const processVariantFields = (variants) => {
  const variantsByCampaign = variants.reduce((variantMap, variant) => {
    variantMap[variant.campaignId] = arrayify(variantMap[variant.campaignId]);
    variantMap[variant.campaignId].push(variant);
    return variantMap;
  }, {});

  return variants.map((variant) => {
    const hasCustomJsByEvents = variant.customJsByEvents?.some((event) => {
      return event.v?.length;
    });
    const variantTypeFields = {};

    if (variant.isControlVariant) {
      const nonControlVariant = variantsByCampaign[variant.campaignId].find(
        (variantByCampaign) => !variantByCampaign.isControlVariant,
      );

      if (nonControlVariant) {
        const { mode, overlayPosition } = nonControlVariant;
        Object.assign(variantTypeFields, { mode, overlayPosition });
      }
    }

    return {
      ...variant,
      ...variantTypeFields,
      hasCustomJS: variant.customJs?.length || hasCustomJsByEvents,
      hasCustomCSS: variant.customCss?.length,
    };
  });
};

let memoryServer: MongoMemoryServer;
const connectToDb = async (connection?: any) => {
  try {
    if (connection) {
      dbConnection = connection;
      return;
    }
    let url = '';
    if (isTest) {
      memoryServer = await MongoMemoryServer.create();
      url = memoryServer.getUri();
      url += 'optimonk_test';
    } else {
      url = process.env.MONGO_ADDRESS || '';
    }
    const client = await MongoClient.connect(url);
    dbConnection = client.db();
    logger.info({ message: `Connected to MongoDB at ${url}` });
  } catch (e) {
    if (e instanceof Error) {
      logger.error({
        message: 'Error during Mongo DB connection',
        errorMessage: e.message,
        e,
      });
    }
  }
};

const closeMemoryServer = async () => {
  if (memoryServer) {
    await memoryServer.stop();
  }
};

const addVariantTemplatesToCampaigns = (campaignAggregations, variantTemplates) => {
  return campaignAggregations.map((campaign) => {
    const variantTemplate = variantTemplates.find(
      (variant) => variant.variantId.toString() === campaign.variantId.toString(),
    );
    if (!variantTemplate && !campaign.isControlVariant) {
      throw new Error(
        `Variant template not found for campaign ${campaign.id} variant ${campaign.variant._id}`,
      );
    }
    if (variantTemplate) {
      const template = variantTemplate.template;
      campaign.closeGestures = template.data.closeGestures;
      campaign.animationType = template.style.animation.type;
      if (typeof template.data.isPermanentTeaser !== 'undefined') {
        campaign.isPermanentTeaser = template.data.isPermanentTeaser;
      }

      campaign.hasElementsArray = Array.isArray(template.elements);
      campaign.elementTypes =
        template.elements?.length > 0 ? template.elements.map((e) => e.type) : [];
      campaign.customJs = template.customJs;
      campaign.customCss = template.customCss;
      campaign.customJsByEvents = Object.entries(template.customJsByEvents || {}).map(([k, v]) => {
        return {
          k,
          v,
        };
      });
      campaign.overlayPosition = template.style.overlay.position;
      campaign.mode = template.style.mode;
    }
    return campaign;
  });
};

const getAccountFeatures = async (accountId) => {
  return dbConnection.collection('master_accounts').findOne(
    {
      databaseId: parseInt(accountId, 10),
    },
    { projection: { features: 1 } },
  );
};

const getVariantTemplate = async (accountId, variantId) => {
  return dbConnection.collection('user_variant_templates').findOne({
    databaseId: parseInt(accountId, 10),
    variantId: new ObjectId(variantId),
  });
};

const loadVariantToCache = async ({
  variantId,
  accountId,
  campaignId,
  key = getVariantCacheKey(accountId, campaignId, variantId),
}) => {
  let matchVariant;

  try {
    matchVariant =
      variantId.length < 10
        ? { 'variants.id': parseInt(variantId, 10) }
        : { 'variants._id': new ObjectId(variantId) };
  } catch (e) {
    // @ts-ignore
    logger.warn(e.message, variantId);
    return null;
  }
  mongoHelperLog('getVariant', accountId, variantId);
  const account = await getAccountFeatures(accountId);
  let variants = await dbConnection
    .collection(`${accountId}_campaigns`)
    .aggregate([
      { $match: { id: campaignId } },
      {
        $project: {
          templateName: 1,
          _id: 1,
          id: 1,
          domainId: 1,
          version: 1,
          variants: 1,
        },
      },
      { $unwind: '$variants' },
      { $match: matchVariant },
      { $addFields: getVariantTypeFields() },
      ...defaultVariantProjections,
    ])
    .toArray();

  if (account?.features?.includes(FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
    const variantIds = variants.map((campaign) => campaign?.variantId);

    const variantTemplates = await dbConnection
      .collection('user_variant_templates')
      .find({
        databaseId: accountId,
        variantId: { $in: variantIds },
      })
      .toArray();

    variants = addVariantTemplatesToCampaigns(variants, variantTemplates);
  }

  variants = processVariantFields(variants);

  const variantToCache = variants.pop();

  if (!variantToCache) {
    mongoHelperLog('getVariant/noVariantToCache', accountId);
    return variantToCache;
  }
  const campaignExperiencesMap = await getCampaignExperiencesMap({
    campaignIds: [variantToCache._id],
    databaseId: accountId,
  });
  variantToCache.experiences = arrayify(campaignExperiencesMap[variantToCache._id]);

  const ttl = 3600 + Math.floor(Math.random() * 3601);
  await redisAdapter.setex(key, JSON.stringify(variantToCache), ttl);

  return variantToCache;
};

const getVariant = async (accountId: number, campaignId: number, variantId: string) => {
  const key = getVariantCacheKey(accountId, campaignId, variantId);
  const cachedVariant = await redisAdapter.get(key);

  if (cachedVariant) {
    return JSON.parse(cachedVariant);
  }

  return loadVariantToCache({ variantId, accountId, campaignId });
};

const getChangesById = async (changeId) => {
  const [changes] = await dbConnection
    .collection('dynamic_contents')
    .aggregate([{ $match: { _id: changeId } }])
    .toArray();

  return changes?.changes ?? [];
};

export const getControlVariantsByCampaigns = async (
  accountId: number,
  campaignIds: number[],
  projection: Record<string, any>[],
) => {
  mongoHelperLog('getControlVariantsByCampaigns', accountId);
  return secondaryDbConnection
    .collection(`${accountId}_campaigns`)
    .aggregate([
      {
        $match: { status: 'active', id: { $in: campaignIds } },
      },
      { $unwind: '$variants' },
      { $match: { 'variants.status': 'active', 'variants.isControlVariant': true } },
      ...projection,
    ])
    .toArray();
};

const getActiveDynamicCampaigns = async (databaseId: number) => {
  mongoHelperLog('getActiveDynamicCampaigns', databaseId);
  const campaigns = await dbConnection
    .collection(`${databaseId}_campaigns`)
    .aggregate([
      {
        $match: {
          status: 'active',
          version: 3,
          type: 'dynamic_content',
          'variants.status': 'active',
        },
      },
      {
        $project: {
          _id: 1,
          id: 1,
          name: 1,
          variants: 1,
          domainId: 1,
          schedule: 1,
          device: 1,
          'settings.frontendRules': 1,
          'settings.frequency': 1,
          createdAt: 1,
          currentExperimentId: 1,
        },
      },
      { $unwind: '$variants' },
      {
        $match: {
          'variants.status': 'active',
          'variants.changes': { $ne: null },
          'variants.isControlVariant': { $ne: true },
        },
      },
      ...defaultDynamicContentVariantsProjections,
    ])
    .toArray();
  return _getDynamicContentCampaigns(databaseId, campaigns);
};

const getDynamicContentCampaignByVariant = async (databaseId: number, variantId: ObjectId) => {
  mongoHelperLog('getDynamicContentCampaignByVariant', databaseId);
  const statuses = { $in: ['active', 'inactive'] };
  const campaigns = await secondaryDbConnection
    .collection(`${databaseId}_campaigns`)
    .aggregate([
      {
        $match: {
          status: statuses,
          version: 3,
          type: 'dynamic_content',
        },
      },
      {
        $project: {
          _id: 1,
          id: 1,
          name: 1,
          variants: 1,
          domainId: 1,
          schedule: 1,
          'settings.frontendRules': 1,
          createdAt: 1,
          currentExperimentId: 1,
        },
      },
      { $unwind: '$variants' },
      {
        $match: {
          'variants._id': variantId,
          'variants.status': statuses,
          'variants.isControlVariant': { $ne: true },
        },
      },
      ...defaultDynamicContentVariantsProjections,
    ])
    .toArray();

  return _getDynamicContentCampaigns(databaseId, campaigns);
};

const _getDynamicContentCampaigns = async (accountId: number, campaigns: any[]) => {
  const domains = await getAccountDomains(accountId);

  const campaignIds = campaigns.map((campaign) => campaign.campaignId);

  const controlVariants = await getControlVariantsByCampaigns(
    accountId,
    campaignIds,
    defaultDynamicContentVariantsProjections,
  );

  const activeVariantIds = [...campaigns, ...controlVariants].map((campaign) =>
    campaign.variantId.toString(),
  );

  const campaignExperiencesMap = await getCampaignExperiencesMap({
    campaignIds: campaigns.map((campaign) => campaign._id),
    databaseId: accountId,
    filterByVariants: activeVariantIds,
  });
  campaigns.push(...controlVariants);

  return Promise.all(
    campaigns.map(async (campaign) => {
      const changes = await getChangesById(campaign.changes);
      const domain = domains.find((domain) => {
        return new ObjectId(domain._id).equals(new ObjectId(campaign.domainId));
      });

      return {
        ...campaign,
        experiences: arrayify(campaignExperiencesMap[campaign._id]),
        changes,
        domain: domain?.domain,
        analytics: domain?.analytics?.enabled ?? true,
      };
    }),
  );
};

const getProcessedActiveVariants = async (
  accountId: number,
  campaignIds: number[],
): Promise<ISelectedVariant[]> => {
  mongoHelperLog('getProcessedActiveVariants', accountId);
  const account = await dbConnection.collection('master_accounts').findOne(
    {
      databaseId: accountId,
    },
    { projection: { features: true } },
  );
  const cacheKey = getProcessedActiveVariantCacheKey(accountId, campaignIds);

  const cachedActiveVariants = await redisAdapter.get(cacheKey);

  if (cachedActiveVariants) {
    return JSON.parse(cachedActiveVariants);
  }
  const result: ISelectedVariant[] = [];

  const lock = RedisLock.createLock(redisAdapter.redis, { timeout: QUERY_TIME_LIMIT });
  try {
    await lock.acquire(`${cacheKey}:lock`);

    let campaignsWithActiveVariants = await dbConnection
      .collection(`${accountId}_campaigns`)
      .aggregate([
        { $match: { id: { $in: campaignIds }, status: { $ne: 'deleted' } } },
        { $project: { variants: 1, domainId: 1, _id: 1, id: 1 } },
        { $unwind: '$variants' },
        { $match: { 'variants.status': 'active' } },
        { $addFields: getVariantTypeFields() },
        ...defaultVariantProjections,
      ])
      .toArray();
    if (account?.features?.includes(FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
      const variantIds = campaignsWithActiveVariants.map((variant) => variant.variantId);

      const variantTemplates = await dbConnection
        .collection('user_variant_templates')
        .find({ databaseId: accountId, variantId: { $in: variantIds } })
        .toArray();

      campaignsWithActiveVariants = addVariantTemplatesToCampaigns(
        campaignsWithActiveVariants,
        variantTemplates,
      );
    }
    const processedVariants = processVariantFields(campaignsWithActiveVariants);
    const activeVariantIds = processedVariants.map((variant) => variant.variantId.toString());
    const campaignExperiencesMap = await getCampaignExperiencesMap({
      campaignIds: processedVariants.map((campaign) => campaign._id),
      databaseId: accountId,
      filterByVariants: activeVariantIds,
    });

    for (const activeVariant of processedVariants) {
      activeVariant.experiences = arrayify(campaignExperiencesMap[activeVariant._id]);
      activeVariant._id = activeVariant.variantId;

      result.push(activeVariant);
    }

    await storeInRedisRandom(cacheKey, result);
    await lock.release();
  } catch (e) {
    if (e instanceof Error && e.name === 'LockAcquisitionError') {
      logger.info(`GetProcessedActiveVariants lockAcquisitionError ${e.message}`);
    } else {
      logger.error(e);
    }
  }

  return result;
};

/**
 * Selects the active variant which will be shown.
 * If there are multiple active variants the one with the least impressions will be chosen.
 * @param accountId
 * @param campaignId
 * @param variantId?
 */
const selectActiveVariantForCampaign = async (
  accountId: number,
  campaignId: number,
  variantId?: string,
): Promise<ISelectedVariant> => {
  mongoHelperLog('selectVariantForCampaign/activeVariantsSortedByActivationTime', accountId);
  const account = await dbConnection.collection('master_accounts').findOne(
    {
      databaseId: accountId,
    },
    { projection: { features: true } },
  );
  let activeVariantsSortedByActivationTime = await secondaryDbConnection
    .collection(`${accountId}_campaigns`)
    .aggregate([
      { $match: { id: campaignId, status: { $ne: 'deleted' } } },
      { $project: { _id: 1, variants: 1, domainId: 1, id: 1 } },
      { $unwind: '$variants' },
      { $match: { 'variants.status': 'active' } },
      { $addFields: getVariantTypeFields() },
      ...defaultVariantProjections,
      { $sort: { lastActivatedDate: -1 } },
    ])
    .toArray();

  if (account?.features?.includes(FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
    const variantIds = activeVariantsSortedByActivationTime.map((variant) => variant?.variantId);

    const variantTemplates = await dbConnection
      .collection('user_variant_templates')
      .find({
        databaseId: accountId,
        variantId: { $in: variantIds },
      })
      .toArray();

    activeVariantsSortedByActivationTime = addVariantTemplatesToCampaigns(
      activeVariantsSortedByActivationTime,
      variantTemplates,
    );
  }

  const processedVariants = processVariantFields(activeVariantsSortedByActivationTime);

  const result: any = [];
  for (const activeVariant of processedVariants) {
    activeVariant._id = activeVariant.variantId;
    result.push({ ...activeVariant, total: 0 });
  }

  if (variantId) {
    const variant = processedVariants.find((v) => v.variantId.toString() === variantId);
    if (variant) {
      return variant;
    }
  }

  const midnight = new Date();
  midnight.setHours(0, 0, 0, 0);

  mongoHelperLog('selectVariantForCampaign/recentlyLeastShowedVariant', accountId);
  const recentlyLeastShowedVariant = await secondaryDbConnection
    .collection('user_statistics')
    .aggregate([
      {
        $match: {
          databaseId: accountId,
          variant: {
            $in: processedVariants.map((campaign) => campaign.variantId),
          },
          period: { $gte: midnight },
        },
      },
      { $group: { _id: '$variant', impr: { $sum: '$impressions' } } },
      { $sort: { impr: 1 } },
    ])
    .toArray();

  for (const stat of recentlyLeastShowedVariant) {
    const index = result.map((res) => res._id.toString()).indexOf(stat._id.toString());
    result[index].total += stat.impr;
  }

  result.sort((a, b) => {
    return a.total - b.total;
  });

  return result[0];
};

const ownerAccountSettings = async (
  subAccountId: number,
): Promise<{ poweredBy: boolean; spamProtection: boolean }> => {
  const cacheKey = `frontend:${subAccountId}:ownerAccountSettings`;

  const settingsCache = await redisAdapter.get(cacheKey);

  if (settingsCache) {
    return JSON.parse(settingsCache);
  }

  mongoHelperLog('ownerAccountSettings');
  const account = await dbConnection
    .collection(ACCOUNTS_COLLECTION_NAME)
    .findOne(
      { subAccounts: { $in: [subAccountId] } },
      { projection: { 'settings.hasPoweredByLinkDisabled': 1, 'settings.spamProtection': 1 } },
    );
  const settings = {
    poweredBy: false,
    spamProtection: false,
  };

  if (account) {
    settings.poweredBy = account.settings.hasPoweredByLinkDisabled !== true;
    settings.spamProtection = !!account.settings.spamProtection;
  }

  await redisAdapter.setex(cacheKey, JSON.stringify(settings), 60);

  return settings;
};

const getAccountSettings = async (accountId: number): Promise<Record<string, any> | null> => {
  const cacheKey = `frontend:${accountId}:settings`;
  const settingsCache = await redisAdapter.get(cacheKey);
  let settings;

  if (settingsCache && settingsCache !== 'null') {
    settings = JSON.parse(settingsCache);
  } else {
    mongoHelperLog('getAccountSettings', accountId);
    logger.info('getAccountSettings', accountId);
    settings = await dbConnection.collection(ACCOUNTS_COLLECTION_NAME).findOne(
      { databaseId: accountId },
      {
        projection: {
          'settings.hasPoweredByLinkDisabled': true,
          'settings.preventSubscribe': true,
          'settings.spamProtection': true,
          'settings.experiments': true,
          'settings.integrations': true,
          'settings.domains': true,
          users: true,
          type: true,
          billing: true,
          features: true,
        },
      },
    );
    if (!settings) {
      // account with given accountId does not exist, we can store it for a longer time in cache
      // so subsequent invocations with this accountId can be resolved directly from Redis instead of
      // going to MongoDB everytime.
      logger.info('getAccountSettings notFound', accountId);
      await redisAdapter.setex(cacheKey, 'null', 600);
      return null;
    }

    settings.ownerLoginId = settings.users[0].loginId;
    // set cross domain tracking roles
    settings.crossDomainTrackingRoles = settings?.settings?.domains?.reduce((acc, domain) => {
      if (domain.inactive || !domain.crossDomainTrackingRole) return acc;
      acc[domain.domain] = domain.crossDomainTrackingRole;
      return acc;
    }, {});
    // set provider service id overrides
    settings.providerServiceIdOverrides = settings?.settings?.domains?.reduce((acc, domain) => {
      if (domain.inactive || !domain.providerServiceIdOverride) return acc;
      acc[domain.domain] = domain.providerServiceIdOverride;
      return acc;
    }, {});

    if (settings.type === 'normal') {
      const isPoweredByDisabledByPackage = ['ESSENTIAL', 'GROWTH', 'PREMIUM', 'MASTER'].some((e) =>
        settings.billing.package.startsWith(e),
      );
      settings.isPoweredByEnabled = isPoweredByDisabledByPackage
        ? false
        : !settings.settings.hasPoweredByLinkDisabled;
      settings.spamProtection = !!settings.settings.spamProtection;
    }

    if (settings.type === 'sub') {
      const ownerAccSettings = await ownerAccountSettings(settings._id);
      settings.isPoweredByEnabled = ownerAccSettings.poweredBy;
      settings.spamProtection = ownerAccSettings.spamProtection;
    }

    delete settings.users;
    await redisAdapter.setex(cacheKey, JSON.stringify(settings), 60);
  }
  return settings;
};

const getAccountWithSubAccounts = async (databaseId: number) => {
  mongoHelperLog('getAccountWithSubAccounts', databaseId);
  const result = await secondaryDbConnection
    .collection('master_accounts')
    .aggregate([
      { $match: { type: 'agency', databaseId } },
      {
        $lookup: {
          localField: 'subAccounts',
          from: 'master_accounts',
          foreignField: '_id',
          as: 'subAccs',
        },
      },
      {
        $project: {
          databaseId: 1,
          'subAccs.databaseId': 1,
        },
      },
    ])
    .toArray();

  if (!result.length) return null;

  return {
    databaseId,
    subAccounts: result[0].subAccs?.map((sub) => sub.databaseId),
  };
};

const getLogin = async (loginId: string) => {
  const cacheKey = `frontend:login:${loginId}`;
  const loginCache = await redisAdapter.get(cacheKey);
  let login;
  if (loginCache) {
    login = JSON.parse(loginCache);
    login.lastLogin = new Date(login.lastLogin);
    login.updatedAt = new Date(login.updatedAt);
    login.createdAt = new Date(login.createdAt);
  } else {
    mongoHelperLog('getLogin');
    login = await secondaryDbConnection.collection(LOGINS_COLLECTION_NAME).findOne({
      _id: new ObjectId(loginId),
    });
    await redisAdapter.setex(cacheKey, JSON.stringify(login), 300);
  }
  return login;
};

const isFreemiumAccount = (account) => {
  return (
    account.type === 'normal' &&
    (FREEMIUM_PACKAGES.includes(account.billing.package) ||
      account.billing.package.indexOf('MASTER_') === 0)
  );
};

/**
 * Check whether the specified account has enough visitors to show popups.
 * @param accountId
 */
const accountHasRemainingVisitors = async (accountId: number) => {
  mongoHelperLog('accountHasRemainingVisitors/account', accountId);
  const account = await dbConnection
    .collection(ACCOUNTS_COLLECTION_NAME)
    .findOne({ databaseId: accountId }, { projection: { type: 1, billing: 1, limits: 1 } });

  if (!account) {
    return false;
  }

  const isFreeAccount = isFreemiumAccount(account);
  const isOverrunEnabledForAccount =
    account.limits.isOverrun && !account.limits.needShopifyApproveForOverrun;

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  if (account.type === 'normal') {
    return (
      account.billing.dateExpires >= today &&
      (isOverrunEnabledForAccount ||
        (!isFreeAccount && account.limits.maxVisitor > account.limits.usedVisitor) ||
        (isFreeAccount && account.limits.maxPageViews > account.limits.pageViews))
    );
  }

  mongoHelperLog('accountHasRemainingVisitors/agencyAccount', accountId);
  const agencyAccount = await dbConnection.collection(ACCOUNTS_COLLECTION_NAME).findOne(
    {
      type: 'agency',
      subAccounts: account._id,
      'billing.dateExpires': { $gte: today },
    },
    { projection: { type: 1, billing: 1, limits: 1 } },
  );

  if (!agencyAccount) {
    return false;
  }

  if (!(agencyAccount.billing.dateExpires >= today)) {
    return false;
  }

  // (0 is not equal unlimited) Reseller intentionally sets 0 as limit to block the campaigns
  // If the limit property is missing or null, then it will mean unlimited.
  const isUnlimited = account.limits.maxVisitor !== 0 && !account.limits.maxVisitor;

  if (!isUnlimited && account.limits.maxVisitor <= account.limits.usedVisitor) {
    return false;
  }

  const isOverrunEnabledForAgencyAccount =
    agencyAccount.limits.isOverrun && !agencyAccount.limits.needShopifyApproveForOverrun;

  if (isOverrunEnabledForAgencyAccount) {
    return true;
  }

  if (agencyAccount.limits.maxVisitor <= agencyAccount.limits.usedVisitor) {
    return false;
  }

  return true;
};

const parseCampaignForPreview = async (campaign, accountId: number): Promise<ICampaign> => {
  const account = await dbConnection.collection('master_accounts').findOne(
    {
      databaseId: accountId,
    },
    { projection: { features: true } },
  );

  const result: ICampaign = {
    _id: campaign._id,
    id: campaign.id,
    templateName: campaign.templateName,
    name: campaign.name,
    createdAt: campaign.createdAt,
    updatedAt: campaign.updatedAt,
    events: campaign.settings.events,
    positions: campaign.settings.positions,
    rules: campaign.settings.frontendRules,
    frequency: campaign.settings.frequency,
    analyticsType: campaign.settings.analyticsType,
    schedule: campaign.schedule,
    domainId: campaign.domainId,
    variants: campaign.variants,
    mode: campaign.variants[0]?.template?.style?.mode,
    integrations: campaign.settings.integrations,
    status: 'active',
    priority: campaign.settings.priority,
    analytics: { status: 1, type: 'preview' },
    settings: { stopTestRunning: false },
    currentExperimentId: campaign.currentExperimentId,
    experiences: [],
    isControlVariant: false,
    locale: campaign.locale,
  };

  if (account?.features?.includes(FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
    const variantTemplates = await dbConnection
      .collection('user_variant_templates')
      .find({
        databaseId: accountId,
        campaignId: campaign.id,
      })
      .toArray();

    result.mode = variantTemplates[0].template.style.mode;
    result.variants = result.variants.map((variant) => {
      const variantTemplate = variantTemplates.find(
        (vt) => vt.variantId.toString() === variant._id.toString(),
      );
      return {
        ...variant,
        template: variantTemplate?.template,
      };
    });
  }
  return result;
};

const getAllCampaignsByAccountId = async (
  accountId: number,
  device: Device,
  domains: any[],
): Promise<ICampaign[]> => {
  const domainIds = domains.map((d) => d._id);
  const account = await dbConnection.collection('master_accounts').findOne(
    {
      databaseId: accountId,
    },
    { projection: { features: true } },
  );
  mongoHelperLog('getAllCampaignsByAccountId', accountId);
  const campaignAggregation = dbConnection
    .collection<ICampaign[]>(`${accountId}_campaigns`)
    .aggregate(
      [
        {
          $match: {
            status: {
              $in: ['active', 'inactive', 'archived'],
            },
            'settings.events': {
              $ne: null,
            },
            'variants.status': 'active',
            domainId: { $in: domainIds },
            device: { $in: ['desktop_and_mobile', `${device}`] },
            version: 2,
          },
        },
        { $sort: { createdAt: -1 } },
        {
          $addFields: {
            mode: {
              ...getProjectionValueFromArray(
                '$variants',
                { $ne: ['$$this.template.style.mode', null] },
                'template.style.mode',
              ),
            },
            'variants.template': null,
          },
        },
        {
          $project: {
            id: 1,
            templateName: 1,
            name: 1,
            createdAt: 1,
            updatedAt: 1,
            events: '$settings.events',
            rules: '$settings.frontendRules',
            positions: '$settings.positions',
            analyticsType: '$settings.analyticsType',
            priority: '$settings.priority',
            status: '$status',
            currentExperimentId: '$currentExperimentId',
            isControlVariant: '$isControlVariant',
            schedule: 1,
            domainId: 1,
            variants: {
              $filter: {
                input: '$variants',
                cond: {
                  $eq: ['$$this.status', 'active'],
                },
              },
            },
            integrations: '$settings.integrations',
            mode: 1,
          },
        },
      ],
      { allowDiskUse: true },
    )
    .toArray();

  const result = await Promise.resolve(campaignAggregation);

  if (account?.features?.includes(FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
    const campaignIds = result.map((c) => c.id);
    const variantIds = result.map((c) => c.variants.map((v) => v._id)).flat();
    const variantTemplates = await dbConnection
      .collection('user_variant_templates')
      .find(
        {
          databaseId: accountId,
          campaignId: { $in: campaignIds },
          variantId: { $in: variantIds },
        },
        { projection: { _id: 0, 'template.style.mode': 1, variantId: 1, campaignId: 1 } },
      )
      .toArray();

    result.forEach((campaign) => {
      campaign.mode = variantTemplates.find(
        (vt) => vt.campaignId === campaign.id,
      )?.template.style.mode;
    });
  }

  const campaignExperiencesMap = await getCampaignExperiencesMap({
    campaignIds: result.map((campaign) => campaign._id),
    databaseId: accountId,
  });
  result.forEach((c) => {
    c.events.forEach((e) => {
      if (e.options) e.options.status = '1';
    });
    const domain = domains.find((domain) => domain._id.toString() === c.domainId.toString());
    let isAnalyticsDisabled = false;

    if (domain && domain.analytics && !domain.analytics.enabled) {
      isAnalyticsDisabled = true;
    }

    c.analytics = isAnalyticsDisabled ? {} : { status: 1 };
    c.experiences = arrayify(campaignExperiencesMap[c._id]);
  });

  return result as ICampaign[];
};

const getAllActiveCampaignsByAccountId = async (
  accountId: number,
  features: Array<String> | null = null,
) => {
  return campaignHelper.getAllActiveCampaignsByAccountId(dbConnection, accountId, features);
};

const getCampaignForPreview = async (accountId: number, variantId) => {
  return campaignHelper.getCampaignForPreview(secondaryDbConnection, accountId, variantId);
};

const getCampaignExperiencesMap = async ({
  campaignIds,
  databaseId,
  filterByVariants,
}: {
  campaignIds: ObjectId[];
  databaseId: number;
  filterByVariants?: string[] | undefined;
}) => {
  mongoHelperLog('getCampaignExperiencesMap', databaseId);
  const experiences = await dbConnection
    .collection(`user_experiences`)
    .find({
      databaseId,
      campaign: { $in: campaignIds },
      deletedAt: { $exists: false },
      'variants.0': { $exists: true },
    })
    .project({
      _id: 1,
      name: 1,
      variants: 1,
      frontendRules: 1,
      campaign: 1,
      priority: 1,
    })
    .sort({ priority: 1 })
    .toArray();

  const experiencesMap = {};
  experiences.forEach((exp) => {
    const campaignExperiences = arrayify(experiencesMap[exp.campaign]);

    let { variants } = exp;

    if (filterByVariants) {
      variants = variants.filter((variantId) => filterByVariants.includes(variantId.toString()));
    }

    if (variants.length) {
      const { _id, frontendRules, priority, name } = exp;
      campaignExperiences.push({ _id, variants, frontendRules, priority, name });
    }

    experiencesMap[exp.campaign] = campaignExperiences;
  });

  return experiencesMap;
};

const getAllCampaignTypesByAccountId = async (accountId: number) => {
  mongoHelperLog('getAllCampaignTypesByAccountId', accountId);
  const variants = await dbConnection
    .collection(`${accountId}_campaigns`)
    .aggregate(
      [
        {
          $match: {
            status: { $ne: 'deleted' },
          },
        },
        {
          $project: {
            id: 1,
            'variants._id': 1,
            'variants.status': 1,
            'variants.isControlVariant': 1,
            'variants.mode': 1,
            'variants.overlayPosition': 1,
            type: 1,
            status: 1,
          },
        },
        {
          $group: {
            _id: '$_id',
            id: { $first: '$id' },
            variants: { $first: '$variants' },
            type: { $first: '$type' },
            status: { $first: '$status' },
          },
        },
        { $unwind: '$variants' },
        {
          $project: {
            id: 1,
            'variants._id': 1,
            'variants.status': 1,
            'variants.isControlVariant': 1,
            'variants.mode': 1,
            'variants.overlayPosition': 1,
            type: 1,
            status: 1,
          },
        },
        { $match: { 'variants.status': { $ne: 'deleted' } } },
        {
          $group: {
            _id: '$variants._id',
            id: { $first: '$id' },
            variant: { $first: '$variants' },
            type: { $first: '$type' },
            status: { $first: '$status' },
          },
        },
        { $sort: { id: 1 } },
      ],
      { allowDiskUse: true },
    )
    .toArray();

  const variantsByCampaign = variants.reduce((variantMap, variant) => {
    variantMap[variant.id] = arrayify(variantMap[variant.id]);
    variantMap[variant.id].push(variant);
    return variantMap;
  }, {});

  return variants.map((variant) => {
    const variantTypeFields = {};

    if (variant?.variant?.isControlVariant) {
      const nonControlVariant = variantsByCampaign[variant.id].find(
        (variantByCampaign) => !variantByCampaign?.variant?.isControlVariant,
      );

      if (nonControlVariant) {
        const { mode, overlayPosition } = nonControlVariant.variant;
        Object.assign(variantTypeFields, { mode, overlayPosition });
      }
    }

    return { ...variant, variant: { ...variant.variant, ...variantTypeFields } };
  });
};

const getAllCampaignTypesByAccountIdSecDB = async (accountId: number) => {
  mongoHelperLog('getAllCampaignTypesByAccountIdSecDB', accountId);
  const variants = await secondaryDbConnection
    .collection(`${accountId}_campaigns`)
    .aggregate(
      [
        {
          $match: {
            status: { $ne: 'deleted' },
          },
        },
        {
          $project: {
            id: 1,
            'variants._id': 1,
            'variants.status': 1,
            'variants.isControlVariant': 1,
            'variants.mode': 1,
            'variants.overlayPosition': 1,
            type: 1,
            status: 1,
          },
        },
        {
          $group: {
            _id: '$_id',
            id: { $first: '$id' },
            variants: { $first: '$variants' },
            type: { $first: '$type' },
            status: { $first: '$status' },
          },
        },
        { $unwind: '$variants' },
        {
          $project: {
            id: 1,
            'variants._id': 1,
            'variants.status': 1,
            'variants.isControlVariant': 1,
            'variants.mode': 1,
            'variants.overlayPosition': 1,
            type: 1,
            status: 1,
          },
        },
        { $match: { 'variants.status': { $ne: 'deleted' } } },
        {
          $group: {
            _id: '$variants._id',
            id: { $first: '$id' },
            variant: { $first: '$variants' },
            type: { $first: '$type' },
            status: { $first: '$status' },
          },
        },
        { $sort: { id: 1 } },
      ],
      { allowDiskUse: true },
    )
    .toArray();

  const variantsByCampaign = variants.reduce((variantMap, variant) => {
    variantMap[variant.id] = arrayify(variantMap[variant.id]);
    variantMap[variant.id].push(variant);
    return variantMap;
  }, {});

  return variants.map((variant) => {
    const variantTypeFields = {};

    if (variant?.variant?.isControlVariant) {
      const nonControlVariant = variantsByCampaign[variant.id].find(
        (variantByCampaign) => !variantByCampaign?.variant?.isControlVariant,
      );

      if (nonControlVariant) {
        const { mode, overlayPosition } = nonControlVariant.variant;
        Object.assign(variantTypeFields, { mode, overlayPosition });
      }
    }

    return { ...variant, variant: { ...variant.variant, ...variantTypeFields } };
  });
};

const updateLastRequestDate = async (accountId: number, domain: string) => {
  const key = `updateLastRequestDate:${accountId}:${domain}:lock`;
  // return: OK = lock not exists, null = lock exists
  const result = await redisAdapter.setexnx(key, '', 300);
  if (result === null) {
    return;
  }

  const domains = await _getAllDomainsForAccount(accountId);
  if (!domains.length) {
    logger.info('No account with id', accountId);
    return;
  }

  const domainMap = domains.map((domainRow) => domainRow.domain);
  const domainRegex = new RegExp(`^(www\.)?${purifyDomain(domain)}$`, 'i');
  const foundDomainsRow = domains.filter((domainRow) => domainRegex.exec(domainRow.domain));
  if (!foundDomainsRow?.length) {
    logger.info('No account with domain.', accountId, domain);
    return;
  }

  const set = {};
  foundDomainsRow.forEach((domain) => {
    heapAnalytics.updateDomainRequestReceivedEvent('v2', {
      accountId,
      domain: domain.domain,
    });
    set[`settings.domains.${domainMap.indexOf(domain.domain)}.lastRequestDate`] = new Date();
  });

  mongoHelperLog('updateLastRequestDate/return', accountId);
  return dbConnection
    .collection(ACCOUNTS_COLLECTION_NAME)
    .updateOne({ databaseId: accountId }, { $set: set });
};

const updateV3LastRequestDate = async (accountId: number, domain: string) => {
  const key = `updateV3LastRequestDate:${accountId}:${domain}:lock`;
  // return: OK = lock not exists, null = lock exists
  const result = await redisAdapter.setexnx(key, '', 300);
  if (result === null) {
    return;
  }

  const domains = await _getAllDomainsForAccount(accountId);
  if (!domains.length) {
    logger.info('No account with id', accountId);
    return;
  }

  const domainMap = domains.map((domainRow) => domainRow.domain);
  const domainRegex = new RegExp(`^(www\.)?${purifyDomain(domain)}$`, 'i');
  const foundDomainsRow = domains.filter((domainRow) => domainRegex.exec(domainRow.domain));
  if (!foundDomainsRow?.length) {
    logger.info('No account with domain.', accountId, domain);
    return;
  }

  const set = {};
  foundDomainsRow.forEach((domain) => {
    heapAnalytics.updateDomainRequestReceivedEvent('v3', {
      accountId,
      domain: domain.domain,
    });
    set[`settings.domains.${domainMap.indexOf(domain.domain)}.v3LastRequestDate`] = new Date();
  });

  mongoHelperLog('updateLastRequestDate/return', accountId);
  return dbConnection
    .collection(ACCOUNTS_COLLECTION_NAME)
    .updateOne({ databaseId: accountId }, { $set: set });
};

const getAllActiveDomainsForAccount = async (accountId: number): Promise<IDomain[]> => {
  mongoHelperLog('getAllActiveDomainsForAccount', accountId);
  const allDomains = await dbConnection
    .collection(ACCOUNTS_COLLECTION_NAME)
    .findOne(
      { databaseId: accountId, 'settings.domains': { $elemMatch: { inactive: { $ne: true } } } },
      { projection: { 'settings.domains': 1 } },
    );

  return allDomains?.settings?.domains || [];
};

const setCampaignStatusInactive = async (accountId: number, campaignId: ObjectId) => {
  mongoHelperLog('setCampaignStatusInactive', accountId);
  const result = await dbConnection
    .collection(`${accountId}_campaigns`)
    .updateOne({ _id: campaignId }, { $set: { status: 'inactive' } });
  return result;
};

const setVariantStatusActive = async (accountId: number, variantId: string) => {
  mongoHelperLog('setVariantStatusActive', accountId);
  return dbConnection
    .collection(`${accountId}_campaigns`)
    .updateOne(
      { 'variants._id': new ObjectId(variantId) },
      { $set: { 'variants.$.status': 'active' } },
    );
};

const increaseImpressionOrConversion = async (
  accountId,
  variantId,
  key: 'impressions' | 'conversions',
  device: Device,
  experimentGroupId,
) => {
  const period = moment().format('YYYY-MM-DD');
  const { _id: campaignId, isControlVariant } = await findCampaignIdByVariant(accountId, variantId);
  return redisAdapter.incrementStatistics({
    key,
    period,
    accountId,
    variantId,
    campaignId,
    device,
    experimentGroupId,
    isControlVariant,
  });
};

const increaseImpressions = async (accountId, variantId, device, experimentGroupId) => {
  increaseImpressionOrConversion(accountId, variantId, 'impressions', device, experimentGroupId);
};

const increaseConversions = async (accountId, variantId, device, experimentGroupId) => {
  increaseImpressionOrConversion(accountId, variantId, 'conversions', device, experimentGroupId);
};

const findCampaignIdByVariant = async (
  accountId,
  variantId: string,
): Promise<{ _id: ObjectId; id: number; isControlVariant: boolean }> => {
  const variant = new ObjectId(variantId);
  mongoHelperLog('findCampaignIdByVariant', accountId);
  const { _id, id, variants }: any = await dbConnection
    .collection(`${accountId}_campaigns`)
    .findOne(
      { 'variants._id': variant },
      { projection: { _id: 1, id: 1, 'variants._id': 1, 'variants.isControlVariant': 1 } },
    );
  const campaignVariant = variants.find(
    (variantItem) => variantItem._id.toString() === variantId.toString(),
  );

  return { _id, id, isControlVariant: campaignVariant?.isControlVariant ?? false };
};

const findActiveCampaignIdByVariant = async (
  accountId,
  variantId: string,
): Promise<{ _id: ObjectId; id: number } | null> => {
  const variant = new ObjectId(variantId);
  mongoHelperLog('findActiveCampaignIdByVariant', accountId);
  const campaign: any = await dbConnection
    .collection(`${accountId}_campaigns`)
    .findOne(
      { status: 'active', variants: { $elemMatch: { _id: variant, status: 'active' } } },
      { projection: { _id: 1, id: 1 } },
    );

  if (!campaign) {
    return null;
  }

  return { _id: campaign._id, id: campaign.id };
};

const findCampaignByVariant = async (accountId, variantId: string): Promise<ICampaign> => {
  const filter = { 'variants._id': new ObjectId(variantId) };

  mongoHelperLog('findCampaignByVariant', accountId);
  return (await dbConnection.collection(`${accountId}_campaigns`).findOne(filter)) as ICampaign;
};

const getVariantStat = async (accountId, campaignId: string, variantId: string) => {
  mongoHelperLog('getVariantStat', accountId);
  const experiencePromise = dbConnection.collection(`user_experiences`).findOne({
    campaign: new ObjectId(campaignId),
    databaseId: parseInt(accountId, 10),
    variants: new ObjectId(variantId),
    deletedAt: { $exists: false },
  });

  const variantsPromise = dbConnection
    .collection(`${accountId}_campaigns`)
    .aggregate([
      { $match: { _id: new ObjectId(campaignId) } },
      { $project: { variants: 1 } },
      { $unwind: '$variants' },
      { $replaceRoot: { newRoot: '$variants' } },
      { $match: { status: { $in: ['active', 'inactive'] } } },
      { $group: { _id: '$_id', impr: { $sum: '$impressions' }, conv: { $sum: '$conversions' } } },
      { $sort: { conv: -1 } },
    ])
    .toArray();

  const [experience, variants] = await Promise.all([experiencePromise, variantsPromise]);

  if (experience) {
    const experienceVariantIds = experience.variants.map((experienceVariantId) =>
      experienceVariantId.toString(),
    );

    const filteredVariants = variants.filter((variant) =>
      experienceVariantIds.includes(variant._id.toString()),
    );
    return { variants: filteredVariants, experience };
  }

  return { variants, experience };
};

interface ICampaignIntegrations {
  id: number;
  _id: ObjectId;
  name: string;
  settings: { [key: string]: any };
}

interface IAccountIntegrations {
  settings: { [key: string]: any };
}

const getIntegrationSettings = async (accountId, variantId) => {
  mongoHelperLog('getIntegrationSettings', accountId);
  const dbResults = await Promise.all([
    dbConnection
      .collection(ACCOUNTS_COLLECTION_NAME)
      .findOne(
        { databaseId: parseInt(accountId, 10) },
        { projection: { 'settings.integrations': 1 } },
      ),
    dbConnection
      .collection(`${accountId}_campaigns`)
      .findOne(
        { 'variants._id': new ObjectId(variantId) },
        { projection: { _id: 1, name: 1, 'settings.integrations': 1 } },
      ),
  ]);
  const account: IAccountIntegrations = dbResults[0] as unknown as IAccountIntegrations;
  // eslint-disable-next-line prettier/prettier
  const campaign: ICampaignIntegrations = dbResults[1] as unknown as ICampaignIntegrations;

  if (campaign === null) {
    integrationSettingLogger.warn(`No campaign for payload: ${accountId} ${variantId}`);
  }

  return {
    campaign_id: campaign._id,
    campaign_name: campaign.name,
    integration_settings: [account.settings, campaign.settings],
    subscriber: {},
  };
};

const getIntegrationSettingsForCampaign = async (accountId, campaignId) => {
  mongoHelperLog('getIntegrationSettingsForCampaign', accountId);
  const dbResults = await Promise.all([
    dbConnection
      .collection(ACCOUNTS_COLLECTION_NAME)
      .findOne(
        { databaseId: parseInt(accountId, 10) },
        { projection: { 'settings.integrations': 1 } },
      ),
    dbConnection
      .collection(`${accountId}_campaigns`)
      .findOne(
        { id: parseInt(campaignId, 10) },
        { projection: { _id: 1, name: 1, 'settings.integrations': 1 } },
      ),
  ]);
  const account: IAccountIntegrations = dbResults[0] as unknown as IAccountIntegrations;
  // eslint-disable-next-line prettier/prettier
  const campaign: ICampaignIntegrations = dbResults[1] as unknown as ICampaignIntegrations;
  return {
    campaign_id: campaign._id,
    campaign_name: campaign.name,
    campaign_template_version: 2,
    integration_settings: [account.settings, campaign.settings],
    subscriber: {},
  };
};

interface ISubscriber {
  pageUserId: string;
  firstName: string;
  lastName: string;
  email: string;
  campaignId: ObjectId;
  variantId: ObjectId;
  syncStatus: string;
  customFields: object | null;
  databaseId?: number | null;
  __META__?: any;
}

const getSubscriber = async (fields) => {
  mongoHelperLog('getSubscriber', fields.databaseId);
  return dbConnection.collection(`user_subscribers`).findOne(fields);
};

const insertSubscriber = async (accountId, subscriber: ISubscriber): Promise<InsertOneResult> => {
  mongoHelperLog('insertSubscriber', accountId);
  return dbConnection.collection(`user_subscribers`).insertOne({
    ...subscriber,
    databaseId: parseInt(accountId, 10),
    deleted: false,
    numberOfFills: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
  });
};

const getSubscribersByCampaignWithCustomDate = async (
  accountId: string | number,
  campaignId: string | ObjectId,
  startDate: string | Date,
  endDate: string | Date,
) => {
  mongoHelperLog('getSubscribersByCampaignWithCustomDate', accountId);
  return dbConnection
    .collection(`user_subscribers`)
    .find({
      databaseId: parseInt(accountId as string, 10),
      campaignId: new ObjectId(campaignId),
      createdAt: { $gte: new Date(startDate), $lt: new Date(endDate) },
    })
    .sort({ _id: 1 });
};

const appearOnLeadsPage = (subscriber): boolean => {
  const hasMeaningFulCustomField =
    subscriber.customFields &&
    Object.entries(subscriber.customFields).some(([key, value]) => {
      return (
        key !== 'coupon_code' && key !== 'coupon_title' && value && !key.startsWith('product_')
      );
    });

  const hasVisitorData = subscriber.firstName || subscriber.lastName || subscriber.email;

  return hasMeaningFulCustomField || Boolean(hasVisitorData);
};

const upsertSubscriber = async (
  accountId,
  filter,
  subscriber: ISubscriber,
  increaseFill: boolean = true,
): Promise<UpdateResult> => {
  if (subscriber.variantId && typeof subscriber.variantId === 'string') {
    subscriber.variantId = new ObjectId(subscriber.variantId);
  }

  if (filter.variantId && typeof filter.variantId === 'string') {
    filter.variantId = new ObjectId(filter.variantId);
  }

  const canAppearOnLeadsPage = appearOnLeadsPage(subscriber);

  const dataToSet = {
    $set: {
      ...subscriber,
      ...(canAppearOnLeadsPage && { canAppearOnLeadsPage }),
      databaseId: parseInt(accountId, 10),
      deleted: false,
      updatedAt: new Date(),
    },
    $inc: { numberOfFills: 1 },
    $setOnInsert: { createdAt: new Date() },
  };
  // @ts-ignore
  if (!increaseFill) delete dataToSet.$inc;

  mongoHelperLog('upsertSubscriber', accountId);
  return dbConnection.collection(`user_subscribers`).updateOne(filter, dataToSet, { upsert: true });
};

const updateSubscriberStatus = async (accountId, filter, status) => {
  mongoHelperLog('updateSubscriberStatus', accountId);
  return dbConnection
    .collection(`user_subscribers`)
    .updateOne(
      { ...filter, databaseId: parseInt(accountId, 10) },
      { $set: { syncStatus: status } },
    );
};

const saveFailedSubscription = async ({
  campaignId,
  databaseId,
  integrationId,
  variantId,
  status,
  message = '',
  data = {},
  context = { type: '' },
}) => {
  const errorCode = IntegrationErrorParser.getErrorCode(context.type, message, status);

  await reportIntegrationErrorAlert({
    campaignId,
    databaseId,
    integrationId,
    context,
    errorCode,
  });
  mongoHelperLog('saveFailedSubscription');
  return dbConnection.collection('subscription_failed').insertOne({
    databaseId: parseInt(databaseId, 10),
    integrationId: new ObjectId(integrationId),
    variantId: new ObjectId(variantId),
    data,
    tryCount: 0,
    reason: message,
    errorCode,
    createdAt: new Date(),
    updatedAt: new Date(),
  });
};

const getFailedSubscriptions = async (): Promise<FindCursor> => {
  mongoHelperLog('getFailedSubscriptions');
  return dbConnection
    .collection('subscription_failed')
    .find({ tryCount: { $lt: 3 }, createdAt: { $gte: new Date(2021, 0, 1, 0, 0, 0, 0) } });
};

const getFailedSubscriptionsForResync = async (
  databaseId,
  integrationId,
  variantIds,
  interval,
  errorCode,
): Promise<any> => {
  const query: any = {
    databaseId,
    integrationId,
    variantId: { $in: variantIds },
  };

  if (interval?.from) {
    query.createdAt = { $gte: interval.from };
  }

  if (interval?.to) {
    if (query.createdAt) {
      query.createdAt.$lte = interval.to;
    } else {
      query.createdAt = { $lte: interval.to };
    }
  }

  if (errorCode) {
    query.errorCode = errorCode;
  }

  mongoHelperLog('getFailedSubscriptionsForResync', databaseId);
  return dbConnection.collection('subscription_failed').find(query).toArray();
};

const getFailedSubscriptionsWithCustomDate = async (
  startDate: string | Date,
  endDate: string | Date,
): Promise<FindCursor> => {
  mongoHelperLog('getFailedSubscriptionsWithCustomDate');
  return dbConnection.collection('subscription_failed').find(
    {
      tryCount: { $lt: 3 },
      createdAt: { $gte: new Date(startDate), $lt: new Date(endDate) },
    },
    { projection: { _id: 1 } },
  );
};

const getFailedSubscriptionById = async (
  id: string | ObjectId,
): Promise<IFailedSubscription | null> => {
  mongoHelperLog('getFailedSubscriptionById');
  return dbConnection
    .collection<IFailedSubscription>('subscription_failed')
    .findOne({ _id: new ObjectId(id) });
};

const getFailedSubscriptionsByDatabaseId = async (
  databaseId: number,
): Promise<IFailedSubscription[]> => {
  mongoHelperLog('getFailedSubscriptionsByDatabaseId');
  return dbConnection
    .collection<IFailedSubscription>('subscription_failed')
    .find({ databaseId })
    .toArray();
};

const incrementTryCountOfFailedSubscription = async (id) => {
  mongoHelperLog('incrementTryCountOfFailedSubscription');
  return dbConnection
    .collection('subscription_failed')
    .updateOne({ _id: new ObjectId(id) }, { $inc: { tryCount: 1 } });
};

const updateFailedSubscription = async (id, doc) => {
  mongoHelperLog('updateFailedSubscription');
  return dbConnection
    .collection('subscription_failed')
    .updateOne({ _id: new ObjectId(id) }, { $set: doc });
};

const deleteFromFailedSubscription = async (failedSubId) => {
  mongoHelperLog('deleteFromFailedSubscription', failedSubId);
  return dbConnection
    .collection('subscription_failed')
    .deleteMany({ _id: new ObjectId(failedSubId) });
};

const checkHasFailedSubscription = async ({
  variantId,
  data: {
    subscriber: { pageUserId },
  },
}) => {
  mongoHelperLog('checkHasFailedSubscription');
  return dbConnection
    .collection('subscription_failed')
    .findOne({ variantId, 'data.subscriber.pageUserId': pageUserId });
};

const _buildStatOperation = (databaseId: number, pageViewsInc: number, usedVisitorInc: number) => {
  return {
    updateOne: {
      filter: { databaseId },
      update: {
        $inc: {
          'limits.pageViews': pageViewsInc,
          'limits.usedVisitor': usedVisitorInc,
        },
      },
    },
  };
};

const getAgencyDatabaseIdForSubAccount = async (subAccountId: ObjectId) => {
  const cacheKey = `frontend:${subAccountId.toHexString()}:agency`;
  const accountCache = await redisAdapter.get(cacheKey);

  if (accountCache) return JSON.parse(accountCache);

  mongoHelperLog('getAgencyDatabaseIdForSubAccount', subAccountId);
  const agencyAccount = await dbConnection.collection(ACCOUNTS_COLLECTION_NAME).findOne(
    {
      subAccounts: subAccountId,
      type: 'agency',
    },
    { projection: { databaseId: 1 } },
  );

  await redisAdapter.setex(cacheKey, JSON.stringify(agencyAccount), 60);

  return agencyAccount;
};

const _buildAgencyPageViewsAndUsedVisitorOperation = async (
  subAccountId: ObjectId,
  increments: { pageViewsInc: number; usedVisitorInc: number },
) => {
  const agencyAccount = await getAgencyDatabaseIdForSubAccount(subAccountId);
  if (agencyAccount) {
    const { databaseId } = agencyAccount;
    const { pageViewsInc, usedVisitorInc } = increments;

    return _buildStatOperation(databaseId, pageViewsInc, usedVisitorInc);
  }

  return false;
};

const getDBIdsFrom = (operations) => {
  return operations.map((operation) => {
    return operation.updateOne.filter.databaseId;
  });
};

const writePageViewsAndUsedVisitorToDb = async (pageViews: object, usedVisitor: object) => {
  const pageViewKeys = Object.getOwnPropertyNames(pageViews); // Object Keys does not wotk
  const usedVisitorKeys = Object.getOwnPropertyNames(usedVisitor); // Object Keys does not wotk
  const deduplicatedKeys = new Set();
  pageViewKeys.forEach((e) => {
    deduplicatedKeys.add(e);
  });
  usedVisitorKeys.forEach((e) => {
    deduplicatedKeys.add(e);
  });

  const operations: AnyBulkWriteOperation[] = [];
  for (const key of Array.from(deduplicatedKeys)) {
    const databaseId = parseInt(key as string, 10);
    const pageViewsInc = pageViews[key as string] ? parseInt(pageViews[key as string], 10) : 0;
    const usedVisitorInc = usedVisitor[key as string]
      ? parseInt(usedVisitor[key as string], 10)
      : 0;

    if (pageViewsInc > 0 || usedVisitorInc > 0) {
      const operation = _buildStatOperation(databaseId, pageViewsInc, usedVisitorInc);
      operations.push(operation);

      const currentAccount = await getAccountSettings(databaseId);
      if (!currentAccount) {
        logger.error(
          'writePageViewsAndUsedVisitorToDb: failed to resolve account settings for account %d',
          databaseId,
        );
        continue;
      }
      if (currentAccount.type === 'sub') {
        const currentAccountObjectId = new ObjectId(currentAccount._id);
        const operation = await _buildAgencyPageViewsAndUsedVisitorOperation(
          currentAccountObjectId,
          {
            pageViewsInc,
            usedVisitorInc,
          },
        );
        if (operation) operations.push(operation);
      }
    }
  }

  if (operations.length > 0) {
    logger.info(`writing pageViews and used Visitors to db, length: ${operations.length}`);
    mongoHelperLog('writePageViewsAndUsedVisitorToDb/pageViews&Visitors_to_db');
    return dbConnection
      .collection(ACCOUNTS_COLLECTION_NAME)
      .bulkWrite(operations)
      .then(async () => {
        mongoHelperLog('writePageViewsAndUsedVisitorToDb/after_pageViews&Visitors_to_db');

        const dbIds = getDBIdsFrom(operations);
        const result = await dbConnection
          .collection(ACCOUNTS_COLLECTION_NAME)
          .aggregate([
            {
              $match: {
                databaseId: { $in: dbIds },
                'limits.limitReachedAt': null,
              },
            },
            {
              $project: {
                databaseId: 1,
                limits: 1,
                billing: 1,
                type: 1,
                pageLimit: {
                  $and: [
                    { $gt: ['$limits.maxPageViews', 0] },
                    { $gte: ['$limits.pageViews', '$limits.maxPageViews'] },
                  ],
                },
                visitorLimit: {
                  $and: [
                    { $gt: ['$limits.maxVisitor', 0] },
                    { $gte: ['$limits.usedVisitor', '$limits.maxVisitor'] },
                  ],
                },
              },
            },
            { $match: { $or: [{ pageLimit: true }, { visitorLimit: true }] } },
          ])
          .toArray();

        if (result.length) {
          const limitReachedOperations: AnyBulkWriteOperation[] = [];
          result.forEach((account) => {
            const isFreeAccount = isFreemiumAccount(account);
            if ((isFreeAccount && account.pageLimit) || (!isFreeAccount && account.visitorLimit)) {
              limitReachedOperations.push({
                updateOne: {
                  filter: { databaseId: account.databaseId },
                  update: {
                    $set: {
                      'limits.limitReachedAt': new Date(),
                    },
                  },
                },
              });

              LimitManager.setLimitReachedInCDN(account.databaseId);
            }
          });

          if (limitReachedOperations.length) {
            logger.info(
              `writing limit reached date to db, length: ${limitReachedOperations.length}`,
            );
            mongoHelperLog('writePageViewsAndUsedVisitorToDb/limit_reached_date_to_db');
            dbConnection.collection(ACCOUNTS_COLLECTION_NAME).bulkWrite(limitReachedOperations);
          }
        }
      });
  }
  return [];
};

const getZapierHooks = async (accountId): Promise<ZapierHook[] | null> => {
  mongoHelperLog('getZapierHooks', accountId);
  const account = await dbConnection
    .collection(ACCOUNTS_COLLECTION_NAME)
    .findOne(
      { databaseId: parseInt(accountId, 10) },
      { projection: { 'settings.zapier.hooks': 1 } },
    );

  if (!account) return null;

  if (
    _get(account, 'settings.zapier.hooks', false) &&
    Array.isArray(account.settings.zapier.hooks) &&
    account.settings.zapier.hooks.length
  ) {
    return account.settings.zapier.hooks;
  }

  return null;
};

const getCampaignData = async (databaseId, campaignId: ObjectId, variantId: ObjectId) => {
  mongoHelperLog('getCampaignData', databaseId, variantId);
  const campaignData = await dbConnection
    .collection(`${databaseId}_campaigns`)
    .findOne(
      { _id: new ObjectId(campaignId), 'variants._id': new ObjectId(variantId) },
      { projection: { id: 1, name: 1, 'variants.$': 1 } },
    );

  if (!campaignData) {
    return null;
  }

  return {
    _id: campaignData._id,
    id: campaignData.id,
    name: campaignData.name,
    variantName: campaignData.variants[0].name,
  };
};

const getShopData = async (databaseId, campaignId, type) => {
  mongoHelperLog('getShopData', databaseId);
  const [account, campaign] = await Promise.all([
    dbConnection
      .collection('master_accounts')
      .findOne({ databaseId }, { projection: { 'settings.shops': 1, 'settings.domains': 1 } }),
    dbConnection
      .collection(`${databaseId}_campaigns`)
      .findOne({ id: campaignId }, { projection: { domainId: 1 } }),
  ]);

  if (!account || !campaign) return;

  const domain = account.settings.domains.find(({ _id }) => _id.equals(campaign.domainId));
  if (!domain) return;

  const shops = account.settings.shops.filter((e) => e.active && e.type === type);

  if (domain.platform === type && domain.shopId) {
    const shop = shops.find(
      // eslint-disable-next-line camelcase
      ({ shopname, myshopify_domain, shopId }) =>
        domain.shopId === shopname ||
        // eslint-disable-next-line camelcase
        domain.shopId === myshopify_domain ||
        domain.shopId === shopId,
    );

    if (shop) {
      if (domain.providerServiceIdOverride) {
        shop.providerServiceIdOverride = domain.providerServiceIdOverride;
      }
      return shop;
    }
  }

  logger.info('Falling back to old domain lookup during shop data resolution, %o', {
    type,
    databaseId,
    campaignId,
    domain,
  });

  const domainStr = domain.domain.replace('www.', '');
  switch (type) {
    case 'shoprenter':
      return shops.find((e) => e.domains && e.domains.some((d) => d.domain.endsWith(domainStr)));
    case 'shopify':
    case 'woocommerce':
    case 'unas':
      // eslint-disable-next-line camelcase
      return shops.find(({ live_domain }) => live_domain && live_domain.endsWith(domainStr));
    default:
      throw Error(`unsupportedShopType ${type}`);
  }
};

const _getShopSetting = async (databaseId) => {
  mongoHelperLog('getShopSetting', databaseId);
  const account = await dbConnection
    .collection('master_accounts')
    .findOne({ databaseId: parseInt(databaseId, 10) }, { projection: { 'settings.shops': 1 } });

  if (!account) {
    return null;
  }

  return _get(account, 'settings.shops', []);
};

const getShopSettingByDomain = async (databaseId, myshopifyDomain: string) => {
  mongoHelperLog('getShopSettingByDomain', databaseId);
  const shopSetting = await _getShopSetting(databaseId);

  return shopSetting.find(
    (s) =>
      s.type === 'shopify' &&
      (s.myshopify_domain === myshopifyDomain ||
        purifyDomain(s.live_domain) === purifyDomain(myshopifyDomain)),
  );
};

const getShopSettingByShopifyId = async (databaseId, shopifyId: number) => {
  mongoHelperLog('getShopSettingByShopifyId', databaseId);
  const shopSetting = await _getShopSetting(databaseId);

  return shopSetting.find((s) => s.type === 'shopify' && s.shopify_id === shopifyId);
};

const insertShopifyCheckout = async (checkout: ICheckout) => {
  mongoHelperLog('insertShopifyCheckout');
  return dbConnection.collection('master_checkouts').insertOne({
    ...checkout,
    type: 'shopify',
    createdAt: new Date(),
  });
};

const saveFeedback = async (databaseId, variantId, submittedFeedback) => {
  const period = new Date(moment().format('YYYY-MM-DD'));
  const variant = new ObjectId(variantId);

  const { _id: campaign } = await findCampaignIdByVariant(databaseId, variantId);

  mongoHelperLog('saveFeedback/stat', databaseId);
  const stat = await dbConnection
    .collection('user_statistics')
    .findOne({ databaseId, period, variant, campaign });

  const clearStat: {
    period: Date;
    campaign: ObjectId;
    variant: any;
    impressions: number;
    conversions: number;
    impressionsDesktop: number;
    conversionsDesktop: number;
    impressionsMobile: number;
    conversionsMobile: number;
    databaseId: number;
  } = {
    period,
    campaign,
    variant,
    impressions: 0,
    conversions: 0,
    impressionsDesktop: 0,
    conversionsDesktop: 0,
    impressionsMobile: 0,
    conversionsMobile: 0,
    databaseId,
  };

  if (!stat) {
    mongoHelperLog('saveFeedback/!stat', databaseId);
    await dbConnection.collection('user_statistics').insertOne(clearStat);
  }

  const feedback = _get(stat, 'feedback', []);

  submittedFeedback.forEach((f) => {
    f.type = f.type || 'radio';
    let elementIndex = null;
    let hasType = false;

    feedback.forEach((fb, index) => {
      if (fb.inputId === f.inputId) {
        if (fb.type === f.type) {
          hasType = true;
        }

        if (fb.optionId === f.optionId) {
          elementIndex = index;
        }
      }
    });

    if (elementIndex !== null && hasType) {
      feedback[elementIndex].counter++;
      feedback[elementIndex].value = f.value;
    } else {
      feedback.push({
        inputId: f.inputId,
        optionId: f.optionId,
        type: f.type,
        value: f.value,
        counter: 1,
      });
    }
  });

  mongoHelperLog('saveFeedback/set_feedback', databaseId);
  return dbConnection
    .collection('user_statistics')
    .updateOne({ databaseId, period, variant, campaign }, { $set: { feedback } });
};

const setSiteInfo = async (databaseId, domain, platform, shopId) => {
  mongoHelperLog('setSiteInfo/account', databaseId);
  const account = await dbConnection
    .collection('master_accounts')
    .findOne({ databaseId }, { projection: { 'settings.domains': 1 } });

  if (account && account.settings && account.settings.domains) {
    const $set = {};

    try {
      account.settings.domains.forEach((d, idx) => {
        if (purifyDomain(d.domain) === domain) {
          $set[`settings.domains.${idx}.platform`] = platform;
          $set[`settings.domains.${idx}.shopId`] = shopId || null;
        }
      });
    } catch (e) {
      logger.error({ msg: '[DOMAINS] parse error', databaseId, domain, platform, shopId });
      throw e;
    }

    if (Object.keys($set).length > 0) {
      mongoHelperLog('setSiteInfo/set', databaseId);
      await dbConnection.collection('master_accounts').updateOne({ databaseId }, { $set });
    }
  }
};

const setKlaviyoInfo = async ({ accountId, domain, isKlaviyoDetected }) => {
  try {
    mongoHelperLog('setKlaviyoInfo', accountId);
    const account = await dbConnection
      .collection('master_accounts')
      .findOne({ databaseId: accountId }, { projection: { 'settings.domains': 1 } });

    if (account && account.settings && account.settings.domains) {
      const domainIndex = account.settings.domains.findIndex(
        (el) => purifyDomain(domain) === purifyDomain(el.domain),
      );

      if (domainIndex === -1) {
        logger.warn('KlaviyoInfo', 'Could not find domain in the database', accountId, domain);
        return { success: false };
      }
      mongoHelperLog('setKlaviyoInfo/isKlaviyoDetected', accountId);
      await dbConnection
        .collection('master_accounts')
        .updateOne(
          { databaseId: accountId },
          { $set: { [`settings.domains.${domainIndex}.isKlaviyoDetected`]: isKlaviyoDetected } },
        );
      return { success: true };
    }
    return { success: false };
  } catch (error) {
    logger.error({ msg: '[DOMAINS] parse error', databaseId: accountId, domain, error });
    return { success: false };
  }
};

const getCampaignByVariant = async (databaseId, variantId) => {
  mongoHelperLog('getCampaignByVariant', databaseId);
  return secondaryDbConnection
    .collection<{ _id: ObjectId }>(`${databaseId}_campaigns`)
    .findOne({ 'variants._id': new ObjectId(variantId) }, { projection: { _id: 1 } });
};

const getReservedCouponByClientId = async (databaseId, campaignId, clientId, operation) => {
  mongoHelperLog('getReservedCouponByClientId', databaseId);

  const dbCoupon = await dbConnection.collection('user_coupons').findOne(
    {
      campaign: new ObjectId(campaignId),
      'reserved.reserveBy': clientId,
      databaseId: parseInt(databaseId, 10),
    },
    { projection: { 'reserved.$': 1 } },
  );
  let coupon = null;
  if (dbCoupon) {
    coupon = dbCoupon.reserved[0].coupon;

    if (operation === 'get' && coupon) {
      mongoHelperLog('getReservedCouponByClientId/update_coupons', databaseId);
      await dbConnection.collection('user_coupons').updateOne(
        { campaign: new ObjectId(campaignId) },
        {
          $pull: { reserved: { reserveBy: clientId, coupon } },
          $inc: { shownCount: 1, shownCountAfterUpload: 1 },
        },
      );
      couponLogger.info(`Coupon ${coupon} served for ${clientId}`, databaseId, campaignId, {
        method: 'getReservedCouponByClientId',
      });
    }
  } else {
    couponLogger.info(`No reserved coupon for ${clientId}`, databaseId, campaignId, {
      method: 'getReservedCouponByClientId',
    });
  }
  return coupon;
};

const getAvailableCoupon = async (databaseId, campaignId, operation: string) => {
  mongoHelperLog('getAvailableCoupon', databaseId);

  const dbResult = await dbConnection
    .collection('user_coupons')
    .aggregate([
      {
        $match: {
          campaign: new ObjectId(campaignId),
          databaseId: parseInt(databaseId, 10),
        },
      },
      { $unwind: '$available' },
      { $sample: { size: 1 } },
      { $project: { coupon: '$available' } },
    ])
    .toArray();
  let coupon = null;
  if (dbResult.length > 0) {
    coupon = dbResult[0].coupon;

    if (operation === 'get' && coupon) {
      mongoHelperLog('getAvailableCoupon/update_coupons', databaseId);
      await dbConnection.collection('user_coupons').updateOne(
        {
          campaign: new ObjectId(campaignId),
          databaseId: parseInt(databaseId, 10),
        },
        { $pull: { available: coupon }, $inc: { shownCount: 1, shownCountAfterUpload: 1 } },
      );
      couponLogger.info(`Coupon ${coupon} is not available from now`, databaseId, campaignId, {
        method: 'getAvailableCoupon',
      });
    }
  } else {
    couponLogger.info(`No available coupon in campaign: ${campaignId} ${databaseId}`, {
      method: 'getAvailableCoupon',
    });
  }

  return coupon;
};

const calculateUsedCoupons = async (databaseId: string, campaignId: ObjectId) => {
  mongoHelperLog('calculateUsedCoupons', databaseId);

  const dbResult = await dbConnection
    .collection('user_coupons')
    .aggregate([
      {
        $match: {
          campaign: new ObjectId(campaignId),
          databaseId: parseInt(databaseId, 10),
        },
      },
      { $project: { available: 1, shownCountAfterUpload: 1, reserved: 1 } },
    ])
    .toArray();

  let available = 0;
  let shownCountAfterUpload = 0;
  let reserved = 0;

  if (dbResult.length > 0) {
    available = dbResult[0].available.length;
    shownCountAfterUpload = dbResult[0].shownCountAfterUpload;
    reserved = dbResult[0].reserved.length;
  }

  const total = available + shownCountAfterUpload + reserved;
  const percent = total === 0 ? 100 : 100 - Math.round((available / total) * 100);
  return {
    percent,
    available,
  };
};

const getReservedCoupons = async (databaseId, campaignId, freeable = true) => {
  const dateCamparison = freeable ? '$lte' : '$gte';
  mongoHelperLog('getReservedCoupons', databaseId);

  return dbConnection
    .collection('user_coupons')
    .aggregate([
      {
        $match: {
          campaign: new ObjectId(campaignId),
          databaseId: parseInt(databaseId, 10),
        },
      },
      { $unwind: '$reserved' },
      { $match: { 'reserved.reserveUntil': { [dateCamparison]: new Date() } } },
      { $project: { reserved: 1 } },
    ])
    .toArray();
};

const getFreeableCoupons = async (databaseId, campaignId) => {
  return getReservedCoupons(databaseId, campaignId);
};

const freeCoupons = async (databaseId, campaignId) => {
  const freeableCoupons = await getFreeableCoupons(databaseId, campaignId);
  if (freeableCoupons.length > 0) {
    const coupons = freeableCoupons.map((e) => e.reserved.coupon);
    const _ids = freeableCoupons.map((e) => e.reserved._id);
    mongoHelperLog('freeCoupons', databaseId);

    await dbConnection.collection('user_coupons').updateOne(
      {
        campaign: new ObjectId(campaignId),
        databaseId: parseInt(databaseId, 10),
      },
      {
        $push: { available: { $each: coupons } },
        $pull: { reserved: { _id: { $in: _ids } } },
      },
    );
    couponLogger.info(
      '###Coupon free coupons in %s campaign: %s, coupons: %o',
      databaseId,
      campaignId.toString(),
      coupons,
      { method: 'freeCoupons' },
    );
  }
};

const holdCoupon = async (databaseId, campaignId, coupon, reserveUntil, clientId) => {
  mongoHelperLog('holdCoupon', databaseId);

  await dbConnection.collection('user_coupons').updateOne(
    {
      campaign: new ObjectId(campaignId),
      databaseId: parseInt(databaseId, 10),
      reserved: { $elemMatch: { reserveBy: clientId, coupon } },
    },
    { $set: { 'reserved.$.reserveUntil': reserveUntil } },
  );
  couponLogger.info(`Holding coupon ${coupon} for ${clientId} until ${reserveUntil}`, databaseId, {
    method: 'holdCoupon',
  });
};

const lockCoupon = async (databaseId, campaignId, coupon, reserveUntil, clientId) => {
  mongoHelperLog('lockCoupon/couponCollection', databaseId);

  const couponCollection = dbConnection.collection('user_coupons');

  mongoHelperLog('lockCoupon/couponCollection.update', databaseId);
  await Promise.all([
    couponCollection.updateOne(
      {
        campaign: new ObjectId(campaignId),
        databaseId: parseInt(databaseId, 10),
      },
      { $pull: { available: coupon } },
    ),
    couponCollection.updateOne(
      {
        campaign: new ObjectId(campaignId),
        databaseId: parseInt(databaseId, 10),
      },
      {
        $push: {
          reserved: {
            _id: new ObjectId(),
            coupon,
            reserveUntil,
            reserveBy: clientId,
          },
        },
      },
    ),
  ]);

  couponLogger.info(`Lock coupon ${coupon} for ${clientId} until ${reserveUntil}`, databaseId, {
    method: 'lockCoupon',
  });
};

const unlockCoupon = async (databaseId, campaignId, coupon, clientId) => {
  mongoHelperLog('unlockCoupon', databaseId);

  await dbConnection.collection('user_coupons').updateOne(
    {
      campaign: new ObjectId(campaignId),
      databaseId: parseInt(databaseId, 10),
    },
    {
      $addToSet: { available: coupon },
      $pull: { reserved: { coupon, reserveBy: clientId } },
    },
  );

  couponLogger.info(`Unlock coupon ${coupon} for ${clientId}`, databaseId, { method: 'pon = ' });
};

const getFields = async (databaseId) => {
  mongoHelperLog('getFields', databaseId);
  return dbConnection
    .collection('user_fields')
    .find({ databaseId }, { projection: { customId: 1, name: 1, type: 1 } })
    .toArray();
};

const getWhiteLabelSettingsForAccount = async (databaseId: number) => {
  mongoHelperLog('getWhiteLabelSettingsForAccount', databaseId);
  return secondaryDbConnection
    .collection('master_accounts')
    .aggregate([
      { $match: { databaseId, type: 'sub' } },
      {
        $lookup: {
          from: 'master_accounts',
          localField: '_id',
          foreignField: 'subAccounts',
          as: 'owner',
        },
      },
      { $project: { owner: { $arrayElemAt: ['$owner', 0] } } },
      { $project: { whiteLabel: '$owner.settings.whiteLabel' } },
    ])
    .toArray();
};

const upsertClientCookie = async (databaseId: number, uuid: string, cookie: object) => {
  if (!databaseId || !uuid) {
    throw new Error('databaseId and uuid is required.');
  }
  logger.info('upsertClientCookie', databaseId, uuid);
  mongoHelperLog('upsertClientCookie', databaseId);
  return dbConnection.collection(`master_cookies`).updateOne(
    { databaseId, uuid },
    {
      $set: { cookie, updatedAt: new Date() },
      $setOnInsert: { createdAt: new Date() },
    },
    { upsert: true },
  );
};

const saveOAuthToken = async (query, update) => {
  mongoHelperLog('saveOAuthToken');
  return dbConnection.collection('master_accounts').updateOne(query, { $set: update });
};

const getCampaignVariantFields = async (
  databaseId: number,
  campaignId: ObjectId,
): Promise<CampaignVariantFields> => {
  mongoHelperLog('getCampaignVariantFields', databaseId);
  const intDatabaseId = typeof databaseId === 'string' ? parseInt(databaseId, 10) : databaseId;
  const account = await dbConnection.collection('master_accounts').findOne(
    {
      databaseId: intDatabaseId,
    },
    { projection: { features: true } },
  );
  if (account?.features?.includes(FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
    const campaign = await dbConnection
      .collection(`${databaseId}_campaigns`)
      .findOne({ _id: campaignId }, { projection: { 'variants._id': 1, 'variants.status': 1 } });
    const variantIds = campaign?.variants
      .filter((variant) => variant.status === 'active')
      .map((variant) => variant?._id);
    const variantTemplates = await dbConnection
      .collection('user_variant_templates')
      .find(
        {
          databaseId: intDatabaseId,
          variantId: { $in: variantIds },
        },
        { projection: { _id: 1, variantId: 1, 'template.inputs': 1 } },
      )
      .toArray();

    return campaign?.variants
      .filter((variant) => variant.status === 'active')
      .map((variant) => {
        const variantTemplate = variantTemplates.find(
          (variantTemplate) => `${variantTemplate.variantId}` === `${variant._id}`,
        );
        return {
          _id: variant._id,
          status: variant.status,
          fields: variantTemplate?.template?.inputs ?? [],
        };
      });
  }
  return dbConnection
    .collection(`${databaseId}_campaigns`)
    .aggregate<VariantFields>([
      { $match: { _id: campaignId } },
      {
        $project: {
          'variants._id': 1,
          'variants.status': 1,
          'variants.template.inputs': 1,
        },
      },
      { $unwind: '$variants' },
      {
        $project: {
          _id: '$variants._id',
          status: '$variants.status',
          fields: '$variants.template.inputs',
        },
      },
    ])
    .toArray();
};

const needLogFirstConversion = async (databaseId: number): Promise<boolean> => {
  const hasFirstConvFlag = await redisAdapter.get(cacheKeyFirstConversion(databaseId));

  if (hasFirstConvFlag) return false;

  mongoHelperLog('needLogFirstConversion/hasFirstConversionDate', databaseId);
  const hasFirstConversionDate = await dbConnection
    .collection('master_accounts')
    .findOne({ databaseId }, { projection: { 'profile.firstConversion': 1 } });

  if (
    hasFirstConversionDate &&
    hasFirstConversionDate.profile &&
    hasFirstConversionDate.profile.firstConversion
  ) {
    return false;
  }

  mongoHelperLog('needLogFirstConversion/hasLoggedConversion', databaseId);
  const hasLoggedConversion = await dbConnection
    .collection('user_statistics')
    .findOne({ databaseId, conversions: { $gt: 0 } });

  return !hasLoggedConversion;
};

const logFirstConversion = async (databaseId: number, date: Date) => {
  mongoHelperLog('logFirstConversion', databaseId);
  return dbConnection
    .collection('master_accounts')
    .updateOne({ databaseId }, { $set: { 'profile.firstConversion': date } });
};

const getProfileValue = async (databaseId: number, key: string): Promise<any> => {
  mongoHelperLog('getProfileValue', databaseId);
  const projectionValue = `profile.${key}`;
  const result = await dbConnection
    .collection('master_accounts')
    .findOne({ databaseId }, { projection: { [projectionValue]: 1 } });
  return result && result.profile && result.profile[key];
};

const setProfileValue = async (databaseId: number, key: string, value: any) => {
  mongoHelperLog('setProfileValue', databaseId);
  return dbConnection
    .collection('master_accounts')
    .updateOne({ databaseId }, { $set: { [`profile.${key}`]: value } });
};

const getConversionCount = async (databaseId: number): Promise<number> => {
  mongoHelperLog('getConversionCount', databaseId);
  return dbConnection.collection(`user_subscribers`).countDocuments({ databaseId });
};

const getCheckoutAmountSummary = async (databaseId: number): Promise<number> => {
  mongoHelperLog('getCheckoutSum', databaseId);
  const checkouts = await dbConnection
    .collection('master_checkouts')
    .find({ databaseId }, { projection: { totalPrice: 1 } })
    .toArray();
  return checkouts.reduce((sum, checkout) => sum + checkout.totalPrice, 0);
};

const getCurrentLeadsCount = async (databaseId: number): Promise<number> => {
  mongoHelperLog('getCurrentLeadsCount', databaseId);
  return dbConnection
    .collection('user_subscribers')
    .countDocuments({ databaseId, canAppearOnLeadsPage: true });
};

const saveProfileData = async (
  data: AnyBulkWriteOperation<Document> | AnyBulkWriteOperation<Document>[],
  databaseId?: number,
) => {
  mongoHelperLog('saveProfileData/collection', databaseId);
  const collection = dbConnection.collection('master_accounts');

  if (Array.isArray(data)) {
    mongoHelperLog('saveProfileData/bulkWrite_data', databaseId);
    return collection.bulkWrite(data);
  }
  if (databaseId && data) {
    const $set = {};

    for (const [key, value] of Object.entries(data)) {
      $set[`profile.${key}`] = value;
    }

    mongoHelperLog('saveProfileData/updateOne', databaseId);
    return collection.updateOne({ databaseId }, { $set });
  }

  return false;
};

const getAccountsWithPackageInfo = async () => {
  const utc = moment().utc();
  const query = {
    'limits.pageViews': { $exists: true },
    'limits.maxPageViews': { $exists: true },
    'billing.dateExpires': { $gte: utc.toDate() },
    'billing.package': { $in: FREEMIUM_PACKAGES },
    'settings.domains.v3LastRequestDate': { $gte: utc.clone().subtract(30, 'days').toDate() },
    createdAt: { $lte: utc.clone().subtract(3, 'days').toDate() },
    type: 'normal',
  };

  mongoHelperLog('getAccountsWithPackageInfo');
  const accounts = await dbConnection
    .collection('master_accounts')
    .find(query)
    .project({
      databaseId: 1,
      limits: 1,
      billing: 1,
      createdAt: 1,
      features: 1,
      'settings.domains': 1,
      'settings.shops': 1,
    })
    .toArray();

  return accounts;
};

const getAccountsWithFeatureFlag = (
  ...flags: string[]
): AsyncIterable<{ _id: ObjectId; databaseId: number }> => {
  if (flags.length === 0) {
    // @ts-ignore
    return Readable.from([]) as any;
  }

  mongoHelperLog('getAccountsWithFeatureFlag');
  return dbConnection
    .collection('master_accounts')
    .find(
      { features: flags.length === 1 ? flags[0] : { $in: flags } },
      { projection: { databaseId: 1 } },
    ) as any;
};

const upsertAccountCookieVisit = (
  databaseId: number,
  cookieId: string,
  visit: {
    firstVisitAt: Date;
    lastVisitAt: Date | null;
    userAgent: string | {} | null;
  },
) => {
  mongoHelperLog('upsertAccountCookieVisit');
  return dbConnection.collection('master_cookie_visits').updateOne(
    { databaseId, cookieId },
    {
      $set: {
        databaseId,
        cookieId,
        userAgent: visit.userAgent,
        firstVisitAt: visit.firstVisitAt,
        lastVisitAt: visit.lastVisitAt,
        updatedAt: new Date(),
      },
      $setOnInsert: {
        _id: new ObjectId(),
        createdAt: new Date(),
      },
    },
    { upsert: true },
  );
};

const writeStatisticsToDb = async () => {
  const statistics = await redisAdapter.getStatistics();
  await redisAdapter.clearStatistics();
  if (statistics) {
    for (const hashKey in statistics) {
      const [
        key,
        accountId,
        campaignId,
        variantId,
        device,
        period,
        experimentGroupId,
        rawIsControlVariant,
      ] = hashKey.split(':');
      const periodDate = new Date(period);
      const variant = new ObjectId(variantId);
      const campaign = new ObjectId(campaignId);
      const incrBy = parseInt(statistics[hashKey], 10);
      const isControlVariant = rawIsControlVariant === 'true';
      const oppositeKey = key === 'impressions' ? 'conversions' : 'impressions';
      const controlVariantKey = `controlVariant${key.charAt(0).toUpperCase() + key.slice(1)}`;

      const typedDevice = `${key}${device.charAt(0).toUpperCase()}${device.substr(1)}`;
      const oppositeDevice = device === Device.Desktop ? Device.Mobile : Device.Desktop;
      const typedOppositeDevice = `${key}${oppositeDevice
        .charAt(0)
        .toUpperCase()}${oppositeDevice.substr(1)}`;

      // update statistics
      mongoHelperLog('writeStatisticsToDb/update_statistics');
      const { value } = await dbConnection.collection('user_statistics').findOneAndUpdate(
        {
          databaseId: Number(accountId),
          period: periodDate,
          variant,
          campaign,
          ...(Boolean(experimentGroupId) && { experimentGroupId }),
        },
        {
          $inc: { [key]: incrBy, [typedDevice]: incrBy },
          $setOnInsert: {
            period: periodDate,
            variant,
            campaign,
            [oppositeKey]: 0,
            [typedOppositeDevice]: 0,
            isControlVariant,
          },
        },
        { upsert: true, returnDocument: 'after' },
      );

      // update heap
      if (value) {
        heapAnalytics.trackStatistics(value[key], key, parseInt(accountId, 10));
      }

      // update campaigns
      mongoHelperLog('writeStatisticsToDb/update_campaigns');
      dbConnection.collection(`${accountId}_campaigns`).bulkWrite([
        {
          updateOne: {
            filter: { _id: campaign },
            update: {
              $inc: {
                [isControlVariant ? controlVariantKey : key]: incrBy,
              },
            },
          },
        },
        {
          updateOne: {
            filter: { 'variants._id': variant },
            update: { $inc: { [`variants.$.${key}`]: incrBy } },
          },
        },
      ]);

      if (key === 'conversions') {
        mongoHelperLog('writeStatisticsToDb/increment_totalConversions');
        dbConnection
          .collection('master_accounts')
          .findOneAndUpdate(
            { databaseId: parseInt(accountId, 10) },
            { $inc: { 'campaignInfos.totalConversions': incrBy } },
          );
      }
    }
  }
};

const getShopifyAccessToken = async (shopName: string, databaseId: number) => {
  mongoHelperLog('getShopifyAccessToken', databaseId);
  const [account] = await dbConnection
    .collection(ACCOUNTS_COLLECTION_NAME)
    .find(
      {
        databaseId,
        'settings.shops': { $elemMatch: { myshopify_domain: shopName, active: 1 } },
      },
      {
        projection: {
          databaseId: 1,
          'settings.shops.$': 1,
        },
      },
    )
    .toArray();

  if (!account || !account.settings || !account.settings.shops) {
    throw new Error(`Could not get access token for ${shopName}`);
  }

  const shops = account.settings.shops;

  const { oauth_token: oauthToken } = shops[0];

  return oauthToken;
};

const getCouponConfig = async (
  databaseId: number,
  variantId: string,
  elementId: string,
): Promise<CouponConfig | null> => {
  mongoHelperLog('getCouponConfig', databaseId);
  return dbConnection
    .collection<CouponConfig>(COUPON_CONFIG_COLLECTION_NAME)
    .findOne({ databaseId, variantId, elementId });
};

const SPA_ACCOUNT_DOMAINS_FROM_ENV = process.env.SPA_ACCOUNTS_DOMAINS || '';
const SPA_ACCOUNT_DOMAINS_CACHE_KEY = 'frontend:spa-accounts-and-domains';

const _getSpaAccountsAndDomainsFromDb = async () => {
  const saved = await secondaryDbConnection
    .collection(CONFIG_COLLECTION_NAME)
    .findOne({ key: 'SPA_DOMAINS' });

  const result = _get(
    saved,
    'value',
    SPA_ACCOUNT_DOMAINS_FROM_ENV.split(',').map((item) => {
      const [accountId, domain] = item.split(':');
      return { accountId, domain };
    }),
  );

  return result;
};

const getSpaAccountsAndDomains = async () => {
  const cached = await redisAdapter.get(SPA_ACCOUNT_DOMAINS_CACHE_KEY);

  if (cached) return JSON.parse(cached);

  const result = await _getSpaAccountsAndDomainsFromDb();
  await redisAdapter.set(SPA_ACCOUNT_DOMAINS_CACHE_KEY, JSON.stringify(result));

  return result;
};

const upsertUserAlert = async ({ databaseId, links, type, context }: UserAlertUpsertPayload) => {
  mongoHelperLog('upsertUserAlert', { databaseId, type, links, context });
  return dbConnection.collection(USER_ALERTS_COLLECTION_NAME).updateOne(
    {
      databaseId,
      type,
      links: {
        $all: links.map((link) => ({ $elemMatch: { id: { $eq: link.id } } })),
      },
    },
    {
      $set: {
        expiresAt: moment.utc().add(7, 'day').toDate(),
        context,
        triggeredAt: new Date(),
      },
      $setOnInsert: {
        databaseId,
        type,
        links,
        createdAt: new Date(),
      },
    },
    { upsert: true },
  );
};

const _getAllDomainsForAccount = async (accountId: number): Promise<IDomain[]> => {
  mongoHelperLog('getAllDomainsForAccount', accountId);
  const allDomains = await dbConnection
    .collection(ACCOUNTS_COLLECTION_NAME)
    .findOne({ databaseId: accountId }, { projection: { 'settings.domains': 1 } });

  return allDomains?.settings?.domains || [];
};

const getRunningExperiments = async ({
  accountId,
  domain,
}: {
  accountId: number;
  domain?: string;
}) => {
  let domainIds = [];

  const domains = await getAccountDomains(accountId);
  let cacheKey;
  // domain is only available if experiments are initialized from preload
  // with the new insert code experiments are cached for all domains
  if (domain) {
    domainIds = domains
      .filter((d) => purifyDomain(d.domain) === purifyDomain(domain))
      .map((domain) => domain._id);

    cacheKey = `frontend:${accountId}:${domainIds.join(',')}:experiments`;
  } else {
    cacheKey = `frontend:${accountId}:experiments`;
  }

  const cachedExperiments = await redisAdapter.get(cacheKey);

  if (cachedExperiments) {
    return JSON.parse(cachedExperiments);
  }
  mongoHelperLog('getRunningExperiments', accountId);
  const experiments = await dbConnection
    .collection(USER_EXPERIMENT_COLLECTION_NAME)
    .find(
      {
        databaseId: accountId,
        ...(domainIds.length && { domainId: { $in: domainIds } }),
        status: 'RUNNING',
      },
      { projection: { name: 1, groups: 1, domainId: 1 } },
    )
    .toArray();

  const mappedExperiments = experiments.map((experiment) => {
    return {
      ...experiment,
      domainId: experiment.domainId.toString(),
      groups: experiment.groups.map((group) => {
        return {
          ...group,
          _id: group._id.toString(),
        };
      }),
      _id: experiment._id.toString(),
      domain: domains.find((domain) => domain._id.toString() === experiment.domainId.toString())
        ?.domain,
    };
  });

  await redisAdapter.setex(cacheKey, JSON.stringify(mappedExperiments), 600);

  return mappedExperiments;
};

const updateSmartAbTest = async ({
  databaseId,
  campaignId,
  changeId,
  running,
  phasedOut,
  currentPairStart,
}: {
  databaseId: number;
  campaignId: number;
  changeId: string;
  running: string[];
  phasedOut: string[];
  currentPairStart: Date;
}) => {
  mongoHelperLog('updateDCSmartAbTest', { databaseId, campaignId, changeId });
  return dbConnection.collection(DYNAMIC_CONTENT_COLLECTION_NAME).updateOne(
    { databaseId, campaignId, 'changes.id': changeId },
    {
      $set: {
        'changes.$.running': running,
        'changes.$.phased_out': phasedOut,
        'changes.$.current_pair_start': currentPairStart,
      },
    },
  );
};

const getPPOPrompts = (databaseId) => {
  databaseId = parseInt(databaseId, 10);
  mongoHelperLog('getPPOPrompts', databaseId);

  return dbConnection.collection('user_ppo_prompts').aggregate([
    { $match: { databaseId } },
    { $unwind: '$variableNames' },
    {
      $project: { variableName: '$variableNames' },
    },
  ]);
};

const getTrackParams = async (databaseId) => {
  mongoHelperLog('getTrackParams', databaseId);
  const params = await dbConnection
    .collection(USER_SMART_PERSONALIZATION_COLLECTION_NAME)
    .find({ databaseId })
    .toArray();

  const result = {};
  const account = await dbConnection
    .collection(ACCOUNTS_COLLECTION_NAME)
    .findOne({ databaseId }, { projection: { 'settings.domains': 1 } });

  if (!account) return result;

  for (const param of params) {
    const domain = account.settings.domains.find(
      ({ _id }) => _id.toString() === param.domainId.toString(),
    );
    if (!domain) continue;

    const domainString = domain.domain;

    if (!result[domainString]) {
      result[domainString] = {
        domainId: domain._id,
        params: [],
      };
    }

    result[domainString].params.push(param.parameter);
  }

  return result;
};

const getSmartPersonalizationChangesByDomain = async (databaseId, domainId) => {
  const SP_CHANGE_TYPE = 'smart-personalization';
  mongoHelperLog('getSmartPersonalizationChangesByDomain', databaseId);
  const campaigns = await dbConnection
    .collection(`${databaseId}_campaigns`)
    .find({
      domainId: new ObjectId(domainId),
      type: 'dynamic_content',
      status: 'active',
    })
    .toArray();

  if (!campaigns.length) return [];

  const campaignIds = campaigns.map(({ id }) => id);

  const dynamicContents = await dbConnection
    .collection(DYNAMIC_CONTENT_COLLECTION_NAME)
    .find({
      databaseId: parseInt(databaseId, 10),
      campaignId: { $in: campaignIds },
      'changes.type': SP_CHANGE_TYPE,
    })
    .toArray();

  const result: any[] = [];
  dynamicContents.forEach(({ changes }) => {
    changes.forEach((change) => {
      if (change.type === SP_CHANGE_TYPE) {
        result.push(change);
      }
    });
  });

  return result;
};

const getVariantIdsByCampaign = async (databaseId, campaignId) => {
  mongoHelperLog('getVariantIdsByCampaign', databaseId);
  const campaign = await dbConnection
    .collection(`${databaseId}_campaigns`)
    .findOne({ id: campaignId }, { projection: { 'variants._id': 1 } });

  return campaign?.variants.map(({ _id }) => _id);
};

const LAST_ACCOUNT_DATA_UPLOADS_COL = 'last_account_data_uploads';

const lastAccountLimitUpload = async ({ databaseId, remaining }) => {
  mongoHelperLog('lastAccountLimitUpload', databaseId);
  return dbConnection
    .collection(LAST_ACCOUNT_DATA_UPLOADS_COL)
    .updateOne(
      { databaseId },
      { $set: { databaseId, limit: new Date(), limitRemaining: remaining, updatedAt: new Date() } },
      { upsert: true },
    );
};

const dismissRelatedAlert = async ({
  databaseId,
  integrationId,
  oldIntegrationId,
  variantId,
  errorCode,
}) => {
  mongoHelperLog('dismissRelatedAlert', databaseId);
  try {
    const { _id: campaignId } = (await getCampaignByVariant(databaseId, variantId)) || {};

    integrationId = oldIntegrationId || integrationId;

    const hasMoreFailedSubscription = await dbConnection
      .collection(SUBSCRIPTION_FAILED_COLLECTION_NAME)
      .findOne({
        databaseId,
        integrationId,
        variantId,
        errorCode,
      });

    if (hasMoreFailedSubscription) return;

    await dbConnection.collection(USER_ALERTS_COLLECTION_NAME).deleteMany({
      databaseId,
      type: errorCode || 'IntegrationError',
      links: {
        $all: [
          { $elemMatch: { id: { $eq: campaignId?.toString() }, type: { $eq: 'Campaign' } } },
          { $elemMatch: { id: { $eq: integrationId.toString() }, type: { $eq: 'Integration' } } },
        ],
      },
    });
  } catch (e: any) {
    logger.error({
      message: 'Error during alert dismiss check',
      errorMessage: e.message,
      e,
    });
  }
};

const getActiveSplitURLABTests = (databaseId) => {
  mongoHelperLog('getActiveSplitURLABTests', databaseId);
  return dbConnection
    .collection(USER_SPLIT_URL_AB_TEST_COLLECTION_NAME)
    .find({
      databaseId: parseInt(databaseId, 10),
      status: 'RUNNING',
    })
    .toArray();
};

export {
  LAST_ACCOUNT_DATA_UPLOADS_COL,
  getAccountWithSubAccounts,
  lastAccountLimitUpload,
  accountHasRemainingVisitors,
  connectToDb,
  closeMemoryServer,
  deleteFromFailedSubscription,
  checkHasFailedSubscription,
  findCampaignByVariant,
  findCampaignIdByVariant,
  findActiveCampaignIdByVariant,
  getVariantStat,
  getAccountSettings,
  getAllCampaignsByAccountId,
  getAllCampaignTypesByAccountId,
  getAllCampaignTypesByAccountIdSecDB,
  getAllActiveDomainsForAccount,
  getCampaignData,
  getCampaignVariantFields,
  getFailedSubscriptions,
  getFailedSubscriptionsWithCustomDate,
  getFailedSubscriptionsForResync,
  getIntegrationSettings,
  getIntegrationSettingsForCampaign,
  getVariant,
  getWhiteLabelSettingsForAccount,
  getLogin,
  getShopData,
  getShopSettingByDomain,
  getShopSettingByShopifyId,
  increaseConversions,
  increaseImpressions,
  incrementTryCountOfFailedSubscription,
  insertShopifyCheckout,
  getSubscriber,
  insertSubscriber,
  ObjectId,
  saveFailedSubscription,
  updateFailedSubscription,
  selectActiveVariantForCampaign,
  setCampaignStatusInactive,
  setVariantStatusActive,
  updateLastRequestDate,
  updateV3LastRequestDate,
  upsertSubscriber,
  updateSubscriberStatus,
  writePageViewsAndUsedVisitorToDb,
  saveFeedback,
  setSiteInfo,
  getCampaignByVariant,
  getReservedCouponByClientId,
  getAvailableCoupon,
  calculateUsedCoupons,
  freeCoupons,
  holdCoupon,
  lockCoupon,
  unlockCoupon,
  upsertClientCookie,
  getZapierHooks,
  getFields,
  saveOAuthToken,
  getFailedSubscriptionById,
  needLogFirstConversion,
  logFirstConversion,
  upsertAccountCookieVisit,
  getAccountsWithFeatureFlag,
  saveProfileData,
  getAccountsWithPackageInfo,
  writeStatisticsToDb,
  getShopifyAccessToken,
  getCouponConfig,
  getFailedSubscriptionsByDatabaseId,
  loadVariantToCache,
  getSubscribersByCampaignWithCustomDate,
  getProfileValue,
  setProfileValue,
  getConversionCount,
  getCheckoutAmountSummary,
  getCurrentLeadsCount,
  getSpaAccountsAndDomains,
  setKlaviyoInfo,
  parseCampaignForPreview,
  upsertUserAlert,
  getActiveDynamicCampaigns,
  getDynamicContentCampaignByVariant,
  getRunningExperiments,
  getCampaignExperiencesMap,
  getProcessedActiveVariants,
  updateSmartAbTest,
  getPPOPrompts,
  getTrackParams,
  getSmartPersonalizationChangesByDomain,
  getVariantIdsByCampaign,
  getAllActiveCampaignsByAccountId,
  getCampaignForPreview,
  dismissRelatedAlert,
  getAccountFeatures,
  getVariantTemplate,
  getActiveSplitURLABTests,
};
