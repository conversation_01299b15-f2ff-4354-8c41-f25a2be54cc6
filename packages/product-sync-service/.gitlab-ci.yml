build product-sync:
  extends: .build
  variables:
    PACKAGE_NAME: product-sync-service

test product-sync:
  needs: [build product-sync]
  extends: .test
  variables:
    PACKAGE_NAME: product-sync-service
  artifacts:
    when: always
    reports:
      coverage_report:
        coverage_format: cobertura
        path: packages/product-sync-service/coverage/cobertura-coverage.xml
      junit: packages/om-backend/coverage/junit.xml
    paths:
      - packages/product-sync-service/coverage
