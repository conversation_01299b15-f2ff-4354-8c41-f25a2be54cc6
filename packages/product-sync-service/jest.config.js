module.exports = {
  verbose: true,
  preset: 'ts-jest',
  testEnvironment: 'node',
  reporters: [
    'default',
    ['@om/jest-num-failed', { outputDirectory: './coverage' }],
    ['jest-junit', { outputDirectory: './coverage' }],
  ],
  moduleFileExtensions: ['ts', 'js'],
  testMatch: ['**/*.e2e-spec.ts', '**/*.test.ts'],
  rootDir: '.',
  moduleNameMapper: {
    // Handle module aliases (if you have them in tsconfig.json)
    // Example: '^@src/(.*)$': '<rootDir>/src/$1'
  },
  coverageDirectory: './coverage',
  testPathIgnorePatterns: ['./node_modules/'],
  collectCoverageFrom: ['./**/*.ts', '!**/node_modules/**'],
  coverageReporters: [
    'html',
    'text',
    'cobertura',
    'lcov',
    ['text-summary', { file: 'coverage-summary.txt' }],
  ],
};
