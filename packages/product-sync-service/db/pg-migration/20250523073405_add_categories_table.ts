import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('categories', (table) => {
    table.increments('id').primary();

    table.bigInteger('databaseId').notNullable();
    table.text('providerServiceId').notNullable();

    table.bigInteger('categoryId').notNullable();
    table.string('name').notNullable();
    table.string('slug').notNullable();
    table.text('parent').nullable();

    table.text('locale').notNullable();
    table.timestamp('timestamp').notNullable();

    table.timestamp('createdAt').defaultTo(knex.fn.now());
    table.timestamp('updatedAt').defaultTo(knex.fn.now());

    table.index(['categoryId']);
    table.index(['databaseId']);
    table.index(['providerServiceId']);

    table.unique(['categoryId', 'providerServiceId', 'databaseId'], {
      indexName: 'categories_unique_idx',
    });
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('categories');
}
