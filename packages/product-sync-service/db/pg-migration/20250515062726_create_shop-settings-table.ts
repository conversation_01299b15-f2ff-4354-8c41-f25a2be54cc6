import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('shop_settings', (table) => {
    // Primary key with auto-increment
    table.increments('id').primary();

    // Required fields
    table.boolean('default').notNullable().defaultTo(true);
    table.bigInteger('databaseId').notNullable();
    table.text('providerServiceId').notNullable();
    table.text('locale').notNullable();
    table.text('currency').notNullable();

    // Unique constraint for databaseId and providerServiceId
    table.unique(
      ['databaseId', 'providerServiceId', 'default'],
      'shop_settings_unique_constraint',
    );

    // Date fields
    table.timestamp('createdAt').defaultTo(knex.fn.now());
    table.timestamp('updatedAt').defaultTo(knex.fn.now());
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('shop_settings');
}
