import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('products', (table) => {
    // Primary key with auto-increment
    table.increments('id').primary();

    // Required fields
    table.timestamp('timestamp').notNullable();
    table.bigInteger('databaseId').notNullable();
    table.text('providerServiceId').notNullable();
    table.text('status').notNullable();
    table.text('locale').notNullable();
    table.text('productId').notNullable();

    // Nullable fields
    table.text('name').nullable();
    table.float('price').nullable();
    table.float('originalPrice').nullable();
    table.text('currency').nullable();
    table.text('imageUrl').nullable();
    table.text('description').nullable();
    table.text('parentProductId').nullable();
    table.text('productUrl').nullable();
    table.boolean('inStock').defaultTo(false).nullable();
    table.jsonb('categories').nullable();
    table.text('sku').nullable();

    // Date fields
    table.timestamp('createdAt').defaultTo(knex.fn.now());
    table.timestamp('updatedAt').defaultTo(knex.fn.now());

    // Indexes
    table.index(['productId']);
    table.index(['databaseId']);
    table.index(['providerServiceId']);
    table.index(['sku']);
    table.index(['name']);
    table.unique(
      ['parentProductId', 'providerServiceId', 'productId', 'databaseId'],
      {
        indexName: 'products_unique_idx',
      },
    );
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('products');
}
