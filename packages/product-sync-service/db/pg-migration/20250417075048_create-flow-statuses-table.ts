import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('flow_statuses', (table) => {
    // Primary key with auto-increment
    table.increments('id').primary();

    // Required fields
    table.text('platform').notNullable();
    table.text('status').notNullable();
    table.text('providerServiceId').notNullable();
    table.bigInteger('databaseId').notNullable();
    table.text('jobId').notNullable();

    // Nullable fields
    table.text('jobName').nullable();
    table.text('parentJobId').nullable();
    table.text('errorMessage').nullable();

    // Date fields
    table.timestamp('createdAt').defaultTo(knex.fn.now());
    table.timestamp('updatedAt').defaultTo(knex.fn.now());

    // Indexes
    table.index(['providerServiceId']);
    table.index(['databaseId']);
    table.index(['jobId']);

    // Unique constraint
    table.unique(
      ['providerServiceId', 'jobName'],
      { indexName: 'job_unique_constraint' }
    );
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('flow_statuses');
}
