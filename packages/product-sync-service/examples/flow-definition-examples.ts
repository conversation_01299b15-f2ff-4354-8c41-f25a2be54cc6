/**
 * Examples of how to use the new flat flow definition format
 */

import { FlowJobDefinition } from '../src/scheduler/Queue';

/**
 * Example 1: Default product sync flow
 * This is the same flow that's used by default in the application
 *
 * The flow is defined in the order of execution
 */
const defaultProductSyncFlow: FlowJobDefinition[] = [
  {
    name: 'fetchAndUpload',
    opts: { attempts: 3, backoff: { type: 'exponential', delay: 5000 } },
    nextJob: 'moveToBigQuery',
  },
  { name: 'moveToBigQuery', nextJob: 'transformToUnified' },
  { name: 'transformToUnified', nextJob: 'insertIntoPostgres' },
  { name: 'insertIntoPostgres' },
];

/**
 * Example 2: A flow with multiple dependencies
 * In this example, 'processData' and 'fetchMetadata' both feed into 'generateReport'
 */
const multiDependencyFlow: FlowJobDefinition[] = [
  { name: 'fetchData', opts: { attempts: 2 }, nextJob: 'processData' },
  { name: 'processData', nextJob: 'generateReport' },
  { name: 'fetchMetadata', opts: { attempts: 2 }, nextJob: 'generateReport' },
  { name: 'generateReport', nextJob: 'sendNotification' },
  { name: 'sendNotification' },
];

/**
 * Example 3: A flow with parallel branches
 * In this example, 'processImages' and 'processText' can run in parallel
 * after 'fetchContent' completes
 */
const parallelBranchesFlow: FlowJobDefinition[] = [
  { name: 'fetchContent', nextJob: ['processImages', 'processText'] },
  { name: 'processImages', nextJob: 'combineResults' },
  { name: 'processText', nextJob: 'combineResults' },
  { name: 'combineResults' },
];

/**
 * Example 4: A complex flow with multiple levels and branches
 */
const complexFlow: FlowJobDefinition[] = [
  { name: 'start', nextJob: ['fetchUserData', 'fetchProductData'] },
  { name: 'fetchUserData', nextJob: 'processUserData' },
  { name: 'fetchProductData', nextJob: 'processProductData' },
  { name: 'processUserData', nextJob: 'generateRecommendations' },
  { name: 'processProductData', nextJob: 'generateRecommendations' },
  { name: 'generateRecommendations', nextJob: 'formatResults' },
  { name: 'formatResults', nextJob: ['saveToDatabase', 'sendEmail'] },
  { name: 'saveToDatabase', nextJob: 'logActivity' },
  { name: 'sendEmail', nextJob: 'logActivity' },
  { name: 'logActivity' },
];

export {
  defaultProductSyncFlow,
  multiDependencyFlow,
  parallelBranchesFlow,
  complexFlow,
};
