FROM node:22.12.0-slim as build
ARG BUILD_DIR=/app
WORKDIR ${BUILD_DIR}
# Install build dependencies for Redis compilation
RUN apt-get update && \
    apt-get install -y \
    build-essential \
    pkg-config \
    python3 \
    && rm -rf /var/lib/apt/lists/*
COPY package.json yarn.lock .yarnrc.yml ./
COPY .yarn .yarn
# Dependencies
COPY libraries/ ./libraries/
COPY packages/product-sync-service/package.json ./packages/product-sync-service/
ENV CI=true
RUN --mount=type=cache,target=/.yarn YARN_CACHE_FOLDER=/.yarn yarn workspaces focus product-sync-service
COPY packages/product-sync-service/ ./packages/product-sync-service/
RUN yarn workspace product-sync-service run build

FROM build as test
RUN apt update && apt install libcurl4-openssl-dev openssl --no-install-recommends -y
RUN yarn workspace product-sync-service test:ci

FROM scratch as export-test-results
ARG BUILD_DIR=/app
COPY --from=test ${BUILD_DIR}/packages/product-sync-service/coverage/. .

FROM node:22.12.0-slim
ARG BUILD_DIR=/app
RUN --mount=type=cache,sharing=locked,target=/var/cache/apt \
    --mount=type=cache,target=/var/lib/apt\
    apt update && apt install -y \
      wget
COPY --from=build ${BUILD_DIR} /app
WORKDIR /app/packages/product-sync-service
CMD [ "yarn", "run", "start" ]
