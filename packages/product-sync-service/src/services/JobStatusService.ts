import { Job } from 'bullmq';
import { logger } from '@om/logger';
import { JobStatusEnum } from '../enums/JobStatusEnum';
import { Knex } from 'knex';
import { JobStatus } from '../interfaces/JobStatus';
import { ProductSyncType } from '../interfaces/RestTypes';

export class JobStatusService {
  static async insertFlowStatus(
    knex: Knex,
    job: Job,
    status: JobStatusEnum,
    error: string | null = null,
  ): Promise<void> {
    try {
      await knex('flow_statuses')
        .insert({
          platform: job.data.platform,
          status,
          providerServiceId: job.data.providerServiceId,
          databaseId: job.data.databaseId,
          jobId: job.id,
          jobName: job.name,
          parentJobId: job.parent?.id,
          errorMessage: error,
          createdAt: knex.fn.now(),
          updatedAt: knex.fn.now(),
        })
        .onConflict(['jobName', 'providerServiceId'])
        .merge({
          status: knex.raw('EXCLUDED.status'),
          errorMessage: knex.raw('EXCLUDED."errorMessage"'),
          updatedAt: knex.fn.now(),
        });
    } catch (error: any) {
      logger.error({
        message: 'Error inserting flow status:',
        error: error?.message,
        stack: error?.stack,
      });
    }
  }

  static async setAllJobsToWaiting(
    knex: Knex,
    providerServiceId: string,
  ): Promise<void> {
    try {
      await knex('flow_statuses')
        .where({ providerServiceId })
        .update({
          status: JobStatusEnum.WAITING,
          updatedAt: knex.fn.now(),
        })
        .onConflict(['providerServiceId'])
        .merge({
          status: knex.raw('EXCLUDED.status'),
          updatedAt: knex.fn.now(),
        });
    } catch (error: any) {
      logger.error({
        message: 'Error setting all jobs to waiting:',
        error: error?.message,
        stack: error?.stack,
      });
      throw Error('Error setting all jobs to waiting');
    }
  }

  static async listJobStatuses(
    knex: Knex,
    providerServiceId: string,
  ): Promise<JobStatus[]> {
    try {
      return await knex('flow_statuses')
        .where({ providerServiceId })
        .orderBy('createdAt', 'desc');
    } catch (error: any) {
      logger.error({
        message: 'Error getting job status:',
        error: error?.message,
        stack: error?.stack,
      });
      throw Error('Error getting job status');
    }
  }

  static async getJobStatusesByShops(
    knex: Knex,
    shops: Array<Pick<ProductSyncType, 'databaseId' | 'providerServiceId'>>,
  ): Promise<Array<JobStatus>> {
    try {
      if (!shops.length) return [];

      return await knex('flow_statuses').where(function () {
        shops.forEach(({ databaseId, providerServiceId }) => {
          this.orWhere(function () {
            this.where({ databaseId, providerServiceId }).andWhere(
              'updatedAt',
              '=',
              knex('flow_statuses')
                .where({ databaseId, providerServiceId })
                .max('updatedAt'),
            );
          });
        });
      });
    } catch (error: any) {
      logger.error({
        message: 'Error getting job statuses by shops:',
        error: error?.message,
        stack: error?.stack,
      });
      throw Error('Error getting job statuses by shops');
    }
  }
}
