import { Knex } from 'knex';
import { ProductsSchemaType } from '../interfaces/RestTypes';

export class ProductService {
  constructor(private readonly _db: Knex) {}

  async getProductsByIds(
    productIds: string[],
    databaseId: number,
    providerServiceId: string,
    limit = 10,
  ): Promise<ProductsSchemaType[]> {
    return this._db('products')
      .whereIn('productId', productIds)
      .where('databaseId', databaseId)
      .where('providerServiceId', providerServiceId)
      .where('inStock', true)
      .limit(limit);
  }

  async getMostPopularProducts(
    databaseId: number,
    providerServiceId: string,
    limit = 10,
  ): Promise<ProductsSchemaType[]> {
    return this._db('products')
      .where('databaseId', databaseId)
      .where('providerServiceId', providerServiceId)
      .where('inStock', true)
      .orderBy('views', 'desc')
      .limit(limit);
  }
}
