// redis-client.ts
import Redis, { Redis as RedisType, RedisOptions } from 'ioredis';
import { logger } from '@om/logger';
import config from '../Config';
import { RedisMemoryServer } from 'redis-memory-server';

class RedisClient {
  private static instance: RedisClient;
  private client!: RedisType; // Using definite assignment assertion
  private isConnected = false;
  private static redisServer?: RedisMemoryServer;
  private static initializationPromise: Promise<void> | null = null;

  private constructor(redisConfig?: RedisOptions) {
    if (process.env.NODE_ENV === 'test') {
      // Initialize in-memory Redis server for testing
      RedisClient.initializationPromise = this.initializeMemoryServer()
        .then((config) => {
          this.setupRedisClient(config);
          logger.info({
            message: 'Using in-memory Redis server for testing',
            host: config.host,
            port: config.port,
          });
        })
        .catch(err => {
          logger.error({
            message: 'Failed to initialize in-memory Redis server',
            error: err,
          });
          throw err; // Re-throw to propagate the error
        });
    } else {
      // Use real Redis server for development and production
      const defaultConfig: RedisOptions = {
        host: config.REDIS_HOST,
        port: config.REDIS_PORT,
        password: config.REDIS_PASSWORD || undefined,
        maxRetriesPerRequest: null,
        enableReadyCheck: false,
        ...(config.REDIS_TLS
          ? {
              tls: {
                rejectUnauthorized: false,
                requestCert: true,
              },
            }
          : {}),
      };

      this.setupRedisClient({ ...defaultConfig, ...redisConfig });
      RedisClient.initializationPromise = Promise.resolve();
    }
  }

  private async initializeMemoryServer(): Promise<RedisOptions> {
    // Create Redis memory server if it doesn't exist
    if (!RedisClient.redisServer) {
      RedisClient.redisServer = new RedisMemoryServer();
    }

    // Get host and port from the memory server
    const host = await RedisClient.redisServer.getHost();
    const port = await RedisClient.redisServer.getPort();

    // Return Redis configuration for the memory server
    return {
      host,
      port,
      maxRetriesPerRequest: null,
      enableReadyCheck: false,
    };
  }

  private setupRedisClient(redisConfig: RedisOptions): void {
    this.client = new Redis(redisConfig);

    this.client.on('connect', () => {
      this.isConnected = true;
      logger.info({
        message: 'Redis connecting...',
        host: redisConfig.host,
        port: redisConfig.port,
      });
    });

    this.client.on('ready', () => {
      logger.info({
        message: 'Redis connected',
        host: redisConfig.host,
        port: redisConfig.port,
      });
    });

    this.client.on('error', (err: Error) => {
      logger.error({
        message: 'Redis error:',
        host: redisConfig.host,
        port: redisConfig.port,
        error: err,
      });
    });

    this.client.on('close', () => {
      this.isConnected = false;
      logger.info({
        message: 'Redis connection closed',
        host: redisConfig.host,
        port: redisConfig.port,
      });
    });

    this.client.on('reconnecting', (delay: number) => {
      logger.info({
        message: 'Redis reconnecting',
        host: redisConfig.host,
        port: redisConfig.port,
        delay,
      });
    });
  }

  public static async waitForInitialization(): Promise<void> {
    if (RedisClient.initializationPromise) {
      await RedisClient.initializationPromise;
    }
  }

  public static getInstance(config?: RedisOptions): RedisType {
    if (!RedisClient.instance) {
      RedisClient.instance = new RedisClient(config);
    }
    return RedisClient.instance.client;
  }

  public static async disconnect(): Promise<void> {
    if (RedisClient.instance) {
      await RedisClient.instance.client.quit();
      RedisClient.instance = null as unknown as RedisClient;

      // Stop the in-memory Redis server if it exists
      if (RedisClient.redisServer) {
        await RedisClient.redisServer.stop();
        RedisClient.redisServer = undefined;
      }
    }
  }

  public static get isConnected(): boolean {
    // Singleton Redis instance
    return RedisClient.instance?.isConnected || false;
  }
  // For BullMQ and other Redis operations
}

const redis = RedisClient.getInstance();

export { redis, RedisClient, RedisType };
