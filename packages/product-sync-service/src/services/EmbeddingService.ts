import config from '../Config';
import { BigQueryClient } from '../adapters/BigQuery';
import { logger } from '@om/logger';
import crypto from 'crypto';
import OpenAI from 'openai';
import { BigQuery, Table } from '@google-cloud/bigquery';

const DATASET_ID = config.GCP_BIGQUERY_DATASET;
const PRODUCTS_TABLE = config.GCP_BIGQUERY_PRODUCTS_TABLE;
const MAX_TOKENS = 2000;

interface EmbeddingRow {
  databaseId: number;
  productId: string;
  providerServiceId: string;
  locale: string;
  descriptionHash: string;
  description: string;
}

class EmbeddingService {
  static async storeEmbeddings(
    providerServiceId: string,
    locale: string,
    databaseId: number,
  ): Promise<void> {
    try {
      const bigQuery = new BigQuery();
      const table = bigQuery.dataset(DATASET_ID).table('product_embeddings');

      const bigQueryClient = new BigQueryClient();

      const query = `
        SELECT productId, description
        FROM \`${DATASET_ID}.${PRODUCTS_TABLE}\`
        WHERE databaseId = @databaseId
          AND providerServiceId = @providerServiceId
          AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
      `;

      const queryStream = bigQueryClient.runQueryStream({
        query,
        queryOptions: {
          params: {
            databaseId,
            providerServiceId,
          },
        },
      });

      let rows: EmbeddingRow[] = [];
      let calculatedTokens = 0;

      for await (const product of queryStream) {
        let description = product.description || '';

        const tokenCount = Math.ceil(description.length / 4);
        if (tokenCount > MAX_TOKENS) {
          description = description.slice(0, MAX_TOKENS);
        }

        if (calculatedTokens + tokenCount > MAX_TOKENS) {
          const hashes = rows.map((row) => row.descriptionHash);
          const existingIndexes = await this.findBQRecordByHash(
            databaseId,
            providerServiceId,
            hashes,
            `${DATASET_ID}.product_embeddings`,
          );

          logger.info({
            message: `[EMBEDDINGS] Found ${existingIndexes.length} existing hashes. Excluding...`,
          });

          rows = rows.filter((_, index) => !existingIndexes.includes(index));

          if (rows.length > 0) {
            await this.createAndStreamEmbeddings(rows, table);
          }

          rows = [];
          calculatedTokens = 0;
        }

        calculatedTokens += tokenCount;

        const descriptionHash = crypto
          .createHash('sha256')
          .update(description)
          .digest('hex');

        rows.push({
          databaseId,
          productId: product.productId,
          providerServiceId,
          locale,
          descriptionHash,
          description,
        });
      }

      if (rows.length > 0) {
        const hashes = rows.map((row) => row.descriptionHash);
        const existingIndexes = await this.findBQRecordByHash(
          databaseId,
          providerServiceId,
          hashes,
          `${DATASET_ID}.product_embeddings`,
        );

        logger.info({
          message: `[EMBEDDINGS] Found ${existingIndexes.length} existing hashes.`,
        });

        rows = rows.filter((_, index) => !existingIndexes.includes(index));

        if (rows.length > 0) {
          await this.createAndStreamEmbeddings(rows, table);
        }
      }

      logger.info({
        message:
          '[EMBEDDINGS] Embeddings streamed and inserted into BigQuery successfully.',
      });
      return;
    } catch (error: any) {
      logger.error({
        message: 'Error storing embeddings:',
        error: error?.message,
        stack: error?.stack,
      });
      throw Error('Error storing embeddings');
    }
  }

  private static async createAndStreamEmbeddings(
    rows: Array<EmbeddingRow>,
    table: Table,
  ): Promise<void> {
    try {
      const descriptions = rows.map((row) => row.description);

      logger.info({
        message: `[EMBEDDINGS] Creating embeddings for ${descriptions.length} descriptions.`,
      });

      const embeddings = await this.computeEmbeddings(descriptions);

      if (embeddings.length !== rows.length) {
        throw new Error('Mismatch between rows and embeddings length');
      }

      const bigQueryRows = rows.map((row, index) => ({
        databaseId: row.databaseId,
        productId: row.productId,
        providerServiceId: row.providerServiceId,
        locale: row.locale,
        descriptionHash: row.descriptionHash,
        embeddings: embeddings[index],
        timestamp: new Date().toISOString(),
      }));

      await table.insert(bigQueryRows);

      logger.info({
        message: `[EMBEDDINGS] Processed and streamed ${rows.length} embeddings into BigQuery.`,
      });
    } catch (error: any) {
      logger.error({
        message: 'Error processing and streaming embeddings:',
        error: error?.message,
        stack: error?.stack,
      });

      throw Error('Error processing and streaming embeddings');
    }
  }

  private static async computeEmbeddings(texts: string[]): Promise<number[][]> {
    const openai = new OpenAI({
      apiKey: config.OPENAI_API_KEY,
    });

    const response = await openai.embeddings.create({
      model: 'text-embedding-3-small',
      input: texts,
    });

    if (response.data.length === 0) {
      throw new Error('No embeddings returned');
    }

    return response.data.map((item) => item.embedding);
  }

  private static async findBQRecordByHash(
    databaseId: number,
    providerServiceId: string,
    descriptionHashes: string[],
    embeddingsTable: string,
  ): Promise<number[]> {
    const bigQuery = new BigQueryClient();

    const query = `
      SELECT descriptionHash
      FROM \`${embeddingsTable}\`
      WHERE databaseId = @databaseId
        AND providerServiceId = @providerServiceId
        AND descriptionHash IN UNNEST(@descriptionHashes)
    `;

    const queryOptions = {
      params: {
        databaseId,
        providerServiceId,
        descriptionHashes,
      },
    };

    const rows = await bigQuery.runQueryWithResult({ query, queryOptions });

    if (!rows || !Array.isArray(rows)) {
      throw new Error(
        'Invalid response from BigQuery: rows is undefined or not an array',
      );
    }

    const existingHashes = rows.map(
      (row) => (row as { descriptionHash: string }).descriptionHash,
    );

    return descriptionHashes
      .map((hash, index) => (existingHashes.includes(hash) ? index : -1))
      .filter((index) => index !== -1);
  }
}

export default EmbeddingService;
