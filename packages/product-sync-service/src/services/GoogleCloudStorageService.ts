import { Storage, CreateWriteStreamOptions } from '@google-cloud/storage';
import { Readable } from 'node:stream';
import { pipeline } from 'stream/promises';
import { AxiosResponse } from 'axios';
import { logger } from '@om/logger';

export class GoogleCloudStorageService {
  private storage: Storage;

  constructor(projectId?: string, credentials?: string) {
    this.storage = new Storage({
      projectId,
      timeout: 1200000,
      ...(projectId && { projectId }),
      ...(credentials && {
        credentials: JSON.parse(credentials),
      }),
    });
  }

  async uploadFromAxiosStream(
    bucketName: string,
    fileName: string,
    axiosResponse: AxiosResponse<Readable>,
    options?: CreateWriteStreamOptions,
  ): Promise<string> {
    let file;
    try {
      const bucket = this.storage.bucket(bucketName);
      file = bucket.file(fileName);

      // Extract content type from axios headers if available
      const contentType = axiosResponse.headers['content-type'];

      const writeStream = file.createWriteStream({
        ...options,
        highWaterMark: 16 * 1024,
        resumable: false,
        metadata: {
          ...options?.metadata,
          contentType: contentType || options?.metadata?.contentType,
        },
      });

      await pipeline(axiosResponse.data, writeStream);
      logger.info({ message: 'Successfully uploaded file to GCP', fileName });
      return file.publicUrl();
    } catch (e) {
      logger.error({ message: 'Error uploading file to GCP', error: e });
      if (file) {
        file.delete().catch(() => {
          console.error('Failed to delete file after upload error');
        }); // Clean up failed upload
      }
      throw e;
    }
  }

  async uploadFromStream(
    bucketName: string,
    fileName: string,
    stream: Readable,
    options?: CreateWriteStreamOptions,
  ): Promise<string> {
    let file;
    try {
      const bucket = this.storage.bucket(bucketName);
      file = bucket.file(fileName);

      const writeStream = file.createWriteStream({
        ...options,
        highWaterMark: 16 * 1024,
        resumable: false,
        metadata: {
          ...options?.metadata,
          contentType:
            options?.metadata?.contentType || 'application/octet-stream',
        },
      });

      await pipeline(stream, writeStream);

      logger.info({ message: 'Successfully uploaded stream to GCP', fileName });
      return `gs://${bucketName}/${fileName}`;
    } catch (e) {
      logger.error({ message: 'Error uploading stream to GCP', error: e });
      if (file) {
        file.delete().catch(() => {}); // Clean up failed upload
      }
      throw e;
    }
  }

  async uploadFromString(
    bucketName: string,
    fileName: string,
    content: string,
    options: { resumable: boolean; metadata: Record<string, any> },
  ): Promise<string> {
    const bucket = this.storage.bucket(bucketName);
    const file = bucket.file(fileName);

    await file.save(content, options);

    return `gs://${bucketName}/${fileName}`;
  }

  async uploadFromDirectStream(
    bucketName: string,
    fileName: string,
    stream: Readable,
    options?: CreateWriteStreamOptions,
  ): Promise<string> {
    let file;
    try {
      const bucket = this.storage.bucket(bucketName);
      file = bucket.file(fileName);

      const writeStream = file.createWriteStream({
        ...options,
        highWaterMark: 16 * 1024,
        resumable: false,
        metadata: {
          ...options?.metadata,
          contentType:
            options?.metadata?.contentType || 'application/octet-stream',
        },
      });

      await pipeline(stream, writeStream);

      logger.info({ message: 'Successfully uploaded stream to GCP', fileName });
      return file.publicUrl();
    } catch (e) {
      logger.error({ message: 'Error uploading stream to GCP', error: e });
      if (file) {
        file.delete().catch(() => {}); // Clean up failed upload
      }
      throw e;
    }
  }
}
