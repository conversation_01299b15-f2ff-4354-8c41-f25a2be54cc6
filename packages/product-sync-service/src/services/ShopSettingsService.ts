import { Knex } from 'knex';
import { ShopSettingsModelType } from '../interfaces/ShopSetting';
import { GetShopSettingsResponseType } from '../interfaces/RestTypes';

export class ShopSettingsService {
  constructor(private readonly _db: Knex) {}

  // Get multiple shop settings
  async getMany(
    shops: Array<{
      databaseId: number;
      providerServiceId: string;
    }>,
  ): Promise<ShopSettingsModelType[]> {
    return this._db('shop_settings').where(function () {
      shops.forEach((shop) => {
        this.orWhere({
          databaseId: shop.databaseId,
          providerServiceId: shop.providerServiceId,
        });
      });
    });
  }

  async getShopSettings({
    databaseId,
    providerServiceId,
  }: {
    databaseId: string;
    providerServiceId: string;
  }): Promise<GetShopSettingsResponseType> {
    return this._db('shop_settings')
      .select(['default', 'locale', 'currency', 'createdAt', 'updatedAt'])
      .where('databaseId', databaseId)
      .where('providerServiceId', providerServiceId)
      .where('default', true)
      .first();
  }
}
