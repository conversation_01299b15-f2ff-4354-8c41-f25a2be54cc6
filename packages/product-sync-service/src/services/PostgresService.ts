import knex, { Knex } from 'knex';
import config from '../Config';
import { logger } from '@om/logger';
import path from 'path';
import { IMemoryDb, newDb } from 'pg-mem';

const {
  POSTGRES_DB,
  POSTGRES_USER,
  POSTGRES_PASSWORD,
  POSTGRES_HOST,
  POSTGRES_PORT,
} = config;

const MIGRATIONS_PATH = path.resolve(__dirname, '../../db/pg-migration');
export class PostgresService {
  private static instance: PostgresService;
  private _knex: Knex;
  private _memDb?: IMemoryDb;

  private constructor() {
    if (process.env.NODE_ENV === 'test') {
      // Setup in-memory PostgreSQL database for testing
      this._memDb = newDb();

      // Get knex instance from pg-mem
      this._knex = this._memDb.adapters.createKnex(0, {
        migrations: {
          tableName: 'knex_migrations',
          directory: MIGRATIONS_PATH,
        },
      });

      logger.info({
        message: 'Using in-memory PostgreSQL database for testing',
      });
    } else {
      // Use real PostgreSQL database for development and production
      this._knex = knex({
        client: 'pg',
        connection: {
          host: POSTGRES_HOST ?? 'localhost',
          port: POSTGRES_PORT ?? 5432,
          user: POSTGRES_USER,
          password: POSTGRES_PASSWORD,
          database: POSTGRES_DB,
        },
        pool: {
          min: 2,
          max: 70,
          acquireTimeoutMillis: 30000,
          idleTimeoutMillis: 30000,
        },
        migrations: {
          tableName: 'knex_migrations',
          directory: MIGRATIONS_PATH,
          extension: process.env.NODE_ENV === 'production' ? 'js' : 'ts',
        },
      });
    }
  }

  public static getInstance(): PostgresService {
    if (!PostgresService.instance) {
      PostgresService.instance = new PostgresService();
    }
    return PostgresService.instance;
  }

  public get knex(): Knex {
    return this._knex;
  }
  private async withTimeout<T>(promise: Promise<T>, ms: number): Promise<T> {
    let timeoutId: NodeJS.Timeout;
    const timeoutPromise = new Promise<never>((_, reject) => {
      timeoutId = setTimeout(() => reject(new Error('Timeout')), ms);
    });

    return Promise.race([
      promise.then((result) => {
        clearTimeout(timeoutId);
        return result;
      }),
      timeoutPromise,
    ]);
  }

  public async healthCheck(): Promise<boolean> {
    try {
      await this.withTimeout(this._knex.raw('SELECT 1'), 3000);
      logger.info({
        message: `Database health check succeeded`,
        host: POSTGRES_HOST,
        port: POSTGRES_PORT,
      });
      return true;
    } catch (error: any) {
      logger.error({
        message: 'Database health check failed',
        error: error.message,
        stack: error.stack,
      });
      return false;
    }
  }

  public async close(): Promise<void> {
    await this._knex.destroy();
    // No need to explicitly close pg-mem as it's in-memory
  }

  public async migrate(): Promise<void> {
    logger.info({ message: 'Starting database migrations' });
    await this._knex.migrate.latest();
    logger.info({ message: 'Database migrations completed' });
  }
  
}

export const dbService = PostgresService.getInstance();
