import { K<PERSON> } from 'knex';
import { BigQueryClient } from '../adapters/BigQuery';
import { logger } from '@om/logger';
import { PostgresService } from './PostgresService';

export class PostgresSyncService {
  constructor(private readonly dbService: PostgresService) {}

  async syncDataToPostgres(
    bigQuery: BigQueryClient,
    databaseId: number,
    providerServiceId: string,
    datasetId: string,
    productsTable: string,
  ): Promise<void> {
    const query = `
      SELECT * FROM \`${datasetId}.${productsTable}\`
      WHERE databaseId = @databaseId
        AND providerServiceId = @providerServiceId
        AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
        AND status = 'active'
    `;

    const dateNow = this.dbService.knex.raw("NOW() - INTERVAL '30 days'");

    try {
      bigQuery
        .runQueryStream({
          query,
          queryOptions: {
            params: {
              databaseId,
              providerServiceId,
            },
          },
        })
        .on('error', (error) => {
          logger.error({
            message: 'Error fetching data from BigQuery:',
            error: error?.message,
            stack: error?.stack,
          });
          throw error;
        })
        .on('data', (data) => {
          this.dbService.knex.transaction(async (trx: Knex.Transaction) => {
            await trx('products')
              .insert({
                ...data,
                createdAt: this.dbService.knex.fn.now(),
                updatedAt: this.dbService.knex.fn.now(),
                timestamp: data.timestamp.value,
              })
              .onConflict([
                'parentProductId',
                'providerServiceId',
                'productId',
                'databaseId',
              ])
              .merge({
                name: this.dbService.knex.raw('EXCLUDED.name'),
                sku: this.dbService.knex.raw('EXCLUDED.sku'),
                price: this.dbService.knex.raw('EXCLUDED.price'),
                originalPrice: this.dbService.knex.raw(
                  'EXCLUDED."originalPrice"',
                ),
                currency: this.dbService.knex.raw('EXCLUDED.currency'),
                imageUrl: this.dbService.knex.raw('EXCLUDED."imageUrl"'),
                description: this.dbService.knex.raw('EXCLUDED.description'),
                parentProductId: this.dbService.knex.raw(
                  'EXCLUDED."parentProductId"',
                ),
                productUrl: this.dbService.knex.raw('EXCLUDED."productUrl"'),
                status: this.dbService.knex.raw('EXCLUDED.status'),
                locale: this.dbService.knex.raw('EXCLUDED.locale'),
                inStock: this.dbService.knex.raw('EXCLUDED."inStock"'),
                categories: this.dbService.knex.raw('EXCLUDED.categories'),
                updatedAt: this.dbService.knex.fn.now(),
                timestamp: this.dbService.knex.raw('EXCLUDED.timestamp'),
              });
          });
        });

      logger.info({
        message: '[PG-SYNC]: Deleting old products',
      });

      await this.dbService
        .knex('products')
        .where('providerServiceId', providerServiceId)
        .where('databaseId', databaseId)
        .andWhere('updatedAt', '<', dateNow)
        .del();
    } catch (error: any) {
      logger.error({
        message: 'Cannot delete old products',
        error: error?.message,
        stack: error?.stack,
      });
      throw Error('Error writing to Postgres');
    }
  }

  async syncCategoriesToPostgres(
    bigQuery: BigQueryClient,
    databaseId: number,
    providerServiceId: string,
    datasetId: string,
    categoriesTable: string,
  ): Promise<void> {
    const dateNow = this.dbService.knex.raw("NOW() - INTERVAL '30 days'");
    try {
      const categoriesQuery = `
      SELECT * FROM \`${datasetId}.${categoriesTable}\`
      WHERE databaseId = @databaseId
        AND providerServiceId = @providerServiceId
        AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
    `;

      bigQuery
        .runQueryStream({
          query: categoriesQuery,
          queryOptions: {
            params: {
              databaseId,
              providerServiceId,
            },
          },
        })
        .on('error', (error) => {
          logger.error({
            message: 'Error fetching data from BigQuery:',
            error: error?.message,
            stack: error?.stack,
          });
          throw error;
        })
        .on('data', (data) => {
          this.dbService.knex.transaction(async (trx: Knex.Transaction) => {
            await trx('categories')
              .insert({
                ...data,
                createdAt: this.dbService.knex.fn.now(),
                updatedAt: this.dbService.knex.fn.now(),
                timestamp: data.timestamp.value,
              })
              .onConflict(['categoryId', 'providerServiceId', 'databaseId'])
              .merge({
                name: this.dbService.knex.raw('EXCLUDED.name'),
                slug: this.dbService.knex.raw('EXCLUDED.slug'),
                parent: this.dbService.knex.raw('EXCLUDED.parent'),
                locale: this.dbService.knex.raw('EXCLUDED.locale'),
                updatedAt: this.dbService.knex.fn.now(),
                timestamp: this.dbService.knex.raw('EXCLUDED.timestamp'),
              });
          });
        });

      logger.info({
        message: '[PG-SYNC]: Deleting old categories',
      });
      await this.dbService
        .knex('categories')
        .where('providerServiceId', providerServiceId)
        .where('databaseId', databaseId)
        .andWhere('updatedAt', '<', dateNow)
        .del();
    } catch (error: any) {
      logger.error({
        message: 'Cannot insert categories',
        error: error?.message,
        stack: error?.stack,
      });
      throw Error('Error writing to Postgres');
    }
  }

  async saveShopSettings(
    databaseId: number,
    providerServiceId: string,
    locale: string,
    currency: string,
    defaultLocale: boolean,
  ): Promise<void> {
    try {
      if (!locale) {
        locale = 'hu';
        logger.info({
          message: 'Locale is empty, setting to default "hu"',
        });
      }
      await this.dbService
        .knex('shop_settings')
        .insert({
          databaseId,
          providerServiceId,
          locale,
          default: defaultLocale,
          currency,
          createdAt: this.dbService.knex.fn.now(),
          updatedAt: this.dbService.knex.fn.now(),
        })
        .onConflict(['databaseId', 'providerServiceId', 'default'])
        .merge({
          locale: this.dbService.knex.raw('EXCLUDED.locale'),
          currency: this.dbService.knex.raw('EXCLUDED.currency'),
          default: this.dbService.knex.raw('EXCLUDED."default"'),
          updatedAt: this.dbService.knex.fn.now(),
        });
    } catch (error: any) {
      logger.error({
        message: 'Error saving shop settings:',
        error: error?.message,
        stack: error?.stack,
      });
      throw Error('Error saving shop settings');
    }
  }
}
