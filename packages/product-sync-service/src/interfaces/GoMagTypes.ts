// Category types
export interface GoMagCategoryParent {
  id: string;
  depth: number;
  name: string;
}

export interface GoMagCategory {
  id: string;
  name: string;
  depth: string;
  position: string;
  parents: { [key: string]: GoMagCategoryParent };
}

export interface GoMagCategoriesResponse {
  total: number;
  categories: { [key: string]: GoMagCategory };
}

// Gomag Product
export interface GoMagAttribute {
  id: string;
  type: string;
  name: string;
  value: string | string[];
}

export interface GoMagProduct {
  id: string;
  sku: string;
  name: string;
  description: string;
  short_description: string;
  url: string;
  canonical_url: string;
  brand: string | null;
  categories: GoMagCategory[];
  weight: string;
  enabled: string;
  parent: string;
  parent_sku: string;
  stockManagement: number;
  stock: string;
  stock_status: string;
  base_price: string;
  price: string;
  vat_included: number;
  vat: string;
  currency: string;
  ecotax: string;
  um: string | null;
  html_title: string;
  html_description: string;
  customSearchKeys: string;
  feedDescription: string;
  allowOrdersWhenOutOfStock: number;
  attributes: null;
  images: string[];
  ean: string;
  group_key: string;
  videos: string | null;
  files: string | null;
  updated: string;
  created: string;
  delivery_time: string | null;
  delivery_time_type: string;
  bundleItems: string[];
}

export interface GoMagProductsResponse {
  total: number;
  page: number;
  pages: number;
  includeHandlingTime: boolean;
  products: { [key: string]: GoMagProduct };
}

// Gomag currency
export interface GoMagCurrency {
  name: string;
  currency: string;
  multiplier: string;
}

export interface GoMagCurrenciesResponse {
  currencies: GoMagCurrency[];
}
