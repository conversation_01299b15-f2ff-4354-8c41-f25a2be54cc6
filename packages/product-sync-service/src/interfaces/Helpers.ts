// import { getPlatformFetcher } from '../adapters/RegisterPlatforms';
import {
  FetchType,
  PlatformFetcher,
  FetchConfig,
  FlowChildJobTypes,
} from './FetchTypes';

// Main job queue types *parnet job
type FetchMethodName<T extends FetchType> = T extends 'stream'
  ? 'fetchStream'
  : T extends 'single'
    ? 'fetchSingle'
    : T extends 'array'
      ? 'fetchArray'
      : never;

export type FetchForPlatform<
  Platform extends string,
  T extends FetchType,
> = FetchMethodName<T> extends keyof PlatformFetcher<T>
    ? FetchConfig<T> & { platform: Platform }
    : never;

// Runtime capability checking
// function validatePlatformCapabilities(platform: string, type: FetchType) {
//   const fetcher = getPlatformFetcher(platform);
//   if (!(type in fetcher)) {
//     throw new Error(`Platform ${platform} doesn't support ${type} fetch`);
//   }
// }

// Child job queue types
export type FlowChildJobMethodName<T extends FlowChildJobTypes> =
  T extends 'fetch'
    ? 'fetchData'
    : T extends 'intobq'
      ? 'moveTOBQ'
      : T extends 'unifiy'
        ? 'unifyData'
        : T extends 'topg'
          ? 'writeToPg'
          : never;
