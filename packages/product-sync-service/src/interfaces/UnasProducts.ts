export interface UnasProductResponse {
  "?xml"?: XmlDeclaration;
  Products: Products;
}

export interface XmlDeclaration {
  "@_version"?: string;
  "@_encoding"?: string;
}

export interface Products {
  Product: Product;
}

export interface Product {
  State: string;
  Id: number;
  Sku: string;
  CreateTime: number;
  LastModTime: number;
  History: History;
  Statuses: Statuses;
  Name: string;
  Unit: string;
  MinimumQty: number;
  Weight: number;
  Description: Description;
  Prices: Prices;
  Categories: Categories;
  Url: string;
  SefUrl: string;
  Images: Images;
  Variants: string | null;
  Stocks: Stocks;
  PackageProduct: string;
}

export interface History {
  Event: Event;
}

export interface Event {
  Action: string;
  Time: number;
  Sku: string;
}

export interface Statuses {
  Status: Status;
}

export interface Status {
  Type: string;
  Value: number;
}

export interface Description {
  Long: string;
}

export interface Prices {
  Vat: string;
  Price: Price;
}

export interface Price {
  Type: string;
  Net: number;
  Gross: number;
  Actual: number;
}

export interface Categories {
  Category: Category;
}

export interface Category {
  Type: string;
  Id: number;
  Name: string;
}

export interface Images {
  DefaultFilename: string;
  DefaultAlt: string;
  OG: number;
  Image: Image;
}

export interface Image {
  Type: string;
  Url: Url;
  SefUrl: string;
  Filename: string;
  Alt: string;
}

export interface Url {
  Medium: string;
}

export interface Stocks {
  Status: StockStatus;
  Stock: Stock;
}

export interface StockStatus {
  Active: number;
  Empty: number;
}

export interface Stock {
  Qty: number;
}
