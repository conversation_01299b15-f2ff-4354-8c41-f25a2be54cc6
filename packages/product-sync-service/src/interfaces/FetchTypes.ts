import { Job } from 'bullmq';

export type FetchType = 'stream' | 'single' | 'array'; // Add new type of fetches here
export type FlowChildJobTypes =
  | 'fetch'
  | 'intobq'
  | 'unifiy'
  | 'topg'
  | 'polling'
  | 'download';

export type Platform = 'unas' | 'woo' | 'shopify' | 'gomag';

export interface BaseConfig {
  platform: string;
  type: FetchType;
}

export type JobFlowData<T = Record<string, unknown>> = T & {
  platform: string;
  type: string;
  [key: string]: unknown;
};

export interface StreamConfig extends BaseConfig {
  url: string;
  path: string;
}

export interface SingleConfig extends BaseConfig {
  id: string;
}

export interface ArrayConfig extends BaseConfig {
  ids: Array<string>;
}

export interface FetchConfig<T extends FetchType> {
  type: T;
  platform: string;
  schedule?: string; // Cron string
  // Type-specific configuration
  config: T extends 'stream'
    ? StreamConfig
    : T extends 'single'
      ? SingleConfig
      : T extends 'array'
        ? ArrayConfig
        : never;
}

export type PlatformFetchTypes = {
  [platform: string]: FetchType[];
};

export interface PlatformFetcher<T extends FetchType> {
  fetchStream?(config: { path: string; url: string }): Promise<string>;
  fetchSingle?(config: { id: string }): Promise<unknown>;
  fetchArray?(config: { ids: Array<string> }): Promise<unknown[]>;
}

export interface FlowChildJobHandler<T extends FlowChildJobTypes> {
  fetchData?(job: Job): Promise<JobFlowData<T>>;
  moveTOBQ?(job: Job): Promise<JobFlowData<T>>;
  unifyData?(job: Job): Promise<JobFlowData<T>>;
  writeToPg?(job: Job): Promise<JobFlowData<T>>;
  polling?(job: Job): Promise<JobFlowData<T>>;
  download?(job: Job): Promise<JobFlowData<T>>;
}
