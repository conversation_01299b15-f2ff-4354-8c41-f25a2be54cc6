import { Static, Type } from '@sinclair/typebox';

export const ShopSettingsModelSchema = Type.Object({
  id: Type.Number(),
  default: Type.Boolean(),
  databaseId: Type.Number(),
  providerServiceId: Type.String(),
  locale: Type.String(),
  currency: Type.String(),
  createdAt: Type.String(),
  updatedAt: Type.String(),
});

export type ShopSettingsModelType = Static<typeof ShopSettingsModelSchema>;
