import {
  FastifyServerOptions,
  RouteHandlerMethod,
  RouteShorthandOptions,
} from 'fastify';
import Redis from 'ioredis';
import { GoogleCloudStorageService } from '../services/GoogleCloudStorageService';
import { Worker, Queue, FlowProducer } from 'bullmq';

export type RouteMethod =
  | 'get'
  | 'post'
  | 'put'
  | 'delete'
  | 'patch'
  | 'options'
  | 'head';

export type RouteParams = {
  method: RouteMethod;
  url: string;
  handler: RouteHandlerMethod;
  options?: RouteShorthandOptions;
};

export interface Services {
  redis?: Redis;
  flowProducer?: FlowProducer;
  googleCloudStorageService?: GoogleCloudStorageService;
  mainQueue?: Queue;
  flowJobQueue?: Queue;
  mainWorker?: Worker;
  flowJobWorker?: Worker;
}

export interface ServerOptions {
  staticDir?: string;
  enableStreamingRoute?: boolean;
  fastifyOptions?: FastifyServerOptions;
}

export interface JWTPayload {
  loginId: string;
  userId: number;
  accountType: string;
  iat: number;
  role: string;
}
