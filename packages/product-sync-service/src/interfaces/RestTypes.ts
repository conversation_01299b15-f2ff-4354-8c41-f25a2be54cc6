import { Static, Type } from '@sinclair/typebox';
import { FastifyError } from 'fastify';

export class PlatformValidationError extends Error implements FastifyError {
  code: string;
  statusCode?: number;
  name: string;

  constructor(message: string) {
    super(message);
    this.code = 'ERR_INVALID_PLATFORM'; // Custom error code
    this.statusCode = 400; // HTTP status code
    this.name = 'PlatformValidationError'; // Error name
  }
}

export const PlatformsLiterals = Type.Union([
  Type.Literal('unas'),
  Type.Literal('woo'),
  Type.Literal('shopify'),
  Type.Literal('gomag'),
]);

export type Platform = Static<typeof PlatformsLiterals>;

export const FlowJobBaseSchema = Type.Object({
  platform: PlatformsLiterals,
  databaseId: Type.Number(),
  providerServiceId: Type.String(),
});

export const ApiKeySchema = Type.Object({
  apiKey: Type.Optional(Type.String()),
});

export const ConsumerSchema = Type.Object({
  consumerKey: Type.String(),
  consumerSecret: Type.String(),
});

export const UnasJobRequestSchema = Type.Intersect([
  FlowJobBaseSchema,
  ApiKeySchema,
]);

export const WoocommerceJobRequestSchema = Type.Intersect([
  FlowJobBaseSchema,
  ConsumerSchema,
]);

export const ShopifyJobRequestSchema = Type.Intersect([
  FlowJobBaseSchema,
  ApiKeySchema,
  Type.Object({
    shopName: Type.String(),
    apiVersion: Type.Optional(Type.String()),
  }),
]);

export const GoMagJobRequestSchema = Type.Intersect([
  FlowJobBaseSchema,
  ApiKeySchema,
  Type.Object({
    shopName: Type.String(),
  }),
]);

export type UnasJobRequestType = Static<typeof UnasJobRequestSchema>;

export type WoocommerceJobRequestType = Static<
  typeof WoocommerceJobRequestSchema
>;

export type ShopifyJobRequestType = Static<typeof ShopifyJobRequestSchema>;

export type GoMagJobRequestType = Static<typeof GoMagJobRequestSchema>;

export const QueryProductsSchema = Type.Object({
  page: Type.Number({ default: 1, minimum: 1 }),
  limit: Type.Number({ default: 10, minimum: 1, maximum: 100 }),
  databaseId: Type.Number(),
  providerServiceId: Type.String(),
  search: Type.Optional(Type.String({ default: '' })),
  outOfStock: Type.Optional(Type.Boolean({ default: false })),
});

export type QueryProductsRequestType = Static<typeof QueryProductsSchema>;

export const ProductSchema = Type.Object({
  id: Type.String(),
  inStock: Type.Boolean(),
  timestamp: Type.String(),
  databaseId: Type.Number(),
  providerServiceId: Type.String(),
  status: Type.String(),
  locale: Type.String(),
  productId: Type.String(),
  name: Type.String(),
  sku: Type.String(),
  price: Type.Number(),
  originalPrice: Type.Number(),
  currency: Type.String(),
  imageUrl: Type.String(),
  description: Type.String(),
  parentProductId: Type.String(),
  categories: Type.String(),
  productUrl: Type.String(),
  createdAt: Type.String(),
  updatedAt: Type.String(),
});

export const GetProductsByIdsResponseSchema = Type.Object({
  products: Type.Array(ProductSchema),
});

export const QueryProductsResponseSchema = Type.Object({
  products: Type.Array(ProductSchema),
  meta: Type.Object({
    page: Type.Number(),
    limit: Type.Number(),
    total: Type.Number(),
    totalPages: Type.Number(),
  }),
});

export const GetShopSettingsRequestTypeSchema = Type.Object({
  databaseId: Type.String(),
  providerServiceId: Type.String(),
});

export type GetShopSettingsRequestType = Static<
  typeof GetShopSettingsRequestTypeSchema
>;

export const GetShopSettingsResponseSchema = Type.Object({
  default: Type.Boolean(),
  locale: Type.String(),
  currency: Type.String(),
  createdAt: Type.String(),
  updatedAt: Type.String(),
});

export type GetShopSettingsResponseType = Static<
  typeof GetShopSettingsResponseSchema
>;

export type ProductsSchemaType = Static<typeof ProductSchema>;

export type GetProductsByIdsResponseType = Static<
  typeof GetProductsByIdsResponseSchema
>;

export type QueryProductsResponseType = Static<
  typeof QueryProductsResponseSchema
>;

export const GetUnifiedProductsRequestSchema = Type.Object({
  productIds: Type.Array(Type.String()),
  databaseId: Type.Number(),
  providerServiceId: Type.String(),
  limit: Type.Optional(Type.Number({ default: 10, minimum: 1, maximum: 100 })),
});

export type GetUnifiedProductsRequestType = Static<
  typeof GetUnifiedProductsRequestSchema
>;

export const RunBigQueryQuerySchema = Type.Object({
  queryName: Type.String(),
});

export type RunBigQueryQuerySchemaType = Static<typeof RunBigQueryQuerySchema>;

export const GetPopularProductsSchema = Type.Object({
  databaseId: Type.Number(),
  providerServiceId: Type.String(),
});

export type GetPopularProductsSchemaType = Static<
  typeof GetPopularProductsSchema
>;

export const BackendProductSyncSchema = Type.Object({
  accountId: Type.Number(), // databaseId
  shopId: Type.String(), // providerServiceId
  shopType: Type.String(), // platform
  token: Type.Optional(Type.String()),
  active: Type.Boolean(),
  username: Type.Optional(Type.String()),
  password: Type.Optional(Type.String()),
});

export type BackendProductSyncType = Static<typeof BackendProductSyncSchema>;

export const ProductSyncSchema = Type.Object({
  databaseId: Type.Number(), // databaseId
  providerServiceId: Type.String(), // providerServiceId
  platform: Type.String(), // platform
  apiKey: Type.Optional(Type.String()),
  active: Type.Boolean(),
  consumerKey: Type.Optional(Type.String()),
  consumerSecret: Type.Optional(Type.String()),
});

export type ProductSyncType = Static<typeof ProductSyncSchema>;

export const ProductResyncRequestSchema = Type.Object({
  data: Type.Array(BackendProductSyncSchema),
});

export type ProductResyncRequestType = Static<
  typeof ProductResyncRequestSchema
>;
