export interface WooProductResponse {
  products: WooProduct[];
  totalPage: number;
}

export interface WooProduct {
  id: number;
  name: string;
  slug: string;
  permalink: string;
  price: string;
  regular_price: string;
  sale_price: string;
  description: string;
  short_description: string;
  stock_status: string;
  categories: { id: number; name: string; slug: string }[];
  images: { id: number; src: string; name: string; alt: string }[];
}

export interface WooCategory {
  id: number;
  name: string;
  slug: string;
  parent?: string;
}
