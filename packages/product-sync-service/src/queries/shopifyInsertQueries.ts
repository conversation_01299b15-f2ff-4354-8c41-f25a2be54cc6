export const generateShopifyInsertQuery = ({
  datasetId,
  targetTableId,
  sourceTableId,
  metadata,
}: {
  datasetId: string;
  targetTableId: string;
  sourceTableId: string;
  metadata: {
    databaseId: number;
    providerServiceId: string;
    locale: string;
    currency: string;
    shopDomain?: string;
  };
}) => {
  return `
    INSERT INTO \`${datasetId}.${targetTableId}\` (
      timestamp,
      databaseId,
      providerServiceId,
      status,
      locale,
      productId,
      name,
      price,
      originalPrice,
      currency,
      imageUrl,
      description,
      parentProductId,
      productUrl
    )
    -- Insert main products
    SELECT
      CURRENT_TIMESTAMP() as timestamp,
      ${metadata.databaseId} as databaseId,
      '${metadata.providerServiceId}' as providerServiceId,
      status as status,
      '${metadata.locale}' as locale,
      REGEXP_EXTRACT(id, r'gid://shopify/Product/(.*)') as productId,
      title as name,
      price as price,
      price as originalPrice,
      '${metadata.currency}' as currency,
      image as imageUrl,
      description as description,
      __parentId as parentProductId,
      COALESCE(
        onlineStoreUrl,
        CONCAT('https://${
          metadata.shopDomain || 'shop.myshopify.com'
        }/products/', handle)
      ) as productUrl
    FROM \`${datasetId}.${sourceTableId}\`
    WHERE status = 'ACTIVE'
  `;
};
