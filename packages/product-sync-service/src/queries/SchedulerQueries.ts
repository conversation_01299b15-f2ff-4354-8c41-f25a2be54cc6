import config from '../Config';

const MOST_POPULAR_PRODUCTS_QUERY = `WITH flattened AS (
  SELECT
    be.accountId,
    be.providerServiceId,
    p.value AS productId,
    COUNT(1) AS cnt
  FROM
    \`${config.GCP_PROJECT_ID}.behavioural_events_optimonk.events\` be,
    UNNEST(be.events) ev,
    UNNEST(ev.props) p
  WHERE
    DATE(be.timestamp) >= DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)
    AND ev.type = 'epv'
    AND p.name = 'productId'
  GROUP BY
    be.accountId,
    be.providerServiceId,
    p.value
)
SELECT
  accountId,
  providerServiceId,
  ARRAY_AGG(productId ORDER BY cnt DESC LIMIT 10) AS top_product_ids
FROM
  flattened
GROUP BY
  accountId,
  providerServiceId
LIMIT 100
`;

export interface ScheduledQuery {
  name: string;
  query: string;
}

export const SCHEDULED_QUERIES = [
  {
    name: 'most_popular_products',
    query: MOST_POPULAR_PRODUCTS_QUERY,
  },
];
