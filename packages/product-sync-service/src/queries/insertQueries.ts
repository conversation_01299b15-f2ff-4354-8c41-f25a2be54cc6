export const deleteProductQuery = ({
  datasetId,
  tableId,
}: {
  datasetId: string;
  tableId: string;
}): string => {
  return `
    DELETE FROM \`${datasetId}.${tableId}\`
    WHERE databaseId = @databaseId AND providerServiceId = @providerServiceId AND timestamp > TIMESTAMP(DATE_SUB(CURRENT_DATE(), INTERVAL 1 YEAR));
  `;
};

interface InsertQueryParams {
  datasetId: string;
  targetTableId: string;
  sourceTableId: string;
  catTempTableId?: string; // Optional for WooCommerce, required for Unas
  metadata: {
    databaseId: number;
    providerServiceId: string;
    // todo rethink
    locale: string;
    // todo rethink
    currency: string;
  };
}

interface WooInsertQueryParams {
  datasetId: string;
  targetTableId: string;
  sourceTableId: string;
  metadata: {
    databaseId: number;
    providerServiceId: string;
    locale: string;
    currency: string;
  };
}

export const generateUnasInsertQuery = ({
  datasetId,
  targetTableId,
  sourceTableId,
  metadata,
}: InsertQueryParams): string => {
  return `
    INSERT INTO \`${datasetId}.${targetTableId}\`
    WITH parent_lookup AS (
      SELECT
        Sku,
        CAST(Id AS STRING) AS parentProductId
      FROM \`${datasetId}.${sourceTableId}\`
    )
    SELECT
      CURRENT_TIMESTAMP() as timestamp,
      ${metadata.databaseId} as databaseId,
      '${metadata.providerServiceId}' as providerServiceId,
      '${metadata.locale}' as locale,
      CASE
        WHEN CAST(p.Status AS INT64) IN (1,2) THEN 'active'
        ELSE 'inactive'
      END as status,
      CAST(p.Id AS STRING) as productId,
      p.Name as name,
      CAST(p.RegularPrice AS FLOAT64) as originalPrice,
      CAST(p.Price AS FLOAT64) as price,
      '${metadata.currency}' as currency,
      p.Image as imageUrl,
      p.LongDescription as description,
      IFNULL(pl.parentProductId, '') as parentProductId,
      p.Url as productUrl,
      CASE
        WHEN CAST(p.InStock AS INT64) > 0 OR CAST(p.StockStatus AS INT64) = 1 THEN TRUE
        ELSE FALSE
      END as inStock,
      TO_JSON(ARRAY(
        SELECT CAST(categoryId AS STRING)
        FROM UNNEST(p.Categories) AS categoryId
        WHERE SAFE_CAST(categoryId AS INT64) IS NOT NULL
      )) AS categories,
      p.Sku as sku
    FROM \`${datasetId}.${sourceTableId}\` p
    LEFT JOIN parent_lookup pl
      ON p.ParentId = pl.Sku
  `;
};

export const generateWooInsertQuery = ({
  datasetId,
  targetTableId,
  sourceTableId,
  metadata,
}: WooInsertQueryParams): string => {
  return `
    INSERT INTO \`${datasetId}.${targetTableId}\`
    SELECT
      CURRENT_TIMESTAMP() as timestamp,
      ${metadata.databaseId} as databaseId,
      '${metadata.providerServiceId}' as providerServiceId,
      '${metadata.locale}' as locale,
      CASE \`status\`
        WHEN 'publish' THEN 'active'
        WHEN 'draft' THEN 'inactive'
        WHEN 'pending' THEN 'inactive'
        WHEN 'private' THEN 'inactive'
        WHEN 'trash' THEN 'inactive'
        ELSE 'inactive'
      END as status,
      CAST(\`id\` AS STRING) as productId,
      \`name\` as name,
      CASE
        WHEN \`price\` IS NULL THEN CAST(\`regular_price\` AS FLOAT64)
        ELSE CAST(\`price\` AS FLOAT64)
      END as price,
      CAST(\`regular_price\` AS FLOAT64) as originalPrice,
      '${metadata.currency}' as currency,
      \`images\`[SAFE_OFFSET(0)].src as imageUrl,
      \`description\` as description,
      CAST(\`parent_id\` AS STRING) as parentProductId,
      IFNULL(\`permalink\`, '') as productUrl,
      CASE \`stock_status\`
        WHEN 'instock' THEN TRUE
        WHEN 'outofstock' THEN FALSE
        WHEN 'onbackorder' THEN TRUE
        ELSE FALSE
      END as inStock,
      TO_JSON(ARRAY(
        SELECT CAST(c.id AS STRING)
        FROM UNNEST(categories) AS c
      )) AS categories,
      \`sku\` as sku
    FROM \`${datasetId}.${sourceTableId}\`;
  `;
};

export const generateGoMagInsertQuery = ({
  datasetId,
  targetTableId,
  sourceTableId,
  metadata,
}: WooInsertQueryParams): string => {
  return `
    INSERT INTO \`${datasetId}.${targetTableId}\`
    SELECT
      CURRENT_TIMESTAMP() as timestamp,
      ${metadata.databaseId} as databaseId,
      '${metadata.providerServiceId}' as providerServiceId,
      '${metadata.locale}' as locale,
      CASE \`enabled\`
        WHEN 1 THEN 'active'
        ELSE 'inactive'
      END as status,
      CAST(\`id\` AS STRING) as productId,
      \`name\` as name,
      CASE
        WHEN \`price\` IS NULL THEN CAST(\`base_price\` AS FLOAT64)
        ELSE CAST(\`price\` AS FLOAT64)
      END as price,
      CAST(\`base_price\` AS FLOAT64) as originalPrice,
      '${metadata.currency}' as currency,
      \`images\`[SAFE_OFFSET(0)] as imageUrl,
      \`description\` as description,
      CAST(\`parent\` AS STRING) as parentProductId,
      IFNULL(\`url\`, '') as productUrl,
      CASE
        WHEN \`stock_status\` = 'instock' OR \`allowOrdersWhenOutOfStock\` <> 0 THEN TRUE
        ELSE FALSE
      END as inStock,
      TO_JSON(ARRAY(
        SELECT CAST(c.id AS STRING)
        FROM UNNEST(categories) AS c
      )) AS categories,
      \`sku\` as sku
    FROM \`${datasetId}.${sourceTableId}\`;
  `;
};

export const generateCategoryInsertQuery = ({
  datasetId,
  targetTableId,
  sourceTableId,
  metadata,
}: InsertQueryParams): string => {
  return `
    MERGE INTO \`${datasetId}.${targetTableId}\` AS target
    USING (
      SELECT DISTINCT
        CAST(c.id AS STRING) AS categoryId,
        c.name AS name,
        c.slug AS slug,
        COALESCE(CAST(c.parent AS STRING), '') AS parent,
        ${metadata.databaseId} AS databaseId,
        '${metadata.providerServiceId}' AS providerServiceId,
        '${metadata.locale}' AS locale,
        CURRENT_TIMESTAMP() AS timestamp
      FROM \`${datasetId}.${sourceTableId}\` c
    ) AS source
    ON target.categoryId = source.categoryId
       AND target.databaseId = source.databaseId
       AND target.providerServiceId = source.providerServiceId
    WHEN MATCHED THEN
      UPDATE SET
        target.name = source.name,
        target.slug = source.slug,
        target.parent = source.parent,
        target.timestamp = CURRENT_TIMESTAMP()
    WHEN NOT MATCHED THEN
      INSERT (categoryId, name, slug, parent, databaseId, providerServiceId, locale, timestamp)
      VALUES (source.categoryId, source.name, source.slug, source.parent, source.databaseId, source.providerServiceId, source.locale, CURRENT_TIMESTAMP());
  `;
};

export const generateGoMagCategoryInsertQuery = ({
  datasetId,
  targetTableId,
  sourceTableId,
  metadata,
}: InsertQueryParams): string => {
  return `
    MERGE INTO \`${datasetId}.${targetTableId}\` AS target
    USING (
      SELECT DISTINCT
        CAST(c.id AS STRING) AS categoryId,
        c.name AS name,
        LOWER(c.name) AS slug,
        COALESCE(CAST((c.parents[SAFE_OFFSET(0)]).id AS STRING), '') AS parent,
        ${metadata.databaseId} AS databaseId,
        '${metadata.providerServiceId}' AS providerServiceId,
        '${metadata.locale}' AS locale,
        CURRENT_TIMESTAMP() AS timestamp
      FROM \`${datasetId}.${sourceTableId}\` c
    ) AS source
    ON target.categoryId = source.categoryId
       AND target.databaseId = source.databaseId
       AND target.providerServiceId = source.providerServiceId
    WHEN MATCHED THEN
      UPDATE SET
        target.name = source.name,
        target.slug = source.slug,
        target.parent = source.parent,
        target.timestamp = CURRENT_TIMESTAMP()
    WHEN NOT MATCHED THEN
      INSERT (categoryId, name, slug, parent, databaseId, providerServiceId, locale, timestamp)
      VALUES (source.categoryId, source.name, source.slug, source.parent, source.databaseId, source.providerServiceId, source.locale, CURRENT_TIMESTAMP());
  `;
};
