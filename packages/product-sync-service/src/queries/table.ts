interface ExternalTableQueryOptions {
  uri?: string | null;
  format?: 'CSV' | 'NEWLINE_DELIMITED_JSON';
  expiry?: string;
  dataset?: string;
  csvOptions?: {
    skipLeadingRows?: number;
    allowJaggedRows?: boolean;
    allowQuotedNewlines?: boolean;
    encoding?: string;
    fieldDelimiter?: string;
  };
}

export const externalTableQuery = (
  tableName: string,
  {
    uri = null,
    format = 'NEWLINE_DELIMITED_JSON',
    expiry = '3 DAY',
    dataset = 'temp',
    csvOptions = {
      skipLeadingRows: 1,
      allowJaggedRows: true,
      allowQuotedNewlines: true,
      encoding: 'UTF-8',
      fieldDelimiter: ',',
    },
  }: ExternalTableQueryOptions = {},
): string => {
  if (!uri) return '';

  const csvOptionsString =
    format === 'CSV'
      ? `,
    skip_leading_rows = ${csvOptions.skipLeadingRows},
    allow_jagged_rows = ${csvOptions.allowJaggedRows},
    allow_quoted_newlines = ${csvOptions.allowQuotedNewlines},
    encoding = "${csvOptions.encoding}",
    field_delimiter = "${csvOptions.fieldDelimiter}"`
      : '';

  return `
CREATE OR REPLACE EXTERNAL TABLE
  \`${dataset}.${tableName}\` OPTIONS (
    format = '${format}',
    uris = ['${uri}'],
    expiration_timestamp = TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL ${expiry})${csvOptionsString}
);`;
};
