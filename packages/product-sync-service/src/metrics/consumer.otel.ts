import { NodeSDK } from '@opentelemetry/sdk-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-proto';
import { OTLPMetricExporter } from '@opentelemetry/exporter-metrics-otlp-proto';
import { PeriodicExportingMetricReader } from '@opentelemetry/sdk-metrics';
import { TraceExporter } from '@google-cloud/opentelemetry-cloud-trace-exporter';
import { MetricExporter } from '@google-cloud/opentelemetry-cloud-monitoring-exporter';
import config from '../Config';
import { logger } from '@om/logger';

export const startConsumerTracing = (): void => {
  logger.info('Starting consumer tracing');
  const sdk = new NodeSDK({
    serviceName: 'consumer',
    traceExporter:
      config.NODE_ENV === 'production'
        ? new TraceExporter({
            projectId: config.GCP_PROJECT_ID,
          })
        : new OTLPTraceExporter({
            url: 'http://127.0.0.1:4318/v1/traces',
          }),
    metricReader: new PeriodicExportingMetricReader({
      exporter:
        config.NODE_ENV === 'production'
          ? new MetricExporter({
              projectId: config.GCP_PROJECT_ID,
            })
          : new OTLPMetricExporter({
              url: 'http://127.0.0.1:4318/v1/metrics',
            }),
    }),
  });

  sdk.start();
};
