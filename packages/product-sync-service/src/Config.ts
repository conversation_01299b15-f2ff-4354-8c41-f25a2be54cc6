import dotenvFlow from 'dotenv-flow';

dotenvFlow.config();

import { z } from 'zod';

// Define the schema for environment variables
const envSchema = z.object({
  // Server configuration
  PORT: z.string().transform(Number).default('3000'),
  NODE_ENV: z
    .enum(['development', 'production', 'test'])
    .default('development'),

  // Service configuration
  SERVICE_NAME: z.string().default('product-sync-service'),
  SHOPIFY_API_VERSION: z.string().default('2025-01'),
  // Tracing and monitoring
  OTEL_EXPORTER_OTLP_ENDPOINT: z.string().url().optional(),

  // GCP configuration
  GCP_PROJECT_ID: z.string(),
  GCP_API_PROJECT_CREDS: z.string(),
  GCP_BIGQUERY_DATASET: z.string(),
  GCP_BIGQUERY_PRODUCTS_TABLE: z.string(),
  GCP_BIGQUERY_CATEGORIES_TABLE: z.string(),
  GCP_BUCKET_NAME: z.string(),
  OTEL_METRICS_EXPORTER: z.string().default('none'),
  OTEL_METRIC_EXPORT_INTERVAL: z.string().default('10000'),
  OTEL_LOG_LEVEL: z.number().optional(),

  // Postgres
  POSTGRES_DB: z.string().default('product-sync'),
  POSTGRES_USER: z.string(),
  POSTGRES_PASSWORD: z.string(),
  POSTGRES_HOST: z.string().default('localhost'),
  POSTGRES_PORT: z.string().transform(Number).default('5432'),

  // Redis
  REDIS_HOST: z.string().default('localhost'),
  REDIS_PORT: z.string().transform(Number).default('6379'),
  REDIS_PASSWORD: z.string().optional(),
  REDIS_TLS: z
    .string()
    .transform((val) => val === 'true')
    .default('false'),

  // Authorization
  OM_SHARED_KEY: z.string(),
  OPENAI_API_KEY: z.string(),
});

// Parse and validate environment variables
function validateEnv() {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors
        .filter(
          (err) => err.code === 'invalid_type' && err.received === 'undefined',
        )
        .map((err) => err.path.join('.'));

      const invalidVars = error.errors
        .filter(
          (err) => err.code !== 'invalid_type' || err.received !== 'undefined',
        )
        .map((err) => `${err.path.join('.')}: ${err.message}`);

      if (missingVars.length > 0) {
        console.error(
          '❌ Missing required environment variables:',
          missingVars.join(', '),
        );
      }

      if (invalidVars.length > 0) {
        console.error(
          '❌ Invalid environment variables:',
          invalidVars.join(', '),
        );
      }

      throw new Error('Environment validation failed. Check your .env file.');
    }
    throw error;
  }
}

// Export the validated config
const config = validateEnv();

export default config;

// Type definition for the config
export type Config = z.infer<typeof envSchema>;
