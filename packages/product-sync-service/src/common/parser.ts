import sax from 'sax';

interface SaxNode {
  name: string;
  attributes: Record<string, string>;
  children: SaxNode[];
  content: string;
}

interface UnasProductFlat {
  Id: string;
  Sku: string;
  Status: string;
  Name: string;
  LongDescription: string;
  Price: string;
  RegularPrice: string;
  Categories: string[];
  Url: string;
  Image: string;
  StockStatus: string;
  InStock: string;
  ParentId: string;
}

export function buildNodeTreeFromSaxStream(
  saxStream: sax.SAXStream,
  onNode: (node: SaxNode) => void,
): void {
  const stack: SaxNode[] = [];

  saxStream.on('opentag', (node: sax.Tag) => {
    const newNode: SaxNode = {
      name: node.name,
      attributes: node.attributes,
      children: [],
      content: '',
    };
    if (stack.length > 0) {
      stack[stack.length - 1].children.push(newNode);
    }
    stack.push(newNode);
  });

  saxStream.on('text', (text: string) => {
    if (stack.length > 0) {
      stack[stack.length - 1].content += text;
    }
  });

  saxStream.on('cdata', (cdata: string) => {
    if (stack.length > 0) {
      stack[stack.length - 1].content += cdata;
    }
  });

  saxStream.on('closetag', (_tagName: string) => {
    const closedNode = stack.pop();
    if (closedNode) {
      onNode(closedNode);
    }
  });
}

export function flattenUnasProduct(node: SaxNode): UnasProductFlat {
  const getText = (name: string, n: SaxNode = node): string => {
    if (n.name === name && n.content && n.content.trim())
      return n.content.trim();
    if (n.children && n.children.length > 0) {
      for (const child of n.children) {
        const result = getText(name, child);
        if (result) return result;
      }
    }
    return '';
  };

  // Categories
  let categories: string[] = [];
  const categoriesNode = node.children.find((c) => c.name === 'Categories');
  if (categoriesNode) {
    categories = categoriesNode.children
      .filter((cat) => cat.name === 'Category')
      .map((cat) => {
        const idNode = cat.children.find((cc) => cc.name === 'Id');
        return idNode ? idNode.content : '';
      })
      .filter((id: string) => id);
  }

  // Image
  let imageUrl = '';
  const imagesNode = node.children.find((c) => c.name === 'Images');
  if (imagesNode) {
    const imageNode = imagesNode.children.find((c) => c.name === 'Image');
    if (imageNode) {
      const urlNode = imageNode.children.find((c) => c.name === 'Url');
      if (urlNode) {
        const mediumNode = urlNode.children.find((c) => c.name === 'Medium');
        if (mediumNode) {
          imageUrl = mediumNode.content;
        }
      }
    }
  }

  // Prices
  let price = '';
  let regularPrice = '';
  const pricesNode = node.children.find((c) => c.name === 'Prices');
  if (pricesNode) {
    const priceNodes = pricesNode.children.filter((c) => c.name === 'Price');
    const normalPrice = priceNodes.find((p) =>
      p.children.some(
        (c) => c.name === 'Type' && c.content.trim() === 'normal',
      ),
    );
    const salePrice = priceNodes.find((p) =>
      p.children.some((c) => c.name === 'Type' && c.content.trim() === 'sale'),
    );

    if (normalPrice) {
      const grossNode = normalPrice.children.find((c) => c.name === 'Gross');
      if (grossNode) {
        price = grossNode.content;
        if (!salePrice) regularPrice = grossNode.content;
      }
    }
    if (salePrice) {
      const grossNode = salePrice.children.find((c) => c.name === 'Gross');
      if (grossNode) {
        regularPrice = grossNode.content;
      }
    }
  }

  // Status
  let status = '';
  const statusesNode = node.children.find((c) => c.name === 'Statuses');
  if (statusesNode) {
    const statusNode = statusesNode.children.find((c) => c.name === 'Status');
    if (statusNode) {
      const valueNode = statusNode.children.find((c) => c.name === 'Value');
      if (valueNode) {
        status = valueNode.content;
      }
    }
  }

  // Stock
  let stockStatus = '';
  let inStock = '';
  const stocksNode = node.children.find((c) => c.name === 'Stocks');
  if (stocksNode) {
    const statusNode = stocksNode.children.find((c) => c.name === 'Status');
    if (statusNode) {
      const activeNode = statusNode.children.find((c) => c.name === 'Active');
      if (activeNode) stockStatus = activeNode.content;
    }
    const stockNode = stocksNode.children.find((c) => c.name === 'Stock');
    if (stockNode) {
      const qtyNode = stockNode.children.find((c) => c.name === 'Qty');
      if (qtyNode) inStock = qtyNode.content;
    }
  }

  // ParentId
  let parentId = '';
  const typesNode = node.children.find((c) => c.name === 'Types');
  if (typesNode) {
    const parentNode = typesNode.children.find((c) => c.name === 'Parent');
    if (parentNode) parentId = parentNode.content;
  }

  // LongDescription
  let longDescription = '';
  const descNode = node.children.find((c) => c.name === 'Description');
  if (descNode) {
    const longNode = descNode.children.find((c) => c.name === 'Long');
    if (longNode) longDescription = longNode.content;
  }

  return {
    Id: getText('Id'),
    Sku: getText('Sku'),
    Status: status,
    Name: getText('Name'),
    LongDescription: longDescription,
    Price: price,
    RegularPrice: regularPrice,
    Categories: categories,
    Url: getText('Url'),
    Image: imageUrl,
    StockStatus: stockStatus,
    InStock: inStock,
    ParentId: parentId,
  };
}

export function newFlattenUnasProduct(product: any): UnasProductFlat {
  // Kategóriák feldolgozása
  const categories = product.Categories?.Category
    ? Array.isArray(product.Categories.Category)
      ? product.Categories.Category.map((cat: any) => cat.Id || '')
      : [product.Categories.Category.Id || '']
    : [];

  // Kép feldolgozása
  const imageUrl = product.Images?.Image?.Url?.Medium || '';

  // Árak feldolgozása
  let price = '';
  let regularPrice = '';
  if (product.Prices?.Price) {
    const normalPrice = Array.isArray(product.Prices.Price)
      ? product.Prices.Price.find((p: any) => p.Type === 'normal')
      : product.Prices.Price.Type === 'normal'
        ? product.Prices.Price
        : null;

    const salePrice = Array.isArray(product.Prices.Price)
      ? product.Prices.Price.find((p: any) => p.Type === 'sale')
      : product.Prices.Price.Type === 'sale'
        ? product.Prices.Price
        : null;

    if (normalPrice) {
      price = normalPrice.Gross || '';
      if (!salePrice) regularPrice = normalPrice.Gross || '';
    }
    if (salePrice) {
      regularPrice = salePrice.Gross || '';
    }
  }

  // Státusz feldolgozása
  const status = product.Statuses?.Status?.Value || '';

  // Készlet feldolgozása
  const stockStatus = product.Stocks?.Status?.Empty || '';
  const inStock = product.Stocks?.Stock?.Qty || '';

  // Szülő ID feldolgozása
  const parentId = product.Types?.Parent || '';

  // Hosszú leírás feldolgozása
  const longDescription = product.Description?.Long || '';

  return {
    Id: product.Id || '',
    Sku: product.Sku || '',
    Status: status,
    Name: product.Name || '',
    LongDescription: longDescription,
    Price: price,
    RegularPrice: regularPrice,
    Categories: categories,
    Url: product.Url || '',
    Image: imageUrl,
    StockStatus: stockStatus,
    InStock: inStock,
    ParentId: parentId,
  };
}
