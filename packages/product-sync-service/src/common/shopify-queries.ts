export const productsBulk = `mutation {
      bulkOperationRunQuery(
        query: """
        {
          products {
            edges {
              node {
                id
                title
                description
                handle
                status
                vendor
                productType
                tags
                onlineStoreUrl
                collections(first: 10) {
                  edges {
                    node {
                      id
                      title
                    }
                  }
                }
                variants(first: 250) {
                  edges {
                    node {
                      id
                      title
                      price
                      inventoryPolicy
                      inventoryQuantity
                      image {
                        id
                        originalSrc
                      }
                      sku
                      compareAtPrice
                    }
                  }
                }
              }
            }
          }
        }
        """
      ) {
        bulkOperation {
          id
          status
        }
        userErrors {
          field
          message
        }
      }
    }`;
