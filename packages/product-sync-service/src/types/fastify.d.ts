import { FastifyReply } from 'fastify';
import { Services } from '../interfaces/ServerTypes';
import { JWT } from '@fastify/jwt';

declare module 'fastify' {
  interface FastifyInstance {
    services: Services;
    authenticate: (
      request: FastifyRequest,
      reply: FastifyReply,
    ) => Promise<void>;
    jwt: JWT;
  }
  interface FastifyRequest {
    user: Record<string, any>;
    jwtVerify: <T = any>() => Promise<T>;
    services: Services;
  }
}
