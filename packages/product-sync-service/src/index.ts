import dotenvFlow from 'dotenv-flow';

dotenvFlow.config();

import { FastifyServer } from './server/FastifyServer';
// import { streamRoute } from './server/routes/StreamRoute';
// import { fileRoute } from './server/routes/FileRoute';
import { logger } from '@om/logger';

// the worker and the queue is initiated with these imports
import { createFlowChildJobQueue } from './scheduler/Queue';
import { createFlowJobWorker } from './scheduler/FlowJobWorker';
import { RedisClient } from './services/RedisService';
import { getFlowProducer } from './scheduler/FlowProducer';
import { createMainWorker } from './scheduler/MainWorker';
import { dbService } from './services/PostgresService';
import { routes } from './server/routes/index';
import { startProducerTracing } from './metrics/producer.otel';
import { startConsumerTracing } from './metrics/consumer.otel';

const main = async () => {
  try {
    // Wait for Redis initialization if in test environment
    if (process.env.NODE_ENV === 'test') {
      await RedisClient.waitForInitialization();
    }

    // Make sure Redis is connected before proceeding
    const redisClient = RedisClient.getInstance();

    // Initialize fastify server
    const server = new FastifyServer();

    server.addServices({
      redis: redisClient,
      flowProducer: getFlowProducer(redisClient),
      mainWorker: createMainWorker(redisClient),
      flowJobWorker: createFlowJobWorker(redisClient),
      flowJobQueue: createFlowChildJobQueue(redisClient),
    });

    // Add routes
    server.setupRoutes(routes);
    // These endpoints are not used at the moment
    // await server.addRoute('get', '/file', fileRoute);
    // await server.addRoute('get', '/stream', streamRoute);

    await dbService.healthCheck();
    await dbService.migrate();

    // Start server
    await server.start();
  } catch (err) {
    logger.error({
      message: 'Error initializing server:',
      err,
    });
    process.exit(1);
  }
};

startProducerTracing();
startConsumerTracing();

// Start application
main().catch((err) => {
  logger.error({
    message: 'Error starting server:',
    err,
  });
  process.exit(1);
});
