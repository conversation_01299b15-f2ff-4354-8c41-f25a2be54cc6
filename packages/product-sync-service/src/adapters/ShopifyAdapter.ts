import { Job<PERSON>lowData, FlowChildJobHandler } from '../interfaces/FetchTypes';
import { DelayedError, Job } from 'bullmq';
import { Readable } from 'node:stream';
import { hashProvider } from '../utils/ProviderServiceIdHash';

import { GoogleCloudStorageService } from '../services/GoogleCloudStorageService';
import { logger } from '@om/logger';
import { BigQueryClient } from './BigQuery';
import { externalTableQuery } from '../queries/table';
import { JobStatusService } from '../services/JobStatusService';
import { JobStatusEnum } from '../enums/JobStatusEnum';
import { ShopifyAPIAdapter } from './ShopifyAPIAdapter';
import { dbService } from '../services/PostgresService';
import { PostgresSyncService } from '../services/PostgresSyncService';
import EmbeddingService from '../services/EmbeddingService';
import config from '../Config';
import { getJobData } from '../common/job';
import { deleteProductQuery } from '../queries/insertQueries';
import { generateShopifyInsertQuery } from '../queries/shopifyInsertQueries';
import { productsBulk } from '../common/shopify-queries';
import axios, { AxiosResponse } from 'axios';
import { formatJobLog } from '../utils/Log';

const DATASET_ID = config.GCP_BIGQUERY_DATASET;
const PRODUCTS_TABLE = config.GCP_BIGQUERY_PRODUCTS_TABLE;

type ShopifyJobProperties = {
  accessToken: string;
  shopName: string;
  providerServiceId: string;
  databaseId: number;
  locale: string;
  currency: string;
  apiVersion?: string;
};

type ShopifyJobData<T = Record<string, unknown>> = T &
  JobFlowData<ShopifyJobProperties>;

export class ShopifyAdapter
  implements
    FlowChildJobHandler<
      'fetch' | 'intobq' | 'unifiy' | 'topg' | 'polling' | 'download'
    >
{
  private static statusLogger = JobStatusService.insertFlowStatus;

  static async fetchData(
    job: Job,
  ): Promise<ShopifyJobData<{ bulkOpId: string }>> {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);
      await JobStatusService.setAllJobsToWaiting(
        dbService.knex,
        job.data.providerServiceId,
      );
      const { accessToken, shopName, apiVersion } =
        await getJobData<ShopifyJobData<{ fileUrl: string }>>(job);

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[SHOPIFY-FLOW][STEP 1] Fetching data from Shopify API',
        }),
        jobId: job.id,
      });

      if (!accessToken) {
        throw new Error('Access token not provided');
      }

      if (!shopName) {
        throw new Error('Shop name not provided');
      }

      const bulkOpId = await ShopifyAPIAdapter.runBulkQuery({
        apiVersion,
        shopName,
        accessToken,
        query: productsBulk,
      });
      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[SHOPIFY-FLOW][STEP 1] Bulk operation created successfully',
        }),
        bulkOpId,
        jobId: job.id,
      });

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

      const currency = await ShopifyAPIAdapter.getCurrency({
        accessToken,
        shopName,
        apiVersion,
      });

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[SHOPIFY-FLOW][STEP 5] Currency fetched successfully',
        }),
        currency,
        jobId: job.id,
      });

      const locale = await ShopifyAPIAdapter.getPrimaryLocale({
        accessToken,
        shopName,
        apiVersion,
      });

      return {
        ...job.data,
        locale,
        currency,
        bulkOpId,
      };
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message || 'Unknown error',
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message:
            '[SHOPIFY-FLOW][STEP 1] Failed to fetch products from Shopify API',
        }),
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error?.stack,
        jobId: job.id,
      });

      throw error;
    }
  }

  static async polling(
    job: Job,
    token?: string,
  ): Promise<ShopifyJobData<{ bulkOpId: string; dataUrl: string }>> {
    const { accessToken, shopName, apiVersion, bulkOpId } =
      await getJobData<ShopifyJobData<{ bulkOpId: string }>>(job);

    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[SHOPIFY-FLOW][STEP 2] Polling Shopify bulk operation',
        }),
        bulkOpId,
        jobId: job.id,
      });

      const dataUrl = await ShopifyAPIAdapter.pollBulkOperation({
        apiVersion,
        shopName,
        accessToken,
        bulkOpId,
        timeout: 60000, // 60 seconds timeout for polling
      });

      logger.info({
        message: formatJobLog({
          ...job.data,
          message:
            '[SHOPIFY-FLOW][STEP 2] Bulk operation completed successfully',
        }),
        bulkOpId,
        dataUrl,
        jobId: job.id,
      });

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

      return {
        ...job.data,
        dataUrl,
      };
    } catch (error: any) {
      if (error.message === 'Polling timed out') {
        logger.warn({
          message: formatJobLog({
            ...job.data,
            message:
              '[SHOPIFY-FLOW][STEP 2] Bulk operation polling timed out. Re-adding job with delay',
          }),
          bulkOpId,
          jobId: job.id,
          delayMs: 30000,
        });

        await job.moveToDelayed(Date.now() + 30000, token); // 30 seconds delay before retry

        throw new DelayedError(
          'Bulk operation polling timed out, job re-added to queue with delay',
        );
      }

      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message || 'Unknown error',
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message:
            '[SHOPIFY-FLOW][STEP 2] Failed to poll Shopify bulk operation',
        }),
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error?.stack,
        bulkOpId,
        jobId: job.id,
      });

      throw error;
    }
  }

  static async download(
    job: Job,
  ): Promise<ShopifyJobData<{ fileUrl: string }>> {
    this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);
    const { dataUrl, bulkOpId } =
      await getJobData<ShopifyJobData<{ dataUrl: string; bulkOpId: string }>>(
        job,
      );

    const options = {
      resumable: true,
      metadata: {
        contentType: 'csv',
        cacheControl: 'public, max-age=31536000',
      },
    };

    const gcpStorageService = new GoogleCloudStorageService();
    const time = new Date()
      .toISOString()
      .replace(/[-:T.]/g, '')
      .slice(0, 16);

    const fileName = `product-sync/shopify-stream-${time}.json`;

    const readableStream: AxiosResponse<Readable> = await axios.get(dataUrl, {
      method: 'get',
      responseType: 'stream',
    });

    logger.info({
      message: formatJobLog({
        ...job.data,
        message: '[SHOPIFY-FLOW][STEP 3] Data fetched successfully',
      }),
      dataUrl,
      bulkOpId,
      jobId: job.id,
    });

    const fileUrl = await gcpStorageService.uploadFromAxiosStream(
      config.GCP_BUCKET_NAME,
      fileName,
      readableStream,
      options,
    );

    logger.info({
      message: formatJobLog({
        ...job.data,
        message:
          '[SHOPIFY-FLOW][STEP 3] Data fetched and uploaded to GCP successfully',
      }),
      fileName,
      fileUrl,
      jobId: job.id,
    });

    this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

    return {
      ...job.data,
      fileUrl,
    };
  }

  static async moveToBQ(
    job: Job,
  ): Promise<ShopifyJobData<{ fileUrl: string; tempTableId: string }>> {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);

      const { fileUrl, providerServiceId, databaseId } =
        await getJobData<ShopifyJobData<{ fileUrl: string }>>(job);

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[SHOPIFY-FLOW][STEP 4] Moving data to BigQuery',
        }),
        jobId: job.id,
      });

      const hashProviderServiceId = hashProvider(providerServiceId);

      const tempTableId = `temp_shopify_products_${databaseId}_${hashProviderServiceId}_${Date.now()}`;

      const bigQuery = new BigQueryClient();

      const externalTableSql = externalTableQuery(tempTableId, {
        dataset: DATASET_ID,
        uri: decodeURIComponent(fileUrl),
        format: 'NEWLINE_DELIMITED_JSON',
        expiry: '3 DAY',
      });

      await bigQuery.runQueryWithResult({ query: externalTableSql });

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[SHOPIFY-FLOW][STEP 4] Data moved to BigQuery successfully',
        }),
        fileUrl,
        tempTableId,
        jobId: job.id,
      });

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

      return {
        ...job.data,
        tempTableId,
      };
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message,
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message:
            '[SHOPIFY-FLOW][STEP 4] Error loading Shopify JSON into BigQuery',
        }),
        error: error?.message,
        stack: error?.stack,
        jobId: job.id,
      });
      throw error;
    }
  }

  static async unifyData(
    job: Job,
  ): Promise<ShopifyJobData<{ fileUrl: string; tempTableId: string }>> {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);

      const {
        locale,
        providerServiceId,
        databaseId,
        tempTableId,
        shopName,
        currency,
      } =
        await getJobData<
          ShopifyJobData<{ fileUrl: string; tempTableId: string }>
        >(job);

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[SHOPIFY-FLOW][STEP 5] Unifying data in BigQuery',
        }),
        tempTableId,
        jobId: job.id,
      });

      const targetTableId = PRODUCTS_TABLE;

      const bigQuery = new BigQueryClient();
      const deleteQuery = deleteProductQuery({
        datasetId: DATASET_ID,
        tableId: targetTableId,
      });

      const insertQuery = generateShopifyInsertQuery({
        datasetId: DATASET_ID,
        targetTableId,
        sourceTableId: tempTableId,
        metadata: {
          databaseId,
          providerServiceId,
          locale,
          currency,
          shopDomain: `${shopName}.myshopify.com`,
        },
      });

      const query = `
      ${deleteQuery}
      ${insertQuery}
      `;

      await bigQuery.runQueryWithResult({
        query: query,
        queryOptions: {
          params: {
            databaseId,
            providerServiceId,
          },
        },
      });

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[SHOPIFY-FLOW][STEP 5] Data unified successfully',
        }),
        jobId: job.id,
      });

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

      return {
        ...job.data,
        locale,
        currency,
      };
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message,
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message: '[SHOPIFY-FLOW][STEP 5] Error unifying data',
        }),
        error: error?.message,
        stack: error?.stack,
        jobId: job.id,
      });
      throw Error('Error unifying data');
    }
  }

  static async writeToPg(job: Job): Promise<ShopifyJobData> {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);

      const { databaseId, providerServiceId } =
        await getJobData<ShopifyJobData>(job);

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[SHOPIFY-FLOW][STEP 6] Writing data to Postgres',
        }),
        jobId: job.id,
      });

      const postgresSyncService = new PostgresSyncService(dbService);

      await postgresSyncService.syncDataToPostgres(
        new BigQueryClient(),
        databaseId,
        providerServiceId,
        DATASET_ID,
        PRODUCTS_TABLE,
      );

      logger.info({
        message: formatJobLog({
          ...job.data,
          message:
            '[SHOPIFY-FLOW][STEP 6] Data written to Postgres successfully',
        }),
        jobId: job.id,
      });

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

      return job.data;
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message,
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message: '[SHOPIFY-FLOW][STEP 6] Error writing to Postgres',
        }),
        error: error?.message,
        stack: error?.stack,
        jobId: job.id,
      });
      throw Error('Error writing to Postgres');
    }
  }

  static async generateEmbeddings(job: Job): Promise<ShopifyJobData> {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);
      const { databaseId, locale, providerServiceId } =
        await getJobData<ShopifyJobData>(job);

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[SHOPIFY-FLOW][STEP 7] Generating embeddings',
        }),
        locale,
        jobId: job.id,
      });

      await EmbeddingService.storeEmbeddings(
        providerServiceId,
        locale,
        databaseId,
      );

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);
      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[SHOPIFY-FLOW][STEP 7] Flow completed successfully',
        }),
        locale,
        jobId: job.id,
      });
      return job.data;
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message,
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message: '[SHOPIFY-FLOW][STEP 7] Error storing embeddings',
        }),
        error: error?.message,
        stack: error?.stack,
        locale: job.data.locale,
        jobId: job.id,
      });
      throw Error('Error storing embeddings');
    }
  }
}
