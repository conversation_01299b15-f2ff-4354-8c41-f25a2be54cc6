import { <PERSON><PERSON>lowD<PERSON>, FlowChildJobHandler } from '../interfaces/FetchTypes';
import { Readable } from 'node:stream';
import { Job } from 'bullmq';
import { GoogleCloudStorageService } from '../services/GoogleCloudStorageService';
import { logger } from '@om/logger';
import { BigQueryClient } from './BigQuery';
import { externalTableQuery } from '../queries/table';
import {
  deleteProductQuery,
  generateGoMagCategoryInsertQuery,
  generateGoMagInsertQuery,
} from '../queries/insertQueries';
import { getJobData } from '../common/job';
import { PostgresSyncService } from '../services/PostgresSyncService';
import { dbService } from '../services/PostgresService';
import config from '../Config';
import { JobStatusService } from '../services/JobStatusService';
import { JobStatusEnum } from '../enums/JobStatusEnum';
import { hashProvider } from '../utils/ProviderServiceIdHash';
import EmbeddingService from '../services/EmbeddingService';
import { GoMagApiAdapter } from './GoMagApiAdapter';
import { GoMagProduct, GoMagProductsResponse } from '../interfaces/GoMagTypes';
import { formatJobLog } from '../utils/Log';

if (!config.GCP_BIGQUERY_DATASET || !config.GCP_BIGQUERY_PRODUCTS_TABLE) {
  throw new Error('GCP_BIGQUERY_DATASET is not defined in the configuration.');
}
const DATASET_ID = config.GCP_BIGQUERY_DATASET;
const PRODUCTS_TABLE = config.GCP_BIGQUERY_PRODUCTS_TABLE;
const CATEGORIES_TABLE = config.GCP_BIGQUERY_CATEGORIES_TABLE;

type GoMagJobProperties = {
  apiKey: string; // aka ApiKey
  providerServiceId: string; // aka: ApiShop
  databaseId: number;
  locale: string;
  currency: string;
};

type GoMagJobData<T = Record<string, unknown>> = T &
  JobFlowData<GoMagJobProperties>;

function convertNumericKeysToArray(obj: any): any {
  if (typeof obj !== 'object' || obj === null) return obj;

  const keys = Object.keys(obj);
  const allNumeric = keys.every((key) => /^\d+$/.test(key));

  if (allNumeric) {
    return keys.map((key) => convertNumericKeysToArray(obj[key]));
  }

  const result: { [key: string]: any } = {};

  for (const key in obj) {
    result[key] = convertNumericKeysToArray(obj[key]);
  }

  return result;
}

export class GoMagAdapter
  implements FlowChildJobHandler<'fetch' | 'intobq' | 'unifiy' | 'topg'>
{
  private static statusLogger = JobStatusService.insertFlowStatus;

  static async *fetchProductPages(
    shopUrl: string,
    apiKey: string,
  ): AsyncGenerator<GoMagProductsResponse> {
    let pageNum = 1;
    let totalPages = 1;

    do {
      const response = await GoMagApiAdapter.getProducts(
        {
          apiShop: shopUrl,
          apiKey,
        },
        pageNum,
      );

      yield response;
      totalPages = response.pages;
      pageNum++;
    } while (pageNum <= totalPages);
  }

  static async fetchData(job: Job): Promise<
    GoMagJobData<{
      fileUrl: string;
      tempTableId: string;
      categoriesFileUrl: string;
      locale: string;
      currency: string;
    }>
  > {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);
      await JobStatusService.setAllJobsToWaiting(
        dbService.knex,
        job.data.providerServiceId,
      );

      logger.info(
        formatJobLog({
          ...job.data,
          message: '[GOMAG-FLOW][STEP 1]: Fetching data from GoMagCommerce API',
        }),
      );

      const { apiKey, providerServiceId } = job.data;
      const gcpStorageService = new GoogleCloudStorageService(
        config.GCP_PROJECT_ID,
        config.GCP_API_PROJECT_CREDS,
      );

      const time = new Date()
        .toISOString()
        .replace(/[-:T.]/g, '')
        .slice(0, 16);

      const fileName = `product-sync/gomag-stream-${time}.json`;
      const productsStream = new Readable({
        read() {
          // No-op
        },
      });

      const uploadPromise = gcpStorageService.uploadFromDirectStream(
        config.GCP_BUCKET_NAME,
        fileName,
        productsStream,
        {
          resumable: false,
          gzip: true,
          metadata: {
            contentType: 'application/json',
          },
        },
      );

      for await (const { page, pages, products } of this.fetchProductPages(
        providerServiceId,
        apiKey,
      )) {
        const productsArray = Object.values(products).map((product) => ({
          ...product,
          categories: product.categories.flat(),
          attributes: null,
        }));

        if (!Array.isArray(productsArray)) {
          throw new Error('Invalid response: products data is not an array');
        }

        productsArray.forEach((product: GoMagProduct) => {
          productsStream.push(`${JSON.stringify(product)}\n`);
        });

        logger.info(
          formatJobLog({
            ...job.data,
            message: `[GOMAG-FLOW][SUB-JOB] Fetched page ${page} of ${pages}`,
          }),
        );
      }

      productsStream.push(null);

      const fileUrl = await uploadPromise;

      logger.info(
        formatJobLog({
          ...job.data,
          message: '[GOMAG-FLOW] Data fetched and upladed to GCP successfully',
        }),
      );

      const { categories } = await GoMagApiAdapter.getCategories({
        apiShop: providerServiceId,
        apiKey: apiKey,
      });

      const categoriesArray = convertNumericKeysToArray(categories);

      const ndjsonCategories = categoriesArray
        .map((category: Record<string, any>) => JSON.stringify(category))
        .join('\n');

      const categoriesFileName = `product-sync/gomag-temp-categories-${time}.json`;

      const categoriesFileUrl = await gcpStorageService.uploadFromString(
        config.GCP_BUCKET_NAME,
        categoriesFileName,
        ndjsonCategories,
        {
          resumable: true,
          metadata: {
            contentType: 'application/json',
            cacheControl: 'public, max-age=31536000',
          },
        },
      );

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: 'Categories NDJSON uploaded successfully',
        }),
        data: { categoriesFileUrl },
      });

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

      const locale = await GoMagApiAdapter.getLocale({
        apiShop: providerServiceId,
        apiKey,
      });

      const currency = await GoMagApiAdapter.getCurrency({
        apiShop: providerServiceId,
        apiKey,
      });

      return {
        ...job.data,
        fileUrl,
        categoriesFileUrl,
        locale,
        currency,
      };
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message || 'Unknown error',
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message: 'Failed to fetch products from GoMagCommerce API',
        }),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  static async moveToBQ(job: Job): Promise<
    GoMagJobData<{
      fileUrl: string;
      tempTableId: string;
      catTempTableId: string;
    }>
  > {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[GOMAG-FLOW][STEP 2]: Moving data to BigQuery',
        }),
      });
      const { fileUrl, categoriesFileUrl, providerServiceId, databaseId } =
        await getJobData<
          GoMagJobData<{ fileUrl: string; categoriesFileUrl: string }>
        >(job);

      const hashProviderServiceId = hashProvider(providerServiceId);

      const tempTableId = `temp_gomag_products_${databaseId}_${hashProviderServiceId}_${Date.now()}`;
      const catTempTableId = `temp_gomag_categories_${databaseId}_${hashProviderServiceId}_${Date.now()}`;

      const bigQuery = new BigQueryClient();

      const externalTableSql = externalTableQuery(tempTableId, {
        dataset: DATASET_ID,
        uri: decodeURIComponent(fileUrl),
        format: 'NEWLINE_DELIMITED_JSON',
        expiry: '3 DAY',
      });

      const categoriesExternalTableSql = externalTableQuery(catTempTableId, {
        dataset: DATASET_ID,
        uri: decodeURIComponent(categoriesFileUrl),
        format: 'NEWLINE_DELIMITED_JSON',
        expiry: '3 DAY',
      });

      await bigQuery.runQueryWithResult({ query: externalTableSql });
      await bigQuery.runQueryWithResult({ query: categoriesExternalTableSql });

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[GOMAG-FLOW] Data moved to BigQuery successfully',
        }),
      });

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

      return {
        ...job.data,
        tempTableId,
        catTempTableId,
      };
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message,
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message: 'Error loading gomag json into BigQuery:',
        }),
        error: error?.message,
        stack: error?.stack,
      });
      throw error;
    }
  }

  static async unifyData(
    job: Job,
  ): Promise<GoMagJobData<{ fileUrl: string; tempTableId: string }>> {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);

      const {
        providerServiceId,
        databaseId,
        tempTableId,
        catTempTableId,
        locale,
        currency,
      } = await getJobData<
        GoMagJobData<{
          fileUrl: string;
          tempTableId: string;
          catTempTableId: string;
        }>
      >(job);
      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[GOMAG-FLOW][STEP 3]: Unifying data in BigQuery',
        }),
      });
      const targetTableId = PRODUCTS_TABLE;
      const targetCategoriesTableId = CATEGORIES_TABLE;

      const bigQuery = new BigQueryClient();
      const deleteQuery = deleteProductQuery({
        datasetId: DATASET_ID,
        tableId: targetTableId,
      });

      const insertQuery = generateGoMagInsertQuery({
        datasetId: DATASET_ID,
        targetTableId,
        sourceTableId: tempTableId,
        metadata: {
          databaseId,
          providerServiceId,
          locale,
          currency,
        },
      });

      const categoryQuery = generateGoMagCategoryInsertQuery({
        datasetId: DATASET_ID,
        targetTableId: targetCategoriesTableId,
        sourceTableId: catTempTableId,
        metadata: {
          databaseId,
          providerServiceId,
          locale: 'ro',
          currency: 'Lei',
        },
      });

      const query = `
      ${deleteQuery}
      ${insertQuery}
      `;

      await bigQuery.runQueryWithResult({
        query: query,
        queryOptions: {
          params: {
            databaseId,
            providerServiceId,
          },
        },
      });

      await bigQuery.runQueryWithResult({
        query: categoryQuery,
        queryOptions: {
          params: {
            databaseId,
            providerServiceId,
          },
        },
      });

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[GOMAG-FLOW] Data unified successfully',
        }),
      });

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

      return job.data;
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message,
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message: 'Error unifying data:',
        }),
        error: error?.message,
        stack: error?.stack,
      });
      throw Error('Error unifying data');
    }
  }

  static async writeToPg(job: Job): Promise<GoMagJobData> {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);

      const { databaseId, providerServiceId } =
        await getJobData<GoMagJobData>(job);

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: 'GOMAG-FLOW][STEP 4]: Writing data to Postgres',
        }),
      });

      const postgresSyncService = new PostgresSyncService(dbService);

      await postgresSyncService.syncDataToPostgres(
        new BigQueryClient(),
        databaseId,
        providerServiceId,
        DATASET_ID,
        PRODUCTS_TABLE,
      );

      await postgresSyncService.syncCategoriesToPostgres(
        new BigQueryClient(),
        databaseId,
        providerServiceId,
        DATASET_ID,
        CATEGORIES_TABLE,
      );

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[GOMAG-FLOW] Data written to Postgres successfully',
        }),
      });

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

      return job.data;
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message,
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message: 'Error writing to Postgres:',
        }),
        error: error?.message,
        stack: error?.stack,
      });
      throw Error('Error writing to Postgres');
    }
  }

  static async generateEmbeddings(job: Job): Promise<GoMagJobData> {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);
      const { apiKey, databaseId, providerServiceId } =
        await getJobData<GoMagJobData>(job);

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[GOMAG-FLOW] [STEP 5] Generating embeddings',
        }),
      });

      const locale = await GoMagApiAdapter.getLocale({
        apiShop: providerServiceId,
        apiKey,
      });

      await EmbeddingService.storeEmbeddings(
        providerServiceId,
        locale,
        databaseId,
      );

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);
      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[GOMAG-FLOW] Flow completed successfully.',
        }),
      });
      return job.data;
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message,
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message: 'Error storing embeddings:',
        }),
        error: error?.message,
        stack: error?.stack,
      });
      throw Error('Error storing embeddings');
    }
  }

  static async storeShopSettings(job: Job): Promise<GoMagJobData> {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);
      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[SETTINGS]: Fetching and save store settings',
        }),
      });
      const { locale, currency } = await getJobData<GoMagJobData>(job);


      const postgresSyncService = new PostgresSyncService(dbService);

      await postgresSyncService.saveShopSettings(
        job.data.databaseId,
        job.data.providerServiceId,
        locale,
        currency,
        true,
      );

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: `[SETTINGS] Store settings fetched successfully: ${locale}, ${currency}`,
        }),
      });

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

      return {
        ...job.data,
      };
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message || 'Unknown error',
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message: 'Failed to fetch store settings from GoMagCommerce API',
        }),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw Error('Failed to fetch store settings from GoMagCommerce API');
    }
  }
}
