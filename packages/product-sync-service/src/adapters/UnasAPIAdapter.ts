import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { parseXml } from '../common/xml';
import { logger } from '@om/logger';
import { UnasProductResponse } from '../interfaces/UnasProducts';

const serviceLogger = logger.child({ service: 'UnasAPIAdapter' });

interface UnasConfig {
  baseURL?: string;
  timeout?: number;
}

interface LoginResponse {
  Login?: {
    Token?: string;
    Error?: string;
    WebshopInfo?: {
      Languages?: Language | undefined;
    };
  };
}

export interface Language {
  Language: {
    Code: string;
    Default: 'yes' | 'no';
  };
}

interface ProductDBResponse {
  getProductDB?: {
    Url: string;
  };
}

class UnasError extends Error {
  constructor(
    message: string,
    public readonly code?: string,
  ) {
    super(message);
    this.name = 'UnasError';
  }
}

export class UnasAPIAdapter {
  public static readonly DEFAULT_BASE_URL = 'https://api.unas.eu';
  private static readonly DEFAULT_TIMEOUT = 120000;

  private readonly api: AxiosInstance;
  private token: string | null = null;

  constructor(config: UnasConfig = {}) {
    this.api = axios.create({
      baseURL: config.baseURL || UnasAPIAdapter.DEFAULT_BASE_URL,
      timeout: config.timeout || UnasAPIAdapter.DEFAULT_TIMEOUT,
    });

    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        serviceLogger.error({
          message: 'API request failed',
          error: error.message,
          status: error.response?.status,
          url: error.config?.url,
        });

        throw new UnasError(
          error.response?.data?.message || error.message,
          error.response?.status?.toString(),
        );
      },
    );
  }

  private async makeRequest<T>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.api.request(config);
      return parseXml(response.data) as T;
    } catch (error) {
      if (error instanceof UnasError) throw error;
      throw new UnasError(
        error instanceof Error ? error.message : 'Unknown error',
      );
    }
  }

  async login(apiKey: string): Promise<Language | undefined> {
    const data = `<?xml version="1.0" encoding="UTF-8" ?>
    <Params>
      <ApiKey>${apiKey}</ApiKey>
      <WebshopInfo>true</WebshopInfo>
    </Params>`;

    try {
      const response = await this.makeRequest<LoginResponse | undefined>({
        method: 'post',
        url: '/shop/Login',
        data,
      });

      const token = response?.Login?.Token;
      if (!token) {
        throw new UnasError(response?.Login?.Error || 'Invalid login response');
      }

      this.token = token;

      serviceLogger.info({
        message:
          '[UNAS-FLOW] [STEP-1] Login successful, token and langs stored.',
      });

      return response?.Login?.WebshopInfo?.Languages;
    } catch (error) {
      serviceLogger.warn({ message: 'Unas login failed', error });
      throw error;
    }
  }

  async getCategories(): Promise<{ id: number; name: string }[]> {
    interface CategoriesResponse {
      Categories: {
        Category: { Id: number; Name: string }[];
      };
    }

    const response = await this.makeRequest<CategoriesResponse>({
      method: 'post',
      url: '/shop/getCategory',
      maxBodyLength: Infinity,
      headers: {
        Authorization: `Bearer ${this.token}`,
        'Content-Type': 'application/xml',
      },
    });

    if (
      !response ||
      !response.Categories ||
      !Array.isArray(response.Categories.Category)
    ) {
      throw new UnasError(
        'Invalid response structure: Categories.Category not found',
      );
    }

    const categories = (response.Categories.Category as any[]).map(
      (category) => ({
        id: category.Id,
        name: category.Name,
        slug: category.Url.split('/').pop() || '',
        parent: category.Parent.Id || null,
      }),
    );

    serviceLogger.info({
      message: '[UNAS-FLOW] [STEP-1] Categories fetched successfully',
    });
    return categories;
  }

  async getProductDB(): Promise<string> {
    const data = `<?xml version="1.0" encoding="UTF-8" ?>
    <Params>
        <Format>csv</Format>
        <Compress>no</Compress>
        <GetStatus>1</GetStatus>
        <GetPrice>1</GetPrice>
        <GetPriceSpecial>1</GetPriceSpecial>
        <GetPriceSale>1</GetPriceSale>
        <GetDescriptionShort>1</GetDescriptionShort>
        <GetStock>1</GetStock>
        <GetImage>1</GetImage>
        <GetName>1</GetName>
        <GetURL>1</GetURL>
        <GetType>1</GetType>
        <GetLang>1</GetLang>
        <GetCategory>1</GetCategory>
    </Params>`;

    try {
      const response = await this.makeRequest<ProductDBResponse>({
        method: 'post',
        url: '/shop/getProductDB',
        data,
        maxBodyLength: Infinity,
        headers: {
          Authorization: `Bearer ${this.token}`,
          'Content-Type': 'application/xml',
        },
      });

      serviceLogger.info({
        message: '[UNAS-FLOW] [STEP-1] Unas getProductDB successful',
      });
      const url = response?.getProductDB?.Url;
      if (!url) {
        throw new UnasError('Invalid getProductDB response');
      }

      return url;
    } catch (error) {
      serviceLogger.error({ message: 'Unas getProductDB failed', error });
      throw error;
    }
  }

  async getCategoriesChunk(
    limitNum: number,
    limitStart: number,
  ): Promise<{ id: number; name: string }[]> {
    interface CategoriesResponse {
      Categories?: {
        Category?: {
          Id: number;
          Name: string;
          Url?: string;
          Parent?: { Id: number };
        }[];
      };
    }

    logger.info({
      message: `[UNAS-FLOW] [STEP-1] Fetching categories from ${limitStart} to ${
        limitStart + limitNum - 1
      }`,
    });

    const data = `<?xml version="1.0" encoding="UTF-8" ?>
    <Params>
      <LimitNum>${limitNum}</LimitNum>
      <LimitStart>${limitStart}</LimitStart>
    </Params>`;

    const response = await this.makeRequest<CategoriesResponse>({
      method: 'post',
      url: '/shop/getCategory',
      data,
      maxBodyLength: Infinity,
      headers: {
        Authorization: `Bearer ${this.token}`,
        'Content-Type': 'application/xml',
      },
    });

    if (!response || !response.Categories || !response.Categories.Category) {
      return [];
    }

    const categories = (response.Categories.Category as any[])
      .filter((category) => category.Id && category.Name)
      .map((category) => ({
        id: category.Id,
        name: category.Name ?? 'No Name',
        slug: category.Url ? category.Url.split('/').pop() || '' : '',
        parent: category.Parent?.Id || null,
      }));

    serviceLogger.info({
      message: `[UNAS-FLOW] [STEP-1] Categories fetched successfully: ${categories.length} items`,
    });

    return categories;
  }

  async getProductChunk(
    limit: number,
    limitStart: number,
  ): Promise<UnasProductResponse> {
    const data = `<?xml version="1.0" encoding="UTF-8" ?>
    <Params>
      <Status>live</Status>
      <StatusBase>1,2</StatusBase>
      <LimitNum>${limit}</LimitNum>
      <LimitStart>${limitStart}</LimitStart>
    </Params>`;

    try {
      const response = await this.makeRequest<Promise<UnasProductResponse>>({
        method: 'post',
        url: '/shop/getProduct',
        data,
        maxBodyLength: Infinity,
        headers: {
          Authorization: `Bearer ${this.token}`,
          'Content-Type': 'application/xml',
        },
      });

      serviceLogger.info({
        message:
          '[UNAS-FLOW] [STEP-1] Unas product chunk downloaded successful',
      });

      return response;
    } catch (error) {
      serviceLogger.error({ message: 'Unas getProductChunk failed', error });
      throw error;
    }
  }
}
