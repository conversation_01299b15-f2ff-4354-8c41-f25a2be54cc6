import axios from 'axios';
import { logger } from '@om/logger';
import config from '../Config';

export class ShopifyAPIAdapter {
  static DEFAULT_API_VERSION = config.SHOPIFY_API_VERSION;

  static async runBulkQuery({
    accessToken,
    shopName,
    apiVersion = this.DEFAULT_API_VERSION,
    query,
  }: {
    accessToken: string;
    shopName: string;
    apiVersion?: string;
    query: string;
  }) {
    const graphqlEndpoint = `https://${shopName}.myshopify.com/admin/api/${apiVersion}/graphql.json`;

    const response = await axios.post(
      graphqlEndpoint,
      { query },
      {
        headers: {
          'X-Shopify-Access-Token': accessToken,
          'Content-Type': 'application/json',
        },
      },
    );

    if (
      response.data.errors ||
      response.data.data.bulkOperationRunQuery.userErrors.length > 0
    ) {
      logger.error({
        message: 'Bulk operation initiation failed',
        data: response.data,
      });
      throw new Error('Failed to initiate bulk operation');
    }

    return response.data.data.bulkOperationRunQuery.bulkOperation.id;
  }

  static async pollBulkOperation({
    accessToken,
    shopName,
    apiVersion = this.DEFAULT_API_VERSION,
    interval = 5000,
    timeout = 10000,
    bulkOpId,
  }: {
    accessToken: string;
    shopName: string;
    apiVersion?: string;
    interval?: number;
    timeout?: number;
    bulkOpId?: string;
  }): Promise<string> {
    const graphqlEndpoint = `https://${shopName}.myshopify.com/admin/api/${apiVersion}/graphql.json`;
    const startTime = Date.now();

    const query = bulkOpId
      ? `query {
      node(id: "${bulkOpId}") {
        ... on BulkOperation {
          id
          status
          url
          errorCode
          createdAt
          completedAt
        }
      }
    }`
      : `query {
      currentBulkOperation {
        id
        status
        url
        errorCode
        createdAt
        completedAt
      }
    }`;

    return new Promise((resolve, reject) => {
      const intervalId = setInterval(async () => {
        try {
          const response = await axios.post(
            graphqlEndpoint,
            { query },
            {
              headers: {
                'X-Shopify-Access-Token': accessToken,
                'Content-Type': 'application/json',
              },
            },
          );

          const op = bulkOpId
            ? response.data.data.node
            : response.data.data.currentBulkOperation;

          if (op.status === 'COMPLETED') {
            clearInterval(intervalId);
            resolve(op.url);
          } else if (op.status === 'FAILED' || op.errorCode) {
            clearInterval(intervalId);
            reject(
              new Error(
                `Bulk operation failed: ${op.errorCode || 'Unknown error'}`,
              ),
            );
          } else if (Date.now() - startTime > timeout) {
            clearInterval(intervalId);
            reject(new Error('Polling timed out'));
          }
        } catch (err) {
          clearInterval(intervalId);
          reject(err);
        }
      }, interval);
    });
  }

  static async getProducts(options: {
    accessToken: string;
    shopName: string;
    page?: number;
    apiVersion?: string;
    productIds?: string[];
  }) {
    const {
      accessToken,
      shopName,
      page = 1,
      apiVersion = this.DEFAULT_API_VERSION,
      productIds,
    } = options;
    try {
      const graphqlEndpoint = `https://${shopName}.myshopify.com/admin/api/${apiVersion}/graphql.json`;
      const first = 250;

      let cursor = null;

      if (page > 1) {
        let previousCursor = null;
        for (let i = 1; i < page; i++) {
          const response = await this.fetchProductsPage({
            graphqlEndpoint,
            accessToken,
            first,
            cursor: previousCursor,
            productIds,
          });
          const pageInfo = response.data.products.pageInfo;

          if (!pageInfo.hasNextPage) {
            return {
              products: [],
              totalPage: i,
            };
          }

          previousCursor = pageInfo.endCursor;
        }
        cursor = previousCursor;
      }

      const response = await this.fetchProductsPage({
        graphqlEndpoint,
        accessToken,
        first,
        cursor,
        productIds,
      });

      const products = response.data.products.edges.map((edge: any) => {
        const node = edge.node;

        const variants = node.variants.edges.map((variantEdge: any) => {
          const variant = variantEdge.node;
          return {
            id: variant.id,
            product_id: node.id,
            title: variant.title,
            price: variant.price,
            compare_at_price: variant.compareAtPrice,
            sku: variant.sku,
            inventory_quantity: variant.inventoryQuantity,
            inventory_management: variant.inventoryManagement,
            inventory_policy: variant.inventoryPolicy,
          };
        });

        const images = node.images.edges.map((imageEdge: any) => {
          const image = imageEdge.node;
          return {
            id: image.id,
            product_id: node.id,
            src: image.url,
            alt: image.altText,
          };
        });

        return {
          id: node.id.replace('gid://shopify/Product/', ''),
          title: node.title,
          body_html: node.descriptionHtml,
          handle: node.handle,
          status: node.status.toLowerCase(),
          published_at: node.publishedAt,
          created_at: node.createdAt,
          updated_at: node.updatedAt,
          variants: variants,
          images: images,
          image: images.length > 0 ? images[0] : null,
          product_url:
            node.onlineStoreUrl ||
            `https://${shopName}.myshopify.com/products/${node.handle}`,
        };
      });

      const hasNextPage = response.data.products.pageInfo.hasNextPage;
      const totalPage = hasNextPage ? page + 1 : page;

      return {
        products,
        totalPage,
      };
    } catch (error: any) {
      logger.error({
        message: 'Error fetching products from Shopify GraphQL API',
        error: error?.message,
        data: error?.response?.data,
      });
      throw error;
    }
  }

  private static async fetchProductsPage(options: {
    graphqlEndpoint: string;
    accessToken: string;
    first: number;
    cursor: string | null;
    productIds?: string[];
  }) {
    const { graphqlEndpoint, accessToken, first, cursor, productIds } = options;

    const query = `
      query getProducts($first: Int!, $after: String, $ids: [ID!]) {
        products(first: $first, after: $after${
          productIds ? ', query: $query' : ''
        }) {
          pageInfo {
            hasNextPage
            endCursor
          }
          edges {
            node {
              id
              title
              descriptionHtml
              handle
              status
              publishedAt
              createdAt
              updatedAt
              onlineStoreUrl
              variants(first: 250) {
                edges {
                  node {
                    id
                    title
                    price
                    compareAtPrice
                    sku
                    inventoryQuantity
                    inventoryManagement
                    inventoryPolicy
                  }
                }
              }
              images(first: 250) {
                edges {
                  node {
                    id
                    url
                    altText
                  }
                }
              }
            }
          }
        }
      }
    `;

    const variables = {
      first,
      after: cursor,
      query: '',
    };

    // If productIds are provided, add them to the query
    if (productIds && productIds.length > 0) {
      // For Shopify GraphQL, we need to use a query string to filter by IDs
      // Converting array of IDs to a query string like "id:gid://shopify/Product/123 OR id:gid://shopify/Product/456"
      const idsQuery = productIds
        .map((id) => {
          // Check if the ID already has the Shopify prefix
          if (id.startsWith('gid://shopify/Product/')) {
            return `id:${id}`;
          }
          return `id:gid://shopify/Product/${id}`;
        })
        .join(' OR ');

      variables.query = idsQuery;
    }

    const response = await axios.post(
      graphqlEndpoint,
      { query, variables },
      {
        headers: {
          'X-Shopify-Access-Token': accessToken,
          'Content-Type': 'application/json',
        },
      },
    );

    if (response.data.errors) {
      throw new Error(
        response.data.errors.map((e: any) => e.message).join(', '),
      );
    }

    return response.data;
  }

  static async getCurrency(options: {
    accessToken: string;
    shopName: string;
    apiVersion?: string;
  }): Promise<string> {
    const {
      accessToken,
      shopName,
      apiVersion = this.DEFAULT_API_VERSION,
    } = options;
    try {
      const graphqlEndpoint = `https://${shopName}.myshopify.com/admin/api/${apiVersion}/graphql.json`;

      const query = `
        query {
          shop {
            currencyCode
            name
          }
        }
      `;

      const response = await axios.post(
        graphqlEndpoint,
        { query },
        {
          headers: {
            'X-Shopify-Access-Token': accessToken,
            'Content-Type': 'application/json',
          },
        },
      );

      if (response.data.errors) {
        throw new Error(
          response.data.errors.map((e: any) => e.message).join(', '),
        );
      }

      return response.data.data.shop.currencyCode || 'USD';
    } catch (error: any) {
      logger.error({
        message: 'Error fetching shop information from Shopify GraphQL API',
        error: error?.message,
        data: error?.response?.data,
      });

      // Default to USD if there's an error
      return 'USD';
    }
  }

  static async getPrimaryLocale(options: {
    accessToken: string;
    shopName: string;
    apiVersion?: string;
  }): Promise<string> {
    const {
      accessToken,
      shopName,
      apiVersion = this.DEFAULT_API_VERSION,
    } = options;
    try {
      const graphqlEndpoint = `https://${shopName}.myshopify.com/admin/api/${apiVersion}/graphql.json`;

      const query = `
        query {
          shopLocales {
            locale
            primary
          }
        }      
      `;

      const response = await axios.post(
        graphqlEndpoint,
        { query },
        {
          headers: {
            'X-Shopify-Access-Token': accessToken,
            'Content-Type': 'application/json',
          },
        },
      );

      if (response.data.errors) {
        throw new Error(
          response.data.errors.map((e: any) => e.message).join(', '),
        );
      }

      const primaryLocaleObj = response.data.data.shopLocales.find(
        ({ primary }: { locale: string; primary: boolean }) => primary,
      );
      return primaryLocaleObj ? primaryLocaleObj.locale : 'USA';
    } catch (error: any) {
      logger.error({
        message: 'Error fetching shop information from Shopify GraphQL API',
        error: error?.message,
        data: error?.response?.data,
      });

      // Default to USD if there's an error
      return 'USA';
    }
  }
}
