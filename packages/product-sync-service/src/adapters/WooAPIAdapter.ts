import axios from 'axios';
import { logger } from '@om/logger';
import { WooCategory, WooProductResponse } from '../interfaces/WooTypes';
import https from 'https';
export class WooAPIAdapter {
  private static readonly DEFAULT_BASE_URI = '/wp-json/wc/v3';

  static async getProducts(
    consumer_key: string,
    consumer_secret: string,
    shopUrl: string,
    page: number,
  ): Promise<WooProductResponse> {
    try {
      const fields = [
        'id',
        'name',
        'sku',
        'price',
        'regular_price',
        'images',
        'description',
        'parent_id',
        'status',
        'permalink',
        'slug',
        'stock_status',
        'categories',
      ];

      const agent = new https.Agent({ keepAlive: false });

      const request = await axios.get(
        `${shopUrl}${this.DEFAULT_BASE_URI}/products?consumer_key=${consumer_key}&consumer_secret=${consumer_secret}&page=${page}&per_page=100&_fields=${fields.join(',')}`,
        {
          timeout: 10000,
          httpsAgent: agent,
        },
      );
      const products = await request.data;

      const totalPages = request.headers['x-wp-totalpages'];
      return { products, totalPage: totalPages };
    } catch (error) {
      logger.error({
        message: 'Failed to fetch products from woo API',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  static async getCategories(
    consumer_key: string,
    consumer_secret: string,
    shopUrl: string,
  ): Promise<WooCategory[]> {
    try {
      const agent = new https.Agent({ keepAlive: false });
      const request = await axios.get(
        `${shopUrl}${this.DEFAULT_BASE_URI}/products/categories?consumer_key=${consumer_key}&consumer_secret=${consumer_secret}&per_page=100`,
        {
          timeout: 10000,
          httpsAgent: agent,
        },
      );
      const categories = await request.data;
      const mapped = categories.map((category: WooCategory) => ({
        id: category.id,
        name: category.name,
        slug: category.slug,
        parent: category.parent ? category.parent.toString() : '',
      }));

      return mapped;
    } catch (error) {
      logger.error({
        message: 'Failed to fetch categories from woo API',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  static async getCurrency(
    consumer_key: string,
    consumer_secret: string,
    shopUrl: string,
  ): Promise<string> {
    try {
      const agent = new https.Agent({ keepAlive: false });
      const request = await axios.get(
        `${shopUrl}${this.DEFAULT_BASE_URI}/settings/general/woocommerce_currency?consumer_key=${consumer_key}&consumer_secret=${consumer_secret}`,
        {
          timeout: 10000,
          httpsAgent: agent,
        },
      );
      const currency = request.data.value;
      return currency;
    } catch (error) {
      logger.error({
        message: 'Failed to fetch currency from woo API',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  static async getLocale(
    consumer_key: string,
    consumer_secret: string,
    shopUrl: string,
  ): Promise<string> {
    try {
      const agent = new https.Agent({ keepAlive: false });
      const request = await axios.get(
        `${shopUrl}${this.DEFAULT_BASE_URI}/settings/general/woocommerce_default_country?consumer_key=${consumer_key}&consumer_secret=${consumer_secret}`,
        {
          timeout: 10000,
          httpsAgent: agent,
        },
      );
      const locale = request.data.value.split(':')[0].toLowerCase();
      return locale;
    } catch (error) {
      logger.error({
        message: 'Failed to fetch locale from woo API',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }
}
