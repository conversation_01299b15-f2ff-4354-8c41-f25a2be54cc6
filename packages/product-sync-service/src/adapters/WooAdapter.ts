import { Job<PERSON>lowData, FlowChildJobHandler } from '../interfaces/FetchTypes';
import { Readable } from 'node:stream';
import { Job } from 'bullmq';
import { GoogleCloudStorageService } from '../services/GoogleCloudStorageService';
import { logger } from '@om/logger';
import { BigQueryClient } from './BigQuery';
import { externalTableQuery } from '../queries/table';
import {
  deleteProductQuery,
  generateCategoryInsertQuery,
  generateWooInsertQuery,
} from '../queries/insertQueries';
import { getJobData } from '../common/job';
import { PostgresSyncService } from '../services/PostgresSyncService';
import { dbService } from '../services/PostgresService';
import config from '../Config';
import { JobStatusService } from '../services/JobStatusService';
import { JobStatusEnum } from '../enums/JobStatusEnum';
import { hashProvider } from '../utils/ProviderServiceIdHash';
import EmbeddingService from '../services/EmbeddingService';
import { WooAPIAdapter } from './WooAPIAdapter';
import { WooProduct } from '../interfaces/WooTypes';
import { formatJobLog } from '../utils/Log';

if (!config.GCP_BIGQUERY_DATASET || !config.GCP_BIGQUERY_PRODUCTS_TABLE) {
  throw new Error('GCP_BIGQUERY_DATASET is not defined in the configuration.');
}
const DATASET_ID = config.GCP_BIGQUERY_DATASET;
const PRODUCTS_TABLE = config.GCP_BIGQUERY_PRODUCTS_TABLE;
const CATEGORIES_TABLE = config.GCP_BIGQUERY_CATEGORIES_TABLE;

type WooJobProperties = {
  apiKey: string;
  providerServiceId: string;
  databaseId: number;
  locale: string;
  currency: string;
};

type WooJobData<T = Record<string, unknown>> = T &
  JobFlowData<WooJobProperties>;

export class WooAdapter
  implements FlowChildJobHandler<'fetch' | 'intobq' | 'unifiy' | 'topg'>
{
  private static statusLogger = JobStatusService.insertFlowStatus;

  static async fetchData(job: Job): Promise<
    WooJobData<{
      fileUrl: string;
      tempTableId: string;
      categoriesFileUrl: string;
      locale: string;
      currency: string;
    }>
  > {
    try {
      const { consumerKey, consumerSecret, shopUrl } = job.data;

      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);

      const locale = await WooAPIAdapter.getLocale(
        consumerKey,
        consumerSecret,
        shopUrl,
      );

      const currency: string = await WooAPIAdapter.getCurrency(
        consumerKey,
        consumerSecret,
        shopUrl,
      );

      await JobStatusService.setAllJobsToWaiting(
        dbService.knex,
        job.data.providerServiceId,
      );

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[WOO-FLOW][STEP 1]: Fetching data from WooCommerce API',
        }),
      });

      const gcpStorageService = new GoogleCloudStorageService(
        config.GCP_PROJECT_ID,
        config.GCP_API_PROJECT_CREDS,
      );

      const time = new Date()
        .toISOString()
        .replace(/[-:T.]/g, '')
        .slice(0, 16);

      const fileName = `product-sync/woo-stream-${time}.json`;
      const productsStream = new Readable({
        read() {
          // No-op
        },
      });

      const uploadPromise = gcpStorageService.uploadFromDirectStream(
        config.GCP_BUCKET_NAME,
        fileName,
        productsStream,
        {
          resumable: false,
          gzip: true,
          metadata: {
            contentType: 'application/json',
          },
        },
      );

      let page = 1;
      let totalPages = 1;

      do {
        const { products, totalPage } = await WooAPIAdapter.getProducts(
          consumerKey,
          consumerSecret,
          shopUrl,
          page,
        );
        totalPages = totalPage;

        if (!Array.isArray(products)) {
          throw new Error('Invalid response: products data is not an array');
        }

        products.forEach((product: WooProduct) => {
          productsStream.push(`${JSON.stringify(product)}\n`);
        });

        logger.info({
          message: formatJobLog({
            ...job.data,
            message: `[WOO-FLOW][SUB-JOB] Fetched page ${page} of ${totalPages}`,
          }),
        });

        page++;
      } while (page <= totalPages);

      productsStream.push(null);

      const fileUrl = await uploadPromise;

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[WOO-FLOW] Data fetched and upladed to GCP successfully',
        }),
      });

      const categories = await WooAPIAdapter.getCategories(
        consumerKey,
        consumerSecret,
        shopUrl,
      );

      const ndjsonCategories = categories
        .map((category) => JSON.stringify(category))
        .join('\n');

      const categoriesFileName = `product-sync/woo-temp-categories-${time}.json`;

      const categoriesFileUrl = await gcpStorageService.uploadFromString(
        config.GCP_BUCKET_NAME,
        categoriesFileName,
        ndjsonCategories,
        {
          resumable: true,
          metadata: {
            contentType: 'application/json',
            cacheControl: 'public, max-age=31536000',
          },
        },
      );

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: 'Categories NDJSON uploaded successfully',
        }),
        data: { categoriesFileUrl },
      });

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

      return {
        ...job.data,
        locale,
        currency,
        fileUrl,
        categoriesFileUrl,
      };
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message || 'Unknown error',
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message: 'Failed to fetch products from WooCommerce API',
        }),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  static async moveToBQ(job: Job): Promise<
    WooJobData<{
      fileUrl: string;
      tempTableId: string;
      catTempTableId: string;
      locale: string;
      currency: string;
    }>
  > {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[WOO-FLOW][STEP 2]: Moving data to BigQuery',
        }),
      });
      const {
        fileUrl,
        categoriesFileUrl,
        providerServiceId,
        databaseId,
        locale,
        currency,
      } =
        await getJobData<
          WooJobData<{ fileUrl: string; categoriesFileUrl: string }>
        >(job);

      const hashProviderServiceId = hashProvider(providerServiceId);

      const tempTableId = `temp_woo_products_${databaseId}_${hashProviderServiceId}_${Date.now()}`;
      const catTempTableId = `temp_woo_categories_${databaseId}_${hashProviderServiceId}_${Date.now()}`;

      const bigQuery = new BigQueryClient();

      const externalTableSql = externalTableQuery(tempTableId, {
        dataset: DATASET_ID,
        uri: decodeURIComponent(fileUrl),
        format: 'NEWLINE_DELIMITED_JSON',
        expiry: '3 DAY',
      });

      const categoriesExternalTableSql = externalTableQuery(catTempTableId, {
        dataset: DATASET_ID,
        uri: decodeURIComponent(categoriesFileUrl),
        format: 'NEWLINE_DELIMITED_JSON',
        expiry: '3 DAY',
      });

      await bigQuery.runQueryWithResult({ query: externalTableSql });
      await bigQuery.runQueryWithResult({ query: categoriesExternalTableSql });

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[WOO-FLOW] Data moved to BigQuery successfully',
        }),
      });

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

      return {
        ...job.data,
        tempTableId,
        catTempTableId,
        locale,
        currency,
      };
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message,
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message: 'Error loading woo json into BigQuery:',
        }),
        error: error?.message,
        stack: error?.stack,
      });
      throw error;
    }
  }

  static async unifyData(
    job: Job,
  ): Promise<WooJobData<{ fileUrl: string; tempTableId: string }>> {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);

      const {
        providerServiceId,
        databaseId,
        tempTableId,
        catTempTableId,
        locale,
        currency,
      } = await getJobData<
        WooJobData<{
          fileUrl: string;
          tempTableId: string;
          catTempTableId: string;
          locale: string;
          currency: string;
        }>
      >(job);
      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[WOO-FLOW][STEP 3]: Unifying data in BigQuery',
        }),
      });
      const targetTableId = PRODUCTS_TABLE;
      const targetCategoriesTableId = CATEGORIES_TABLE;

      const bigQuery = new BigQueryClient();
      const deleteQuery = deleteProductQuery({
        datasetId: DATASET_ID,
        tableId: targetTableId,
      });

      const insertQuery = generateWooInsertQuery({
        datasetId: DATASET_ID,
        targetTableId,
        sourceTableId: tempTableId,
        metadata: {
          databaseId,
          providerServiceId,
          locale,
          currency,
        },
      });

      const categoryQuery = generateCategoryInsertQuery({
        datasetId: DATASET_ID,
        targetTableId: targetCategoriesTableId,
        sourceTableId: catTempTableId,
        metadata: {
          databaseId,
          providerServiceId,
          locale,
          currency: 'HUF',
        },
      });

      const query = `
      ${deleteQuery}
      ${insertQuery}
      `;

      await bigQuery.runQueryWithResult({
        query: query,
        queryOptions: {
          params: {
            databaseId,
            providerServiceId,
          },
        },
      });

      await bigQuery.runQueryWithResult({
        query: categoryQuery,
        queryOptions: {
          params: {
            databaseId,
            providerServiceId,
          },
        },
      });

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[WOO-FLOW] Data unified successfully',
        }),
      });

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

      return {
        ...job.data,
        locale,
        currency,
      };
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message,
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message: 'Error unifying data:',
        }),
        error: error?.message,
        stack: error?.stack,
      });
      throw Error('Error unifying data');
    }
  }

  static async writeToPg(job: Job): Promise<WooJobData> {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);

      const { databaseId, providerServiceId, locale, currency } =
        await getJobData<WooJobData>(job);

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[WOO-FLOW][STEP 4]: Writing data to Postgres',
        }),
      });

      const postgresSyncService = new PostgresSyncService(dbService);

      await postgresSyncService.syncDataToPostgres(
        new BigQueryClient(),
        databaseId,
        providerServiceId,
        DATASET_ID,
        PRODUCTS_TABLE,
      );

      await postgresSyncService.syncCategoriesToPostgres(
        new BigQueryClient(),
        databaseId,
        providerServiceId,
        DATASET_ID,
        CATEGORIES_TABLE,
      );

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[WOO-FLOW] Data written to Postgres successfully',
        }),
      });

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

      return {
        ...job.data,
        currency,
        locale,
      };
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message,
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message: 'Error writing to Postgres:',
        }),
        error: error?.message,
        stack: error?.stack,
      });
      throw Error('Error writing to Postgres');
    }
  }

  static async generateEmbeddings(job: Job): Promise<WooJobData> {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);
      const { databaseId, locale, providerServiceId, currency, } =
        await getJobData<WooJobData>(job);

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[WOO-FLOW] [STEP 5] Generating embeddings',
        }),
      });

      await EmbeddingService.storeEmbeddings(
        providerServiceId,
        locale,
        databaseId,
      );

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);
      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[WOO-FLOW] Flow completed successfully.',
        }),
      });
      return {
        ...job.data,
        currency,
        locale,
      };
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message,
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message: 'Error storing embeddings:',
        }),
        error: error?.message,
        stack: error?.stack,
      });
      throw Error('Error storing embeddings');
    }
  }

  static async storeShopSettings(job: Job): Promise<WooJobData> {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);
      logger.info({
        message: '[SETTINGS]: Fetching and save store settings',
      });
      const { locale, currency } = await getJobData<WooJobData>(job);

      const postgresSyncService = new PostgresSyncService(dbService);

      await postgresSyncService.saveShopSettings(
        job.data.databaseId,
        job.data.providerServiceId,
        locale,
        currency,
        true,
      );

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: `[SETTINGS] Store settings fetched successfully: ${locale}, ${currency}`,
        }),
      });

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

      return {
        ...job.data,
      };
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message || 'Unknown error',
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message: 'Failed to fetch store settings from WooCommerce API',
        }),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw Error('Failed to fetch store settings from WooCommerce API');
    }
  }
}
