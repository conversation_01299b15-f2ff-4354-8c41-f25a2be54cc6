import {
  FlowChildJob<PERSON><PERSON><PERSON>,
  FlowChildJobTypes,
} from '../interfaces/FetchTypes';
import { UnasAdapter } from './UnasAdapter';
import { WooAdapter } from './WooAdapter';

// This function checks if the platform is registered
// and returns the fetcher for that platform
// export function getPlatformFetcher(platform: string) {
//   const fetcher = platformFetchers[platform];
//   if (!fetcher) throw new Error(`No fetcher for platform ${platform}`);
//   return fetcher;
// }

// In this constant we register the fetch types and functions to each platform
// These gonna be used for type checks, or shovws never
// const platformFetchers: Record<string, PlatformFetcher<FetchType>> = {
//   unas: new UnasPlatformFetcher(),
// };

const flowChildJobs: Record<string, FlowChildJobHandler<FlowChildJobTypes>> = {
  unas: new UnasAdapter(),
  woo: new WooAdapter(),
};
