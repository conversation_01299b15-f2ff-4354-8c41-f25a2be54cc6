import {
  BigQuery,
  Query,
  TableField,
  Table,
  RowMetadata,
} from '@google-cloud/bigquery';
import { ResourceStream } from '@google-cloud/paginator';
import { logger } from '@om/logger';
import config from '../Config';

const PROJECT_ID = config.GCP_PROJECT_ID;
const GCP_CREDENTIALS = config.GCP_API_PROJECT_CREDS;

interface CreateTableOptions {
  datasetId: string;
  tableId: string;
  tableOptions: {
    schema: TableField[] | string;
    [key: string]: unknown;
  };
}

interface RunQueryOptions {
  query: string;
  queryOptions?: Query;
}

export class BigQueryClient {
  public client: BigQuery;

  constructor() {
    this.client = new BigQuery({
      credentials: GCP_CREDENTIALS ? JSON.parse(GCP_CREDENTIALS) : undefined,
      projectId: PROJECT_ID,
    });
  }

  /**
   * This method runs a query without waiting for the job to complete in BigQuery and returns the job id.
   * @param query
   * @param queryOptions
   * @returns jobId
   */
  // TODO: add tracing
  async runQueryInBackground<T>(
    query: string,
    queryOptions = {},
    onComplete: (rows: T[]) => void,
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      this.client
        .createQueryJob({
          query,
          ...queryOptions,
        })
        .then(([job]) => {
          job.on('complete', async () => {
            try {
              const [rows] = await job.getQueryResults();
              // Run succes callback
              onComplete && onComplete(rows);

              resolve('DONE');
            } catch (err) {
              reject(err);
            }
          });

          job.on('error', (error: any) => {
            reject(error);
          });
        })
        .catch((e: any) => {
          logger.error('BigQuery job creation error', e);
          reject(e);
        });
    });
  }

  async runQueryWithResult<T>({
    query,
    queryOptions = {},
  }: RunQueryOptions): Promise<T[]> {
    const options = {
      query,
      ...queryOptions,
    };

    return new Promise((resolve, reject) => {
      this.client
        .createQueryJob(options)
        .then(([job]) => {
          job.on('complete', async () => {
            try {
              const [rows] = await job.getQueryResults();
              resolve(rows as T[]);
            } catch (err) {
              reject(err);
            }
          });

          job.on('error', (error: any) => {
            reject(error);
          });
        })
        .catch((e: any) => {
          if (e?.errors?.[0]?.reason !== 'notFound') {
            logger.error('bq job creation error', e);
          }
          reject(e);
        });
    });
  }

  runQueryStream({
    query,
    queryOptions = {},
  }: RunQueryOptions): ResourceStream<RowMetadata> {
    return this.client.createQueryStream({
      query,
      ...queryOptions,
    });
  }

  async createTable({
    datasetId,
    tableId,
    tableOptions,
  }: CreateTableOptions): Promise<Table> {
    const dataset = this.client.dataset(datasetId);
    let table = dataset.table(tableId);
    const [exists] = await table.exists();
    if (!exists) {
      [table] = await dataset.createTable(tableId, tableOptions);
    }
    return table;
  }
}
