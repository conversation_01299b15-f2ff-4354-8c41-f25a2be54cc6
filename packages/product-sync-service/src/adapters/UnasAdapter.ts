import { Job<PERSON>lowData, FlowChildJobHandler } from '../interfaces/FetchTypes';
import { Job } from 'bullmq';
import { GoogleCloudStorageService } from '../services/GoogleCloudStorageService';
import { Language, UnasAPIAdapter } from './UnasAPIAdapter';
import { logger } from '@om/logger';
import { BigQueryClient } from './BigQuery';
import { externalTableQuery } from '../queries/table';
import {
  deleteProductQuery,
  generateCategoryInsertQuery,
  generateUnasInsertQuery,
} from '../queries/insertQueries';
import { getJobData } from '../common/job';

import { PostgresSyncService } from '../services/PostgresSyncService';
import config from '../Config';
import { JobStatusService } from '../services/JobStatusService';
import { JobStatusEnum } from '../enums/JobStatusEnum';
import { hashProvider } from '../utils/ProviderServiceIdHash';
import EmbeddingService from '../services/EmbeddingService';
import { dbService } from '../services/PostgresService';
import { formatJobLog } from '../utils/Log';

import { PassThrough } from 'stream';
import { newFlattenUnasProduct } from '../common/parser';

const DATASET_ID = config.GCP_BIGQUERY_DATASET;
const PRODUCTS_TABLE = config.GCP_BIGQUERY_PRODUCTS_TABLE;
const CATEGORIES_TABLE = config.GCP_BIGQUERY_CATEGORIES_TABLE;

type UnasJobProperties = {
  apiKey: string;
  providerServiceId: string;
  databaseId: number;
  locale: string;
  currency: string;
};

type UnasJobData<T = Record<string, unknown>> = T &
  JobFlowData<UnasJobProperties>;

export class UnasAdapter
  implements FlowChildJobHandler<'fetch' | 'intobq' | 'unifiy' | 'topg'>
{
  private static statusLogger = JobStatusService.insertFlowStatus;

  static langs: Language | undefined;

  static async fetchData(job: Job): Promise<
    UnasJobData<{
      fileUrl: string;
      langs: Language;
      categoriesFileUrl: string;
      currency: string;
      locale: string;
    }>
  > {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);
      await JobStatusService.setAllJobsToWaiting(
        dbService.knex,
        job.data.providerServiceId,
      );

      const jobData = job.data;

      if (!jobData.apiKey) {
        throw new Error('Apikey not provided');
      }

      const gcpStorageService = new GoogleCloudStorageService(
        config.GCP_PROJECT_ID,
        config.GCP_API_PROJECT_CREDS,
      );

      const time = new Date()
        .toISOString()
        .replace(/[-:T.]/g, '')
        .slice(0, 16);

      const api = new UnasAPIAdapter();
      this.langs = await api.login(jobData.apiKey);

      const categoriesFileName = `product-sync/unas-temp-categories-${time}.json`;
      const categoriesStream = new PassThrough();

      let catLimitStart = 0;
      const catLimitNum = 1000;
      let hasMoreCategories = true;

      while (hasMoreCategories) {
        logger.info(
          `[UNAS-FLOW] Fetching categories from ${catLimitStart} to ${
            catLimitStart + catLimitNum - 1
          }`,
        );

        const categoryChunk = await api.getCategoriesChunk(
          catLimitNum,
          catLimitStart,
        );

        if (!categoryChunk || categoryChunk.length < 2) {
          hasMoreCategories = false;
        }

        categoryChunk.forEach((category) => {
          categoriesStream.write(JSON.stringify(category) + '\n');
        });

        catLimitStart += catLimitNum;
      }

      categoriesStream.end();

      const categoriesFileUrl = await gcpStorageService.uploadFromStream(
        config.GCP_BUCKET_NAME,
        categoriesFileName,
        categoriesStream,
        {
          resumable: true,
          metadata: {
            contentType: 'application/x-ndjson',
            cacheControl: 'public, max-age=31536000',
          },
        },
      );

      logger.info({
        message: formatJobLog({
          ...job.data,
          message:
            '[UNAS-FLOW] [STEP-1] Categories NDJSON uploaded successfully',
        }),
        data: { categoriesFileUrl },
      });

      const ndjsonFileName = `product-sync/unas-stream-${time}.json`;
      const ndjsonStream = new PassThrough();

      let limitStart = 0;
      const limitNum = 1000;
      let hasMoreProducts = true;

      while (hasMoreProducts) {
        logger.info(
          `[UNAS-FLOW] Fetching products from ${limitStart} to ${
            limitStart + limitNum - 1
          }`,
        );

        const productChunk = await api.getProductChunk(limitNum, limitStart);

        if (!productChunk.Products || !productChunk.Products.Product) {
          hasMoreProducts = false;
          break;
        }

        const products = Array.isArray(productChunk.Products.Product)
          ? productChunk.Products.Product
          : [productChunk.Products.Product];

        products.forEach((product) => {
          const productFlat = newFlattenUnasProduct(product);
          ndjsonStream.write(JSON.stringify(productFlat) + '\n');
        });

        if (products.length < 2) {
          hasMoreProducts = false;
        }

        limitStart += limitNum;
      }

      ndjsonStream.end();

      const fileUrl = await gcpStorageService.uploadFromStream(
        config.GCP_BUCKET_NAME,
        ndjsonFileName,
        ndjsonStream,
        {
          resumable: true,
          metadata: {
            contentType: 'application/x-ndjson',
            cacheControl: 'public, max-age=31536000',
          },
        },
      );

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);
      return {
        ...job.data,
        fileUrl,
        categoriesFileUrl,
        langs: this.langs,
        currency: 'HUF',
        locale: 'hu',
      };
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error?.message,
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message: 'Error fetching data:',
        }),
        error: error?.message,
        stack: error?.stack,
      });
      throw error;
    }
  }
  static async moveToBQ(job: Job): Promise<
    UnasJobData<{
      fileUrl: string;
      tempTableId: string;
      catTempTableId: string;
    }>
  > {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);

      const {
        fileUrl,
        categoriesFileUrl,
        locale,
        providerServiceId,
        databaseId,
      } =
        await getJobData<
          UnasJobData<{ fileUrl: string; categoriesFileUrl: string }>
        >(job);

      const missingFields = [];
      if (!fileUrl) missingFields.push('fileUrl');
      if (!providerServiceId) missingFields.push('providerServiceId');
      if (!databaseId) missingFields.push('databaseId');

      if (missingFields.length > 0) {
        throw new Error(
          `The following fields are required but missing: ${missingFields.join(
            ', ',
          )}`,
        );
      }

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[UNAS-FLOW] [STEP-2] Starting moveToBQ process',
        }),
        data: { fileUrl, locale, providerServiceId, databaseId },
      });

      const hashProviderServiceId = hashProvider(providerServiceId);

      const tempTableId = `temp_unas_products_${databaseId}_${hashProviderServiceId}_${Date.now()}`;
      const catTempTableId = `temp_unas_categories_${databaseId}_${hashProviderServiceId}_${Date.now()}`;

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[UNAS-FLOW] [STEP-2] Initialized BigQuery client',
        }),
        data: { datasetId: DATASET_ID, tempTableId },
      });

      const bigQuery = new BigQueryClient();

      const externalTableSql = externalTableQuery(tempTableId, {
        dataset: DATASET_ID,
        uri: decodeURIComponent(fileUrl),
        format: 'NEWLINE_DELIMITED_JSON',
        expiry: '3 DAY',
      });

      const categoriesExternalTableSql = externalTableQuery(catTempTableId, {
        dataset: DATASET_ID,
        uri: decodeURIComponent(categoriesFileUrl),
        format: 'NEWLINE_DELIMITED_JSON',
        expiry: '3 DAY',
      });

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[UNAS-FLOW] [STEP-2] Generated external table query',
        }),
      });

      await bigQuery.runQueryWithResult({ query: externalTableSql });
      await bigQuery.runQueryWithResult({ query: categoriesExternalTableSql });

      logger.info({
        message: formatJobLog({
          ...job.data,
          message:
            '[UNAS-FLOW] [STEP-2] Executed external table and categories query',
        }),
        data: { tempTableId },
      });
      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

      return {
        ...job.data,
        tempTableId,
        catTempTableId,
      };
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error?.message,
      );
      logger.error({
        message: formatJobLog({
          ...job.data,
          message: 'Error loading unas csv into BigQuery:',
        }),
        error: error?.message,
        stack: error?.stack,
      });
      throw error;
    }
  }

  static async unifyData(
    job: Job,
  ): Promise<UnasJobData<{ fileUrl: string; tempTableId: string }>> {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);

      const {
        locale,
        providerServiceId,
        databaseId,
        tempTableId,
        catTempTableId,
      } = await getJobData<
        UnasJobData<{
          fileUrl: string;
          tempTableId: string;
          catTempTableId: string;
        }>
      >(job);

      const targetTableId = PRODUCTS_TABLE;
      const targetCategoriesTableId = CATEGORIES_TABLE;

      const bigQuery = new BigQueryClient();

      const deleteQuery = deleteProductQuery({
        datasetId: DATASET_ID,
        tableId: targetTableId,
      });

      const insertSql = generateUnasInsertQuery({
        datasetId: DATASET_ID,
        targetTableId,
        sourceTableId: tempTableId,
        catTempTableId,
        metadata: {
          databaseId,
          providerServiceId,
          locale,
          currency: 'HUF',
        },
      });

      const query = `
    ${deleteQuery}
    ${insertSql}
    `;

      const categoryQuery = generateCategoryInsertQuery({
        datasetId: DATASET_ID,
        targetTableId: targetCategoriesTableId,
        sourceTableId: catTempTableId,
        metadata: {
          databaseId,
          providerServiceId,
          locale,
          currency: 'HUF',
        },
      });

      await bigQuery.runQueryWithResult({
        query: query,
        queryOptions: {
          params: {
            databaseId,
            providerServiceId,
          },
        },
      });

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[UNAS-FLOW] [STEP-3] Executed unify product query',
        }),
        data: {
          datasetId: DATASET_ID,
          targetTableId,
          databaseId,
          providerServiceId,
        },
      });

      await bigQuery.runQueryWithResult({
        query: categoryQuery,
        queryOptions: {
          params: {
            databaseId,
            providerServiceId,
          },
        },
      });

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[UNAS-FLOW] [STEP-3] Executed unify category query',
        }),
        data: {
          datasetId: DATASET_ID,
          targetTableId,
          databaseId,
          providerServiceId,
        },
      });

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

      return job.data;
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error?.message,
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message: 'Error unifying data:',
        }),
        error: error?.message,
        stack: error?.stack,
      });
      throw Error('Error unifying data');
    }
  }

  static async writeToPg(job: Job): Promise<UnasJobData> {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);
      const { databaseId, providerServiceId } =
        await getJobData<UnasJobData>(job);

      const postgresSyncService = new PostgresSyncService(dbService);

      await postgresSyncService.syncDataToPostgres(
        new BigQueryClient(),
        databaseId,
        providerServiceId,
        DATASET_ID,
        PRODUCTS_TABLE,
      );

      await postgresSyncService.syncCategoriesToPostgres(
        new BigQueryClient(),
        databaseId,
        providerServiceId,
        DATASET_ID,
        CATEGORIES_TABLE,
      );

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[UNAS-FLOW] [STEP-4] Data written to Postgres successfully',
        }),
      });
      return job.data;
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error?.message,
      );
      logger.error({
        message: formatJobLog({
          ...job.data,
          message: 'Error writing to Postgres:',
        }),
        error: error?.message,
        stack: error?.stack,
      });
      throw Error('Error writing to Postgres');
    }
  }

  static async generateEmbeddings(
    job: Job,
  ): Promise<UnasJobData<{ fileUrl: string; tempTableId: string }>> {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);
      const { databaseId, locale, providerServiceId } =
        await getJobData<UnasJobData>(job);

      await EmbeddingService.storeEmbeddings(
        providerServiceId,
        locale,
        databaseId,
      );

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);
      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[UNAS-FLOW] Flow completed successfully.',
        }),
      });
      return job.data;
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message,
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message: 'Error storing embeddings:',
        }),
        error: error?.message,
        stack: error?.stack,
      });
      throw Error('Error storing embeddings');
    }
  }

  static async storeShopSettings(job: Job): Promise<UnasJobData> {
    try {
      this.statusLogger(dbService.knex, job, JobStatusEnum.ACTIVE);
      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[SETTINGS]: Fetching and save store settings',
        }),
      });

      const lang = this.langs;

      if (!lang) {
        logger.error({
          message: formatJobLog({
            ...job.data,
            message: 'No language data found in job data',
          }),
          jobData: job.data,
        });
        return job.data;
      }

      const postgresSyncService = new PostgresSyncService(dbService);

      await postgresSyncService.saveShopSettings(
        job.data.databaseId,
        job.data.providerServiceId,
        lang.Language.Code,
        'HUF', // TODO: Find a way to get default currency from the API
        lang.Language.Default === 'yes',
      );

      logger.info({
        message: formatJobLog({
          ...job.data,
          message: '[SETTINGS] Store settings fetched successfully',
        }),
      });

      this.statusLogger(dbService.knex, job, JobStatusEnum.COMPLETED);

      return {
        ...job.data,
      };
    } catch (error: any) {
      this.statusLogger(
        dbService.knex,
        job,
        JobStatusEnum.FAILED,
        error.message || 'Unknown error',
      );

      logger.error({
        message: formatJobLog({
          ...job.data,
          message: 'Failed to fetch store settings from Unas API:',
        }),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw Error('Failed to fetch store settings from Unas API');
    }
  }
}
