import { logger } from '@om/logger';
import axios, { AxiosInstance } from 'axios';
import {
  GoMagCategoriesResponse,
  GoMagProductsResponse,
} from '../interfaces/GoMagTypes';

export interface GoMagApiOptions {
  apiShop: string;
  apiKey: string;
}

export class GoMagApiAdapter {
  private static readonly DEFAULT_BASE_URL = 'https://api.gomag.ro/api';
  private static readonly DEFAULT_TIMEOUT = 60000;

  static getAxios(options: GoMagApiOptions): AxiosInstance {
    const api = axios.create({
      baseURL: GoMagApiAdapter.DEFAULT_BASE_URL,
      timeout: GoMagApiAdapter.DEFAULT_TIMEOUT,
      headers: {
        ApiShop: options.apiShop,
        ApiKey: options.apiKey,
      },
    });

    api.interceptors.response.use(
      (response) => response,
      (error) => {
        logger.error({
          message: 'API request failed',
          error: error.message,
          status: error.response?.status,
          url: error.config?.url,
        });

        throw new Error(error.response?.data?.message || error.message);
      },
    );
    return api;
  }

  static async getCategories(
    options: GoMagApiOptions,
  ): Promise<GoMagCategoriesResponse> {
    try {
      const response = await GoMagApiAdapter.getAxios(options).get(
        '/v1/category/read/json',
      );
      return response.data;
    } catch (error) {
      logger.error({
        message: 'Failed to fetch categories from GoMag API',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  static async getProducts(
    options: GoMagApiOptions,
    page: number,
  ): Promise<GoMagProductsResponse> {
    try {
      const response = await GoMagApiAdapter.getAxios(options).get(
        '/v1/product/read/json',
        {
          params: {
            page,
          },
        },
      );

      return response.data;
    } catch (error) {
      logger.error({
        message: 'Failed to fetch products from GoMag API',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  static async getCurrency(options: GoMagApiOptions): Promise<string> {
    const firstProduct = await GoMagApiAdapter.getProducts(options, 1);
    const firstProductId = Object.keys(firstProduct.products)[0];
    const product = firstProduct.products[firstProductId];
    return product.currency;
  }

  static async getLocale(options: GoMagApiOptions): Promise<string> {
    const currency = await GoMagApiAdapter.getCurrency(options);
    let locale = 'ro';

    switch (currency.toLowerCase()) {
      case 'lei':
        locale = 'ro';
        break;
      case 'huf':
        locale = 'hu';
        break;
      default:
        locale = 'eur';
        break;
    }
    return locale;
  }
}
