import { jest, describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { getProductsByIdsRoute } from '../routes/GetProductsById';
import { createTestServer } from '../../../test/utils/test-server-utils';
import { dbService } from '../../services/PostgresService';
import request from 'supertest';
import { FastifyServer } from '../FastifyServer';

describe('Products', () => {
  let server: FastifyServer;

  jest.setTimeout(30000);

  beforeAll(async () => {
    // Ensure we're in test mode to use in-memory Redis
    process.env.NODE_ENV = 'test';

    server = await createTestServer([getProductsByIdsRoute], false);
  });

  afterAll(async () => {
    // Clean up and reset mocks
    jest.clearAllMocks();

    // Close the server (this will also unseed the database)
    await server.instance.close();

    // Close the database connection last
    await dbService.close();
  });

  it('should read the products from the database', async () => {
    const res = await request(server.instance.server)
      .post('/products')
      .send({
        productIds: ['test-product-1', 'test-product-2'],
        databaseId: -1,
        providerServiceId: 'testshop.dev',
      })
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/json')
      .expect(200);

    expect(res.body).toBeDefined();
    expect(Array.isArray(res.body.products)).toBe(true);
    expect(res.body.products).toHaveLength(2);

    // Sort for deterministic comparison
    const sortedProducts = [...res.body.products].sort((a, b) =>
      a.productId.localeCompare(b.productId),
    );

    expect(sortedProducts[0]).toMatchObject({
      databaseId: -1,
      providerServiceId: 'testshop.dev',
      status: 'active',
      locale: 'en-US',
      productId: 'test-product-1',
      name: 'Test Product 1',
      price: 99.99,
      originalPrice: 129.99,
      currency: 'USD',
      imageUrl: 'https://example.com/test-product-1.jpg',
      description: 'This is a test product 1 description',
      parentProductId: 'test-parent-1',
      productUrl: 'https://testshop.dev/products/test-product-1',
    });
    expect(sortedProducts[1]).toMatchObject({
      databaseId: -1,
      providerServiceId: 'testshop.dev',
      status: 'active',
      locale: 'en-US',
      productId: 'test-product-2',
      name: 'Test Product 2',
      price: 149.99,
      originalPrice: 199.99,
      currency: 'USD',
      imageUrl: 'https://example.com/test-product-2.jpg',
      description: 'This is a test product 2 description',
      parentProductId: 'test-parent-2',
      productUrl: 'https://testshop.dev/products/test-product-2',
    });
  });
});
