import { queryJobsRoute } from '../routes/QueryJobs';
import { FastifyServer } from '../FastifyServer';
import { jest, describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { createTestServer } from '../../../test/utils/test-server-utils';
import request from 'supertest';

describe('QueryJobs Route', () => {
  let server: FastifyServer;

  jest.setTimeout(30000);

  beforeAll(async () => {
    // Ensure we're in test mode to use in-memory Redis
    process.env.NODE_ENV = 'test';

    server = await createTestServer([queryJobsRoute], true);
  });

  afterAll(async () => {
    // Clean up and reset mocks
    jest.clearAllMocks();

    // Close the server
    await server.instance.close();
  });

  it('should read the products from the database', async () => {
    await request(server.instance.server).get('/jobs').expect(200);
    const response = await server.instance.inject({
      method: 'GET',
      url: '/jobs',
    });

    expect(response.statusCode).toBe(200);
    expect(response.json()).toEqual({
      active: [],
      waiting: [],
      completed: [],
      failed: [],
    });
  });
});
