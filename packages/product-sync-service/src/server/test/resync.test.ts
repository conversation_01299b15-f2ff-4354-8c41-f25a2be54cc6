import * as QueueModule from '../../scheduler/Queue';

import { FastifyServer } from '../FastifyServer';
import {
  jest,
  describe,
  it,
  expect,
  beforeAll,
  afterAll,
  afterEach,
} from '@jest/globals';
import { createTestServer } from '../../../test/utils/test-server-utils';
import { resyncProductsRoute } from '../routes/Resync';
import { dbService } from '../../services/PostgresService';

const testJobStatusData = [
  {
    id: 1,
    platform: 'woo',
    status: 'COMPLETED',
    providerServiceId: 'woocommersetestsite.kinsta.cloud',
    databaseId: 12345,
    jobId: 'jobId1',
    jobName: 'storeShopSettings',
    parentJobId: 'parentJobId1',
    errorMessage: null,
    createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago, ISO string for Postgres timestamp
    updatedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: 2,
    platform: 'unas',
    status: 'COMPLETED',
    providerServiceId: 'www.megyen.hu',
    databaseId: 44,
    jobId: '3f3b90b4-d6ad-4b3e-ac35-4f0ad68daff4',
    jobName: 'fetchAndUpload',
    parentJobId: 'c1a1e1b0-570a-49e5-bf69-b375d0e0133d',
    errorMessage: null,
    createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago, ISO string for Postgres timestamp
    updatedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
  },
];

const requestData = [
  {
    accountId: 12345,
    shopId: 'woocommersetestsite.kinsta.cloud',
    shopType: 'woo',
    username: 'username',
    password: 'password',
    active: true,
  },
  {
    accountId: 44,
    shopId: 'www.megyen.hu',
    shopType: 'unas',
    active: true,
    token: 'dfkjhgsldkbgsldkjfhsdj',
  },
];

describe('Resync Route Test', () => {
  let server: FastifyServer;

  jest.setTimeout(300000);

  beforeAll(async () => {
    // Ensure we're in test mode to use in-memory Redis
    process.env.NODE_ENV = 'test';

    jest.spyOn(QueueModule, 'createJobFlow').mockResolvedValue({
      jobId: '123132sdad',
    } as any);

    server = await createTestServer([resyncProductsRoute], true);
  });

  afterAll(async () => {
    // Clean up and reset mocks
    jest.clearAllMocks();

    await dbService.knex('flow_statuses').del();

    // Close the server
    await server.instance.close();
  });

  afterEach(async () => {
    // clean up flow_status table
    await dbService.knex('flow_statuses').del();
  });

  it('should start a single flow job', async () => {
    await dbService.knex('flow_statuses').insert(testJobStatusData[0]);

    const response = await server.instance.inject({
      method: 'POST',
      url: '/resync',
      payload: {
        data: [requestData[0]],
      },
    });

    expect(response.statusCode).toBe(200);
    expect(response.json().message).toEqual('Resync started: 1');
  });

  it('should start multiple flow job', async () => {
    await dbService.knex('flow_statuses').insert(testJobStatusData);

    const response = await server.instance.inject({
      method: 'POST',
      url: '/resync',
      payload: {
        data: requestData,
      },
    });

    expect(response.statusCode).toBe(200);
    expect(response.json().message).toEqual('Resync started: 2');
  });

  it('should NOT start jobs that ran less than 3 hours ago', async () => {
    // Create job status data with recent timestamps (1 hour ago)
    const recentJobStatusData = [
      {
        id: 1,
        platform: 'woo',
        status: 'COMPLETED',
        providerServiceId: 'woocommersetestsite.kinsta.cloud',
        databaseId: 12345,
        jobId: 'jobId1',
        jobName: 'storeShopSettings',
        parentJobId: 'parentJobId1',
        errorMessage: null,
        createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
        updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
      },
    ];

    await dbService.knex('flow_statuses').insert(recentJobStatusData);

    const response = await server.instance.inject({
      method: 'POST',
      url: '/resync',
      payload: {
        data: [requestData[0]],
      },
    });

    expect(response.statusCode).toBe(200);
    // This test expects 0 jobs to start (correct behavior)
    // but due to the bug, 1 job will start, causing this test to fail
    expect(response.json().message).toEqual('Resync started: 0');
  });

  it('should handle databaseId type mismatch correctly', async () => {
    // Create job status data with recent timestamps (30 minutes ago)
    // Database stores databaseId as number, but request sends it as string
    const veryRecentJobStatusData = [
      {
        id: 1,
        platform: 'woo',
        status: 'COMPLETED',
        providerServiceId: 'woocommersetestsite.kinsta.cloud',
        databaseId: 12345, // number in database
        jobId: 'jobId1',
        jobName: 'storeShopSettings',
        parentJobId: 'parentJobId1',
        errorMessage: null,
        createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
        updatedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
      },
    ];

    await dbService.knex('flow_statuses').insert(veryRecentJobStatusData);

    // Request with databaseId as string (this was causing the bug)
    const requestWithStringId = {
      ...requestData[0],
      accountId: '12345', // string in request
    };

    const response = await server.instance.inject({
      method: 'POST',
      url: '/resync',
      payload: {
        data: [requestWithStringId],
      },
    });

    expect(response.statusCode).toBe(200);
    // Should correctly identify existing job and prevent running (not treat as missing shop)
    expect(response.json().message).toEqual('Resync started: 0');
  });
});
