import { FastifyServer } from '../FastifyServer';
import { jest, describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { createTestServer } from '../../../test/utils/test-server-utils';
import request from 'supertest';
import { getPopularProductsRoute } from '../routes/GetPopularProducts';
import { Redis } from 'ioredis';
import { UnifiedProduct } from '../../interfaces/Product';

const CACHE_DATA = {
  accountId: -1,
  providerServiceId: 'testshop.dev',
  top_product_ids: ['test-product-1', 'test-product-2', 'test-product-3'],
};

describe('GetPopularProducts Route', () => {
  let server: FastifyServer;
  let redis: Redis;

  jest.setTimeout(30000);

  beforeAll(async () => {
    // Ensure we're in test mode to use in-memory Redis
    process.env.NODE_ENV = 'test';

    server = await createTestServer([getPopularProductsRoute], true);
    const redisInstance = (await server.getServices()).redis;
    if (!redisInstance) {
      throw new Error('Redis instance is undefined');
    }
    redis = redisInstance;
    if (redis) {
      const key = `products:popular:${CACHE_DATA.accountId}:${CACHE_DATA.providerServiceId}`;
      await redis.set(key, JSON.stringify(CACHE_DATA.top_product_ids));
    }
  });

  afterAll(async () => {
    // Clean up and reset mocks
    jest.clearAllMocks();

    // Close the server
    await server.instance.close();
  });

  it('should read the popular products from the cache', async () => {
    const key = `products:popular:${CACHE_DATA.accountId}:${CACHE_DATA.providerServiceId}`;
    redis.set(key, JSON.stringify(CACHE_DATA.top_product_ids));

    await request(server.instance.server)
      .post('/products/popular')
      .send({
        databaseId: CACHE_DATA.accountId,
        providerServiceId: CACHE_DATA.providerServiceId,
      })
      .expect(200);
    const response = await server.instance.inject({
      method: 'POST',
      url: '/products/popular',
      payload: {
        databaseId: CACHE_DATA.accountId,
        providerServiceId: CACHE_DATA.providerServiceId,
      },
    });

    expect(response.statusCode).toBe(200);
    const responseData = response.json();
    expect(responseData).toHaveProperty('products');
    expect(
      responseData.products.map((product: UnifiedProduct) => product.productId),
    ).toEqual(CACHE_DATA.top_product_ids);
  });

  it('should return empty array if cached products are not found', async () => {
    const response = await server.instance.inject({
      method: 'POST',
      url: '/products/popular',
      payload: {
        databaseId: -2,
        providerServiceId: 'testshop.dev',
      },
    });

    expect(response.statusCode).toBe(200);
    const responseData = response.json();
    expect(responseData).toHaveProperty('products');
    expect(responseData.products).toEqual([]);
  });
});
