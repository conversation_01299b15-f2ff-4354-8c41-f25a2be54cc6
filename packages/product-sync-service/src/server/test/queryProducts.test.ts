import { jest, describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { queryProductsRoute } from '../routes/QueryProducts';
import { createTestServer } from '../../../test/utils/test-server-utils';
import { dbService } from '../../services/PostgresService';
import request from 'supertest';
import { FastifyServer } from '../FastifyServer';

describe('QueryProducts Route', () => {
  let server: FastifyServer;

  jest.setTimeout(30000);

  beforeAll(async () => {
    // Ensure we're in test mode to use in-memory Redis
    process.env.NODE_ENV = 'test';

    server = await createTestServer([queryProductsRoute], false);
  });

  afterAll(async () => {
    // Clean up and reset mocks
    jest.clearAllMocks();

    // Close the server (this will also unseed the database)
    await server.instance.close();

    // Close the database connection last
    await dbService.close();
  });

  it('should filter products by databaseId and providerServiceId', async () => {
    // First request with databaseId = -1 and providerServiceId = 'testshop.dev'
    const res1 = await request(server.instance.server)
      .get('/products?databaseId=-1&providerServiceId=testshop.dev&limit=10')
      .set('Accept', 'application/json')
      .expect(200);

    expect(res1.body.products).toBeDefined();
    expect(Array.isArray(res1.body.products)).toBe(true);

    // All products should have databaseId = -1 and providerServiceId = 'testshop.dev'
    res1.body.products.forEach((product: any) => {
      // Validate required fields using object comparison
      expect(product).toMatchObject({
        databaseId: -1,
        providerServiceId: 'testshop.dev',
        currency: 'USD',
        locale: 'en-US',
      });

      // Validate that price is a valid number and makes sense
      expect(product.price).toBeGreaterThan(0);
      expect(product.originalPrice).toBeGreaterThanOrEqual(product.price);

      // Validate URLs are properly formatted
      expect(product.imageUrl).toMatch(/^https:\/\//);
      expect(product.productUrl).toMatch(/^https:\/\/testshop\.dev\//);
    });

    // Second request with different databaseId
    const res2 = await request(server.instance.server)
      .get('/products?databaseId=-2&providerServiceId=testshop.dev&limit=10')
      .set('Accept', 'application/json')
      .expect(200);

    expect(res2.body.products).toBeDefined();
    expect(Array.isArray(res2.body.products)).toBe(true);

    // All products should have databaseId = -2 and providerServiceId = 'testshop.dev'
    res2.body.products.forEach((product: any) => {
      // Validate required fields using object comparison
      expect(product).toMatchObject({
        databaseId: -2,
        providerServiceId: 'testshop.dev',
      });

      // Validate specific values for databaseId = -2
      expect(product.productUrl).toMatch(/^https:\/\/anothershop\.dev\//);

      // Validate that price is a valid number
      expect(product.price).toBeGreaterThan(0);
    });

    // The two result sets should be different
    expect(res1.body.products).not.toEqual(res2.body.products);
  });

  it('should require databaseId parameter', async () => {
    // Request without databaseId should fail
    await request(server.instance.server)
      .get('/products?providerServiceId=testshop.dev&limit=10')
      .set('Accept', 'application/json')
      .expect(400);
  });

  it('should require providerServiceId parameter', async () => {
    // Request without providerServiceId should fail
    await request(server.instance.server)
      .get('/products?databaseId=-1&limit=10')
      .set('Accept', 'application/json')
      .expect(400);
  });

  it('should limit the number of results', async () => {
    // Request with limit=5
    const res = await request(server.instance.server)
      .get('/products?databaseId=-1&providerServiceId=testshop.dev&limit=5')
      .set('Accept', 'application/json')
      .expect(200);

    expect(res.body.products).toBeDefined();
    expect(Array.isArray(res.body.products)).toBe(true);
    expect(res.body.products.length).toBeLessThanOrEqual(5);
  });

  it('should support pagination', async () => {
    // Get all products for reference
    const allProductsRes = await request(server.instance.server)
      .get('/products?databaseId=-1&providerServiceId=testshop.dev&limit=10')
      .set('Accept', 'application/json')
      .expect(200);

    const allProducts = allProductsRes.body.products;
    expect(allProducts.length).toBe(3); // We know there are 3 test products with databaseId=-1

    // Get first page with 1 product per page
    const page1Res = await request(server.instance.server)
      .get(
        '/products?databaseId=-1&providerServiceId=testshop.dev&page=1&limit=1',
      )
      .set('Accept', 'application/json')
      .expect(200);

    // Validate first page content
    expect(page1Res.body.products.length).toBe(1);
    const firstPageProduct = page1Res.body.products[0];

    // Get second page with 1 product per page
    const page2Res = await request(server.instance.server)
      .get(
        '/products?databaseId=-1&providerServiceId=testshop.dev&page=2&limit=1',
      )
      .set('Accept', 'application/json')
      .expect(200);

    // Validate second page content
    expect(page2Res.body.products.length).toBe(1);
    const secondPageProduct = page2Res.body.products[0];

    // First and second pages should have different products
    expect(firstPageProduct.productId).not.toBe(secondPageProduct.productId);

    // Get third page with 1 product per page
    const page3Res = await request(server.instance.server)
      .get(
        '/products?databaseId=-1&providerServiceId=testshop.dev&page=3&limit=1',
      )
      .set('Accept', 'application/json')
      .expect(200);

    // Validate third page content
    expect(page3Res.body.products.length).toBe(1);
    const thirdPageProduct = page3Res.body.products[0];

    // All three pages should have different products
    expect(firstPageProduct.productId).not.toBe(thirdPageProduct.productId);
    expect(secondPageProduct.productId).not.toBe(thirdPageProduct.productId);

    // Verify pagination metadata is correct
    expect(page1Res.body.meta.total).toBe(3);
    expect(page1Res.body.meta.totalPages).toBe(3);
    expect(page1Res.body.meta.page).toBe(1);
    expect(page1Res.body.meta.limit).toBe(1);
  });

  it('should search products by name', async () => {
    // Search for products with "Product 1" in the name
    const res = await request(server.instance.server)
      .get(
        '/products?databaseId=-1&providerServiceId=testshop.dev&search=Product%201&limit=10',
      )
      .set('Accept', 'application/json')
      .expect(200);

    // Validate search results
    expect(res.body.products).toBeDefined();
    expect(Array.isArray(res.body.products)).toBe(true);

    // Should have exactly one result
    expect(res.body.products.length).toBe(1);

    // Validate the specific product returned using object comparison
    const product = res.body.products[0];
    expect(product).toMatchObject({
      productId: 'test-product-1',
      name: 'Test Product 1',
      sku: 'SKU-001',
      price: 99.99,
      originalPrice: 129.99,
      parentProductId: 'test-parent-1',
    });

    // Verify search is case insensitive by searching with lowercase
    const resLowercase = await request(server.instance.server)
      .get(
        '/products?databaseId=-1&providerServiceId=testshop.dev&search=product%201&limit=10',
      )
      .set('Accept', 'application/json')
      .expect(200);

    expect(resLowercase.body.products.length).toBe(1);
    expect(resLowercase.body.products[0].productId).toBe('test-product-1');
  });

  it('should search products by SKU', async () => {
    // Search for products with exact SKU match
    const res = await request(server.instance.server)
      .get(
        '/products?databaseId=-1&providerServiceId=testshop.dev&search=SKU-001&limit=10',
      )
      .set('Accept', 'application/json')
      .expect(200);

    // Validate search results
    expect(res.body.products).toBeDefined();
    expect(Array.isArray(res.body.products)).toBe(true);

    // Should have exactly one result
    expect(res.body.products.length).toBe(1);

    // Validate the specific product returned using object comparison
    const product = res.body.products[0];
    expect(product).toMatchObject({
      productId: 'test-product-1',
      name: 'Test Product 1',
      sku: 'SKU-001',
      price: 99.99,
      originalPrice: 129.99,
      parentProductId: 'test-parent-1',
    });

    // Verify search is case insensitive by searching with lowercase SKU
    const resLowercase = await request(server.instance.server)
      .get(
        '/products?databaseId=-1&providerServiceId=testshop.dev&search=sku-001&limit=10',
      )
      .set('Accept', 'application/json')
      .expect(200);

    expect(resLowercase.body.products.length).toBe(1);
    expect(resLowercase.body.products[0].productId).toBe('test-product-1');
  });

  it('should search products by partial SKU match', async () => {
    // Search for products with partial SKU match "SKU-" should match all three SKUs
    const res = await request(server.instance.server)
      .get(
        '/products?databaseId=-1&providerServiceId=testshop.dev&search=SKU-&limit=10',
      )
      .set('Accept', 'application/json')
      .expect(200);

    // Validate search results
    expect(res.body.products).toBeDefined();
    expect(Array.isArray(res.body.products)).toBe(true);

    // Should have exactly three results (all products have SKU- in their SKU)
    expect(res.body.products.length).toBe(3);

    // Sort products by SKU for consistent testing
    const products = [...res.body.products].sort((a, b) =>
      a.sku.localeCompare(b.sku),
    );

    // Validate all three products returned (sorted alphabetically by SKU)
    expect(products[0]).toMatchObject({
      productId: 'test-product-1',
      name: 'Test Product 1',
      sku: 'SKU-001',
    });

    expect(products[1]).toMatchObject({
      productId: 'test-product-2',
      name: 'Test Product 2',
      sku: 'SKU-002',
    });

    expect(products[2]).toMatchObject({
      productId: 'test-product-3',
      name: 'Test Product 3',
      sku: 'SPECIAL-SKU-003',
    });
  });

  it('should search products by unique SKU pattern', async () => {
    // Search for products with "SPECIAL" in SKU should match only SPECIAL-SKU-003
    const res = await request(server.instance.server)
      .get(
        '/products?databaseId=-1&providerServiceId=testshop.dev&search=SPECIAL&limit=10',
      )
      .set('Accept', 'application/json')
      .expect(200);

    // Validate search results
    expect(res.body.products).toBeDefined();
    expect(Array.isArray(res.body.products)).toBe(true);

    // Should have exactly one result
    expect(res.body.products.length).toBe(1);

    // Validate the specific product returned
    const product = res.body.products[0];
    expect(product).toMatchObject({
      productId: 'test-product-3',
      name: 'Test Product 3',
      sku: 'SPECIAL-SKU-003',
      status: 'inactive',
    });
  });

  it('should return products with correct field values', async () => {
    const res = await request(server.instance.server)
      .get('/products?databaseId=-1&providerServiceId=testshop.dev&limit=10')
      .set('Accept', 'application/json')
      .expect(200);

    // Verify we got all 3 expected products
    expect(res.body.products.length).toBe(3);

    // Sort products by ID for consistent testing
    const products = [...res.body.products].sort((a, b) =>
      a.productId.localeCompare(b.productId),
    );

    // Validate specific product values using object comparison

    // Product 1
    expect(products[0]).toMatchObject({
      productId: 'test-product-1',
      name: 'Test Product 1',
      sku: 'SKU-001',
      price: 99.99,
      originalPrice: 129.99,
      status: 'active',
      parentProductId: 'test-parent-1',
    });

    // Product 2
    expect(products[1]).toMatchObject({
      productId: 'test-product-2',
      name: 'Test Product 2',
      sku: 'SKU-002',
      price: 149.99,
      originalPrice: 199.99,
      status: 'active',
      parentProductId: 'test-parent-2',
    });

    // Product 3
    expect(products[2]).toMatchObject({
      productId: 'test-product-3',
      name: 'Test Product 3',
      sku: 'SPECIAL-SKU-003',
      price: 49.99,
      originalPrice: 49.99,
      status: 'inactive',
    });

    // Check parentProductId separately since it can be empty string or null
    expect(['', null]).toContain(products[2].parentProductId);

    // Verify all products have the same currency and locale
    products.forEach((product) => {
      expect(product.currency).toBe('USD');
      expect(product.locale).toBe('en-US');
      expect(product.databaseId).toBe(-1);
      expect(product.providerServiceId).toBe('testshop.dev');
    });

    // Verify product URLs follow the expected pattern
    expect(products[0].productUrl).toBe(
      'https://testshop.dev/products/test-product-1',
    );
    expect(products[1].productUrl).toBe(
      'https://testshop.dev/products/test-product-2',
    );
    expect(products[2].productUrl).toBe(
      'https://testshop.dev/products/test-product-3',
    );
  });
});
