import fastify, {
  FastifyInstance,
  FastifyPluginCallback,
  RouteHandlerMethod,
  RouteShorthandOptions,
} from 'fastify';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import fastifyJwt from '@fastify/jwt';
import path from 'path';
import {
  RouteMethod,
  ServerOptions,
  Services,
} from '../interfaces/ServerTypes';
import { authenticate } from './middleware/Authenticate';
import config from '../Config';
import { logger } from '@om/logger';

export class FastifyServer {
  private _server: FastifyInstance | undefined;

  private staticDir = 'public';
  constructor(private options: ServerOptions = {}) {
    this.init(options);
  }

  init(options: ServerOptions): void {
    this.staticDir = options.staticDir || path.join(process.cwd(), 'public');
    this._server = fastify({
      logger: true,
    });
    this._server.register(cors as unknown as FastifyPluginCallback);
    this._server.register(helmet as unknown as FastifyPluginCallback);
    this._server.register(fastifyJwt as any, {
      secret: config.OM_SHARED_KEY,
    });

    // Adding auth middleware to all routes
    if (process.env.NODE_ENV === 'production') {
      this._server.addHook('preHandler', authenticate);
    }

    if (this._server) {
      const signals: NodeJS.Signals[] = ['SIGINT', 'SIGTERM'];
      signals.forEach((signal) => {
        process.on(signal, async () => {
          try {
            await this._server?.close();
            this._server?.log.error(`Closed application on ${signal}`);
            process.exit(0);
          } catch (err) {
            this._server?.log.error(
              `Error closing application on ${signal}`,
              err,
            );
            process.exit(1);
          }
        });
      });
    }
  }

  addRoute(
    method: RouteMethod,
    url: string,
    handler: RouteHandlerMethod,
    options?: RouteShorthandOptions,
  ): this {
    this._server?.[method](url, options || {}, handler);
    return this;
  }

  async setupRoutes(
    routes: ((server: FastifyInstance) => void)[],
  ): Promise<this> {
    if (!this._server) {
      throw new Error('Fastify instance is not initialized');
    }

    routes.forEach((routeHandler: (server: FastifyInstance) => void) => {
      routeHandler(this._server as FastifyInstance);
    });

    return this;
  }

  // inject services
  async addServices(services: Services): Promise<void> {
    this._server?.decorate('services', services);
  }

  async getServices(): Promise<Services> {
    if (!this._server) {
      throw new Error('Fastify instance is not initialized');
    }
    return this._server.services;
  }

  get instance(): FastifyInstance {
    if (!this._server) {
      throw new Error('Fastify instance is not initialized');
    }
    return this._server;
  }

  async start(): Promise<void> {
    try {
      const host = config.NODE_ENV === 'development' ? 'localhost' : '0.0.0.0';
      logger.info({
        message: `Starting server on port ${config.PORT} and host ${host}`,
      });
      if (!this._server) {
        throw new Error('Fastify instance is not initialized');
      }
      await this._server.listen({
        port: config.PORT,
        host: host,
      });

      logger.info({
        message: `Server started on port ${config.PORT} and host ${host}`,
      });
    } catch (error) {
      logger.error(error);
      process.exit(1);
    }
  }

  async stop(): Promise<void> {
    if (!this._server) {
      throw new Error('Fastify instance is not initialized');
    }
    await this._server.close();
  }
}
