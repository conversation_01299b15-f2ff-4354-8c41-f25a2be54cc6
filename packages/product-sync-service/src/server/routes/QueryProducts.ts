import { FastifyInstance } from 'fastify';
import { dbService } from '../../services/PostgresService';
import {
  QueryProductsSchema,
  QueryProductsRequestType,
  QueryProductsResponseSchema,
  QueryProductsResponseType,
} from '../../interfaces/RestTypes';

export const queryProductsRoute = (
  fastify: FastifyInstance,
): FastifyInstance => {
  return fastify.get<{
    Querystring: QueryProductsRequestType;
    Reply: QueryProductsResponseType;
  }>(
    '/products',
    {
      schema: {
        querystring: {
          ...QueryProductsSchema,
        },
        response: {
          200: QueryProductsResponseSchema,
        },
      },
    },
    async (request, reply) => {
      try {
        const {
          page,
          limit,
          databaseId,
          providerServiceId,
          search,
          outOfStock,
        } = request.query;
        // Calculate offset for pagination
        const offset = (page - 1) * limit;

        const [products, totalCount] = await Promise.all([
          dbService
            .knex('products')
            .select('*')
            .where('databaseId', databaseId)
            .where('providerServiceId', providerServiceId)
            .andWhere((builder) => {
              if (!outOfStock) {
                builder.where('inStock', true);
              }
            })
            .andWhere((builder) => {
              if (search) {
                builder.where((subBuilder) => {
                  subBuilder
                    .where('name', 'ILIKE', `%${search}%`)
                    .orWhere('sku', 'ILIKE', `%${search}%`);
                });
              }
            })
            .offset(offset)
            .limit(limit),
          dbService
            .knex('products')
            .where('databaseId', databaseId)
            .where('providerServiceId', providerServiceId)
            .andWhere((builder) => {
              if (!outOfStock) {
                builder.where('inStock', true);
              }
            })
            .andWhere((builder) => {
              if (search) {
                builder.where((subBuilder) => {
                  subBuilder
                    .where('name', 'ILIKE', `%${search}%`)
                    .orWhere('sku', 'ILIKE', `%${search}%`);
                });
              }
            })
            .count('* as count')
            .first(),
        ]);

        const total = parseInt(totalCount?.count as string, 10) || 0;

        return reply.send({
          products,
          meta: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
          },
        });
      } catch (error: any) {
        reply.status(400).send(error);
        return;
      }
    },
  );
};
