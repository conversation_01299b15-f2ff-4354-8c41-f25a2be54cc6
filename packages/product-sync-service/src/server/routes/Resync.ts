import { FastifyInstance } from 'fastify';
import { dbService } from '../../services/PostgresService';
import {
  ProductResyncRequestType,
  ProductResyncRequestSchema,
  UnasJobRequestType,
  WoocommerceJobRequestType,
  ShopifyJobRequestType,
  GoMagJobRequestType,
  BackendProductSyncType,
} from '../../interfaces/RestTypes';
import { JobStatusService } from '../../services/JobStatusService';
import { JobStatusEnum } from '../../enums/JobStatusEnum';
import { JobStatus } from '../../interfaces/JobStatus';
import { redis } from '../../services/RedisService';
import { createJobFlow } from '../../scheduler/Queue';
import { Platform } from '../../interfaces/FetchTypes';
import { Type } from '@sinclair/typebox';
import { JobNode } from 'bullmq';
import { logger } from '@om/logger';

type AnyJobDataType =
  | UnasJobRequestType
  | WoocommerceJobRequestType
  | ShopifyJobRequestType
  | GoMagJobRequestType;

function* batchIterator(
  items: AnyJobDataType[],
  batchSize: number,
): Generator<AnyJobDataType[]> {
  for (let i = 0; i < items.length; i += batchSize) {
    yield items.slice(i, i + batchSize);
  }
}

async function processBatches(
  jobs: Array<AnyJobDataType>,
  batchSize = 10,
  delayMs = 10,
): Promise<JobNode[]> {
  const results: JobNode[] = [];

  const batchOfJobs = batchIterator(jobs, batchSize);

  for (const batch of batchOfJobs) {
    const batchResults = await Promise.all(batch.map((job) => startJob(job)));
    results.push(...batchResults);

    if (batchOfJobs.next().done === false) {
      await new Promise((resolve) => setTimeout(resolve, delayMs));
    }
  }

  return results;
}

const startJob = async (job: AnyJobDataType) => {
  let newJob:
    | UnasJobRequestType
    | WoocommerceJobRequestType
    | ShopifyJobRequestType
    | GoMagJobRequestType;

  switch (job.platform) {
    case 'unas':
      newJob = job as UnasJobRequestType;
      break;
    case 'woo':
      newJob = job as WoocommerceJobRequestType;
      break;
    case 'shopify':
      newJob = job as ShopifyJobRequestType;
      break;
    case 'gomag':
      newJob = job as GoMagJobRequestType;
      break;
  }
  return await createJobFlow(redis, newJob);
};

export const resyncProductsRoute = (
  fastify: FastifyInstance,
): FastifyInstance => {
  return fastify.post<{
    Body: ProductResyncRequestType;
    Reply: {
      message: string;
    };
  }>(
    '/resync',
    {
      schema: {
        body: {
          ...ProductResyncRequestSchema,
        },
        response: {
          200: Type.Object({
            message: Type.String(),
          }),
        },
      },
    },
    async (request, reply) => {
      // Map incoming servieIntegrations fields to prod sync compilant
      const shops = request.body.data.map(
        (shop: BackendProductSyncType) =>
          ({
            databaseId: shop.accountId,
            providerServiceId: shop.shopId,
            platform: shop.shopType,
            apiKey: shop.token || null,
            consumerKey: shop.username || null,
            consumerSecret: shop.password || null,
          }) as AnyJobDataType,
      );

      // Get latest job statuses from job_statuses table by shops
      const shopStatuses: Array<JobStatus> =
        await JobStatusService.getJobStatusesByShops(dbService.knex, shops);

      // Find shops that are not present in shopStatuses
      const missingShops = shops.filter(
        (shop) =>
          !shopStatuses.some(
            (status) =>
              status.databaseId == shop.databaseId &&
              status.providerServiceId === shop.providerServiceId,
          ),
      );

      // Optional for now, also attach those jobs which did not ran yet
      // Originally the first sync should run when shop is added
      const threeHoursAgo = Date.now() - ********;

      // Filter jobs needs to run
      let failedJobsCount = 0;
      let activeWaitingJobsCount = 0;
      let recentJobsCount = 0;
      
      const filteredJobs = shopStatuses.filter((status: JobStatus) => {
        if (status.status === JobStatusEnum.FAILED) {
          failedJobsCount++;
          return true;
        }

        if (
          status.status === JobStatusEnum.ACTIVE ||
          status.status === JobStatusEnum.WAITING
        ) {
          activeWaitingJobsCount++;
          return false;
        }

        if (status.updatedAt) {
          const lastUpdate = new Date(status.updatedAt).getTime();
          if (threeHoursAgo - lastUpdate > 0) {
            return true;
          } else {
            recentJobsCount++;
          }
        }

        return false;
      });

      // Get shops subset for run and add locale also
      const shopsForSync = filteredJobs.map((job) => {
        const activeShop = shops.find(
          (shop) =>
            shop.databaseId === job.databaseId &&
            shop.providerServiceId === job.providerServiceId,
        );

        // Ensure job.platform is of type Platform
        return {
          ...(activeShop as AnyJobDataType),
          platform: job.platform as Platform,
        };
      });

      const allShopsForSync = [...shopsForSync, ...missingShops];

      // Log filtering results
      logger.info({
        message: 'Resync filtering summary',
        data: {
          totalShopsRequested: shops.length,
          shopsInDatabase: shopStatuses.length,
          missingShops: missingShops.length,
          failedJobs: failedJobsCount,
          activeWaitingJobs: activeWaitingJobsCount,
          recentJobs: recentJobsCount,
          eligibleJobs: filteredJobs.length,
          totalJobsToStart: allShopsForSync.length,
        },
      });

      const runningJobs = await processBatches(allShopsForSync);

      reply.code(200).send({
        message: `Resync started: ${runningJobs.length}`,
      });
      return;
    },
  );
};
