import { flowRoute } from './Flow';
import { flowStatusRoute } from './FlowStatus';
import { getProductsByIdsRoute } from './GetProductsById';
import { queryJobsRoute } from './QueryJobs';
import { dbHealthRoute } from './Health';
import { queryProductsRoute } from './QueryProducts';
import { getPopularProductsRoute } from './GetPopularProducts';
import { runBigQueryQueryRoute } from './RunBQuery';
import { shopSettings } from './ShopSettings';
import { resyncProductsRoute } from './Resync';

export const routes = [
  flowRoute,
  flowStatusRoute,
  getPopularProductsRoute,
  getProductsByIdsRoute,
  queryJobsRoute,
  queryProductsRoute,
  runBigQueryQueryRoute,
  dbHealthRoute,
  shopSettings,
  resyncProductsRoute,
];
