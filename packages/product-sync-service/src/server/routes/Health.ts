import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { dbService } from '../../services/PostgresService';

export const dbHealthRoute = (fastify: FastifyInstance): FastifyInstance => {
  return fastify.get<{
    Reply: string;
  }>(
    '/health',
    {
      schema: {
        response: {
          200: {
            type: 'string',
          },
        },
      },
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      const isHealthy = await dbService.healthCheck();

      return reply.send(isHealthy ? 'OK' : 'FAILED');
    },
  );
};
