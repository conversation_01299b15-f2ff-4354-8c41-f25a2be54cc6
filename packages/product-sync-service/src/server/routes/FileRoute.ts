import { resolve, join } from 'node:path';
import fs from 'node:fs';
import mime from 'mime-types';
import { RouteHandlerMethod } from 'fastify';

export const fileRoute: RouteHandlerMethod = async (request, reply) => {
  const rootDir = resolve(process.cwd(), 'public');
  const filePath = resolve(join(rootDir, 'unas.csv'));

  request.log.info(`File to download: ${filePath}`);

  try {
    await fs.promises.access(filePath, fs.constants.R_OK);
    const stats = await fs.promises.stat(filePath);

    if (!stats.isFile()) {
      reply.code(404).send('Not found');
      return;
    }
    // Read the entire file content
    const fileContent = await fs.promises.readFile(filePath);

    // Set headers and send the file content as response
    const mimeType = mime.lookup(filePath) || 'application/octet-stream';
    reply.header('Content-Type', mimeType);
    reply.header('Content-Length', stats.size);
    reply.send(fileContent);
  } catch (error) {
    request.log.error('Stream error:', error);
  }
};
