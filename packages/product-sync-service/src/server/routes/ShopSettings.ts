import { FastifyInstance } from 'fastify';
import { dbService } from '../../services/PostgresService';
import { ShopSettingsService } from '../../services/ShopSettingsService';

import {
  GetShopSettingsRequestType,
  GetShopSettingsRequestTypeSchema,
  GetShopSettingsResponseType,
  GetShopSettingsResponseSchema,
} from '../../interfaces/RestTypes';

export const shopSettings = (fastify: FastifyInstance): FastifyInstance => {
  return fastify.get<{
    Querystring: GetShopSettingsRequestType;
    Reply: { shopSettings: GetShopSettingsResponseType };
  }>(
    '/getShopSettings',
    {
      schema: {
        querystring: GetShopSettingsRequestTypeSchema,
        response: {
          200: { shopSettings: GetShopSettingsResponseSchema },
        },
      },
    },
    async (request, reply) => {
      const { databaseId, providerServiceId } = request.query;

      const service = new ShopSettingsService(dbService.knex);
      const data = await service.getShopSettings({
        databaseId,
        providerServiceId,
      });

      reply.code(200).send({ shopSettings: data ?? null });
      return;
    },
  );
};
