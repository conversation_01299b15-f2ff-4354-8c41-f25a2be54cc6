import { FastifyInstance } from 'fastify';
import { ProductService } from '../../services/ProductService';
import { dbService } from '../../services/PostgresService';
import {
  GetPopularProductsSchema,
  GetPopularProductsSchemaType,
  GetProductsByIdsResponseType,
  GetProductsByIdsResponseSchema,
} from '../../interfaces/RestTypes';

export const getPopularProductsRoute = (
  fastify: FastifyInstance,
): FastifyInstance => {
  return fastify.post<{
    Body: GetPopularProductsSchemaType;
    Reply: GetProductsByIdsResponseType;
  }>(
    '/products/popular',
    {
      schema: {
        body: {
          ...GetPopularProductsSchema,
        },
        response: {
          200: GetProductsByIdsResponseSchema,
        },
      },
    },
    async (request, reply) => {
      try {
        const service = new ProductService(dbService.knex);
        const { databaseId, providerServiceId } = request.body;

        const redis = request.server.services.redis;
        if (!redis) {
          throw new Error('Redis instance is undefined');
        }

        // Most popular product ids should be store in redis
        const key = `products:popular:${databaseId}:${providerServiceId}`;
        const productIdsFromCache = await redis.get(key);

        // Most popular products not found in cache, reply empty product array
        if (!productIdsFromCache) {
          reply.send({
            products: [],
          });
          return;
        }

        const productIds = productIdsFromCache && JSON.parse(productIdsFromCache);

        const result = {
          products: await service.getProductsByIds(
            productIds,
            databaseId,
            providerServiceId,
          ),
        };

        reply.code(200).send(result);
        return;
      } catch (error: any) {
        reply.status(500).send(error);
      }
    },
  );
};
