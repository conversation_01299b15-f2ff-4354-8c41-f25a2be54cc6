import { FastifyInstance } from 'fastify';
import {
  ShopifyJobRequestSchema,
  ShopifyJobRequestType,
  UnasJobRequestSchema,
  UnasJobRequestType,
  WoocommerceJobRequestSchema,
  WoocommerceJobRequestType,
  GoMagJobRequestType,
  GoMagJobRequestSchema,
} from '../../interfaces/RestTypes';
import { logger } from '@om/logger';
import { redis } from '../../services/RedisService';
import { createJobFlow } from '../../scheduler/Queue';

export const flowRoute = (fastify: FastifyInstance): FastifyInstance => {
  return fastify.post<{
    Body:
      | UnasJobRequestType
      | WoocommerceJobRequestType
      | ShopifyJobRequestType
      | GoMagJobRequestType;
    Reply: string;
  }>(
    '/flow',
    {
      schema: {
        body: {
          anyOf: [
            UnasJobRequestSchema,
            WoocommerceJobRequestSchema,
            ShopifyJobRequestSchema,
            GoMagJobRequestSchema,
          ],
        },
        response: {
          200: {
            type: 'string',
          },
        },
      },
    },
    async (request, reply): Promise<void> => {
      try {
        await createJobFlow(redis, request.body);
        return reply.send('Flow started');
      } catch (error) {
        logger.error('Error starting flow:', error);
        return reply.send('Flow start failed.');
      }
    },
  );
};
