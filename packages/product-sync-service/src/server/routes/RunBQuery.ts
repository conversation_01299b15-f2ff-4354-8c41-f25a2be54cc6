import { FastifyInstance } from 'fastify';
import { BigQueryClient } from '../../adapters/BigQuery';
import {
  ScheduledQuery,
  SCHEDULED_QUERIES,
} from '../../queries/SchedulerQueries';
import {
  RunBigQueryQuerySchema,
  RunBigQueryQuerySchemaType,
} from '../../interfaces/RestTypes';
import { redis } from '../../services/RedisService';
import { logger } from '@om/logger';

export const CACHE_EXPIRATION = 60 * 60 * 24; // 1 day

export interface QueryResult {
  accountId: string;
  providerServiceId: string;
  top_product_ids: string[];
}

/*
  This endpoint runs a BigQuery query by query name
  This is used for scheduled queries
*/
export const runBigQueryQueryRoute = (
  fastify: FastifyInstance,
): FastifyInstance => {
  return fastify.get<{
    Querystring: RunBigQueryQuerySchemaType;
    Reply: string;
  }>(
    '/runQuery',
    {
      schema: {
        querystring: {
          ...RunBigQueryQuerySchema,
        },
        response: {
          200: {
            type: 'string',
          },
        },
      },
    },
    async (request, reply) => {
      try {
        const { queryName } = request.query;

        const client = new BigQueryClient();

        const queryString = SCHEDULED_QUERIES.find(
          (query: ScheduledQuery) => query.name === queryName,
        )?.query;

        if (!queryString) {
          const availableQueries = SCHEDULED_QUERIES.map(
            (query: ScheduledQuery) => query.name,
          );
          return reply
            .status(400)
            .send(
              `Query not found. Available queries: ${availableQueries.join(', ')}`,
            );
        }

        client.runQueryInBackground(queryString, {}, (rows: QueryResult[]) => {
          // We have the query result in rows
          if (!rows || !rows.length) {
            return;
          }
          rows.forEach((row: QueryResult) => {
            const {
              accountId: databaseId,
              providerServiceId,
              top_product_ids: productIds,
            } = row;

            const key = `products:popular:${databaseId}:${providerServiceId}`;
            redis.set(key, JSON.stringify(productIds), 'EX', CACHE_EXPIRATION);
          });
          logger.info({
            message: `Most popular product query processed ${rows.length} rows}`,
            queryName,
          });
        });

        return reply.status(200).send('Query started');
      } catch (error: any) {
        logger.error('Error querying jobs:', error);
        reply.status(500).send(error.message);
      }
    },
  );
};
