import { FastifyInstance } from 'fastify';
import { JobStatus } from '../../interfaces/JobStatus';
import { JobStatusService } from '../../services/JobStatusService';
import { dbService } from '../../services/PostgresService';

export const flowStatusRoute = (fastify: FastifyInstance): FastifyInstance => {
  return fastify.get<{
    Querystring: { provider_service_id: string };
    Reply: JobStatus[];
  }>('/flow/status', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          provider_service_id: { type: 'string' },
        },
        required: ['provider_service_id'],
      },
    },
    handler: async (request, reply) => {
      const { provider_service_id } = request.query;
      const jobStatuses: JobStatus[] = await JobStatusService.listJobStatuses(
        dbService.knex,
        provider_service_id,
      );
      return reply.send(jobStatuses);
    },
  });
};
