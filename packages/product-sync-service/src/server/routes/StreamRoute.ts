import { resolve, join } from 'node:path';
import fs from 'node:fs';
import mime from 'mime-types';
import { RouteHandlerMethod } from 'fastify';

export const streamRoute: RouteHandlerMethod = async (request, reply) => {
  const rootDir = resolve(process.cwd(), 'public');
  const filePath = resolve(join(rootDir, 'unas.csv'));

  request.log.info(`File to download: ${filePath}`);

  try {
    await fs.promises.access(filePath, fs.constants.R_OK);
    const stats = await fs.promises.stat(filePath);

    if (!stats.isFile()) {
      reply.code(404).send('Not found');
      return;
    }
    const mimeType = mime.lookup(filePath) || 'application/octet-stream';
    reply.header('Content-Type', mimeType);
    reply.header(
      'Content-Disposition',
      `attachment; filename="${filePath.split('/').pop()}"`,
    );
    const stream = fs.createReadStream(filePath);
    stream.on('error', (err) => {
      request.log.error('Stream error:', err);
      if (!reply.sent) {
        reply.code(500).send('File stream error');
      }
    });

    return reply.send(stream);
  } catch (error) {
    request.log.error('Stream error:', error);
  }
};
