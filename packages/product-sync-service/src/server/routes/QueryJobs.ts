import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { logger } from '@om/logger';
import { Job } from 'bullmq';

export interface QueryJobsResponse {
  active: Job[];
  waiting: Job[];
  completed: Job[];
  failed: Job[];
}

export const queryJobsRoute = (fastify: FastifyInstance): FastifyInstance => {
  return fastify.get<{
    Reply: QueryJobsResponse;
  }>(
    '/jobs',
    {
      schema: {
        response: {
          200: {
            type: 'object',
            properties: {
              active: {
                type: 'array',
                items: { type: 'object' },
              },
              waiting: {
                type: 'array',
                items: { type: 'object' },
              },
              completed: {
                type: 'array',
                items: { type: 'object' },
              },
              failed: {
                type: 'array',
                items: { type: 'object' },
              },
            },
          },
        },
      },
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const queue = request.server.services.flowJobQueue;
        if (queue) {
          const [activeJobs, waitingJobs, completedJobs, failedJobs] =
            await Promise.all([
              queue.getActive(),
              queue.getWaiting(),
              queue.getCompleted(),
              queue.getFailed(),
            ]);

          reply.send({
            active: activeJobs,
            waiting: waitingJobs,
            completed: completedJobs,
            failed: failedJobs,
          });
        } else {
          logger.error('Cannot find queue.');
          throw new Error('Cannot find queue.');
        }
      } catch (error) {
        logger.error('Error querying jobs:', error);
        reply.status(500).send({ error: 'Failed to query jobs' });
      }
    },
  );
};
