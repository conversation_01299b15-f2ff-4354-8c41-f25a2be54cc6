import { FastifyInstance } from 'fastify';
import { ProductService } from '../../services/ProductService';
import { dbService } from '../../services/PostgresService';
import {
  GetUnifiedProductsRequestSchema,
  GetUnifiedProductsRequestType,
  GetProductsByIdsResponseType,
  GetProductsByIdsResponseSchema,
} from '../../interfaces/RestTypes';

export const getProductsByIdsRoute = (
  fastify: FastifyInstance,
): FastifyInstance => {
  return fastify.post<{
    Body: GetUnifiedProductsRequestType;
    Reply: GetProductsByIdsResponseType;
  }>(
    '/products',
    {
      schema: {
        body: {
          ...GetUnifiedProductsRequestSchema,
        },
        response: {
          200: GetProductsByIdsResponseSchema,
        },
      },
    },
    async (request, reply) => {
      const service = new ProductService(dbService.knex);
      const { productIds, databaseId, providerServiceId, limit } = request.body;

      const result = {
        products: await service.getProductsByIds(
          productIds,
          databaseId,
          providerServiceId,
          limit,
        ),
      };

      reply.code(200).send(result);
      return;
    },
  );
};
