import { FastifyRequest, FastifyReply } from 'fastify';
import { logger } from '@om/logger';
import jwt from 'jsonwebtoken';
import config from '../../Config';
import { JWTPayload } from '../../interfaces/ServerTypes';

export const authenticate = async (
  request: FastifyRequest,
  reply: FastifyReply,
): Promise<void> => {
  try {
    if (request.url.includes('/health')) {
      return;
    }

    const token = request.headers['x-om-token'] as string;
    if (!token) {
      logger.warn(`request without token ${request.url}`);
      reply.status(401).send({ message: 'Missing token' });
      return;
    }

    try {
      const secret = config.OM_SHARED_KEY;
      const decoded = jwt.verify(token, secret) as JWTPayload;

      if (!decoded || typeof decoded !== 'object') {
        logger.error('Invalid token payload structure');
        reply.status(401).send({ message: 'Invalid token structure' });
        return;
      }

      request.user = decoded;
    } catch (error) {
      logger.error(
        `JWT verification failed: ${
          error instanceof Error ? error.message : String(error)
        }, token: ${token}`,
      );
      reply.status(401).send({ message: 'Invalid token' });
      return;
    }
    return;
  } catch (err) {
    logger.error('Authentication error:', err);
    reply.status(401).send({ message: 'Invalid token' });
  }
};
