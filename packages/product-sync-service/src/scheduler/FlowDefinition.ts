import { Platform } from "../interfaces/RestTypes";

export interface FlowJobDefinition {
  name: string;
  nextJob?: string;
  opts?: {
    attempts?: number;
    backoff?: {
      type: 'exponential' | 'fixed';
      delay: number;
    };
  };
}
// Default flow definition for the product sync process
// The flow is defined in the order of execution
export const defaultProductSyncFlow: FlowJobDefinition[] = [
  {
    name: 'fetchAndUpload',
    opts: { attempts: 3, backoff: { type: 'exponential', delay: 5000 } },
    nextJob: 'moveToBigQuery',
  },
  { name: 'moveToBigQuery', nextJob: 'transformToUnified' },
  { name: 'transformToUnified', nextJob: 'insertIntoPostgres' },
  { name: 'insertIntoPostgres', nextJob: 'storeShopSettings' },
  { name: 'storeShopSettings' },
];

export const shopifySyncFlow: FlowJobDefinition[] = [
  {
    name: 'fetchAndUpload',
    opts: { attempts: 3, backoff: { type: 'exponential', delay: 5000 } },
    nextJob: 'polling',
  },
  {
    name: 'polling',
    opts: { attempts: 5, backoff: { type: 'exponential', delay: 30000 } },
    nextJob: 'download',
  },
  {
    name: 'download',
    opts: { attempts: 3, backoff: { type: 'exponential', delay: 5000 } },
    nextJob: 'moveToBigQuery',
  },
  { name: 'moveToBigQuery', nextJob: 'transformToUnified' },
  { name: 'transformToUnified', nextJob: 'insertIntoPostgres' },
  { name: 'insertIntoPostgres', nextJob: 'generateEmbeddings' },
  { name: 'generateEmbeddings' },
  { name: 'storeShopSettings' },
];

export const getFlowDefinition = (platform: Platform): FlowJobDefinition[] => {
  switch (platform) {
    case 'shopify':
      return shopifySyncFlow;
    default:
      return defaultProductSyncFlow;
  }
};
