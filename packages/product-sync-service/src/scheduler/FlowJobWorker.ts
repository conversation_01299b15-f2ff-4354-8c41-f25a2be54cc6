import { Worker, Job } from 'bullmq';
import { UnasAdapter } from '../adapters/UnasAdapter';
import { FLOW_JOB_QUEUE_NAME } from './Queue';
import { trace, SpanStatusCode, Exception } from '@opentelemetry/api';
import { WooAdapter } from '../adapters/WooAdapter';
import { Redis } from 'ioredis';
import { ShopifyAdapter } from '../adapters/ShopifyAdapter';
import { GoMagAdapter } from '../adapters/GoMagAdapter';
import { BullMQOtel } from 'bullmq-otel';

const tracer = trace.getTracer('bullmq-worker');

type AdapterType = {
  fetchData: (job: Job) => Promise<any>;
  moveToBQ: (job: Job) => Promise<any>;
  polling?: (job: Job, token?: string) => Promise<any>;
  download?: (job: Job) => Promise<any>;
  unifyData: (job: Job) => Promise<any>;
  writeToPg: (job: Job) => Promise<any>;
  generateEmbeddings: (job: Job) => Promise<any>;
  storeShopSettings?: (job: Job) => Promise<any>;
};

export const createFlowJobWorker = (
  connection: Redis,
  limiter?: Record<string, number | string>,
): Worker => {
  const flowJobWorker = new Worker(
    FLOW_JOB_QUEUE_NAME,
    async (job: Job, token?: string) => {
      const span = tracer.startSpan(`process ${job.name}`, {
        attributes: {
          'job.id': job.id,
          'job.name': job.name,
          'queue.name': FLOW_JOB_QUEUE_NAME,
          'job.attempts': job.attemptsMade,
          'job.platform': job.data.platform,
          'job.providerServiceId': job.data.providerServiceId,
          'job.databaseId': job.data.databaseId,
        },
      });

      let adapter: AdapterType;
      switch (job.data.platform) {
        case 'woo':
          adapter = WooAdapter;
          break;
        case 'unas':
          adapter = UnasAdapter;
          break;
        case 'shopify':
          adapter = ShopifyAdapter;
          break;
        case 'gomag':
          adapter = GoMagAdapter;
          break;
        default:
          throw new Error(`Unknown platform: ${job.data.platform}`);
      }

      try {
        let result;

        switch (job.name) {
          case 'fetchAndUpload':
            result = await adapter.fetchData(job);
            break;
          case 'storeShopSettings':
            if (adapter.storeShopSettings) {
              result = await adapter.storeShopSettings(job);
            }
            break;
          case 'moveToBigQuery':
            result = await adapter.moveToBQ(job);
            break;
          case 'polling':
            if (adapter.polling) {
              result = await adapter.polling(job, token);
            }
            break;
          case 'download':
            if (adapter.download) {
              result = await adapter.download(job);
            }
            break;
          case 'transformToUnified':
            result = await adapter.unifyData(job);
            break;
          case 'insertIntoPostgres':
            result = await adapter.writeToPg(job);
            break;
          case 'generateEmbeddings':
            result = await adapter.generateEmbeddings(job);
            break;
          default:
            throw new Error(`Unknown job: ${job.name}`);
        }
        span && span.setStatus({ code: SpanStatusCode.OK });
        return result;
      } catch (error: any) {
        span && span.recordException(error as Exception);
        span &&
          span.setStatus({
            code: SpanStatusCode.ERROR,
            message: error?.message,
          });
        throw error;
      } finally {
        span && span.end();
      }
    },
    {
      connection,
      telemetry: new BullMQOtel(FLOW_JOB_QUEUE_NAME),
      ...(limiter ? limiter : {}),
    },
  );
  return flowJobWorker;
};
