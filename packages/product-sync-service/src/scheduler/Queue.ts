import { Job, JobNode, Queue } from 'bullmq';
import { FetchConfig, FetchType } from '../interfaces/FetchTypes';
import { getFlowDefinition, FlowJobDefinition } from './FlowDefinition';
import { getFlowProducer } from './FlowProducer';
import { getRateLimit } from '../utils/RateLimit';
import { propagation, context, trace, Exception } from '@opentelemetry/api';
import {
  ShopifyJobRequestType,
  UnasJobRequestType,
  WoocommerceJobRequestType,
  GoMagJobRequestType,
} from '../interfaces/RestTypes';
import { Redis } from 'ioredis';
import { BullMQOtel } from 'bullmq-otel';

export const FLOW_QUEUE_NAME = 'flow-queue';
export const FLOW_JOB_QUEUE_NAME = 'flow-job-queue';

const SpanStatusCode = {
  OK: 1,
  ERROR: 2,
  UNSET: 0,
} as const;

export interface JobContext {
  traceparent?: string;
  tracestate?: string;
}

// Main queue
export const getSyncJobQueue = (connection: Redis): Queue =>
  new Queue(FLOW_QUEUE_NAME, {
    connection,
    telemetry: new BullMQOtel(FLOW_QUEUE_NAME),
  });

export async function scheduleFetch<T extends FetchType>(
  connection: Redis,
  fetchConfig: FetchConfig<T>,
): Promise<Job<any, any, string>> {
  return getSyncJobQueue(connection).add(fetchConfig.type, fetchConfig, {
    repeat: fetchConfig.schedule
      ? { pattern: fetchConfig.schedule }
      : undefined,
  });
}

// Child job queue
export const createFlowChildJobQueue = (connection: Redis): Queue => {
  return new Queue(FLOW_JOB_QUEUE_NAME, {
    connection,
    telemetry: new BullMQOtel(FLOW_JOB_QUEUE_NAME),
  });
};

export async function createJobFlow(
  connection: Redis,
  config:
    | UnasJobRequestType
    | WoocommerceJobRequestType
    | ShopifyJobRequestType
    | GoMagJobRequestType,
  parentJobData: Record<string, unknown> = {},
): Promise<JobNode> {
  const tracer = trace.getTracer('bullmq-flow-producer');
  const span = tracer.startSpan('create-job-flow');

  return context.with(trace.setSpan(context.active(), span), async () => {
    try {
      const carrier: JobContext = {};
      propagation.inject(context.active(), carrier);

      const tree = await getFlowProducer(connection).add({
        name: `sync-flow-job`,
        queueName: FLOW_QUEUE_NAME,
        data: {
          ...parentJobData,
          ...carrier,
        },
        children: createJobFlowChildren(
          config,
          carrier,
          getFlowDefinition(config.platform),
        ),
      });

      span.setStatus({ code: SpanStatusCode.OK });
      return tree;
    } catch (error) {
      span.recordException(error as unknown as Exception);
      span.setStatus({ code: SpanStatusCode.ERROR });
      throw error;
    } finally {
      span.end();
    }
  });
}

// Define the job node structure for BullMQ
export interface JobTreeNode {
  name: string;
  data: Record<string, unknown>;
  queueName: string;
  opts: Record<string, unknown>;
  children: JobTreeNode[];
}

// Helper function to build a tree from flat flow definitions
// The flow is defined in the order of execution (first job in the list is the first to run)
// The tree is built in reverse order (last job to execute is the root, first job is the leaf)
// This allows for a more intuitive flow definition while still producing the correct tree structure
export function buildJobTree(
  flowDefinitions: FlowJobDefinition[],
  data: Record<string, unknown>,
  queueName: string,
): JobTreeNode[] {
  // Create a map of job definitions by name for easy lookup
  const jobMap = new Map<string, FlowJobDefinition>();
  flowDefinitions.forEach((job) => jobMap.set(job.name, job));

  // Create a map to track previous jobs (which jobs come before this one)
  const prevJobsMap = new Map<string, string[]>();

  // Initialize previous jobs map
  flowDefinitions.forEach((job) => {
    const nextJobs = Array.isArray(job.nextJob)
      ? job.nextJob
      : job.nextJob
        ? [job.nextJob]
        : [];

    nextJobs.forEach((nextJob) => {
      if (!prevJobsMap.has(nextJob)) {
        prevJobsMap.set(nextJob, []);
      }
      const prevJobsList = prevJobsMap.get(nextJob);
      if (prevJobsList) {
        prevJobsList.push(job.name);
      }
    });
  });

  // Find leaf jobs (those that don't have any next jobs)
  const leafJobs = flowDefinitions.filter(
    (job) =>
      !job.nextJob || (Array.isArray(job.nextJob) && job.nextJob.length === 0),
  );

  // Recursive function to build the tree in reverse
  function buildTreeNode(jobName: string): JobTreeNode {
    const job = jobMap.get(jobName);
    if (!job) {
      throw new Error(`Job ${jobName} not found in job definitions`);
    }

    const prevJobs = prevJobsMap.get(jobName) || [];

    return {
      name: job.name,
      data,
      queueName,
      opts: job.opts || {},
      children:
        prevJobs.length > 0
          ? prevJobs.map((prevJobName) => buildTreeNode(prevJobName))
          : [],
    };
  }

  // Build the tree starting from leaf jobs
  return leafJobs.map((job) => buildTreeNode(job.name));
}

// Helper function to create job flow children
function createJobFlowChildren(
  config:
    | UnasJobRequestType
    | WoocommerceJobRequestType
    | ShopifyJobRequestType
    | GoMagJobRequestType,
  carrier: JobContext = {},
  flow: FlowJobDefinition[],
) {
  // Add rate limits to fetchAndUpload job if it exists
  const flowWithRateLimits = flow.map((job) => {
    if (job.name === 'fetchAndUpload') {
      return {
        ...job,
        opts: {
          ...job.opts,
          ...getRateLimit(config.platform),
        },
      };
    }
    return job;
  });

  // Prepare the job data with config and carrier
  const jobData = { ...config, ...carrier };
  return buildJobTree(flowWithRateLimits, jobData, FLOW_JOB_QUEUE_NAME);
}
