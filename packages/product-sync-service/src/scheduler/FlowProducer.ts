import { FlowProducer } from 'bullmq';
import { logger } from '@om/logger';
import { Redis } from 'ioredis';
class FlowProducerSingleton {
  private static instance: FlowProducer;

  public static getInstance(connection: Redis): FlowProducer {
    if (!FlowProducerSingleton.instance) {
      FlowProducerSingleton.instance = new FlowProducer({
        connection,
        // prefix: process.env.BULLMQ_PREFIX || 'bullmq',
      });
    }
    this.instance.on('error', (error) => logger.error(error));
    process.on('SIGTERM', async () => {
      logger.info('Closing flowproducer.');
      await this.instance.close();
    });
    return FlowProducerSingleton.instance;
  }

  public static async close(): Promise<void> {
    if (FlowProducerSingleton.instance) {
      await FlowProducerSingleton.instance.close();
      FlowProducerSingleton.instance = null as unknown as FlowProducer;
    }
  }
}

export const getFlowProducer = (connection: Redis): FlowProducer =>
  FlowProducerSingleton.getInstance(connection);
