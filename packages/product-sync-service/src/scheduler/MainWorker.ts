import { Worker } from 'bullmq';
import { FLOW_QUEUE_NAME } from './Queue';
import { logger } from '@om/logger';
import { Redis } from 'ioredis';

export const createMainWorker = (
  connection: Redis,
  limiter?: Record<string, number | string>,
) => {
  const mainWorker = new Worker(
    FLOW_QUEUE_NAME,
    // async (job: Job<FetchConfig<FetchType>>) => {},
    async () => {},
    {
      connection,
      ...(limiter ? limiter : {}),
    },
  );

  // Handle different result types
  mainWorker.on('completed', (job, result) => {
    switch (job.data.type) {
      case 'stream':
        logger.info('Stream processing completed');
        break;
      case 'single':
        logger.info('Single product:', result);
        break;
      case 'array':
        logger.info('Product array:');
        break;
    }
  });
  return mainWorker;
};
