type FlattenedObject = Record<string, unknown>;

export function flattenConfig<T extends Record<string, any>>(
  obj: T,
  prefix = 'bull',
): FlattenedObject {
  const result: FlattenedObject = {};

  for (const [key, value] of Object.entries(obj)) {
    if (key.startsWith(prefix)) {
      if (isObject(value)) {
        Object.assign(result, flattenConfig(value, prefix));
      }
    } else {
      if (isObject(value)) {
        Object.assign(result, flattenConfig(value, prefix));
      } else {
        result[key] = value;
      }
    }
  }

  return result;
}

function isObject(value: unknown): value is Record<string, unknown> {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}
