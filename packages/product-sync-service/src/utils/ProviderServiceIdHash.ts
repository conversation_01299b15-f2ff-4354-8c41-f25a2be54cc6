import crypto from 'crypto';

/**
 * Generates a SHA-256 hash of the provider service ID and returns the first 6 characters.
 *
 * @param {string} providerServiceId - The provider service ID to hash.
 * @returns {string} - The first 6 characters of the SHA-256 hash of the provider service ID.
 */
export const hashProvider = (providerServiceId: string): string => {
  return crypto
    .createHash('sha256')
    .update(providerServiceId)
    .digest('hex')
    .slice(0, 6);
};
