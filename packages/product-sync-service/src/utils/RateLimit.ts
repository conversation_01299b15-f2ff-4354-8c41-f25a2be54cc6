import { Platform } from '../interfaces/RestTypes';

export interface RateLimit {
  max: number;
  duration: number;
  groupKey: string;
}

export type RateLimits = Record<Platform, RateLimit>;

const rateLimits: RateLimits = {
  unas: {
    max: 1,
    duration: 5000,
    groupKey: 'unas-stream',
  },
  woo: {
    max: 1,
    duration: 5000,
    groupKey: 'woo-stream',
  },
  shopify: {
    max: 1,
    duration: 5000,
    groupKey: 'shopify-stream',
  },
  gomag: {
    max: 1,
    duration: 5000,
    groupKey: 'gomag-stream',
  },
};

export const getRateLimit = (
  platform: Platform,
): RateLimit | Record<string, never> =>
  !rateLimits[platform] && !rateLimits[platform] ? {} : rateLimits[platform];
