import { Knex } from 'knex';

export async function seed(knex: Knex): Promise<void> {
  // Only run in test environment
  if (process.env.NODE_ENV !== 'test') {
    return;
  }

  // Sample test products
  const testProducts = [
    {
      timestamp: new Date(),
      databaseId: -1,
      providerServiceId: 'testshop.dev',
      status: 'active',
      locale: 'en-US',
      productId: 'test-product-1',
      name: 'Test Product 1',
      sku: 'SKU-001',
      price: 99.99,
      originalPrice: 129.99,
      currency: 'USD',
      inStock: true,
      imageUrl: 'https://example.com/test-product-1.jpg',
      description: 'This is a test product 1 description',
      parentProductId: 'test-parent-1',
      productUrl: 'https://testshop.dev/products/test-product-1',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      timestamp: new Date(),
      databaseId: -1,
      providerServiceId: 'testshop.dev',
      status: 'active',
      locale: 'en-US',
      productId: 'test-product-2',
      name: 'Test Product 2',
      sku: 'SKU-002',
      price: 149.99,
      originalPrice: 199.99,
      currency: 'USD',
      inStock: true,
      imageUrl: 'https://example.com/test-product-2.jpg',
      description: 'This is a test product 2 description',
      parentProductId: 'test-parent-2',
      productUrl: 'https://testshop.dev/products/test-product-2',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      timestamp: new Date(),
      databaseId: -1,
      providerServiceId: 'testshop.dev',
      status: 'inactive',
      locale: 'en-US',
      productId: 'test-product-3',
      name: 'Test Product 3',
      sku: 'SPECIAL-SKU-003',
      price: 49.99,
      originalPrice: 49.99,
      currency: 'USD',
      inStock: true,
      imageUrl: 'https://example.com/test-product-3.jpg',
      description: 'This is a test product 3 description',
      parentProductId: null,
      productUrl: 'https://testshop.dev/products/test-product-3',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      timestamp: new Date(),
      databaseId: -2,
      providerServiceId: 'anothershop.dev',
      status: 'active',
      locale: 'en-US',
      productId: 'test-product-1', // conflicting in purpose
      name: 'Test Product 4',
      sku: 'SKU-004',
      price: 199.99,
      originalPrice: 249.99,
      currency: 'USD',
      inStock: true,
      imageUrl: 'https://example.com/test-product-4.jpg',
      description: 'This is a test product 4 description',
      parentProductId: 'test-parent-4',
      productUrl: 'https://anothershop.dev/products/test-product-4',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  // Insert test products
  await knex('products').insert(testProducts);
}

export async function unseed(knex: Knex): Promise<void> {
  // Only run in test environment
  if (process.env.NODE_ENV !== 'test') {
    return;
  }

  // Clean up test data
  await knex('products')
    .where('databaseId', -1)
    .where('providerServiceId', 'testshop.dev')
    .del();
}
