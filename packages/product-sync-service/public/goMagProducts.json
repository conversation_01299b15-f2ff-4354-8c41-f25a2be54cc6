{"total": 3, "page": 1, "pages": 1, "products": {"1": {"id": "1", "sku": "C1", "name": "Test product", "description": "<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">Test product description</font></font></font></font>", "short_description": "", "url": "http://placeholder9129.gomag.ro/test-product.html", "canonical_url": "http://placeholder9129.gomag.ro/best-sellers/test-product.html", "brand": null, "categories": [[{"id": "1", "name": "Best sellers"}]], "weight": "0", "enabled": "1", "parent": "1", "parent_sku": "C1", "stockManagement": 1, "stock": "10", "stock_status": "instock", "base_price": "100.0000", "price": "90.0000", "vat_included": 1, "vat": "19", "currency": "<PERSON><PERSON>", "ecotax": "0.0000", "um": null, "html_title": "", "html_description": "", "customSearchKeys": "", "feedDescription": "", "allowOrdersWhenOutOfStock": 0, "attributes": [], "images": [], "ean": "", "group_key": "", "videos": null, "files": null, "updated": "2025-05-28 09:10:07", "created": "2025-05-26 17:51:33", "delivery_time": null, "delivery_time_type": "text", "bundleItems": []}, "2": {"id": "2", "sku": "b685297e-f5f2-4a68-93fb-8542e6", "name": "Intelligent Wooden Chips", "description": "You can't quantify the capacitor without programming the cross-platform IB interface!", "short_description": "circuit administration Card Singapore", "url": "http://placeholder9129.gomag.ro/intelligent-wooden-chips.html", "canonical_url": "http://placeholder9129.gomag.ro/categorie-principala-practical/intelligent-wooden-chips.html", "brand": "Health", "categories": [[{"id": "2", "name": "Categorie principala Practical"}, {"id": "3", "name": "Categorie secundara Gorgeous"}], [{"id": "4", "name": "Categorie principala Handmade"}, {"id": "5", "name": "Categorie secundara Gorgeous"}]], "weight": "715", "enabled": "1", "parent": "2", "parent_sku": "", "stockManagement": 1, "stock": "367", "stock_status": "instock", "base_price": "815.0800", "price": "195.8200", "vat_included": 1, "vat": "19", "currency": "<PERSON><PERSON>", "ecotax": "0.0000", "um": "Kg", "html_title": "Mouse", "html_description": "maroon Representative firewall", "customSearchKeys": "", "feedDescription": "", "allowOrdersWhenOutOfStock": 0, "attributes": [{"id": "4", "type": "dropdown", "name": "Nume atribut - firewall", "value": "Soft"}, {"id": "5", "type": "multipleselect", "name": "Nume atribut - transmitter", "value": ["Metal"]}], "images": [], "ean": "915834", "group_key": "Cod Grupa - 647", "videos": null, "files": null, "updated": "2025-05-28 09:09:57", "created": "2025-05-28 09:08:56", "delivery_time": "<PERSON>hu <PERSON> 23 2025 15:28:50 GMT+0000 (Coordinated Universal Time)", "delivery_time_type": "text", "bundleItems": []}, "3": {"id": "3", "sku": "b9227523-c435-448e-98d0-536b75", "name": "Intelligent Wooden Chips", "description": "You can't quantify the capacitor without programming the cross-platform IB interface!", "short_description": "circuit administration Card Singapore", "url": "http://placeholder9129.gomag.ro/intelligent-wooden-chips-3.html", "canonical_url": "http://placeholder9129.gomag.ro/categorie-principala-practical/intelligent-wooden-chips-3.html", "brand": "Health", "categories": [[{"id": "2", "name": "Categorie principala Practical"}, {"id": "3", "name": "Categorie secundara Gorgeous"}], [{"id": "4", "name": "Categorie principala Handmade"}, {"id": "5", "name": "Categorie secundara Gorgeous"}]], "weight": "838", "enabled": "1", "parent": "2", "parent_sku": "", "stockManagement": "1", "stock": "361", "stock_status": "instock", "base_price": "842.1900", "price": "825.7000", "vat_included": 1, "vat": "19", "currency": "<PERSON><PERSON>", "ecotax": "0.0000", "um": "Kg", "html_title": "Mouse", "html_description": "maroon Representative firewall", "customSearchKeys": "", "feedDescription": "", "allowOrdersWhenOutOfStock": "0", "attributes": [{"id": "6", "type": "dropdown", "name": "Nume atribut - system", "value": "Soft"}, {"id": "5", "type": "multipleselect", "name": "Nume atribut - transmitter", "value": ["Metal"]}], "images": [], "ean": "956857", "group_key": "Cod Grupa - 647", "videos": null, "files": null, "updated": "2025-05-28 09:09:57", "created": "2025-05-28 09:08:56", "delivery_time": "Fri Jul 19 2024 03:06:49 GMT+0000 (Coordinated Universal Time)", "delivery_time_type": "text", "bundleItems": []}}, "includeHandlingTime": false}