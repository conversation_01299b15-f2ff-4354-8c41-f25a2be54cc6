# PostgresSyncService Documentation

## Overview

The `PostgresSyncService` class is responsible for synchronizing data from BigQuery to a PostgreSQL database. It streams data from BigQuery, processes it in chunks, and inserts or updates the data in the PostgreSQL `products` table. Additionally, it deletes outdated records from the database.

---

## Class: `PostgresSyncService`

### Constructor

#### `constructor(bigQuery: BigQueryClient, dbService: any)`

**Description**:  
Initializes the `PostgresSyncService` with a BigQuery client and a PostgreSQL database service.

**Parameters**:

- `bigQuery: BigQueryClient`  
  An instance of the BigQuery client for querying data.
- `dbService: any`  
  A database service instance (e.g., Knex.js) for interacting with PostgreSQL.

---

### Methods

#### 1. `syncDataToPostgres(databaseId: number, providerServiceId: string, datasetId: string, productsTable: string): Promise<void>`

**Description**:  
Synchronizes data from BigQuery to the PostgreSQL `products` table. It streams data from BigQuery, processes it in chunks, and inserts or updates the data in PostgreSQL. Outdated records are deleted after synchronization.

**Parameters**:

- `databaseId: number`  
  The database identifier.
- `providerServiceId: string`  
  The unique identifier of the provider.
- `datasetId: string`  
  The BigQuery dataset ID.
- `productsTable: string`  
  The BigQuery table name containing product data.

**Returns**:

- `Promise<void>`  
  Resolves when the synchronization is complete.

**Steps**:

1. Queries BigQuery for product data matching the `databaseId` and `providerServiceId` within the last 30 days.
2. Streams the data and processes it in chunks of 100 rows.
3. Inserts or updates the data in the PostgreSQL `products` table.
4. Deletes outdated records from the `products` table.

**Error Handling**:

- Logs an error message if the synchronization fails.
- Throws an error if the operation cannot be completed.

**Example Usage**:

```typescript
const postgresSyncService = new PostgresSyncService(bigQueryClient, dbService);
await postgresSyncService.syncDataToPostgres(
  1,
  'provider-id',
  'dataset-id',
  'products-table',
);
```

---

#### 2. `private insertChunk(chunk: any[]): Promise<void>`

**Description**:  
Inserts a chunk of data into the PostgreSQL `products` table. If a conflict occurs (based on `parentProductId`, `providerServiceId`, and `productId`), the existing record is updated.

**Parameters**:

- `chunk: any[]`  
  An array of product data to be inserted or updated.

**Returns**:

- `Promise<void>`  
  Resolves when the chunk is successfully inserted or updated.

**Steps**:

1. Inserts the chunk into the `products` table.
2. If a conflict occurs, updates the existing record with the new data.
3. Clears the chunk array after processing.

**Error Handling**:

- Skips processing if the chunk is empty.

**Example Usage**:

```typescript
await postgresSyncService.insertChunk(productChunk);
```

---

## Database Table: `products`

The `products` table stores product data synchronized from BigQuery. Below is the structure of the table:

| Column              | Type        | Description                                     |
| ------------------- | ----------- | ----------------------------------------------- |
| `timestamp`         | `timestamp` | The timestamp of the product data.              |
| `databaseId`        | `number`    | The database identifier.                        |
| `providerServiceId` | `string`    | The unique identifier of the provider.          |
| `status`            | `string`    | The status of the product.                      |
| `locale`            | `string`    | The locale of the product (e.g., `en`, `hu`).   |
| `productId`         | `string`    | The unique identifier of the product.           |
| `name`              | `string`    | The name of the product.                        |
| `price`             | `number`    | The price of the product.                       |
| `originalPrice`     | `number`    | The original price of the product.              |
| `currency`          | `string`    | The currency of the product price.              |
| `imageUrl`          | `string`    | The URL of the product image.                   |
| `description`       | `string`    | The description of the product.                 |
| `parentProductId`   | `string`    | The ID of the parent product (if applicable).   |
| `createdAt`         | `timestamp` | The timestamp when the record was created.      |
| `updatedAt`         | `timestamp` | The timestamp when the record was last updated. |

---

## Dependencies

- **`BigQueryClient`**:  
  Used for querying data from BigQuery.
- **`dbService`**:  
  Used for interacting with the PostgreSQL database.
- **`logger`**:  
  Used for logging messages and errors.

---

## Example Usage

### Synchronize Data to PostgreSQL

```typescript
import { PostgresSyncService } from './PostgresSyncService';
import { BigQueryClient } from '../adapters/BigQuery';
import { dbService } from '../services/PostgresService';

const bigQueryClient = new BigQueryClient();
const postgresSyncService = new PostgresSyncService(bigQueryClient, dbService);

await postgresSyncService.syncDataToPostgres(
  1,
  'provider-id',
  'dataset-id',
  'products-table',
);
```

---

## Summary

The `PostgresSyncService` class provides a robust solution for synchronizing data from BigQuery to PostgreSQL. It efficiently handles large datasets by processing them in chunks and ensures data consistency through conflict resolution. The class also includes functionality for cleaning up outdated records, making it a comprehensive tool for data synchronization workflows.
