# JobStatusService Documentation

## Overview

The `JobStatusService` class is responsible for managing the status of jobs in the `flow_statuses` database table. It provides methods to insert or update job statuses and retrieve a list of job statuses for a specific provider.

---

## Class: `JobStatusService`

### Methods

#### 1. `insertFlowStatus(job: Job, status: JobStatusEnum, error?: string | null): Promise<void>`

**Description**:  
Inserts or updates the status of a job in the `flow_statuses` table. If a conflict occurs (based on `jobName` and `providerServiceId`), the existing record is updated.

**Parameters**:

- `job: Job`  
  The BullMQ job object containing job details.
- `status: JobStatusEnum`  
  The status of the job (e.g., `ACTIVE`, `COMPLETED`, `FAILED`).
- `error?: string | null`  
  An optional error message if the job failed.

**Returns**:

- `Promise<void>`  
  Resolves when the operation is complete.

**Steps**:

1. Inserts a new record into the `flow_statuses` table with the provided job details.
2. If a conflict occurs (based on `jobName` and `providerServiceId`), updates the `status`, `errorMessage`, and `updatedAt` fields.

**Error Handling**:

- Logs an error message if the database operation fails.

**Example Usage**:

```typescript
await JobStatusService.insertFlowStatus(job, JobStatusEnum.ACTIVE);
```

---

#### 2. `listJobStatuses(providerServiceId: string): Promise<any[]>`

**Description**:  
Retrieves a list of job statuses for a specific provider, ordered by the `createdAt` timestamp in descending order.

**Parameters**:

- `providerServiceId: string`  
  The unique identifier of the provider whose job statuses are to be retrieved.

**Returns**:

- `Promise<any[]>`  
  An array of job status records.

**Steps**:

1. Queries the `flow_statuses` table for records matching the given `providerServiceId`.
2. Orders the results by the `createdAt` field in descending order.

**Error Handling**:

- Logs an error message if the database query fails.
- Throws an error if the operation cannot be completed.

**Example Usage**:

```typescript
const statuses = await JobStatusService.listJobStatuses('provider-id');
console.log(statuses);
```

---

## Database Table: `flow_statuses`

The `flow_statuses` table stores job status information. Below is the structure of the table:

| Column              | Type        | Description                                     |
| ------------------- | ----------- | ----------------------------------------------- |
| `platform`          | `string`    | The platform associated with the job.           |
| `status`            | `string`    | The status of the job (e.g., `ACTIVE`).         |
| `providerServiceId` | `string`    | The unique identifier of the provider.          |
| `databaseId`        | `string`    | The database identifier.                        |
| `jobId`             | `string`    | The unique identifier of the job.               |
| `jobName`           | `string`    | The name of the job.                            |
| `parentJobId`       | `string`    | The ID of the parent job (if applicable).       |
| `errorMessage`      | `string`    | The error message if the job failed.            |
| `createdAt`         | `timestamp` | The timestamp when the record was created.      |
| `updatedAt`         | `timestamp` | The timestamp when the record was last updated. |

---

## Dependencies

- **`dbService`**:  
  Used for interacting with the PostgreSQL database.
- **`logger`**:  
  Used for logging errors and other messages.
- **`JobStatusEnum`**:  
  Enum defining possible job statuses (e.g., `ACTIVE`, `COMPLETED`, `FAILED`).

---

## Example Usage

### Insert or Update Job Status

```typescript
import { JobStatusService } from './JobStatusService';
import { JobStatusEnum } from '../enums/JobStatusEnum';

const job = {
  id: 'job-id',
  name: 'example-job',
  data: {
    platform: 'example-platform',
    providerServiceId: 'provider-id',
    databaseId: 'db-id',
  },
};

await JobStatusService.insertFlowStatus(job, JobStatusEnum.ACTIVE);
```

## Reassign the static instance

If you want to use the `insertFlowStatus` method as a static logger, you can bind it to ensure the correct context:

```typescript
import { JobStatusService } from './JobStatusService';

private static statusLogger = JobStatusService.insertFlowStatus.bind(JobStatusService);

await this.statusLogger(job, JobStatusEnum.ACTIVE);
```

### List Job Statuses

```typescript
import { JobStatusService } from './JobStatusService';

const statuses = await JobStatusService.listJobStatuses('provider-id');
console.log(statuses);
```

---

## Summary

The `JobStatusService` class provides a simple and efficient way to manage job statuses in the `flow_statuses` table. It supports inserting or updating job statuses and retrieving job status records for a specific provider. The class ensures proper error handling and logging for all database operations.
