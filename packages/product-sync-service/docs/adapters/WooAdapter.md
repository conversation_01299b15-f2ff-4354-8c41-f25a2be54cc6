# WooAdapter Documentation

## Overview

The `WooAdapter` class is responsible for synchronizing data from WooCommerce. It implements four main steps in the data synchronization process:

1. **Fetching data** from the WooCommerce API and uploading it to Google Cloud Storage.
2. **Moving data** to BigQuery.
3. **Unifying data** in BigQuery.
4. **Writing data** to a PostgreSQL database.

The class implements the `FlowChildJobHandler` interface and uses the BullMQ `Job` object to handle each step.

---

## Class: `WooAdapter`

### Static Fields

- **`DEFAULT_BASE_URI`**:  
  The default base URI for the WooCommerce API.  
  **Type**: `string`  
  **Value**: `'/wp-json/wc/v3'`

- **`statusLogger`**:  
  A static reference to the `JobStatusService` for logging job statuses.  
  **Type**: `JobStatusService`

---

## Methods

### 1. `fetchData(job: Job): Promise<WooJobData<{ fileUrl: string; tempTableId: string }>>`

**Description**:  
Fetches product data from the WooCommerce API and uploads it to Google Cloud Storage.

**Parameters**:

- `job: Job`  
  The BullMQ job object containing the necessary data (e.g., API key, shop URL).

**Returns**:

- `Promise<WooJobData<{ fileUrl: string; tempTableId: string }>>`  
  The job data, including the uploaded file URL.

**Steps**:

1. Logs the job status as `ACTIVE`.
2. Fetches product data from the WooCommerce API.
3. Streams the data to Google Cloud Storage.
4. Logs the job status as `COMPLETED`.

**Error Handling**:

- If an error occurs, the job status is logged as `FAILED`, and the error is re-thrown.

---

### 2. `moveToBQ(job: Job): Promise<WooJobData<{ fileUrl: string; tempTableId: string }>>`

**Description**:  
Moves data from Google Cloud Storage to BigQuery.

**Parameters**:

- `job: Job`  
  The BullMQ job object.

**Returns**:

- `Promise<WooJobData<{ fileUrl: string; tempTableId: string }>>`  
  The job data, including the temporary BigQuery table ID.

**Steps**:

1. Logs the job status as `ACTIVE`.
2. Creates a temporary table in BigQuery.
3. Loads data from Google Cloud Storage into BigQuery.
4. Logs the job status as `COMPLETED`.

**Error Handling**:

- If an error occurs, the job status is logged as `FAILED`, and the error is re-thrown.

---

### 3. `unifyData(job: Job): Promise<WooJobData<{ fileUrl: string; tempTableId: string }>>`

**Description**:  
Unifies data in BigQuery by deleting old data and inserting new data into the target table.

**Parameters**:

- `job: Job`  
  The BullMQ job object.

**Returns**:

- `Promise<WooJobData<{ fileUrl: string; tempTableId: string }>>`  
  The job data.

**Steps**:

1. Logs the job status as `ACTIVE`.
2. Deletes existing data from the target BigQuery table.
3. Inserts unified data into the target BigQuery table.
4. Logs the job status as `COMPLETED`.

**Error Handling**:

- If an error occurs, the job status is logged as `FAILED`, and the error is re-thrown.

---

### 4. `writeToPg(job: Job): Promise<WooJobData>`

**Description**:  
Writes data from BigQuery to the PostgreSQL database.

**Parameters**:

- `job: Job`  
  The BullMQ job object.

**Returns**:

- `Promise<WooJobData>`  
  The job data.

**Steps**:

1. Logs the job status as `ACTIVE`.
2. Synchronizes data from BigQuery to PostgreSQL.
3. Logs the job status as `COMPLETED`.

**Error Handling**:

- If an error occurs, the job status is logged as `FAILED`, and the error is re-thrown.

---

## Types

### `WooJobProperties`

Defines the properties required for a WooCommerce job.

**Fields**:

- `apiKey: string`  
  The WooCommerce API key.
- `providerServiceId: string`  
  The provider's unique identifier.
- `databaseId: number`  
  The database identifier.
- `locale: string`  
  The locale (e.g., `en`, `hu`).

### `WooJobData<T>`

Extends the job data with WooCommerce-specific properties.

---

## Dependencies

- **`GoogleCloudStorageService`**:  
  Used for uploading data to Google Cloud Storage.
- **`BigQueryClient`**:  
  Used for interacting with BigQuery.
- **`JobStatusService`**:  
  Used for logging job statuses.
- **`WooAPIAdapter`**:  
  Used for communicating with the WooCommerce API.

---

## Example Usage

```typescript
const job: Job = ...; // BullMQ job object

// Fetch data from WooCommerce
await WooAdapter.fetchData(job);

// Move data to BigQuery
await WooAdapter.moveToBQ(job);

// Unify data in BigQuery
await WooAdapter.unifyData(job);

// Write data to PostgreSQL
await WooAdapter.writeToPg(job);
```

## Summary
The WooAdapter class provides a comprehensive solution for synchronizing WooCommerce data. It handles fetching, moving, unifying, and writing data while ensuring proper job status logging at each step. This adapter is modular, reusable, and can be extended for similar workflows on other platforms.
