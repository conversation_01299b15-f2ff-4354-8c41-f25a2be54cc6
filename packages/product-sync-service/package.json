{"name": "product-sync-service", "version": "1.0.1", "description": "Schedule and monitor product synchronization with vendor shops", "main": "index.js", "author": "<EMAIL>", "license": "MIT", "engines": {"node": "<=22.0.0"}, "scripts": {"build": "tsc --project ./tsconfig.json", "debug": "node --inspect dist/index.js", "test:ci": "yarn run test:e2e --ci --coverage", "start": "NODE_ENV=production node dist/src/index.js", "format": "prettier --write \"src/**/*.ts\"", "lint": "eslint \"{src,test}/**/*.ts\"", "lint:fix": "eslint \"{src,test}/**/*.ts\" --fix", "dev": "nodemon --exec ts-node --files --project ./tsconfig.json src/index.ts", "db:migrate": "NODE_ENV=development npx knex migrate:latest", "db:migrate:prod": "NODE_ENV=production npx knex migrate:latest", "db:make": "npx knex migrate:make", "db:rollback": "npx knex  migrate:rollback", "db:rollback:all": "npx knex  migrate:rollback --all", "test:e2e": "TZ=UTC NODE_ENV=test jest --config jest.config.js --runInBand --detectOpenHandles --forceExit", "test:e2e-product": "TZ=UTC NODE_ENV=test jest --config jest.config.js --runInBand --detectOpenHandles  --testNamePattern=Products --forceExit", "test:e2e-unas": "NODE_ENV=test jest --config jest.config.js --runInBand --detectOpenHandles --forceExit --testNamePattern=unas-flow", "test:e2e-woo": "NODE_ENV=test jest --config jest.config.js --runInBand --detectOpenHandles --forceExit --testNamePattern=woo-flow", "test:e2e-gomag": "NODE_ENV=test jest --config jest.config.js --runInBand --detectOpenHandles --forceExit --testNamePattern=gomag-flow"}, "devDependencies": {"@eslint/js": "^9.23.0", "@om/jest-num-failed": "workspace:^", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/mime-types": "^2.1.4", "@types/node": "^22.13.14", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^4.28.0", "@typescript-eslint/parser": "^4.28.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "globals": "^16.0.0", "jest": "^29.7.0", "jest-junit": "^16.0.0", "nodemon": "^3.1.9", "prettier": "^3.5.3", "supertest": "^7.1.0", "ts-jest": "^29.3.1", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.28.0"}, "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/helmet": "^13.0.1", "@fastify/jwt": "^9.1.0", "@google-cloud/bigquery": "^7.9.3", "@google-cloud/opentelemetry-cloud-monitoring-exporter": "^0.20.0", "@google-cloud/opentelemetry-cloud-trace-exporter": "^2.4.1", "@google-cloud/storage": "^7.15.2", "@google-cloud/trace-agent": "^8.0.0", "@om/logger": "workspace:^", "@opentelemetry/auto-instrumentations-node": "^0.57.1", "@opentelemetry/exporter-trace-otlp-grpc": "^0.200.0", "@opentelemetry/instrumentation-fastify": "^0.45.0", "@opentelemetry/instrumentation-http": "^0.200.0", "@opentelemetry/instrumentation-redis": "^0.47.0", "@opentelemetry/resources": "^2.0.0", "@opentelemetry/sdk-node": "^0.200.0", "@opentelemetry/semantic-conventions": "^1.32.0", "@types/sax": "^1.2.7", "axios": "^1.8.4", "bullmq": "^5.45.0", "bullmq-otel": "^1.0.1", "dotenv": "^16.4.7", "dotenv-flow": "^4.1.0", "fastify": "^5.2.2", "http-status-code": "^2.1.0", "ioredis": "^5.6.0", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "mime-type": "^5.0.3", "openai": "^4.97.0", "pg": "^8.14.1", "pg-mem": "^3.0.5", "redis-memory-server": "^0.12.1", "sax": "^1.4.1", "uuid": "^11.1.0", "zod": "^3.24.2"}}