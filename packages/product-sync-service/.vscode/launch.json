{
    "version": "0.2.0",
    "configurations": [
      {
        "type": "node",
        "request": "launch",
        "name": "Debug Current Jest File",
        "runtimeExecutable": "node",
        "runtimeArgs": [
          "--inspect-brk",
          "${workspaceFolder}/node_modules/.bin/jest",
          "${file}",           // Targets the currently opened file
          "--runInBand",       // Disable parallel execution
          "--watchAll=false",  // Ensure tests run once
          "--no-cache",        // Bypass caching
          "--detectOpenHandles"
        ],
        "console": "integratedTerminal",
        "internalConsoleOptions": "neverOpen",
        "port": 9229,
        "cwd": "${workspaceFolder}"
      }
    ]
  }