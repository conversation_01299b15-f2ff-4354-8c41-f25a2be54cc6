import { WooAPIAdapter } from '../src/adapters/WooAPIAdapter';
import { expect } from '@jest/globals';

const consumerKey = 'ck_832f501f02c60bfa4a31ba84025b44c77d58c0a6';
const consumerSecret = 'cs_6abec431c5f03d57fba4d1942f4a9c85a56e09e3';
const shopUrl = 'https://woocommersetestsite.kinsta.cloud/';

const representativeProduct = {
  id: 8096,
  name: 'Gorgeous Bamboo Hat',
  price: '887.99',
  regular_price: '887.99',
  images: [
    {
      id: 65,
      date_created: '2025-04-07T07:57:03',
      date_created_gmt: '2025-04-07T07:57:03',
      date_modified: '2025-04-25T12:42:47',
      date_modified_gmt: '2025-04-25T12:42:47',
      src: 'https://woocommersetestsite.kinsta.cloud/wp-content/uploads/2025/04/images.jpeg',
      name: 'images',
      alt: '',
    },
  ],
  description:
    '<p>Ergonomic Pants made with Plastic for all-day breakable support</p>\n',
  parent_id: 0,
  status: 'publish',
};

describe('WooAPIAdapter - getProducts', () => {
  const page = 1;

  afterAll(() => {
    jest.clearAllMocks();
  });

  it('should verify that the response contains the required fields with correct types', async () => {
    const result = await WooAPIAdapter.getProducts(
      consumerKey,
      consumerSecret,
      shopUrl,
      page,
    );

    expect(result.products).toBeInstanceOf(Array);

    result.products.forEach((product) => {
      expect(product).toEqual(
        expect.objectContaining({
          id: expect.any(Number),
          name: expect.any(String),
          price: expect.any(String),
          regular_price: expect.any(String),
          images: expect.any(Array),
          description: expect.any(String),
          parent_id: expect.any(Number),
          status: expect.any(String),
        }),
      );

      if (product.images.length > 0) {
        product.images.forEach((image) => {
          expect(image).toEqual(
            expect.objectContaining({
              src: expect.any(String),
            }),
          );
        });
      }
    });

    expect(result.totalPage).toBeDefined();
  });

  it('should verify that the response contains the expected product', async () => {
    const result = await WooAPIAdapter.getProducts(
      consumerKey,
      consumerSecret,
      shopUrl,
      page,
    );

    const product = result.products.find(
      (product) => product.id === representativeProduct.id,
    );

    expect(product).toEqual(
      expect.objectContaining({
        id: representativeProduct.id,
        name: representativeProduct.name,
        price: representativeProduct.price,
        regular_price: representativeProduct.regular_price,
        images: expect.arrayContaining([
          expect.objectContaining({
            src: representativeProduct.images[0].src,
          }),
        ]),
        // description: representativeProduct.description,
        parent_id: representativeProduct.parent_id,
        status: representativeProduct.status,
      }),
    );

    // Just checking if the description exists
    expect(product?.description).toBeDefined();
  });
});

describe('WooAPIAdapter - getCurrency', () => {
  const expectedCurrencyType = 'string';
  const expectedCurrencyLength = 3;
  const expectedCurrencyPattern = /^[A-Z]{3}$/;

  afterAll(() => {
    jest.clearAllMocks();
  });
  it('should verify that the response contains the expected currency', async () => {
    const result = await WooAPIAdapter.getCurrency(
      consumerKey,
      consumerSecret,
      shopUrl,
    );

    expect(typeof result).toBe(expectedCurrencyType);
    expect(result.length).toBe(expectedCurrencyLength);
    expect(result).toMatch(expectedCurrencyPattern);
  });
});
