import path from 'path';
import fs from 'fs';

// Create mock implementations
const jsonFilePath = path.join(__dirname, '..', 'public', 'woo.json');
const categoriesFilePath = path.join(
  __dirname,
  '..',
  'public',
  'wooCategories.json',
);

const wooApiAdapterMock = {
  WooAPIAdapter: {
    getProducts: jest.fn().mockImplementation(async () => {
      const content = fs.readFileSync(jsonFilePath, 'utf-8');
      const products = JSON.parse(content);

      return {
        products,
        totalPage: 1,
      };
    }),
    getCurrency: jest.fn().mockImplementation(() => {
      return 'HUF';
    }),
    getLocale: jest.fn().mockImplementation(() => {
      return 'hu';
    }),
    getCategories: jest.fn().mockImplementation(() => {
      const content = fs.readFileSync(categoriesFilePath, 'utf-8');
      return JSON.parse(content);
    }),
  },
};

jest.doMock('../src/adapters/WooAPIAdapter', () => wooApiAdapterMock);

import request from 'supertest';
import { FastifyInstance } from 'fastify';
import { FastifyServer } from '../src/server/FastifyServer';
import { routes } from '../src/server/routes/index';
import { createFlowChildJobQueue } from '../src/scheduler/Queue';
import { createMainWorker } from '../src/scheduler/MainWorker';
import { createFlowJobWorker } from '../src/scheduler/FlowJobWorker';
import { RedisClient } from '../src/services/RedisService';
import { getFlowProducer } from '../src/scheduler/FlowProducer';
import { dbService } from '../src/services/PostgresService';
import { waitForResponse, genRandomDatabaseId } from './utils/test-utils';

// Constants for test configuration
const PROVIDER_SERVICE_ID = 'woocommersetestsite.kinsta.cloud';
const DATABASE_ID = genRandomDatabaseId();
const JEST_TIMEOUT = 60000;
const FLOW_TIMEOUT = 40000;

describe('woo-flow', () => {
  let serverInstance: FastifyInstance;
  let server: FastifyServer;

  jest.setTimeout(JEST_TIMEOUT);

  beforeAll(async () => {
    await RedisClient.waitForInitialization();

    server = new FastifyServer();
    serverInstance = server.instance;

    const redisClient = RedisClient.getInstance();

    const flowProducer = getFlowProducer(redisClient);
    const mainWorker = createMainWorker(redisClient);
    const flowJobWorker = createFlowJobWorker(redisClient);
    const flowJobQueue = createFlowChildJobQueue(redisClient);

    server.addServices({
      redis: redisClient,
      flowProducer: flowProducer,
      mainWorker: mainWorker,
      flowJobWorker: flowJobWorker,
      flowJobQueue: flowJobQueue,
    });

    server.setupRoutes(routes);

    await dbService.healthCheck();
    await dbService.migrate();
    await flowJobQueue.waitUntilReady();

    await serverInstance.listen({ port: 0, host: 'localhost' });
  });

  afterAll(async () => {
    jest.clearAllMocks();
    serverInstance && serverInstance.close();
  });

  it('should test product are loaded into postgres after woo flow', async () => {
    await request(serverInstance.server)
      .post('/flow')
      .send({
        platform: 'woo',
        providerServiceId: PROVIDER_SERVICE_ID,
        databaseId: DATABASE_ID,
        consumerKey: 'ck_832f501f02c60bfa4a31ba84025b44c77d58c0a6',
        consumerSecret: 'cs_6abec431c5f03d57fba4d1942f4a9c85a56e09e3',
        shopUrl: 'https://woocommersetestsite.kinsta.cloud/',
      })
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/json')
      .expect(200);

    const productsResponse = await waitForResponse(
      () =>
        request(serverInstance.server)
          .get(
            `/products?providerServiceId=${PROVIDER_SERVICE_ID}&databaseId=${DATABASE_ID}&limit=100`,
          )
          .expect(200),
      (response) => response.body.products && response.body.products.length > 1,
      {
        timeoutMs: FLOW_TIMEOUT,
        intervalMs: 1000,
        timeoutMessage: 'Timed out waiting for products to be loaded',
      },
    );

    expect(productsResponse.body.products).toBeDefined();
    expect(productsResponse.body.products.length).toBeGreaterThan(1);

    const product_8035 = productsResponse.body.products.find(
      (product: { productId: string }) => product.productId === '8035',
    );

    expect(product_8035).toBeDefined();

    expect(product_8035).toMatchObject({
      databaseId: DATABASE_ID,
      providerServiceId: PROVIDER_SERVICE_ID,
      status: 'active',
      locale: 'hu',
      productId: '8035',
      name: 'Frozen Silk Car',
      sku: 'SKU0938',
      price: 397.55,
      originalPrice: 397.55,
      categories: '["15"]',
      productUrl:
        'https://woocommersetestsite.kinsta.cloud/termek/frozen-silk-car/',
      currency: 'HUF',
      imageUrl:
        'https://woocommersetestsite.kinsta.cloud/wp-content/uploads/2025/04/images.jpeg',
      description:
        '<p>The sleek and bony Shoes comes with yellow LED lighting for smart functionality</p>\n',
      parentProductId: '0',
    });
  });
});
