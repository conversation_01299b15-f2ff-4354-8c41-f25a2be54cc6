import { Response } from 'supertest';
import { Readable } from 'stream';
import fs from 'fs';
import path from 'path';
import { jest } from '@jest/globals';
import { parseXml } from '../../src/common/xml';

/**
 * Generates a random number for use as a databaseId
 * @returns {number} A random number between 1 and 99999999
 */
export function genRandomDatabaseId(): number {
  // Generate a random number between 1 and 99999999
  return Math.floor(Math.random() * 99999999) + 1;
}

/**
 * Waits for a condition to be met by repeatedly calling a request function
 * and checking the response against a predicate function.
 *
 * @param requestFn - Function that returns a promise resolving to a response
 * @param predicateFn - Function that checks if the condition is met based on the response
 * @param options - Configuration options
 * @returns The last response received
 */
export async function waitForResponse<T extends Response>(
  requestFn: () => Promise<T>,
  predicateFn: (response: T) => boolean,
  options: {
    timeoutMs?: number;
    intervalMs?: number;
    timeoutMessage?: string;
  } = {},
): Promise<T> {
  const {
    timeoutMs = 15000,
    intervalMs = 1000,
    timeoutMessage = 'Timed out waiting for condition to be met',
  } = options;

  const startTime = Date.now();
  let lastResponse: T | null = null;

  while (Date.now() - startTime < timeoutMs) {
    lastResponse = await requestFn();

    if (predicateFn(lastResponse)) {
      return lastResponse;
    }

    await new Promise((resolve) => setTimeout(resolve, intervalMs));
  }

  if (!lastResponse) {
    throw new Error('No response received during the waiting period');
  }

  throw new Error(timeoutMessage);
}

/**
 * Creates mock implementations for UnasAPIAdapter and axios.
 * This allows tests to run without making actual API calls to Unas
 * and without downloading real CSV files.
 *
 * @param xmlFilePath - Path to the CSV file to use for mocking
 * @returns Mock implementations for UnasAPIAdapter and axios
 */
export function createUnasMocks(
  xmlFilePath: string = path.join(process.cwd(), 'public', 'unas.xml'),
  categoriesFilePath: string = path.join(
    process.cwd(),
    'public',
    'unasCategories.json',
  ),
): { unasApiAdapterMock: any; axiosMock: any } {
  let productChunkCallCount = 0;
  let categoryChunkCallCount = 0;

  // UnasAPIAdapter mock implementation
  const unasApiAdapterMock = {
    UnasAPIAdapter: jest.fn().mockImplementation(() => ({
      login: jest.fn().mockImplementation(() => Promise.resolve(true)),
      getProductChunk: jest.fn().mockImplementation(async () => {
        productChunkCallCount++;
        if (productChunkCallCount === 1) {
          const content = fs.readFileSync(xmlFilePath, 'utf-8');
          const parsedContent = await parseXml(content); // Parse XML to JSON
          return Promise.resolve(parsedContent);
        }

        return Promise.resolve({
          '?xml': {
            '@_version': '1.0',
            '@_encoding': 'UTF-8',
          },
          Products: '',
        });
      }),
      getCategoriesChunk: jest.fn().mockImplementation(() => {
        categoryChunkCallCount++;
        if (categoryChunkCallCount === 1) {
          const content = fs.readFileSync(categoriesFilePath, 'utf-8');
          const categories = JSON.parse(content);
          return Promise.resolve(categories);
        }
        // Második meghívásnál üres adatokat ad vissza
        return Promise.resolve([]);
      }),
    })),
  };

  // Axios mock implementation
  const axiosMock = {
    __esModule: true,
    default: jest.fn().mockImplementation(() => {
      let content: string;

      try {
        content = fs.readFileSync(xmlFilePath, 'utf-8');
      } catch (error) {
        throw new Error(`Error reading XML file ${xmlFilePath}: ${error}`);
      }

      // Create a readable stream from the XML content
      const stream = new Readable();
      stream.push(content);
      stream.push(null); // End of stream

      return Promise.resolve({
        data: stream,
        headers: {
          'content-type': 'application/xml',
        },
      });
    }),
  };

  return {
    unasApiAdapterMock,
    axiosMock,
  };
}

/**
 * Creates mock implementations for WooAPIAdapter and axios.
 * This allows tests to run without making actual API calls to WooCommerce
 * and without downloading real JSON files.
 *
 * @returns Mock implementations for WooAPIAdapter and axios
 */
export function createWooMocks(): { wooApiAdapterMock: any; axiosMock: any } {
  const jsonFilePath = path.join(process.cwd(), 'public', 'woo.json');
  const wooApiAdapterMock = {
    WooAPIAdapter: {
      getProducts: jest.fn().mockImplementation(async () => {
        const content = fs.readFileSync(jsonFilePath, 'utf-8');
        const products = JSON.parse(content);
        return {
          products,
          totalPage: 1,
        };
      }),
      getCurrency: jest.fn().mockImplementation(() => {
        return 'HUF';
      }),
    },
  };

  const axiosMock = {
    default: jest.fn().mockImplementation(() => {
      let content: string;

      try {
        content = fs.readFileSync(jsonFilePath, 'utf-8');
      } catch (error) {
        throw new Error(`Error reading JSON file ${jsonFilePath}: ${error}`);
      }

      return Promise.resolve({
        data: content,
        headers: {
          'content-type': 'application/json',
        },
      });
    }),
  };

  return {
    wooApiAdapterMock,
    axiosMock,
  };
}
