import { FastifyInstance } from 'fastify';
import { FastifyServer } from '../../src/server/FastifyServer';
import { RedisClient } from '../../src/services/RedisService';
import { getFlowProducer } from '../../src/scheduler/FlowProducer';
import { createMainWorker } from '../../src/scheduler/MainWorker';
import { createFlowJobWorker } from '../../src/scheduler/FlowJobWorker';
import { createFlowChildJobQueue } from '../../src/scheduler/Queue';
import { dbService } from '../../src/services/PostgresService';
import { FlowProducer, Queue, Worker } from 'bullmq';
import { seed, unseed } from '../../src/database/seeds/test-seed';

/**
 * Creates a test server with the given routes and optional queues
 * @param routes: Array of route handlers
 * @param hasQueues: Whether to include queues in the server
 * @returns FastifyServer instance
 */
export async function createTestServer(
  routes: ((server: FastifyInstance) => void)[],
  hasQueues = false,
): Promise<FastifyServer> {
  let flowProducer: FlowProducer;
  let mainWorker: Worker;
  let flowJobWorker: Worker;
  let flowJobQueue: Queue;

  // Initialize a minimal server for testing
  const server = new FastifyServer();

  // Wait for Redis initialization to complete
  await RedisClient.waitForInitialization();

  // Get Redis client from the singleton that's already using in-memory Redis
  const redisClient = RedisClient.getInstance();

  // Setup all routes
  server.setupRoutes(routes);

  // Wait for database to be ready and run migrations
  await dbService.healthCheck();
  await dbService.migrate();

  // Seed test data
  await seed(dbService.knex);

  if (hasQueues) {
    flowProducer = getFlowProducer(redisClient);
    mainWorker = createMainWorker(redisClient);
    flowJobWorker = createFlowJobWorker(redisClient);
    flowJobQueue = createFlowChildJobQueue(redisClient);

    server.addServices({
      redis: redisClient,
      flowProducer: flowProducer,
      mainWorker: mainWorker,
      flowJobWorker: flowJobWorker,
      flowJobQueue: flowJobQueue,
    });
    // Wait for queue to be ready
    await flowJobQueue.waitUntilReady();
  }

  // Start the server
  await server.instance.listen({ port: 0, host: 'localhost' });

  // Add cleanup function to server instance
  const originalClose = server.instance.close.bind(server.instance);
  const newClose = async (closeListener?: () => void) => {
    await unseed(dbService.knex);
    if (closeListener) {
      originalClose(closeListener);
    } else {
      await originalClose();
    }
  };
  server.instance.close = newClose as typeof server.instance.close;

  return server;
}
