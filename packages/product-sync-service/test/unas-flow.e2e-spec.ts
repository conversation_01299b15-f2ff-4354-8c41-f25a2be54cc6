import path from 'path';
import { createUnasMocks } from './utils/test-utils';
// Create mock implementations
const csvFilePath = path.join(__dirname, '..', 'public', 'unas.xml');
const categoriesFilePath = path.join(
  __dirname,
  '..',
  'public',
  'unasCategories.json',
);
const { unasApiAdapterMock, axiosMock } = createUnasMocks(
  csvFilePath,
  categoriesFilePath,
);
// Setup mocks before importing any modules that use them
jest.doMock('../src/adapters/UnasAPIAdapter', () => unasApiAdapterMock);
jest.doMock('axios', () => axiosMock);

// Now import the modules that will use the mocks
import request from 'supertest';
import { FastifyInstance } from 'fastify';
import { FastifyServer } from '../src/server/FastifyServer';
import { routes } from '../src/server/routes/index';
import { createFlowChildJobQueue } from '../src/scheduler/Queue';
import { createMainWorker } from '../src/scheduler/MainWorker';
import { createFlowJobWorker } from '../src/scheduler/FlowJobWorker';
import { RedisClient } from '../src/services/RedisService';
import { getFlowProducer } from '../src//scheduler/FlowProducer';
import { dbService } from '../src/services/PostgresService';
import { waitForResponse, genRandomDatabaseId } from './utils/test-utils';

const JEST_TIMEOUT = 60000;
const FLOW_TIMEOUT = 30000;
const PROVIDER_SERVICE_ID = 'optimonkdev.unas.hu';
const TEST_PRODUCT_ID = '**********';
const SECOND_TEST_PRODUCT_ID = '**********';

describe('unas-flow', () => {
  let serverInstance: FastifyInstance;
  let server: FastifyServer;

  // Set timeout to 60 seconds for all tests in this describe block
  jest.setTimeout(JEST_TIMEOUT);

  beforeAll(async () => {
    // Wait for Redis initialization to complete
    await RedisClient.waitForInitialization();

    // Initialize a minimal server for testing
    server = new FastifyServer();
    serverInstance = server.instance;

    // Get Redis client from the singleton that's already using in-memory Redis
    const redisClient = RedisClient.getInstance();

    const flowProducer = getFlowProducer(redisClient);
    const mainWorker = createMainWorker(redisClient);
    const flowJobWorker = createFlowJobWorker(redisClient);
    const flowJobQueue = createFlowChildJobQueue(redisClient);

    // Add all services to the server
    server.addServices({
      redis: redisClient,
      flowProducer: flowProducer,
      mainWorker: mainWorker,
      flowJobWorker: flowJobWorker,
      flowJobQueue: flowJobQueue,
    });

    // Setup all routes
    server.setupRoutes(routes);
    // Wait for queue to be ready
    await dbService.healthCheck();
    await dbService.migrate();
    await flowJobQueue.waitUntilReady();

    // Start the server
    await serverInstance.listen({ port: 0, host: 'localhost' });
  });

  afterAll(async () => {
    // Clean up and reset mocks
    jest.clearAllMocks();

    // Close the server
    await serverInstance.close();
  });

  it('should test product are loaded into postgres after unas flow', async () => {
    // Generate a random databaseId for this test run
    const testDatabaseId = genRandomDatabaseId();

    await request(serverInstance.server)
      .post('/flow')
      .send({
        platform: 'unas',
        providerServiceId: PROVIDER_SERVICE_ID,
        databaseId: testDatabaseId,
        apiKey: '21e6798d911ca94072b218966fab27f392b36e60',
      })
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/json')
      .expect(200); // Assert status code

    // Query /products endpoint and wait for response or timeout
    const productsResponse = await waitForResponse(
      () =>
        request(serverInstance.server)
          .get(
            `/products?providerServiceId=${PROVIDER_SERVICE_ID}&databaseId=${testDatabaseId}&limit=100&outOfStock=true`,
          )
          .expect(200),
      (response) => response.body.products && response.body.products.length > 1,
      {
        timeoutMs: FLOW_TIMEOUT,
        intervalMs: 1000,
        timeoutMessage: 'Timed out waiting for products to be loaded',
      },
    );

    const products = productsResponse.body.products;
    expect(products).toBeDefined();
    expect(products.length).toBeGreaterThan(9);

    const product007 = products.find(
      (product: { productId: string }) => product.productId === TEST_PRODUCT_ID,
    );

    expect(product007).toBeDefined();
    expect(product007.productId).toBe(TEST_PRODUCT_ID);

    // Use the generated databaseId in the assertion
    expect(product007).toMatchObject({
      databaseId: testDatabaseId,
      providerServiceId: PROVIDER_SERVICE_ID,
      status: 'active',
      locale: 'hu',
      productId: TEST_PRODUCT_ID,
      name: 'Amet dolor sit',
      sku: 'product_007',
      price: 6990,
      originalPrice: 9990,
      currency: 'HUF',
      categories: '["100001"]',
      productUrl: `https://${PROVIDER_SERVICE_ID}/amet-dolor-sit`,
    });

    // Query the paired products endpoint with the product ID
    const pairedProductResponse = await request(serverInstance.server)
      .post('/products')
      .send({
        productIds: [SECOND_TEST_PRODUCT_ID],
        databaseId: testDatabaseId,
        providerServiceId: PROVIDER_SERVICE_ID,
      })
      .expect(200);

    // Assert that we get the product back
    expect(pairedProductResponse.body).toBeDefined();
    expect(pairedProductResponse.body.products).toBeDefined();
    expect(pairedProductResponse.body.products.length).toBe(1);

    // Verify the returned product matches our expectations
    const pairedProduct = pairedProductResponse.body.products[0];
    expect(pairedProduct.productId).toBe(SECOND_TEST_PRODUCT_ID);
    expect(pairedProduct).toMatchObject({
      databaseId: testDatabaseId,
      providerServiceId: PROVIDER_SERVICE_ID,
      productId: SECOND_TEST_PRODUCT_ID,
      name: 'Amet sit ipsum',
      productUrl: `https://${PROVIDER_SERVICE_ID}/Amet-sit-ipsum`,
    });
  });
});
