import { Queue, QueueEvents, Worker } from 'bullmq';
import Redis from 'ioredis';
import RedisMemoryServer from 'redis-memory-server';

describe('BullMQ Job Test', () => {
  let queue: Queue;
  let worker: Worker;
  let host: string;
  let port: number;
  let connection: Redis;
  let redisServer: RedisMemoryServer;
  let queueEvents: QueueEvents;

  beforeAll(async () => {
    // Setup Redis memory server
    redisServer = new RedisMemoryServer();
    host = await redisServer.getHost();
    port = await redisServer.getPort();
    // Create Redis connection with required BullMQ options
    connection = new Redis({
      host,
      port,
      maxRetriesPerRequest: null,
      enableReadyCheck: false,
    });
  });

  afterAll(async () => {
    // Close all connections
    await connection.quit();
    await redisServer.stop();
  });

  beforeEach(async () => {
    worker = new Worker(
      'test-queue',
      async (job) => {
        return job.data;
      },
      { connection },
    );
    queue = new Queue('test-queue', { connection });
    queueEvents = new QueueEvents('test-queue', { connection });
    await queue.obliterate({ force: true });
  });

  afterEach(async () => {
    // Close all BullMQ instances
    await queue.close();
    await worker.close();
    await queueEvents.close();
  });

  test('should process job', async () => {
    const testData = { foo: 'bar' };

    // Add the job to the queue
    const job = await queue.add('test-job', testData);

    // Wait for the job to be processed
    const result = await job.waitUntilFinished(queueEvents);

    expect(result).toEqual(testData);
  });
});
