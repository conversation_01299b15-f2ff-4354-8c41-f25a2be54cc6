import { FlowJobDefinition, buildJobTree } from '../src/scheduler/Queue';

describe('Flow Builder', () => {
  const testData = { testKey: 'testValue' };
  const testQueueName = 'test-queue';

  it('should build a simple linear flow correctly', () => {
    // Define a simple linear flow
    const linearFlow: FlowJobDefinition[] = [
      { name: 'job1', nextJob: 'job2' },
      { name: 'job2', nextJob: 'job3' },
      { name: 'job3' },
    ];

    // Build the job tree
    const jobTree = buildJobTree(linearFlow, testData, testQueueName);

    // Compare with expected structure
    expect(jobTree).toMatchObject([
      {
        name: 'job3',
        data: testData,
        queueName: testQueueName,
        opts: {},
        children: [
          {
            name: 'job2',
            data: testData,
            queueName: testQueueName,
            opts: {},
            children: [
              {
                name: 'job1',
                data: testData,
                queueName: testQueueName,
                opts: {},
                children: [],
              },
            ],
          },
        ],
      },
    ]);
  });

  it('should build a flow with multiple dependencies correctly', () => {
    // Define a flow with multiple dependencies
    const multiDependencyFlow: FlowJobDefinition[] = [
      { name: 'job1', nextJob: 'job3' },
      { name: 'job2', nextJob: 'job3' },
      { name: 'job3' },
    ];

    // Build the job tree
    const jobTree = buildJobTree(multiDependencyFlow, testData, testQueueName);

    // Sort the root nodes by name for consistent testing
    const sortedJobTree = [...jobTree].sort((a, b) =>
      a.name.localeCompare(b.name),
    );

    // Compare with expected structure
    expect(sortedJobTree).toMatchObject([
      {
        name: 'job3',
        data: testData,
        queueName: testQueueName,
        opts: {},
        children: [
          {
            name: 'job1',
            data: testData,
            queueName: testQueueName,
            opts: {},
            children: [],
          },
          {
            name: 'job2',
            data: testData,
            queueName: testQueueName,
            opts: {},
            children: [],
          },
        ],
      },
    ]);
  });

  it('should build a complex flow with multiple levels correctly', () => {
    // Define a complex flow with multiple levels
    const complexFlow: FlowJobDefinition[] = [
      { name: 'start', nextJob: 'fetchData' },
      { name: 'fetchData', nextJob: 'processData' },
      { name: 'processData', nextJob: 'saveResults' },
      { name: 'saveResults', nextJob: 'sendNotification' },
      { name: 'sendNotification' },
    ];

    // Build the job tree
    const jobTree = buildJobTree(complexFlow, testData, testQueueName);

    // Compare with expected structure
    expect(jobTree).toMatchObject([
      {
        name: 'sendNotification',
        data: testData,
        queueName: testQueueName,
        opts: {},
        children: [
          {
            name: 'saveResults',
            data: testData,
            queueName: testQueueName,
            opts: {},
            children: [
              {
                name: 'processData',
                data: testData,
                queueName: testQueueName,
                opts: {},
                children: [
                  {
                    name: 'fetchData',
                    data: testData,
                    queueName: testQueueName,
                    opts: {},
                    children: [
                      {
                        name: 'start',
                        data: testData,
                        queueName: testQueueName,
                        opts: {},
                        children: [],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    ]);
  });

  it('should handle a flow with parallel branches correctly', () => {
    // Define a flow with parallel branches
    const parallelBranchesFlow: FlowJobDefinition[] = [
      { name: 'start', nextJob: ['branch1', 'branch2'] },
      { name: 'branch1', nextJob: 'end' },
      { name: 'branch2', nextJob: 'end' },
      { name: 'end' },
    ];

    // Build the job tree
    const jobTree = buildJobTree(parallelBranchesFlow, testData, testQueueName);

    // Compare with expected structure - we need to sort the children for consistent testing
    const sortedChildren = [...jobTree[0].children].sort((a, b) =>
      a.name.localeCompare(b.name),
    );

    // Verify we have exactly 2 children for start
    expect(jobTree[0].children.length).toBe(2);

    // Verify the structure of each child
    expect(sortedChildren[0]).toMatchObject({
      name: 'branch1',
      data: testData,
      queueName: testQueueName,
      opts: {},
      children: [
        {
          name: 'start',
          data: testData,
          queueName: testQueueName,
          opts: {},
          children: [],
        },
      ],
    });

    expect(sortedChildren[1]).toMatchObject({
      name: 'branch2',
      data: testData,
      queueName: testQueueName,
      opts: {},
      children: [
        {
          name: 'start',
          data: testData,
          queueName: testQueueName,
          opts: {},
          children: [],
        },
      ],
    });
  });
});
