import path from 'path';
import fs from 'fs';

// Create mock implementations
const jsonFilePath = path.join(__dirname, '..', 'public', 'goMagProducts.json');
const categoriesFilePath = path.join(
  __dirname,
  '..',
  'public',
  'goMagCategories.json',
);

const goMagApiAdapterMock = {
  GoMagApidapter: {
    getProducts: jest.fn().mockImplementation(async () => {
      const content = fs.readFileSync(jsonFilePath, 'utf-8');
      const products = JSON.parse(content);

      return {
        products,
        total: 1,
      };
    }),
    getCurrency: jest.fn().mockImplementation(() => {
      return 'Lei';
    }),
    getLocale: jest.fn().mockImplementation(() => {
      return 'ro';
    }),
    getCategories: jest.fn().mockImplementation(() => {
      const content = fs.readFileSync(categoriesFilePath, 'utf-8');
      return JSON.parse(content);
    }),
  },
};

jest.doMock('../src/adapters/WooAPIAdapter', () => goMagApiAdapterMock);

import request from 'supertest';
import { FastifyServer } from '../src/server/FastifyServer';
import { waitForResponse, genRandomDatabaseId } from './utils/test-utils';
import { createTestServer } from './utils/test-server-utils';
import { flowRoute } from '../src/server/routes/Flow';
import { queryProductsRoute } from '../src/server/routes/QueryProducts';

// Constants for test configuration
const PROVIDER_SERVICE_ID = 'placeholder9129.gomag.ro';
const DATABASE_ID = genRandomDatabaseId();
const JEST_TIMEOUT = 60000;
const FLOW_TIMEOUT = 30000;

describe('goMag-flow', () => {
  let server: FastifyServer;

  jest.setTimeout(JEST_TIMEOUT);

  beforeAll(async () => {
    server = await createTestServer([flowRoute, queryProductsRoute], true);
  });

  afterAll(async () => {
    jest.clearAllMocks();
    server.instance && server.instance.close();
  });

  it('should test product are loaded into postgres after goMag flow', async () => {
    await request(server.instance.server)
      .post('/flow')
      .send({
        platform: 'gomag',
        providerServiceId: PROVIDER_SERVICE_ID,
        databaseId: DATABASE_ID,
        apiKey: '65e6d6f0e392d2d778c1503bbe65e2d3',
        shopUrl: 'http://placeholder9129.gomag.ro/',
      })
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/json')
      .expect(200);

    const productsResponse = await waitForResponse(
      () =>
        request(server.instance.server)
          .get(
            `/products?providerServiceId=${PROVIDER_SERVICE_ID}&databaseId=${DATABASE_ID}&limit=100`,
          )
          .expect(200),
      (response) => response.body.products && response.body.products.length > 1,
      {
        timeoutMs: FLOW_TIMEOUT,
        intervalMs: 1000,
        timeoutMessage: 'Timed out waiting for products to be loaded',
      },
    );

    expect(productsResponse.body.products).toBeDefined();
    expect(productsResponse.body.products.length).toBeGreaterThan(1);

    const product_1 = productsResponse.body.products.find(
      (product: { productId: string }) => product.productId === '1',
    );

    expect(product_1).toBeDefined();

    expect(product_1).toMatchObject({
      databaseId: DATABASE_ID,
      providerServiceId: PROVIDER_SERVICE_ID,
      status: 'active',
      locale: 'ro',
      productId: '1',
      name: 'Test product',
      sku: 'C1',
      price: 90.0,
      originalPrice: 100.0,
      categories: '["1","2","3","4","5"]',
      productUrl: 'http://placeholder9129.gomag.ro/test-product.html',
      currency: 'Lei',
      imageUrl: '',
      description:
        '<font style="vertical-align: inherit;"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Test product description</font></font></font></font></font></font>',
      parentProductId: '1',
    });
  });
});
