import axios from 'axios';
import { ShopifyAPIAdapter } from '../src/adapters/ShopifyAPIAdapter';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('ShopifyAPIAdapter Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('Bulk Operations', () => {
    const testParams = {
      accessToken: 'test-access-token',
      shopName: 'test-shop',
      apiVersion: '2023-04',
      query: 'test-query',
    };

    describe('runBulkQuery', () => {
      it('should successfully initiate a bulk operation and return operation ID', async () => {
        mockedAxios.post.mockResolvedValueOnce({
          data: {
            data: {
              bulkOperationRunQuery: {
                userErrors: [],
                bulkOperation: {
                  id: 'gid://shopify/BulkOperation/123456',
                },
              },
            },
          },
        } as any);

        const result = await ShopifyAPIAdapter.runBulkQuery(testParams);

        expect(mockedAxios.post).toHaveBeenCalledWith(
          `https://${testParams.shopName}.myshopify.com/admin/api/${testParams.apiVersion}/graphql.json`,
          { query: testParams.query },
          {
            headers: {
              'X-Shopify-Access-Token': testParams.accessToken,
              'Content-Type': 'application/json',
            },
          },
        );

        expect(result).toBe('gid://shopify/BulkOperation/123456');
      });

      it('should throw an error when bulk operation initiation fails', async () => {
        mockedAxios.post.mockResolvedValueOnce({
          data: {
            data: {
              bulkOperationRunQuery: {
                userErrors: [{ message: 'Invalid query' }],
                bulkOperation: null,
              },
            },
          },
        } as any);

        await expect(
          ShopifyAPIAdapter.runBulkQuery(testParams),
        ).rejects.toThrow('Failed to initiate bulk operation');
      });
    });

    describe('pollBulkOperation', () => {
      const pollParams = {
        ...testParams,
        bulkOpId: 'gid://shopify/BulkOperation/123456',
        interval: 100,
        timeout: 1000,
      };

      it('should successfully poll and return URL when operation completes', async () => {
        mockedAxios.post
          .mockResolvedValueOnce({
            data: {
              data: {
                node: {
                  id: pollParams.bulkOpId,
                  status: 'RUNNING',
                  url: null,
                  errorCode: null,
                },
              },
            },
          } as any)
          .mockResolvedValueOnce({
            data: {
              data: {
                node: {
                  id: pollParams.bulkOpId,
                  status: 'COMPLETED',
                  url: 'https://shopify.com/bulk-data.jsonl',
                  errorCode: null,
                },
              },
            },
          } as any);

        const resultPromise = ShopifyAPIAdapter.pollBulkOperation(pollParams);

        jest.advanceTimersByTime(pollParams.interval);

        await Promise.resolve();

        jest.advanceTimersByTime(pollParams.interval);

        const result = await resultPromise;

        expect(mockedAxios.post).toHaveBeenCalledTimes(2);

        expect(result).toBe('https://shopify.com/bulk-data.jsonl');
      });

      it('should handle timeout and throw appropriate error', async () => {
        mockedAxios.post.mockResolvedValue({
          data: {
            data: {
              node: {
                id: pollParams.bulkOpId,
                status: 'RUNNING',
                url: null,
                errorCode: null,
              },
            },
          },
        } as any);

        const resultPromise = ShopifyAPIAdapter.pollBulkOperation(pollParams);

        jest.advanceTimersByTime(pollParams.timeout + 100);

        await Promise.resolve();

        await expect(resultPromise).rejects.toThrow('Polling timed out');
      });

      it('should handle operation failure and throw appropriate error', async () => {
        mockedAxios.post.mockResolvedValueOnce({
          data: {
            data: {
              node: {
                id: pollParams.bulkOpId,
                status: 'FAILED',
                url: null,
                errorCode: 'ACCESS_DENIED',
              },
            },
          },
        } as any);

        const resultPromise = ShopifyAPIAdapter.pollBulkOperation(pollParams);

        jest.advanceTimersByTime(pollParams.interval);

        await expect(resultPromise).rejects.toThrow(
          'Bulk operation failed: ACCESS_DENIED',
        );
      });

      it('should handle external errors during polling', async () => {
        mockedAxios.post.mockRejectedValueOnce(new Error('Network error'));

        const resultPromise = ShopifyAPIAdapter.pollBulkOperation(pollParams);

        jest.advanceTimersByTime(pollParams.interval);

        await expect(resultPromise).rejects.toThrow('Network error');
      });

      it('should handle multiple polling attempts before timeout', async () => {
        mockedAxios.post.mockResolvedValue({
          data: {
            data: {
              node: {
                id: pollParams.bulkOpId,
                status: 'RUNNING',
                url: null,
                errorCode: null,
              },
            },
          },
        } as any);

        const customParams = {
          ...pollParams,
          interval: 200,
          timeout: 1000,
        };

        const resultPromise = ShopifyAPIAdapter.pollBulkOperation(customParams);

        for (let i = 0; i < 5; i++) {
          jest.advanceTimersByTime(customParams.interval);
          await Promise.resolve();
        }

        jest.advanceTimersByTime(customParams.interval);

        await expect(resultPromise).rejects.toThrow('Polling timed out');

        expect(mockedAxios.post.mock.calls.length).toBeGreaterThanOrEqual(5);
      });
    });
  });
});
