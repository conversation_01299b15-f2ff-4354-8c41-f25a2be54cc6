# Product Sync Service

Shyncronize product data from different platforms into a unified postgres database

## Requirement

- node version >= v22.0.0
- running Redis instance
- proper service account JSON in .env

## Environment

Fill the environment file (.env) with appropriate values

## Start

- Build the app

```sh
yarn build
```

- Start app

```sh
yarn start
```

- Start app in develop mode

```sh
yarn dev
```

- Format the source

```sh
yarn format
```

- Linting

```sh
yarn lint
yarn lint:fix
```

## Adding new platform

New platform should be registered in RegisterPlatform.ts file

First implement the platform specific fetchers in an Adapter class:
If you have multiple kind of fetch for the given platform, like stream and single

Create the adapter class in adapter dir:

```typescript
export class NewPlatformPlatformFetcher implements PlatformFetcher<'stream' | 'single'> {
    // implement the fetchers for the type
    async fetchStream(config: StreamConfig): Promise<string> {...}
    async fetchSingle(config: SingleConfig): Promise<unknown> {...}  // unknown can be specific return type
}
```

Register the new platform in adapters/RegisterPlatform.ts

```typescript
const platformFetchers: Record<string, PlatformFetcher<FetchType>> = {
  unas: new UnasPlatformFetcher(),
  newPlatform: new NewPlatformFetcher(),
};
```
