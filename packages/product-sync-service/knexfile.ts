import { <PERSON><PERSON> } from 'knex';
import { join } from 'path';

import config from './src/Config';

/**
 * @type { Object.<string, import("knex").Knex.Config> }
 */
const dbConfig: { [key: string]: Knex.Config } = {
  development: {
    client: 'postgresql',
    connection: {
      host: config.POSTGRES_HOST || 'localhost',
      database: config.POSTGRES_DB,
      port: config.POSTGRES_PORT,
      user: config.POSTGRES_USER,
      password: config.POSTGRES_PASSWORD,
      connectionString:
        `postgresql://${config.POSTGRES_USER}:${config.POSTGRES_PASSWORD}@${config.POSTGRES_HOST}:${config.POSTGRES_PORT}/${config.POSTGRES_DB}`.replace(
          '[ \n\t]',
          '',
        ),
    },
    pool: {
      min: 2,
      max: 70,
    },
    migrations: {
      tableName: 'knex_migrations',
      directory: join(__dirname, './db/pg-migration'),
      extension: 'ts',
    },
  },
  production: {
    client: 'postgresql',
    connection: {
      host: config.POSTGRES_HOST || 'localhost',
      database: config.POSTGRES_DB,
      port: config.POSTGRES_PORT,
      user: config.POSTGRES_USER,
      password: config.POSTGRES_PASSWORD,
      connectionString:
        `postgresql://${config.POSTGRES_USER}:${config.POSTGRES_PASSWORD}@${config.POSTGRES_HOST}:${config.POSTGRES_PORT}/${config.POSTGRES_DB}`.replace(
          '[ \n\t]',
          '',
        ),
    },
    pool: {
      min: 2,
      max: 70,
    },
    migrations: {
      tableName: 'knex_migrations',
      directory: join(__dirname, './db/pg-migration'),
      extension: 'js',
    },
  },
};

module.exports = dbConfig;

export default dbConfig;
