<template lang="pug">
div(v-if="quillToolbarInfo.show" :class="quillToolbarInfo.toolbarSelector")
  template(v-if="!mobilePreview")
  span.ql-formats
    span.d-flex-1(:class="{'justify-content-between': !mobilePreview}")
      button.ql-emoji(v-tooltip="{content: 'Emoji'}")
      button.ql-italic(v-tooltip="{content: $t('italic')}")
      button.ql-underline(v-tooltip="{content: $t('underline')}")
      button.ql-strike(v-tooltip="{content: $t('strike')}")
      button.ql-uppercase(v-tooltip="{content: $t('uppercase')}")
        uppercase-svg
      span.ql-picker-wrapper.ql-picker-link
        button.ql-link(v-if="!isButton && !teaserSelected" v-tooltip="{content: $t('link')}")
      .ql-separator-vertical
      button.ql-clean(v-tooltip="{content: $t('clean')}")
      .ql-separator-vertical
      select.ql-align(v-if="hideAlign")
        option(value="justify")
        option(value="left")
        option(value="center")
        option(value="right")
      .ql-separator-vertical(v-if="!isText && mobilePreview")
      span.ql-picker-wrapper.ql-picker-color
        label(v-if="!mobilePreview") {{$t('textColor')}}:
        span.ql-color-picker.ql-picker(:title="$t('textColor')")
          span.ql-picker-label
            color-svg.ql-picker-svg
      .ql-separator-vertical(v-if="mobilePreview")
      span.ql-picker-wrapper.ql-picker-weight(:title="$t('weight')")
        label(v-if="!mobilePreview") {{$t('weight')}}:
        select.ql-weight
          option(selected) Weight
          option(v-for="option in fontWeightOptions" :value="option.value") {{option.label}}
    div.ql-separator-horizontal
    span.d-flex-1
      span.ql-picker-wrapper.ql-picker-font
        label(v-if="!mobilePreview") {{$t('font')}}:
        select.ql-font(:title="$t('font')" @change="$emit('fontChange')")
          option(selected) Style
          option(v-for="option in fontFormatOptions" :value="option.value") {{option.label}}
        button.ql-btn.ql-btn-add(:title="$t('addNewFont')") +
      .ql-separator-vertical(v-if="mobilePreview")
      span.ql-picker-wrapper.ql-picker-size
        label(v-if="!mobilePreview") {{$t('size')}}:
        select.ql-size(:title="$t('size')")
          option(selected) Size
          option(v-for="option in fontSizeOptions" :value="option.value") {{option.label}}
      .ql-separator-vertical(v-if="mobilePreview")
      span.ql-picker-wrapper.ql-picker-lineHeight
        label(v-if="!mobilePreview") {{$t('lineHeight')}}:
        select.ql-lineheight(:title="$t('lineHeight')")
          option(selected) Line Height
          option(v-for="option in lineHeightOptions" :value="option.value") {{option.label}}
      .ql-separator-vertical
      span.ql-picker-wrapper.ql-picker-smart-tags(@click="trackListing('desktop')")
        select.ql-smart-tags(:title="$t('smartTags.label')")
          template(v-for="(options, groupName) in getGroupedOptions")
            option(:value="`smart_tag_${groupName}`" data-format="underline") {{ $t(`smartTags.tagGroups.${groupName}`) }}
            option(v-for="option in options" :value="getOptionValue(groupName, option)") {{ groupName === 'smart_product_tags' ? option.value : $t(`smartTags.tags.${option.key}`) }}
</template>
<script>
  import { mapGetters, mapState } from 'vuex';
  import {
    fontSizeOptions,
    fontSpacingOptions,
    fontWeightOptions,
    lineHeightOptions,
    groupedOptionsInSmartTags,
  } from '@/editor/quill_options';
  import { getMostUsedFonts } from '@/editor/util/getMostUsedFonts';
  import ColorSvg from '@/editor/components/svg/QuillColorSvg';
  import UppercaseSvg from '@/editor/components/svg/UppercaseSvg';
  import { trackListing } from '@/services/userInteractionTracker/smartTagTracker';

  export default {
    components: { ColorSvg, UppercaseSvg },
    computed: {
      ...mapState([
        'accountFeatures',
        'quillToolbarInfo',
        'selectedElement',
        'mobilePreview',
        'teaserSelected',
        'databaseId',
        'campaign',
        'ppoVariableNames',
        'isShopifyActive',
        'isShoprenterActive',
        'isWooCommerceActive',
        'isUnasActive',
      ]),
      ...mapGetters(['installedFonts', 'isEmbedded']),
      features() {
        return this.accountFeatures;
      },
      isButton() {
        return this.selectedElement && this.selectedElement.type === 'OmButton';
      },
      fontSpacingOptions() {
        return fontSpacingOptions;
      },
      fontFormatOptions() {
        return getMostUsedFonts(this.databaseId, this.installedFonts);
      },
      fontSizeOptions() {
        return fontSizeOptions;
      },
      fontWeightOptions() {
        return fontWeightOptions;
      },
      lineHeightOptions() {
        return lineHeightOptions;
      },
      isText() {
        return this.selectedElement && this.selectedElement.type === 'OmText';
      },
      hideAlign() {
        return !(this.mobilePreview && this.isText);
      },
      getGroupedOptions() {
        return groupedOptionsInSmartTags(
          this.ppoVariableNames,
          this.isEmbedded,
          this.isShopifyActive ||
            this.isShoprenterActive ||
            this.isWooCommerceActive ||
            this.isUnasActive,
          this.isUnasActive ? 'unas' : '',
        );
      },
    },
    methods: {
      getOptionValue(groupName, option) {
        return groupName === 'smart_product_tags' ? option.key : option.value;
      },
      trackListing(type) {
        trackListing(type, {
          databaseId: this.databaseId,
          campaignId: this.campaign.campaignId,
          elementId: this.selectedElement.uid,
        });
      },
    },
  };
</script>
