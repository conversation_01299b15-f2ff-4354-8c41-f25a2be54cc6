<template lang="pug">
DropdownMenu#link-toolbar-menu(
  ghost
  ref="popper"
  :tooltip="$t('tiptap.link')"
  @hide="onHide"
  @show="input = value"
  @handleToolbarOpen="handleToolbarOpen"
)
  template(slot="trigger")
    component(
      :is="href ? 'UilLinkBroken' : 'UilLinkAlt'"
      size="24px"
      :style="{ color: href ? 'var(--brand-primary-color)' : null }"
    )
      span "{{ href }}"
  .d-flex.p-1.link-toolbar-menu-content
    OmInput#link-toolbar-menu-input(
      small
      :error="validationError"
      :errorText="$t('invalidUrl.invalid')"
      :value="value"
      @change="dirty = true"
      @input="handleInput"
      @blur="validateInput(input)"
      placeholder="https://example.org"
    )
    OmButton.px-3(v-if="href" small secondary @click="deleteLink") {{ $t('deleteLink') }}
    OmLink.px-3(primary @click="value = input") {{ href ? $t('modify') : $t('apply') }}
</template>

<script>
  import DropdownMenu from '@/components/Elements/DropdownMenu/DropdownMenu.vue';
  import { UilLinkAlt, UilLinkBroken } from '@iconscout/vue-unicons';
  import { validateUrl } from '@/util';
  import commonMixin from './mixins/common';
  import { getStyles } from '../helpers/getStyles';

  export default {
    name: 'Link',
    components: { DropdownMenu, UilLinkAlt, UilLinkBroken },
    mixins: [commonMixin],
    data: () => ({ href: null, input: null, validationError: false, dirty: false }),
    computed: {
      value: {
        get() {
          if (this.dirty) return this.input;
          return this.href;
        },
        set(value) {
          this.apply(value);
        },
      },
    },
    watch: {
      'selectedElement.uid': {
        handler() {
          this.$nextTick(() => {
            this.updateSelected();
          });
        },
        immediate: false,
      },
    },
    mounted() {
      this.init();
      this.$bus.$on('selectionUpdate', this.updateSelected);
    },
    beforeDestroy() {
      this.$bus.$off('selectionUpdate', this.updateSelected);
    },
    methods: {
      init() {
        this.updateSelected();
      },
      updateSelected() {
        // Get href from textStyle mark's linkAttrs instead of link mark
        const textStyleAttrs = window.top._editor?.getAttributes?.('textStyle') ?? {};
        const href = textStyleAttrs.linkAttrs?.href || null;
        this.href = href;
        this.input = href;
      },
      deleteLink() {
        const styles = getStyles();

        window.top._editor
          .chain()
          .focus()
          .setMark('textStyle', { ...styles, linkAttrs: null })
          .run();

        this.$bus.$emit('reportEditorEvent', {
          interactionType: 'settingChanged',
          location: 'toolbar',
          extras: {
            elementType: this.selectedElement.type,
            eventType: 'removeLink',
            settingName: 'link',
            settingValue: null,
          },
        });
      },
      apply(url) {
        if (!this.validateInput(url)) return;

        const editor = window.top._editor;
        const styles = getStyles();

        // Only include link-specific attributes in linkAttrs
        const linkAttrs = { href: url };

        editor
          .chain()
          .focus()
          .setMark('textStyle', { ...styles, linkAttrs })
          .run();

        const { from, to } = editor.state.selection;
        const selectedText = editor.state.doc.textBetween(from, to, ' ');
        this.$bus.$emit('reportEditorEvent', {
          interactionType: 'settingChanged',
          location: 'toolbar',
          extras: {
            elementType: this.selectedElement.type,
            eventType: 'markToLink',
            settingName: 'link',
            settingValue: url,
            selectedText,
          },
        });

        this.$nextTick(() => {
          this.updateSelected();
        });

        this.$refs.popper.hideMenu();
      },

      handleInput(event) {
        this.dirty = true;
        this.validationError = false;
        this.input = event;
      },

      validateInput(urlRaw) {
        let url = urlRaw?.trim?.() ?? '';

        if (!url) {
          this.validationError = true;
          return false;
        }

        if (!/^https?:\/\//i.test(url)) {
          url = `https://${url}`;
        }

        if (!validateUrl(url)) {
          this.validationError = true;
          return false;
        }

        this.validationError = false;
        return true;
      },
      onHide() {
        this.dirty = false;
        this.input = null;
        this.validationError = false;
      },
    },
  };
</script>

<style lang="sass">
  .link-toolbar-menu-content
    gap: .5rem
</style>
