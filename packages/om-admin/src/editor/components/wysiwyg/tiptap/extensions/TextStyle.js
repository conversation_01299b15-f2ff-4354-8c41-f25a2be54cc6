import BaseTextStyle from '@tiptap/extension-text-style';
import { getPaletteColorIndex } from '../helpers/paletteColors';
import { getStyles } from '../helpers/getStyles';
import { updateStyles } from '../helpers/updateStyles';

function getAttrs(element, overrides = {}) {
  const attrs = { ...overrides };
  const style = element.style;

  // Parse font family from class or parent elements
  const fontClass = element.className.match(/ql-font-([\w-\.]+)/)?.[1];
  if (fontClass) {
    attrs.fontFamily = fontClass;
  } else {
    // Check parent em, u, s elements for font-family
    const parentEm = element.closest && element.closest('em');
    if (parentEm) {
      const parentFontClass = parentEm.className.match(/ql-font-([\w-\.]+)/)?.[1];
      if (parentFontClass) {
        attrs.fontFamily = parentFontClass;
      }
    } else {
      const parentU = element.closest && element.closest('u');
      if (parentU) {
        const parentFontClass = parentU.className.match(/ql-font-([\w-\.]+)/)?.[1];
        if (parentFontClass) {
          attrs.fontFamily = parentFontClass;
        }
      } else {
        const parentS = element.closest && element.closest('s');
        if (parentS) {
          const parentFontClass = parentS.className.match(/ql-font-([\w-\.]+)/)?.[1];
          if (parentFontClass) {
            attrs.fontFamily = parentFontClass;
          }
        }
      }
    }
  }

  // Parse font size from class or parent elements
  const fontSizeClass = element.className.match(/om-(text|button)-fontsize-(\d+)/)?.[2];
  if (fontSizeClass) {
    attrs.fontSize = fontSizeClass;
  } else {
    // Check parent em, u, s elements for font-size
    const parentEm = element.closest && element.closest('em');
    if (parentEm) {
      const parentFontSizeClass = parentEm.className.match(/om-(text|button)-fontsize-(\d+)/)?.[2];
      if (parentFontSizeClass) {
        attrs.fontSize = parentFontSizeClass;
      }
    } else {
      const parentU = element.closest && element.closest('u');
      if (parentU) {
        const parentFontSizeClass = parentU.className.match(/om-(text|button)-fontsize-(\d+)/)?.[2];
        if (parentFontSizeClass) {
          attrs.fontSize = parentFontSizeClass;
        }
      } else {
        const parentS = element.closest && element.closest('s');
        if (parentS) {
          const parentFontSizeClass = parentS.className.match(
            /om-(text|button)-fontsize-(\d+)/,
          )?.[2];
          if (parentFontSizeClass) {
            attrs.fontSize = parentFontSizeClass;
          }
        }
      }
    }
  }

  // Parse color from class or style or parent elements
  const colorClass = element.className.match(/om-color-palette-([\w\d]+)/)?.[1];
  if (colorClass) {
    attrs.color = colorClass;
  } else if (style.color) {
    attrs.color = style.color;
  } else {
    // Check parent em, u, s elements for color
    const parentEm = element.closest && element.closest('em');
    if (parentEm) {
      const parentColorClass = parentEm.className.match(/om-color-palette-([\w\d]+)/)?.[1];
      if (parentColorClass) {
        attrs.color = parentColorClass;
      } else if (parentEm.style.color) {
        attrs.color = parentEm.style.color;
      }
    } else {
      const parentU = element.closest && element.closest('u');
      if (parentU) {
        const parentColorClass = parentU.className.match(/om-color-palette-([\w\d]+)/)?.[1];
        if (parentColorClass) {
          attrs.color = parentColorClass;
        } else if (parentU.style.color) {
          attrs.color = parentU.style.color;
        }
      } else {
        const parentS = element.closest && element.closest('s');
        if (parentS) {
          const parentColorClass = parentS.className.match(/om-color-palette-([\w\d]+)/)?.[1];
          if (parentColorClass) {
            attrs.color = parentColorClass;
          } else if (parentS.style.color) {
            attrs.color = parentS.style.color;
          }
        }
      }
    }
  }

  // Parse font weight from style or parent em/u/s elements
  if (style.fontWeight) {
    attrs.fontWeight = style.fontWeight;
  } else {
    // Check parent em, u, s elements for font-weight
    const parentEm = element.closest && element.closest('em');
    if (parentEm && parentEm.style.fontWeight) {
      attrs.fontWeight = parentEm.style.fontWeight;
    } else {
      const parentU = element.closest && element.closest('u');
      if (parentU && parentU.style.fontWeight) {
        attrs.fontWeight = parentU.style.fontWeight;
      } else {
        const parentS = element.closest && element.closest('s');
        if (parentS && parentS.style.fontWeight) {
          attrs.fontWeight = parentS.style.fontWeight;
        }
      }
    }
  }

  // Parse italic from tag or style - use textItalic to match extension attributes
  const [italic = null] =
    element.tagName === 'EM'
      ? ['italic']
      : style.fontStyle && style.fontStyle.match(/italic/)
      ? ['italic']
      : [];

  if (italic) {
    attrs.textItalic = italic;
  }

  const textDecorationSet = new Set();
  attrs.textDecoration?.split?.(' ')?.forEach?.((decoration) => textDecorationSet.add(decoration));
  const [underline = null] =
    element.tagName === 'U'
      ? ['underline']
      : style.textDecoration && style.textDecoration.match(/underline/)
      ? ['underline']
      : [];

  const [lineThrough = null] =
    element.tagName === 'S'
      ? ['line-through']
      : style.textDecoration && style.textDecoration.match(/line-through/)
      ? ['line-through']
      : [];

  [underline, lineThrough]
    .filter(Boolean)
    .forEach((decoration) => textDecorationSet.add(decoration));
  const decorations = Array.from(textDecorationSet).join(' ');
  if (decorations) {
    attrs.textDecoration = decorations;
  }

  // Parse text transform from style or parent elements
  const [uppercase = null] =
    style.textTransform && style.textTransform.match(/uppercase/) ? ['uppercase'] : [];
  if (uppercase) {
    attrs.textTransform = uppercase;
  } else {
    // Check parent em, u, s elements for text-transform
    const parentEm = element.closest && element.closest('em');
    if (parentEm) {
      const [parentUppercase = null] =
        parentEm.style.textTransform && parentEm.style.textTransform.match(/uppercase/)
          ? ['uppercase']
          : [];
      if (parentUppercase) {
        attrs.textTransform = parentUppercase;
      }
    } else {
      const parentU = element.closest && element.closest('u');
      if (parentU) {
        const [parentUppercase = null] =
          parentU.style.textTransform && parentU.style.textTransform.match(/uppercase/)
            ? ['uppercase']
            : [];
        if (parentUppercase) {
          attrs.textTransform = parentUppercase;
        }
      } else {
        const parentS = element.closest && element.closest('s');
        if (parentS) {
          const [parentUppercase = null] =
            parentS.style.textTransform && parentS.style.textTransform.match(/uppercase/)
              ? ['uppercase']
              : [];
          if (parentUppercase) {
            attrs.textTransform = parentUppercase;
          }
        }
      }
    }
  }

  // Handle link attributes
  if (element.tagName === 'A' && element.href) {
    attrs.linkAttrs = {
      href: element.href,
      target: element.target || undefined,
      rel: element.rel || undefined,
    };
  }

  // Parse line height from style or parent elements
  const rawLineHeight = `${style.lineHeight}`;
  if (rawLineHeight) {
    // Parse string line-height values
    const trimmed = rawLineHeight.trim().toLowerCase();

    // Convert "normal" to 1.2 (standard browser default)
    if (trimmed === 'normal') {
      attrs.lineHeight = '1.2';
    } else if (['inherit', 'initial', 'unset', 'revert'].includes(trimmed)) {
      attrs.lineHeight = '1';
    } else {
      // For numeric strings, percentages, or other valid CSS values, return as-is
      attrs.lineHeight = rawLineHeight;
    }
  } else {
    // Check parent em, u, s elements for line-height
    const parentEm = element.closest && element.closest('em');
    if (parentEm && parentEm.style.lineHeight) {
      const parentRawLineHeight = `${parentEm.style.lineHeight}`;
      const parentTrimmed = parentRawLineHeight.trim().toLowerCase();
      if (parentTrimmed === 'normal') {
        attrs.lineHeight = '1.2';
      } else if (['inherit', 'initial', 'unset', 'revert'].includes(parentTrimmed)) {
        attrs.lineHeight = '1';
      } else {
        attrs.lineHeight = parentRawLineHeight;
      }
    } else {
      const parentU = element.closest && element.closest('u');
      if (parentU && parentU.style.lineHeight) {
        const parentRawLineHeight = `${parentU.style.lineHeight}`;
        const parentTrimmed = parentRawLineHeight.trim().toLowerCase();
        if (parentTrimmed === 'normal') {
          attrs.lineHeight = '1.2';
        } else if (['inherit', 'initial', 'unset', 'revert'].includes(parentTrimmed)) {
          attrs.lineHeight = '1';
        } else {
          attrs.lineHeight = parentRawLineHeight;
        }
      } else {
        const parentS = element.closest && element.closest('s');
        if (parentS && parentS.style.lineHeight) {
          const parentRawLineHeight = `${parentS.style.lineHeight}`;
          const parentTrimmed = parentRawLineHeight.trim().toLowerCase();
          if (parentTrimmed === 'normal') {
            attrs.lineHeight = '1.2';
          } else if (['inherit', 'initial', 'unset', 'revert'].includes(parentTrimmed)) {
            attrs.lineHeight = '1';
          } else {
            attrs.lineHeight = parentRawLineHeight;
          }
        }
      }
    }
  }

  return attrs;
}

function getAttrsUnderline(element) {
  const attrs = { textDecoration: 'underline' };

  // Handle font-weight from this u element or parent elements
  if (element.style.fontWeight) {
    attrs.fontWeight = element.style.fontWeight;
  } else {
    // Check parent em or s elements for font-weight
    const parentEm = element.closest('em');
    if (parentEm && parentEm.style.fontWeight) {
      attrs.fontWeight = parentEm.style.fontWeight;
    } else {
      const parentS = element.closest('s');
      if (parentS && parentS.style.fontWeight) {
        attrs.fontWeight = parentS.style.fontWeight;
      }
    }
  }

  // Check for nested strikethrough
  const s = element.closest('s');
  if (s) {
    attrs.textDecoration = 'underline line-through';
  }

  // Find the most relevant parent element to get other attributes from
  const em = element.closest('em');
  const targetElement = em || element;

  return getAttrs(targetElement, attrs);
}

function getAttrsLineThrough(element) {
  const attrs = { textDecoration: 'line-through' };

  // Handle font-weight from this s element or parent elements
  if (element.style.fontWeight) {
    attrs.fontWeight = element.style.fontWeight;
  } else {
    // Check parent em or u elements for font-weight
    const parentEm = element.closest('em');
    if (parentEm && parentEm.style.fontWeight) {
      attrs.fontWeight = parentEm.style.fontWeight;
    } else {
      const parentU = element.closest('u');
      if (parentU && parentU.style.fontWeight) {
        attrs.fontWeight = parentU.style.fontWeight;
      }
    }
  }

  // Check for nested underline
  const u = element.closest('u');
  if (u) {
    attrs.textDecoration = 'line-through underline';
  }

  // Find the most relevant parent element to get other attributes from
  const em = element.closest('em');
  const targetElement = em || element;

  return getAttrs(targetElement, attrs);
}

function getAttrsEm(element) {
  const attrs = { textItalic: 'italic' };

  // Handle font-weight from this em element or parent elements
  if (element.style.fontWeight) {
    attrs.fontWeight = element.style.fontWeight;
  } else {
    // Check parent u or s elements for font-weight
    const parentU = element.closest('u');
    if (parentU && parentU.style.fontWeight) {
      attrs.fontWeight = parentU.style.fontWeight;
    } else {
      const parentS = element.closest('s');
      if (parentS && parentS.style.fontWeight) {
        attrs.fontWeight = parentS.style.fontWeight;
      }
    }
  }

  // More thorough check for nested text decorations
  const decorations = [];

  // Check if this em element contains nested u or s elements
  if (element.querySelector('u')) {
    decorations.push('underline');
  }
  if (element.querySelector('s')) {
    decorations.push('line-through');
  }

  // Also check if this em element is inside u or s elements
  if (element.closest('u')) {
    decorations.push('underline');
  }
  if (element.closest('s')) {
    decorations.push('line-through');
  }

  // Remove duplicates and join
  const uniqueDecorations = [...new Set(decorations)];
  if (uniqueDecorations.length > 0) {
    attrs.textDecoration = uniqueDecorations.join(' ');
  }

  // Get other attributes but don't override textDecoration or fontWeight
  const otherAttrs = getAttrs(element, {});

  // Merge attributes, giving priority to our specific attributes
  const finalAttrs = { ...otherAttrs, ...attrs };

  return finalAttrs;
}

export const TextStyle = BaseTextStyle.extend({
  name: 'textStyle',

  addOptions() {
    return {
      defaultFont: null,
    };
  },

  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          fontSize: {
            default: null,
            parseHTML(element) {
              const fontSize = element.className.match(/om-(text|button)-fontsize-(\d+)/)?.[2];
              if (fontSize) {
                return fontSize;
              }

              // Check parent em, u, s elements for font-size
              const parentEm = element.closest && element.closest('em');
              if (parentEm) {
                const parentFontSize = parentEm.className.match(
                  /om-(text|button)-fontsize-(\d+)/,
                )?.[2];
                if (parentFontSize) {
                  return parentFontSize;
                }
              }

              const parentU = element.closest && element.closest('u');
              if (parentU) {
                const parentFontSize = parentU.className.match(
                  /om-(text|button)-fontsize-(\d+)/,
                )?.[2];
                if (parentFontSize) {
                  return parentFontSize;
                }
              }

              const parentS = element.closest && element.closest('s');
              if (parentS) {
                const parentFontSize = parentS.className.match(
                  /om-(text|button)-fontsize-(\d+)/,
                )?.[2];
                if (parentFontSize) {
                  return parentFontSize;
                }
              }

              return null;
            },
            renderHTML(attributes) {
              if (!attributes.fontSize) {
                return {};
              }

              return {
                class: `om-text-fontsize-${attributes.fontSize}`,
              };
            },
          },
          fontFamily: {
            default: null,
            parseHTML(element) {
              const fontKey = element.className.match(/ql-font-([\w-\.]+)/)?.[1];
              if (fontKey) {
                return fontKey;
              }

              // Check parent em, u, s elements for font-family
              const parentEm = element.closest && element.closest('em');
              if (parentEm) {
                const parentFontKey = parentEm.className.match(/ql-font-([\w-\.]+)/)?.[1];
                if (parentFontKey) {
                  return parentFontKey;
                }
              }

              const parentU = element.closest && element.closest('u');
              if (parentU) {
                const parentFontKey = parentU.className.match(/ql-font-([\w-\.]+)/)?.[1];
                if (parentFontKey) {
                  return parentFontKey;
                }
              }

              const parentS = element.closest && element.closest('s');
              if (parentS) {
                const parentFontKey = parentS.className.match(/ql-font-([\w-\.]+)/)?.[1];
                if (parentFontKey) {
                  return parentFontKey;
                }
              }

              return null;
            },
            renderHTML: (attributes) => {
              if (!attributes.fontFamily) {
                return {};
              }

              return {
                class: `ql-font-${attributes.fontFamily}`,
              };
            },
          },
          fontWeight: {
            default: null,
            parseHTML(element) {
              // Check current element's style first
              if (element.style.fontWeight) {
                return element.style.fontWeight;
              }

              // Check parent em, u, s elements for font-weight
              const parentEm = element.closest && element.closest('em');
              if (parentEm && parentEm.style.fontWeight) {
                return parentEm.style.fontWeight;
              }

              const parentU = element.closest && element.closest('u');
              if (parentU && parentU.style.fontWeight) {
                return parentU.style.fontWeight;
              }

              const parentS = element.closest && element.closest('s');
              if (parentS && parentS.style.fontWeight) {
                return parentS.style.fontWeight;
              }

              return null;
            },
            renderHTML(attributes) {
              if (!attributes.fontWeight) {
                return {};
              }

              return {
                style: `font-weight: ${attributes.fontWeight}`,
              };
            },
          },
          color: {
            default: null,
            parseHTML(element) {
              const colorKey = element.className.match(/om-color-palette-([\w\d]+)/)?.[1];
              if (colorKey) {
                return colorKey;
              }

              const customColor = element.style.color;
              if (customColor) {
                return customColor;
              }

              // Check parent em, u, s elements for color
              const parentEm = element.closest && element.closest('em');
              if (parentEm) {
                const parentColorKey = parentEm.className.match(/om-color-palette-([\w\d]+)/)?.[1];
                if (parentColorKey) {
                  return parentColorKey;
                }
                if (parentEm.style.color) {
                  return parentEm.style.color;
                }
              }

              const parentU = element.closest && element.closest('u');
              if (parentU) {
                const parentColorKey = parentU.className.match(/om-color-palette-([\w\d]+)/)?.[1];
                if (parentColorKey) {
                  return parentColorKey;
                }
                if (parentU.style.color) {
                  return parentU.style.color;
                }
              }

              const parentS = element.closest && element.closest('s');
              if (parentS) {
                const parentColorKey = parentS.className.match(/om-color-palette-([\w\d]+)/)?.[1];
                if (parentColorKey) {
                  return parentColorKey;
                }
                if (parentS.style.color) {
                  return parentS.style.color;
                }
              }

              return null;
            },
            renderHTML({ color }) {
              if (!color) {
                return {};
              }

              const colorIndex = getPaletteColorIndex(color);
              if (colorIndex !== null) {
                return {
                  class: `om-color-palette-${color}`,
                };
              }

              return {
                style: `color: ${color}`,
              };
            },
          },
          textDecoration: {
            default: null,
            parseHTML(element) {
              // Parse text decoration from tags or style - same logic as getAttrs function
              const [underline = null] =
                element.tagName === 'U'
                  ? ['underline']
                  : element.style.textDecoration && element.style.textDecoration.match(/underline/)
                  ? ['underline']
                  : [];

              const [lineThrough = null] =
                element.tagName === 'S'
                  ? ['line-through']
                  : element.style.textDecoration &&
                    element.style.textDecoration.match(/line-through/)
                  ? ['line-through']
                  : [];

              // Check for parent elements to preserve nested decorations
              const parentUnderline = element.closest && element.closest('u') ? 'underline' : null;
              const parentLineThrough =
                element.closest && element.closest('s') ? 'line-through' : null;

              // Also check for nested child elements
              const nestedUnderline =
                element.querySelector && element.querySelector('u') ? 'underline' : null;
              const nestedLineThrough =
                element.querySelector && element.querySelector('s') ? 'line-through' : null;

              // Combine all decorations and remove duplicates
              const allDecorations = [
                underline,
                lineThrough,
                parentUnderline,
                parentLineThrough,
                nestedUnderline,
                nestedLineThrough,
              ].filter(Boolean);
              const uniqueDecorations = [...new Set(allDecorations)];
              const decorations = uniqueDecorations.join(' ');

              return decorations;
            },
            renderHTML({ textDecoration }) {
              if (!textDecoration) {
                return {};
              }

              return {
                style: `text-decoration: ${textDecoration}`,
              };
            },
          },
          textItalic: {
            default: null,
            parseHTML(element) {
              const [italic = null] =
                element.tagName === 'EM'
                  ? ['italic']
                  : element.style.fontStyle.match(/italic/) ?? [];
              return italic;
            },
            renderHTML({ textItalic }) {
              if (!textItalic) {
                return {};
              }

              return {
                style: `font-style: ${textItalic}`,
              };
            },
          },
          textTransform: {
            default: null,
            parseHTML(element) {
              const [uppercase = null] = element.style.textTransform?.match(/uppercase/) ?? [];
              if (uppercase) {
                return uppercase;
              }

              // Check parent em, u, s elements for text-transform
              const parentEm = element.closest && element.closest('em');
              if (parentEm) {
                const [parentUppercase = null] =
                  parentEm.style.textTransform?.match(/uppercase/) ?? [];
                if (parentUppercase) {
                  return parentUppercase;
                }
              }

              const parentU = element.closest && element.closest('u');
              if (parentU) {
                const [parentUppercase = null] =
                  parentU.style.textTransform?.match(/uppercase/) ?? [];
                if (parentUppercase) {
                  return parentUppercase;
                }
              }

              const parentS = element.closest && element.closest('s');
              if (parentS) {
                const [parentUppercase = null] =
                  parentS.style.textTransform?.match(/uppercase/) ?? [];
                if (parentUppercase) {
                  return parentUppercase;
                }
              }

              return null;
            },
            renderHTML(attributes) {
              if (!attributes.textTransform) {
                return {};
              }

              return {
                style: `text-transform: ${attributes.textTransform}`,
              };
            },
          },
          linkAttrs: {
            default: null,
            parseHTML(element) {
              if (element.tagName === 'A' && element.href) {
                return {
                  href: element.href,
                  target: element.target,
                  rel: element.rel,
                };
              }
              return null;
            },
            renderHTML({ linkAttrs }) {
              if (!linkAttrs) {
                return {};
              }
              // Return empty object here since linkAttrs are handled in main renderHTML
              return {};
            },
          },
          lineHeight: {
            default: null,
            parseHTML(element) {
              const lineHeight = `${element.style.lineHeight}`;
              if (!lineHeight) {
                return null;
              }

              // Parse string line-height values
              const trimmed = lineHeight.trim().toLowerCase();

              // Convert "normal" to 1.2 (standard browser default)
              if (trimmed === 'normal') {
                return '1.2';
              }

              // Handle other common string values - fallback to '1'
              switch (trimmed) {
                case 'inherit':
                case 'initial':
                case 'unset':
                case 'revert':
                  return '1';
                default:
                  // For numeric strings, percentages, or other valid CSS values, return as-is
                  return lineHeight;
              }
            },
            renderHTML(attributes) {
              if (!attributes.lineHeight) {
                return {};
              }
              return {
                style: `line-height: ${attributes.lineHeight}`,
              };
            },
          },
        },
      },
    ];
  },

  addCommands() {
    return {
      setFontFamily:
        (fontFamily) =>
        ({ chain }) => {
          chain()
            .focus()
            .setMark('textStyle', { fontFamily })
            .updateAttributes('link', { fontFamily })
            .run();
        },

      setFontSize: (fontSize) => (ctx) => updateStyles(ctx, { fontSize }),

      setFontWeight:
        (fontWeight) =>
        ({ chain }) => {
          chain()
            .focus()
            .setMark('textStyle', { fontWeight })
            .updateAttributes('link', { fontWeight })
            .run();
        },

      setColor:
        (color) =>
        ({ chain }) => {
          chain().focus().setMark('textStyle', { color }).updateAttributes('link', { color }).run();
        },

      toggleItalic:
        () =>
        ({ chain }) => {
          const styles = getStyles();
          if (styles.textItalic) {
            chain().focus().setMark('textStyle', { textItalic: null }).run();
          } else {
            chain().focus().setMark('textStyle', { textItalic: 'italic' }).run();
          }
        },
      toggleUnderline:
        () =>
        ({ chain }) =>
          chain().focus().toggleDecoration('underline').run(),
      toggleStrike:
        () =>
        ({ chain }) =>
          chain().focus().toggleDecoration('line-through').run(),
      toggleDecoration:
        (decoration) =>
        ({ chain }) => {
          const styles = getStyles();
          if (styles.textDecoration?.includes(decoration)) {
            chain()
              .focus()
              .setMark('textStyle', {
                textDecoration: styles.textDecoration.replace(decoration, '').trim(),
              })
              .run();
          } else {
            const value = [decoration, styles.textDecoration].filter(Boolean).join(' ').trim();
            chain().focus().setMark('textStyle', { textDecoration: value }).run();
          }
        },
      toggleUppercase: () => (ctx) => {
        const styles = getStyles();
        if (styles.textTransform) {
          updateStyles(ctx, { textTransform: null });
        } else {
          updateStyles(ctx, { textTransform: 'uppercase' });
        }
      },

      setLineHeight:
        (lineHeight, isInput) =>
        ({ chain }) => {
          const pipeline = isInput ? chain() : chain().focus();
          pipeline
            .updateAttributes('textStyle', { lineHeight })
            .updateAttributes('paragraph', { lineHeight })
            .run();
        },
    };
  },

  renderHTML({ HTMLAttributes, mark }) {
    // Check for href in mark attributes (linkAttrs)
    const href = mark.attrs.linkAttrs?.href || HTMLAttributes.href;

    // If there's an href attribute, render as <a> tag, otherwise as <span>
    const tag = href ? 'a' : 'span';

    // Start with HTMLAttributes (which already contain properly formatted classes and styles)
    let finalAttributes = { ...HTMLAttributes };

    if (href && mark.attrs.linkAttrs) {
      // Only add link-specific attributes, not the styling attributes
      const linkSpecificAttrs = {
        href: mark.attrs.linkAttrs.href,
        target: mark.attrs.linkAttrs.target,
        rel: mark.attrs.linkAttrs.rel,
      };

      // Filter out undefined values
      Object.keys(linkSpecificAttrs).forEach((key) => {
        if (linkSpecificAttrs[key] === undefined) {
          delete linkSpecificAttrs[key];
        }
      });

      finalAttributes = {
        ...HTMLAttributes,
        ...linkSpecificAttrs,
      };
    }

    return [tag, finalAttributes, 0];
  },

  parseHTML() {
    return [
      { tag: 'u', getAttrs: getAttrsUnderline },
      { tag: 's', getAttrs: getAttrsLineThrough },
      { tag: 'em', getAttrs: getAttrsEm },
      { tag: 'span', getAttrs },
      { tag: 'a[href]', getAttrs },
    ];
  },
});
