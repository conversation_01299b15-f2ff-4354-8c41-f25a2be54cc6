<template lang="pug">
.om-modal.image-manager(@click="hide")
  .content(@click.stop="")
    .header
      .header-inner
        .om-row
          .om-col.om-col-12.header-content
            .om-title-image {{ $t('imageManager') }}
            .button.om-close(@click="hide") &times;
    .main(
      :class="{ 'no-img': !deduplicatedImages.length }"
      @drop.prevent="onUploadDrop"
      @dragover.prevent="onDragOver"
      @dragleave.prevent="onDragLeave"
    )
      .upload-cloud-wrapper(:class="{ 'center-img': deduplicatedImages.length > 0 }")
        i.fa.fa-cloud-upload
      .upload-area
        .upload-container
          .upload-cloud-wrapper(:class="{ 'no-img': deduplicatedImages.length === 0 }")
            i.fa.fa-cloud-upload
          .upload-message.p-2 {{ $t('imageUploadMessage') }}
          form.upload-area-form
            .button.button-large.upload-button.p-3(@click="openFileChooser") {{ $t('imageUploadNew') }}
              input(
                type="file"
                multiple="multiple"
                accept=".jpeg,.jpg,.jpeg-2000,.png,.svg,.gif,.tiff,.webp,.avif"
                style="display: none"
                ref="fileInput"
                @change="onFileChange"
              )
          .upload-size.upload-maxfile.p-2 {{ $t('imageMaxSize', { size: MAX_KBYTE, sizeMB: MAX_KBYTE / 1024 }) }}
          .image-replace-toggle(v-if="showReplaceToggle")
            om-switch#replaceImageToggle(
              :label="$t('imageManagerModals.library.toggle.label')"
              v-model.sync="imageReplace"
              :disabled="imageManagerLogoUpload"
            )
      .images.scrollable
        .image(v-if="newImageLoading")
          img(:src="require('@/assets/editor/svg/om_picture.svg')")
        .image(v-for="(i, index) in deduplicatedImages")
          inline-svg(v-if="i.url.includes('.svg')" :src="i.url")
          om-progressive-image(
            v-else
            :src="`${i.url}?ts=${timestamp(i.updatedAt)}`"
            placeholder="/om_picture.svg"
            backgroundColor="transparent"
            :lazyLoad="true"
          )
          .image-hover
            .image-data
              .image-name {{ i.name }}
              .image-res {{ $t('dimensions') }}: {{ i.width }}x{{ i.height }}
            .card.text-center.m-2(v-if="isBlacklisted(i.url)")
              .card-box.p-1 {{ $t('imageCantUse', { ext: isBlacklisted(i.url) }) }}
            .button.button-primary.button-large.p-3(v-else @click="useImage(index)") {{ $t('imageUse') }}
            .button-group(v-if="inEditor")
              .button.button-large
                i.fa.fa-trash.fa-2x(@click="removeImage(index)")
      .upload-drop-message.pointer-events-none(v-show="dragover")
        i.fa.fa-plus.fa-5x
</template>
<script>
  import { mapGetters, mapMutations, mapState, mapActions } from 'vuex';
  import { logoTypeWhitelist } from '@om/template-properties/src/imageReplace';
  import { track } from '@/services/xray';
  import { isSvgUrl } from '@/editor/util';
  import { get as _get } from 'lodash-es';
  import { restClient as axios } from '@/editor/axios';
  import itemMixin from '@/editor/mixins/item';
  import imageReplaceMixin from '@/mixins/imageManager/imageReplace';

  const MAX_KBYTE = 4096;

  const frame = () => document.getElementById('workspaceFrame');
  const frameWindow = () => frame()?.contentWindow;
  const frameStore = () => frameWindow()?.om.store;

  export default {
    mixins: [itemMixin, imageReplaceMixin],
    data() {
      return {
        MAX_KBYTE,
        dragover: false,
        newImageLoading: false,
        canEditMobile: true,
        canUseNested: true,
        selectedPageEl: null,
      };
    },
    computed: {
      ...mapGetters(['columnInfoOfSelectedElement', 'isNano']),
      ...mapState([
        'template',
        'templateSaveData',
        'selectedElement',
        'selectedPath',
        'mobilePreview',
        'selectedPage',
        'themeKit',
        'imageManagerLogoUpload',
        'sourceThemeKit',
      ]),
      ...mapState('imageManager', ['images', 'imageManagerLogoUpload', 'imageManagerDestination']),
      ...mapState('campaignCreation', { campaignCreationThemeKit: 'themeKit' }),
      inEditor() {
        return this.$route.path.includes('/variant/');
      },
      extensionBlacklist() {
        return this.imageManagerDestination.extBlacklist || [];
      },
      deduplicatedImages() {
        return this.images.filter((image, index, self) => {
          return self.findIndex((t) => t._id === image._id) === index;
        });
      },
    },
    watch: {
      selectedPage: {
        handler(v) {
          if (v && v.uid) {
            this.$nextTick(() => {
              this.selectedPageEl = frameWindow().document.getElementById(v.uid);
            });
          }
        },
        immediate: true,
      },
    },
    mounted() {
      this.loadImages();
    },
    methods: {
      ...mapMutations('imageManager', ['setImageManagerLogoUpload']),
      ...mapActions('imageManager', [
        'fetchImages',
        'fetchTemplateImages',
        'replaceImage',
        'hideImageManager',
      ]),
      ...mapActions(['changeImageOnPages']),
      openFileChooser() {
        this.$refs.fileInput.click();
      },
      timestamp(str) {
        return new Date(str).getTime();
      },
      onFileChange(e) {
        this.createImages(e.target.files);
      },
      onDragOver() {
        this.dragover = true;
      },
      onDragLeave() {
        this.dragover = false;
      },
      onUploadDrop(e) {
        this.dragover = false;
        this.createImages(e.dataTransfer.files);
      },
      createImages(files) {
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          if (file.type.match('^image/')) {
            this.createImage(file);
          } else {
            this.$notify({
              type: 'error',
              text: this.$t('notifications.unsupportedFileFormat'),
            });
          }
        }
      },
      createImage(file) {
        const isLogoUpload = this.imageManagerLogoUpload === true;

        const self = this;
        const reader = new FileReader();
        reader.fileName = file.name;
        this.$bus.$emit('showAdminLoader', true);
        self.newImageLoading = true;
        reader.onload = (e) => {
          const url = e.target.result;
          const htmlImage = new Image();
          htmlImage.src = url;
          htmlImage.onload = function () {
            const kbytes = file.size / 1024;
            track('campaignImageUpload', { kbytes });
            if (kbytes > MAX_KBYTE) {
              const msg = self.$t('fileSizeToLargeMsg', { size: MAX_KBYTE });
              self.$bus.$emit('showAdminLoader', false);
              self.newImageLoading = false;
              // eslint-disable-next-line
              window.alert(msg);
              return;
            }
            const splitName = e.target.fileName.split('.');
            const name = `${splitName[0].substr(0, 17)}_${new Date().getTime()}.${splitName[1]}`;
            const data = new FormData();
            data.append('file', file, name);
            data.append('type', self.templateSaveData.type);
            data.append('value', self.templateSaveData.value);
            data.append('height', this.height);
            data.append('width', this.width);
            axios
              .post('upload/editor', data, {
                headers: { 'content-type': 'multipart/form-data' },
                params: { trim: isLogoUpload ? '1' : '0' },
              })
              .then(() => {
                document.querySelector('.upload-area-form')?.reset?.();
                self.loadImages();
              })
              .catch((err) => {
                document.querySelector('.upload-area-form')?.reset?.();
                const text =
                  err.response && err.response.data && err.response.data.error
                    ? err.response.data.error
                    : this.$t('notifications.errorWhileUploadingImage');

                self.$notify({
                  type: 'error',
                  text,
                });
              });
          };
        };
        reader.readAsDataURL(file);
      },
      removeImage(index) {
        const image = this.deduplicatedImages[index];
        this.$bus.$emit('showAdminLoader', true);
        axios
          .delete(`upload/editor/${image._id}`, {
            params: {
              name: image.name,
              saveType: this.templateSaveData.type,
              saveValue: this.templateSaveData.value,
            },
          })
          .then((result) => {
            const { success, useCount } = result.data;
            if (success) {
              this.loadImages();
            } else {
              // eslint-disable-next-line
              window.alert(`Image is used in ${useCount} campaigns`);
              this.$bus.$emit('showAdminLoader', false);
            }
          });
      },
      async useImage(index) {
        const image = this.deduplicatedImages[index];

        if (!this.inEditor) {
          this.$emit('use', image);
          this.hide();
          return;
        }

        const isLogoUpload = this.imageManagerLogoUpload === true;
        const { _id, url, width, height } = image;
        const currentImage = { ...this.currentImage };
        const selectedImage = { imageId: _id, imageUrl: url, width, height };

        if (isSvgUrl(url)) {
          await this.loadSvgInline(image);
        }

        // handle logo replace
        if (isLogoUpload) {
          const currentImage = this.getLastLogoImage();

          this.changeImageOnPages({
            type: 'allPages',
            imageData: { currentImage, selectedImage },
            shouldResetVisibility: !this.themeKit?.logo?.current,
            whitelist: logoTypeWhitelist,
          });

          const name = this.getNameFromURL(url);
          this.themeKit.logo.current = name;
          this.themeKit.logo.history?.push(name);
          this.$bus.$emit('re-render-all-previews');
          this.hide();
          return;
        }

        this.$nextTick(() => {
          this.replaceImage({ image, selectedPageEl: this.selectedPageEl });
          this.hide();
        });

        this.$bus.$emit('re-render-all-previews');
        this.$bus.$emit('rebuild-color-instance');
        this.showImageReplaceIfNeeded({ currentImage, selectedImage });
      },
      async loadSvgInline(image) {
        const { data: svg } = await axios.get(image.url, { withCredentials: false });

        if (frameStore()) {
          frameStore().state.template.inlineSvgs = frameStore().state.template.inlineSvgs || {};
          frameStore().state.template.inlineSvgs[image._id] = svg;
        }
        this.$store.state.inlineSvgs = this.$store.state.inlineSvgs || {};
        this.$store.state.inlineSvgs[image._id] = svg;
      },
      async loadImages() {
        this.$bus.$emit('showAdminLoader', true);

        const { type, value } = this.templateSaveData;
        let result;

        if (type === 'base') {
          result = await this.fetchTemplateImages({ template: value });
        } else {
          const variables = {};

          if (this.sourceThemeKit?.name) {
            variables.themeName = this.sourceThemeKit.name;
          }

          const themeKitName = this.$route.query?.theme || this.campaignCreationThemeKit?.name;
          if (themeKitName) {
            variables.themeName = themeKitName;
          }

          result = await this.fetchImages(variables);
        }

        const usedImages = this.template?.images || [];
        const frameStoreBaseImages = frameStore()?.state?.baseImages || [];
        const baseImages = [...frameStoreBaseImages, ...usedImages];
        const baseImageHash = {};
        baseImages.forEach((image) => {
          baseImageHash[image._id] = true;
        });
        const allImages = [...baseImages, ...result.filter((image) => !baseImageHash[image._id])];

        await frameStore()?.commit('addImages', allImages);

        this.$nextTick(() => {
          this.$bus.$emit('showAdminLoader', false);
          this.newImageLoading = false;
        });
      },
      isBlacklisted(url) {
        let blacklisted = null;
        this.extensionBlacklist.forEach((ext) => {
          if (url.endsWith(`.${ext}`)) blacklisted = ext;
        });
        return blacklisted;
      },
      hide() {
        const { addElement } = this.imageManagerDestination;

        let imageId = _get(this.selectedElement, 'desktop.background.imageId');

        this.setImageManagerLogoUpload(false);

        if (this.mobilePreview) {
          imageId = _get(this.selectedElement, 'mobile.background.imageId');
        }

        if (addElement && !imageId && this.selectedElement) {
          this.$bus.$emit('removeElement', { uid: this.selectedElement.uid });
        }
        this.hideImageManager();
      },
      getLastLogoImage() {
        const last =
          this.themeKit?.logo?.current ??
          this.themeKit?.logo?.history?.[this.themeKit?.logo?.history?.length - 1] ??
          null;

        if (!last) return;
        const image =
          this.deduplicatedImages?.find?.(({ url }) => {
            return this.getNameFromURL(url) === this.getNameFromURL(last);
          }) ?? null;

        return image
          ? {
              imageId: image._id,
              imageUrl: image.url,
            }
          : null;
      },
    },
  };
</script>
<style lang="sass">
  @import "../../sass/shared/_vars.sass"
  @import "../../sass/image_manager.sass"
  @import "../../sass/editor.sass"
</style>
