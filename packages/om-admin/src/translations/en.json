{"100h": "Full height", "HTMLcode": "HTML code", "WL": {"login": "{brand} Login", "loginTitle": "Login to your {brand} account", "registerTitle": "Create your {brand} account", "social": {"shareLink": "https://facebook.com/{brand}"}, "title": "{brand} Admin"}, "_cart_total": "Cart value", "_number_of_cart_item_kinds": "Number of different products in the cart", "_number_of_cart_items": "Total number of products in the cart", "aBlock": {"disabled": "don't use", "enabled": "use", "labelSuffix": "AdBlock, AdBlock Plus, etc."}, "aCampaign": "a campaign", "aCampaignAlternate": "in a campaign", "abTestCenter": "A/B Test Center", "abTestModal": {"addControlVariant": "Add control variant", "addNewVariant": "Add new variant", "controlVariant": "Control variant", "controlVariantDesc": "No variant will be shown", "copyPreviousVariant": "Copy a previous variant", "dynamicContentDesc": "The original content will be displayed.", "newVariant": "New variant", "title": "Add A/B test variant"}, "abTestStarted": "Test started on <span class=\"font-weight-bold\">{startedOn}</span>", "about": "About", "accept": "Accept", "acceptInvitation": "Accept invitation", "accepted": "Accepted", "accountExpired": "Your account has expired, so we are not able to display your campaigns in the future. Please, extend your subscription!", "accountExpiredInvitedUser": "There is a problem with your OptiMonk account, that requires the account owner’s attention. Please ask the owner to log in.", "accountSharing": "Users", "accountTrialExpired": "Your account has expired, so we are not able to display your campaigns in the future. Please, subscribe to our service!", "accounts": "Accounts", "action": "Action", "activate": "Activate", "afterClick": "After click", "activateFirstCampaign": "Activate your first campaign", "activateFirstCampaignPopover": {"description": "Launch your campaign immediately or use the schedule to start it later.", "title": "Activate your campaign"}, "smartPopupsPopover": {"title": "Display personalized popups<br/>without any manual work and<br/>maximize conversions", "description": "Use the power of AI & experience the<br/>future of personalization."}, "active": "Active", "activeCampaignLimitReached": "Unsuccessfull activation! Active campaign limit reached!", "activeDomainsCount": "Active domains", "activeOnly": "Active only", "activeSites": "Active sites", "actualPageTooltip": "You are currently editing this page.", "add": "Add", "addAnotherService": "Add another service", "addCustomClassText": "Add your custom class without the dot (more can be specified separated by space)", "addField": "Add field", "addFirstSubAccount": "Add your first sub-account", "addImage": "Add image", "addItem": "Add value", "addKlaviyo": "<PERSON><PERSON>", "addMobileOnlyImage": "Add mobile only image", "addMore": "Add more", "addMoreTrigger": "Add new trigger", "addNew": "Add new", "addNewColor": "Add new Theme color", "addNewCondition": "Add new rule", "addNewDomain": "Add new domain", "addNewFont": "Add New Font", "addNewIntegration": "Add new integration", "addNewOption": "Add new option", "addNewPage": "Add new page", "addNewPageTooltip": "Add a new page to your popup", "addNewStaticField": "Add static field", "addProject": "Add project", "addThemeColor": "Add theme color", "added": "Added", "additionalVAT": "", "address": "Address", "advancedSettings": "Spacing", "affiliate": {"address": "Address", "awaitingPayout": "Awaiting payout", "city": "City", "commissionRate": "Commission rate", "commissions": "Commission", "copyUrl": "Copy URL", "customers": "Customers", "email": "Email | Emails", "invoicingInfo": "Invoicing information", "learnMoreAgencyButtonText": "Learn more", "learnMoreAgencySubtitle": "If you want to learn more about our Certified Partner Program, <a class=\"color-brand-primary font-weight-bold\" href=\"https://www.optimonk.com/certified-partner/\" target=\"_blank\">please click here</a>.", "learnMoreAgencyTitle": "Are you an agency offering OptiMonk to your clients?", "menu": "Affiliate", "minimumPayout": "Minimum payout", "modal": {"subscribers": {"title": "Trial signups"}}, "name": "Name", "netAmount": "Net amount", "noLinkAvailable": "No link available. Please contact with support!", "paid": "Paid", "payoutAmount": "Payout amount", "payoutDetails": "Payout details", "phone": "Phone", "postalCode": "Zip code", "sendReport": "Send Report", "subscriber": "Trial signups", "thisMonth": "This month", "thisWeek": "This week", "thisYear": "This year", "lastMonth": "Last month", "lastYear": "Last year", "useThisLink": "Use this link for referrals", "visitors": "Visitors", "firstSubscriptionDate": "First subscription date"}, "after": "When the popup is closed", "afterTooltip": "If you enable the option \"After the popup is closed\", the teaser will show up - by default - 2 seconds after you closed the popup. If the visitor continues to explore your website and visit different pages, the teaser will continue to show up on your website for that visitor almost instantly after each page finishes loading, based on the segmentation rules you have set for your Campaign.", "agencyBenefits": {"subtitles": {"client-management": "Easily manage all your clients' campaigns in the same place. Our dashboard not only saves a ton of time for you but also makes reporting easier.", "custom-branding": "You’ll get a White-Label version of OptiMonk. You’ll also get exclusive partner resources, such as white-label content and online training for your team.", "delight-visitors": "Collect valuable customer feedback and make the navigation smoother to help the visitors find the desired product.", "get-more-leads": "Most visitors aren’t ready to buy yet – make them subscribe to an email or Messenger list and convert them into sales later.", "sell-more": "Drive immediate sales by preventing cart abandonment and promoting your best products and offers. ", "support": "In addition to a live product tour, you’ll be provided with a dedicated account manager to ensure the highest level of customer service to you, every single day."}, "titles": {"client-management": "Client Management & Partner Dashboard", "custom-branding": "Custom branding (Fully White-Label)", "delight-visitors": "Delight visitors", "get-more-leads": "Get more leads", "sell-more": "Sell more products", "support": "Dedicated Account Manager, Training & Support", "we-can": "What You (& your clients) gonna be able to do with OptiMonk", "we-care": "How we care & support your Agency"}}, "agencyPlanDescription": "Unlimited campaigns<br/>\nUnlimited domains<br/>\n{noOfVisitors} visitors / month", "agencyReport": {"dateRange": "Date range", "emails": "Email recipient(s)"}, "align": "Align", "alignment": "Alignment", "all": "All", "allCampaigns": "All campaigns", "allItems": "all items in the cart", "allPages": "All pages", "allSides": "All sides", "allThemes": "All themes", "allow": "Allow", "altTitles": {"FREE": {"row1": "A simple way to get", "row2": "started with OptiMonk"}, "ESSENTIAL": {"row1": "For new brands that want to", "row2": "start driving revenue with popups"}, "GROWTH": {"row1": "For fast-growing brands", "row2": "to maximize sales and subscribers"}, "PREMIUM": {"row1": "Full solution for", "row2": "high-performance brands"}, "MASTER": "A custom-made plan for power users who need more"}, "alternativeImage": "Alternative image", "alternativelyEmbedManually": "Alternatively you can <span style=\"color: #ED5A29; cursor:pointer;\">manually place the embed code</span> into the desired location.", "partnerDashboard": "Partner dashboard", "ambassador": "Affiliate", "ambassadorAffiliate": {"byClicking": "*By clicking on the ‘Become an Ambassador’ button, you accept<br> the Terms & Conditions of the OptiMonk Ambassador Program.", "cta": "Become an Ambassador", "description": "Now it’s easier than ever. Simply share your unique referral link <br/>with your friends, audience or clients in exchange for a recurring,<br/>lifelong commission.<br/><br/>What’s more, the more users your refer, the higher your <br/>commission gets. Start earning now!", "heading": "Love OptiMonk? Become<br/> an Ambassador, spread the word & make money", "learnMore": "Learn more"}, "ambassadorPayoutRequestPage": {"backButton": "Back to dashboard", "detectedChangeModal": {"actionButton": "Refresh", "cancelButton": "Back", "message": "There was a change in the payout amount during this session. To send the payout request, refresh this page and click the Send payout request button again.", "title": "The payout amount has changed"}, "head": {"added": "Added", "commission": "Commission", "name": "Name", "orderTotal": "Order total", "status": "Status"}, "item": {"migration": "modifying item", "modify": "modifying item", "order": "order", "orderName": "order by {email}", "refund": "refunded order", "refundName": "refunded order by {email}", "status": {"invalid": "revoked", "valid": "valid"}}, "payoutItems": "Payout items", "sendButton": "Send payout request", "sendButtonInfo": "By clicking the Send payout request will be sent to our team who will contact you via email to ask for the invoice with the correct amount.", "startButton": "Start payout process", "startButtonInfo": "By starting the payout process you will see the overview of the earned commissions in this period so you can then send the payout request.", "successModal": {"actionButton": "OK", "message": "We will contact you via email to ask for the invoice with the correct amount.", "title": "Next step"}, "title": "Payout request"}, "ambassadorSummary": "Partner summary", "analytics": "Analytics", "analyticsHelp": "Would you like to measure the effectiveness of OptiMonk in a more efficient way? We've created an OptiMonk Dashboard for Google Analytics.</br>\n    It makes it really easy to see how effective OptiMonk is on your site, and helps you find areas of improvement.</br>\n    Here's how to add the OptiMonk dashboard to your Analytics account:</br>\n    <ol>\n    <li>Log into Analytics with the username and password you’d like to track the data.</li>\n    <li>Open a new window or tab in your browser and enter the following URL: <a href=\"https://www.google.com/analytics/web/template?uid=Ywh383lDR2GWenGNLIpGAA\" target=\"_blank\">https://www.google.com/analytics/web/template?uid=Ywh383lDR2GWenGNLIpGAA</a></li>\n    <li>A window will appear, choose the report view of the website using OptiMonk.</li>\n    <li>Click on Create. Your new OptiMonk Dashboard is created and you can easily measure the effectiveness of OptiMonk on your site.</li></ol>", "analyticsLead": "Where would you like to see your detailed OptiMonk results - in Google Analytics or in Google Tag Manager?", "analyticsSection": {"howToEnable": "Learn more", "statuses": {"success": "Analytics integration is <span class=\"text-color-success-green font-weight-bold\">enabled</span> on <span class=\"font-weight-bold\">{domain}</span>", "turnedOff": "Analytics integration is <span class=\"text-color-error-red font-weight-bold\">disabled</span> on <span class=\"font-weight-bold\">{domain}</span>"}}, "analyticsSelectLabel": "Which solution are you using to analyze your traffic?", "analyticsType": {"classic": "Classic Analytics code", "tag-manager": "Tag Manager", "universal": "Universal Analytics code (new)"}, "and": "and", "andOperation": "AND", "animated": "Animated", "animation": "Animation", "animations": "Animations", "annual": "year", "any": "Any", "apiKeySettings": {"buttonDelete": "Delete API key", "buttonGenerate": "Generate API key", "buttonGenerateNew": "Generate new API key", "description": "You can generate your key to access API. The API manual is available at ", "label": "API Keys", "labelCurrent": "Your current API key:", "modalButtonGenerate": "Generate", "modalDelete": "Are your sure you want to delete this key?<br>Deleting an API key immediately renders it unusable.", "modalGenerate": "Are your sure you want to generate a new key<br>Generate a new API key immediately renders unusable the current one.", "notifications": {"keyDeleteFail": "Failed to delete API key", "keyDeleted": "API key has been deleted", "keyGenerateFail": "Failed to generate new API key", "keyGenerated": "New API key successfully generated"}}, "appEmbedAlert": {"buttons": {"doItLater": "Do it later", "enableLater": "Enable later", "goToDomains": "Go to Domains", "goToShopify": "Go to Shopify", "nonShopify": "Go to Insert Code"}, "description": {"mixedDomains": "As you may have noticed OptiMonk is undergoing a transformation and we are working on many new and useful features. In order to achieve the best results and experience, we also had to change the way OptiMonk is set up. Don't be scared, it takes just a few minutes to change over, here's what you need to do:", "mixedDomains1": "Navigate to the Domains page.", "mixedDomains2": "Follow the installation instructions for each domain you have.", "nonShopify": "As you may have noticed OptiMonk is undergoing a transformation and we are working on many new and useful features. In order to achieve the best results and experience, we also had to change the way OptiMonk is set up. Don't be scared, it takes just a few minutes to change over, here's what you need to do:", "nonShopify1": "Navigate to the “Insert code” page", "nonShopify2": "Remove the old JavaScript from your site. (Except for Wordpress, Magento.)", "nonShopify3": "Insert the new OptiMonk JavaScript code in the <head> section of each page of your website.", "singleShopify": "As you may have noticed OptiMonk is undergoing a transformation and we are working on many new and useful features. In order to achieve the best results and experience, we also had to change the way OptiMonk is set up. Don't be scared, it takes just a few minutes to change over, here's what you need to do:", "singleShopify1": "Navigate to the Theme Settings in Shopify.", "singleShopify2": "Once there, enable the OptiMonk Onsite Script in the App embed list.", "singleShopify3": "Once enabled, press \"Save\" and close the tab."}, "modalTitle": "OptiMonk installation changes"}, "appEmbedInsertCode": {"checkDomain": "Check domain", "enabled": "The OptiMonk App Embed is enabled on your Shopify store", "furtherHelp": "For further help click {0}.", "furtherHelp_link": "here", "goToShopify": "Go to Shopify", "plsEnable": "The OptiMonk App Embed is not enabled. Please navigate to your Theme Settings in Shopify and enable your App Embed.", "todo": {"0": "Navigate to your OptiMonk Theme Settings in Shopify.", "1": "Once there, enable the OptiMonk App Embeds for OptiMonk Onsite Script.", "2": "Once enabled, press “Save” and close the tab."}}, "appId": "App ID", "appearIfVisitor": "If the visitor has", "appearOn": "Appear on every", "apply": "Apply", "deleteLink": "Delete link", "archive": "Archive", "archiveConfirmationDialog": "Are you sure you want to archive it?", "archived": "Archived", "areYouSure": "Are you sure you'd like to continue?", "assignable": "Assignable", "assigned": "Assigned", "assignedAvailableStat": "assigned / total unique visitors", "assistedRevenue": "Assisted revenue", "atLeastOneItem": "at least one item in the cart", "attention": "Attention", "attentionSeeker": "Attention seeker", "attentionSeekerFreq": "Attention seeker frequency", "audience": "Audience", "authenticate": "Authenticate", "authenticationLink": "Authentication link", "auto": "Auto", "autoRedeem": "Auto redeem", "autoRedeemTooltip": "If you enable Auto redeem, the discount code will be automatically applied at checkout for those visitors who have seen the discount code.", "automatic": "Automatically generated codes", "autoplay": "Autoplay", "autoplayYoutubeTooltip": "If the video is played automatically, the vide will be muted", "available": "available", "availableCoupons": "Available coupons", "availableLimit": "Available limit", "avgConversion": "Avg. conversion rate", "avgConversionLong": "Average<br>conversion rate", "back": "Back", "backToAgency": "Back to partner dashboard", "backToAgencyDashboard": "Back to partner dashboard", "backToGallery": "Back to gallery", "backendMessages": {"emailAlreadyExists": "Email already registered!", "firstNameTooLong": "First name must be maximum 30 characters long.", "invalidDomain": "Invalid domain!", "lastNameTooLong": "Last name must be maximum {length} characters long.", "passwordTooLong": "Password is too long! Max 32 characters"}, "background": "Background", "backgroundAnimation": "Background<br>animation", "backgroundAnimationOverlay": "Overlay", "backgroundAnimationPopup": "Popup background", "backgroundAnimationPosition": "Background<br>animation position", "backgroundColor": "Background color", "backgroundFill": "Background fill", "backgroundAndBorderElements": {"block": "Block background", "column": "Column background", "page": "Page background"}, "basedOnFeedback": "Based on feedback", "basicSettings": "Basic settings", "before": "Before the popup is displayed", "beforeTooltip": "If you enable the option \"Before the popup is displayed\", the teaser will show up - by default - 6 seconds after your webpage finished loading. If the visitor continues to explore your website and visit different pages, the teaser will continue to show up on your website for that visitor 1,5 seconds after each page finishes loading, based on the segmentation rules you have set for your Campaign.", "beta": "BETA", "between": "between", "billedNextOn": "billed next on", "billedNextOnWithDiscount": "Next billing with discount", "billingAddress": "Billing address", "billingCycle": {"annual": "annual", "monthly": "monthly", "quarterly": "quarterly"}, "billingHelp": "(e.g.: company name)", "billingInfo": "Billing", "billingName": "Billing name", "block": "Block", "blockExistingTooltip": "Block email addresses that have subscribed to any previous campaigns.", "blockWithXCol": "Block with {columnCnt} column{plural}", "blockingTime": "Blocking time (in seconds)", "blurRadius": "Blur radius", "bold": "Bold", "border": "Border", "borderAndShadow": "Border and shadow", "borderColor": "Border color", "borderRadius": "Corner radius", "borderThemeKitRounding": "Use Custom theme rounding", "borderType": "Type", "bottom": "Bottom", "bottomLeft": "Bottom left", "bottomRight": "Bottom right", "bounce": "<PERSON><PERSON><PERSON>", "brandAgency": {"btnText": "Click here to learn more about our Agency Account", "title": "Are you a Marketing, CRO, eCommerce or Website development agency?"}, "brandColors": {"dark": "Dark", "light": "Light", "neutral1": "Neutral 1", "neutral2": "Neutral 2"}, "brandColorsTitle": "Brand colors", "brandKitGroup": {"colors": "Colors", "font": "Font", "fontPlaceholder": "Select font", "radius": {"large": "Large", "medium": "Medium", "none": "None", "small": "Small"}, "rounding": "Corners"}, "brandName": "Brand name", "breakLines": "Break into multiple lines", "browse": "Browse", "button": "<PERSON><PERSON>", "buttonAction": "Action", "buttonActionTooltip": "Choose how you want this action to be reported in our system", "buttonCampaignStatus": "Campaign goal reached", "buttonCampaignStatusTooltip": "If this switch is on, your campaign won't appear again to your visitors after clicking the button, and we will record the action as a conversion in our system.", "buttonCampaignStatusTooltipOff": "If this switch is off, we won’t record the action as a conversion in our system.", "buttonConfirmOrder": "Confirm order", "buttonConfirmShopifyOrder": "Go to Shopify", "infoAboutConfirmShopifyOrder": {"line1": "As the last step to change your subscription,", "line2": "you have to approve this plan on your Shopify store"}, "buttonEvent": "Button event", "buttonEventsTooltip": "Decide what should happen when your users click on this button", "buttonId": "Button ID", "buttonMsg": {"send": "Send"}, "buttonText": "Button text", "buttonUrl": "Button URL", "feeds": {"title": "Google Product Feeds", "newFeed": {"title": "New feed", "domain": {"placeholder": "Select domain"}, "type": {"placeholder": "Select feed type"}, "url": {"placeholder": "Paste the url of the feed here...", "alert": "Invalid url format"}, "actionButton": "Add feed", "success": "Successfully added", "failed": "Failed to add"}, "delete": {"modal": {"title": "Are you sure?", "description": "Are you sure you want to delete this feed?"}, "success": "Successfully deleted", "failed": "Failed to delete"}, "sync": {"modal": {"title": "Are you sure?", "description": "Are you sure you want to run synchronization?"}, "success": "Successfully triggered", "failed": "Failed to trigger"}, "table": {"states": {"waiting": "Waiting", "running": "Running", "finished": "Finished", "error": "Error"}, "errors": "(%{count} errors)"}}, "SPR": {"title": "Description based product recommendation", "new": {"title": "Start product description procesing for recommendation", "domain": {"placeholder": "Select domain"}, "actionButton": "Start processing", "success": "Successfully added", "failed": "Failed to add"}, "delete": {"modal": {"title": "Are you sure?", "description": "Are you sure you want to delete this SPR config?"}, "success": "Successfully deleted", "failed": "Failed to delete"}, "generate": {"modal": {"title": "Recommendation generating", "amountLabel": "Generate recommendations for {amount-input} products.", "all": "all", "generate": "Generate"}, "success": "Successfully triggered", "failed": "Failed to trigger"}, "table": {"states": {"raw": "-", "running": "Running", "finished": "Finished"}, "error": "Unsuccesful processing", "startedAt": "Started at: %{time}", "finishedAt": "Finished at: %{time}"}}, "productPageOptimizer": {"featureUnavailable": "This feature is unavailable for you.", "featureUnavailableDescription": "You need to have at least one active Shopify/ShopRenter store to use this feature.", "heading": "Product Page Optimizer", "export100": "Export top 100", "exportexternal100": "Export top 100 external", "exportResults": "Export results", "exportExternalResults": "Export external results", "exportResultsTooltip": "Export the results of prompts that finished generation to a CSV file.", "exportResultsDisabledTooltip": "Unavailable until at least one prompt completes generation.", "generate": "Generate", "publish": "Publish", "hasUnpublishedChanges": "Unpublished changes.", "publishError": "Error happened during publish", "lastSuccessfulPublish": "Last successful publish:", "CSVImport": {"importCSV": "Import CSV", "invalidFileFormatError": "Invalid file format, please upload a CSV file.", "successfulCSVImport": "Successfully imported CSV file", "invalidCSVFile": "Invalid CSV file, check column names and values.", "csvImportError": "Error happened during CSV import."}, "upsertModal": {"heading": "Save prompt", "displayName": "Name", "outputType": "Type", "variableName": "Output variable name(s)", "variableNameRegexError": "You can only use alphanumeric characters, colon and underscore in this field.", "prompt": "Prompt", "chatgptVersion": "ChatGPT version", "useExternalData": "Use external data", "dataValidationHeading": "Data validation settings", "postFormatHeading": "Post format settings", "notifications": {"duplicatedVariables": "Variable(s) already exists in your shop"}, "postFormatSettings": {"removeTextLabel": "Remove this part of the sentence", "removeTextValue": "You'll love this product because it is", "capitalizeFirstLetterLabel": "First letter capital", "periodAtTheEndLabel": "Period at the end of the sentence", "removeApostrophe": "Remove quotation mark"}, "dataValidationSettings": {"minDescriptionLengthLabel": "Check the minimum description length before the generation. Min. char.:", "minResultLengthLabel": "Check the minimum length of the generated result. Min. char.:", "maxResultLengthLabel": "Check the maximum length of the generated result. Max char.:"}}, "confirmationModal": {"availableProducts": "Available products:", "availableItems": "Available items:", "generate": {"title": "Generation settings", "description": "Prompt \"{prompt-name}\" will be generated for {amount-input} products.", "all": "all", "generationMode": "Generation mode", "generateAll": "Generate all", "generateMissing": "Generate missing"}, "publish": {"title": "Are you sure to publish all prompts?"}, "export-results": {"title": "Export results", "description": "Export {amount-input} results of the following prompts: {prompt-names}", "all": "all"}, "delete": {"title": "Are you sure you want to delete this prompt?"}}, "autoGenerateModal": {"title": "Auto-generation settings", "enableLabel": "Auto-generation of prompt variables for missing products", "amountLabel": "Generate the variables automatically for {amount-input} products."}, "columns": {"name": "Name", "status": "Status", "generationStatus": "Generation Status", "actions": "Actions"}, "date": {"since": "since %{date}", "at": "at %{date}"}, "status": {"created": "Created", "queued": "Queued", "inProgress": "In Progress", "finished": "Finished", "timeout": "Timeout", "failed": "Failed", "errorsCount": "{count} errors"}}, "buttons": "Buttons", "byVisitors": "by visitors", "campaign": "Campaign", "campaignAlerts": {"CountdownInThePast": {"description": "The campaign can’t be displayed as the end that of the countdown element in the following variants is in the past:", "howToFix": "<span style=\"color: #505763; font-weight: 500;\">How to fix:</span><br>Open <a class=\"cursor-pointer\" target=\"_blank\"  href=\"{editorLink}\">the editor</a>, select the countdown element, and set the end date to be in the future.", "item": "{variantName} variant", "title": "Countdown’s end date is in the past"}, "CampaignAlmostOutOfDiscountCodes": {"description": "80% of your discount codes are used in the following variant(s). If the variant run out of discount codes then it will not display to your visitors:", "howToFix": "<span style=\"color: #505763; font-weight: 500;\">How to fix:</span><br>Please go to <a class=\"cursor-pointer\" target=\"_blank\"  href=\"{editorLink}\">the editor</a>, select the discount component and upload new discount codes for the variant. If you already fixed it and your campaign is appearing then just simply dismiss this message.", "item": "{variantName} variant is almost out of discount codes.", "title": "Campaign is almost out of discount codes"}, "CampaignOutOfDiscountCodes": {"description": "Your campaign ran out of discount codes. The campaign won’t appear to the visitors.", "howToFix": "<span style=\"color: #505763; font-weight: 500;\">How to fix:</span><br>Please go to the <a class=\"cursor-pointer\" target=\"_blank\"  href=\"{editorLink}\">editor</a>, select the discount component and upload new discount codes for the variant. If you already fixed it and your campaign is appearing then just simply dismiss this message.", "item": "{variantName} variant is ran out of discount codes.", "title": "Campaign is out of discount codes"}, "InsertCodeMissing": {"description": "We haven`t detected your OptiMonk code on your site yet.", "howToFix": "<span style=\"color: #505763; font-weight: 500;\">How to fix:</span><br>Please go to the <a class=\"cursor-pointer\" target=\"_blank\"  href=\"{insertCodeLink}\">Insert code</a> page and follow the installation instructions. If you already fixed it and your campaign is appearing then just simply dismiss this message.", "title": "OptiMonk code not inserted"}, "InsertCodeV3Missing_nonShopify": {"description": "For faster loading of embedded campaigns, you need to insert the new OptiMonk JavaScript code in the <head> section of each page of your website.", "howToFix": "How to fix", "step1": "Remove the old JavaScript from your site. (Except for Wordpress, Magento.)", "step2": "Navigate to the <a class=\"cursor-pointer\" href=\"{insertCodePage}\">Insert Code</a> page", "step3": "Insert the new OptiMonk JavaScript code in the <head> section of each page of your website.", "title": "New OptiMonk JavaScript is not inserted"}, "InsertCodeV3Missing_shopify": {"description": "For faster loading of embedded campaigns, you need to enable the OptiMonk App Embed on your Shopify site.", "howToFix": "<span style=\"color: #505763; font-weight: 500;\">How to fix:</span><br>{0}", "howToFix_link": "Enable OptiMonk App Embed", "title": "OptiMonk App embed not enabled"}, "IntegrationError": {"description": "We’ve experienced error(s) when synchronizing data with your {ctx_type} account.", "howToFix": "<a class=\"cursor-pointer\" target=\"_blank\"  href=\"{filteredLeadsLink}\">Click here</a> to find further information about the problem.", "title": "Integration error"}, "IntegrationErrorAPIKey": {"title": "{integration} integration - Incorrect API Key", "description": "The API key set in the integration settings is incorrect or not valid anymore.", "howToFix": "How to fix: Verify the API key that was added in the integration settings. {link} in the campaign settings page, click on the integration and {highlighted}. If the API key doesn't exist anymore, create a new one and paste it in.", "howToFixLink": "Go to Integrations", "howToFixHighlighted": "check the API key on the first page"}, "IntegrationErrorList": {"title": "{integration} integration - Invalid list", "description": "The previously selected list in the integration doesn't exist anymore.", "howToFix": "How to fix: {link} in the campaign settings page, click on the integration and {highlighted} to send the subscribers to.", "howToFixLink": "Go to Integrations", "howToFixHighlighted": "select a new list"}, "IntegrationErrorField": {"title": "{integration} integration - Field mapping issue", "description": "The field mapping is not correct.", "howToFix": "How to fix: {link} in the campaign settings page, click on the integration, and {highlighted}", "howToFixLink": "Go to Integrations", "howToFixHighlighted": "check the field mappings."}, "IntegrationErrorAccountIssue": {"title": "{integration} integration - Account issue", "description": "Please check your integration’s account settings.", "howToFix": "How to fix: Please check your integration’s account settings."}, "ManychatCodeMissing": {"description": "You are using a Manychat widget in the {ctx_variants} variant(s) but we did not detect Manychat plugin on your site.", "howToFix": "How to fix: Please go to your <a class=\"cursor-pointer\" target=\"_blank\" href=\"{manyChatLoginLink}\">Manychat account</a>, follow the installation instructions and install the plugin to your site to display this variant. If you already fixed it and your campaign is appearing then just simply dismiss this message.", "title": "Manychat code not inserted"}, "NoActiveChange": {"description": "In order to make your campaign run properly, add at least one change in one of your active variants.", "title": "Your campaign has no active variant with dynamic content changes."}, "NoRequestFromDomain": {"description": "We haven`t received any requests from this domain in the last 24 hours.", "howToFix": "<span style=\"color: #505763; font-weight: 500;\">How to fix:</span><br>Please go to the <a class=\"cursor-pointer\" target=\"_blank\"  href=\"{insertCodeLink}\">Insert code</a> page and review the installation instructions to confirm that OptiMonk is properly installed on your site. If you already fixed it and your campaign is appearing then just simply dismiss this message.", "title": "No request from this domain"}, "ProductOutOfStock": {"description": "Your campaign contains out-of-stock product(s).You’ll find more information below:", "howToFix": "<span style=\"color: #505763; font-weight: 500;margin-bottom:12px;\">How to fix:</span><br>Please go to <a class=\"cursor-pointer\" target=\"_blank\" href=\"{editorLink}\">the editor</a>, select the product component and select another product from the list or make it available in your <a class=\"cursor-pointer\" target=\"_blank\" href=\"{storeAdminLink}/products\">Shopify store</a>. If you already fixed it and your campaign is appearing then just simply dismiss this message.", "item": "{productName} product in the {variantName} variant is out of stock.", "title": "A product becomes out-of-stock"}, "MissingFieldBindings": {"title": "Field binding missing", "description": "You don't submit the data collected in the {fields} field(s) to any integration.", "howToFix": "How to fix: {link} in the campaign settings page, select the integration where you want to send the data. Click on the field mapping step. Make sure the field is mapped to a corresponding field in your integration.", "howToFixLink": "Go to Integrations"}, "danger": "error | errors", "header": "The following {type} is blocking your campaign from running properly: | The following {type} are blocking your campaign from running properly:", "headerWarning": "The following issue could prevent your campaign from running properly: | The following issues could prevent your campaign from running properly:", "modal": {"action": "Action", "alert": "<PERSON><PERSON>", "fix": "Fix", "error": "Error", "lastTriggered": "Last triggered", "title": "Campaign errors & alerts", "learnMore": "You can see the list of errors and alert triggered in the past 7 days. By dismissing an error or alert, we will hide it temporarily from this list. If we detect the error or alert, later on, we will display it again.\nIf you think an error or alert shouldn't be here please contact <a style=\"text-decoration: underline;\" href=\"#\" onClick=\"window.HubSpotConversations?.widget?.open() || window.open('https://support.optimonk.com/hc/en-us', '_blank')\">our support</a> for further information.", "supportArticle": "https://support.optimonk.com/hc/en-us"}, "viewDetails": "View details", "warning": "alert | alerts"}, "campaignCreate": "Campaign create", "campaignName": "Campaign name", "newCampaignSMSPrompt": {"title": "What type of list do you want to build now?", "email": "Email", "emailAndSMS": "Email + SMS"}, "campaignPalette": "Template colors", "campaignPrioritySettings": {"campaignPrioritySettingOverlap": "If two campaigns, with same type (popup, side message, sticky bar), should appear at the exact same time\nbased on their triggering rules then their priority settings\nwill determine which campaign should come first.", "high": "High", "low": "Low", "normal": "Normal", "priority": "Priority", "setPriority": "Set priority"}, "campaignSelected": "1 campaign selected | {count} campaigns selected", "campaignSettings": "Campaign settings", "campaignStatistics": "Campaign statistics", "campaignStopped": "Campaign stopped running on", "campaignVariantsTitle": "Status and Schedule", "campaignWord": "campaign | campaigns", "campaigns": "Campaigns", "canCopyOnlySecondVersion": "Campaigns can only be duplicated if they were created with the Drag & Drop editor.", "cancel": "Cancel", "cancelDelete": "Cancel", "cancelDialog": "You have unsaved changes.</br>Do you want to save or discard them?", "cancelDialogTwoOpts": "You have unsaved changes.</br>Do you want to discard them?", "cancelPlan": {"buttons": {"acceptOffer": "I accept this offer", "bookConsultation": "Book your session", "cancel": "Cancel subscription", "close": "Close", "contactSupport": "Contact support", "staying": "I'm staying"}, "hints": {"forFreemium": "If you wish to cancel your subscription simply downgrade to our Free Plan.", "forNotFreemiumShopify": "If you wish to cancel your subscription just uninstall the OptiMonk app."}, "offer1": "Keep your subscription and", "offer2": "use OptiMonk in the <span class=\"color-brand-primary\">next month for FREE</span>", "offer3": "and get a</br><span class=\"color-brand-primary\">1-hour FREE performance enhancement consultation</span>", "pages": {"anotherProvider": {"lead": "Could you please share a reason for the switch? It would be very helpful and your feedback would be really appreciated! Please, share your option and help us to improve our software.", "title": "We're sad to see you switch to another tool"}, "anotherProvider2": {"lead": "In recent years, we’ve crafted one of the best growth marketing technologies for E-commerce, that outcompetes most similar tools in terms of customer satisfaction.</br>Let us show you during personalized 1:1 call how we are different from other solutions. We'll also help you get the most out of OptiMonk to get more sales while increasing your ROI:", "title": "Before switching to another tool,</br>Here's something for you"}, "base": {"lead": "We’re sad to see you want to cancel your subscription. Before you go, we'd love to understand why you have decided to close your OptiMonk account.", "title": "Sorry to see you go, {firstName}!"}, "beforeYouGo": {"lead": "We’re sad to see you want to cancel your subscription. Before you go, we'd love to understand why you have decided to close your OptiMonk account.", "title": "We’re here to give you a hand - if you need it"}, "cancellationConfirmed": {"lead": "We're sorry to see you go. Remember you can come back to us any time.", "title": "Cancellation confirmed"}, "congratulate": {"lead": "Your next month's subscription is on us. We'll activate it within 24 hours. You're now also eligible for a complimentary 1-hour consultation with our CRO expert.", "title": "Congratulations! Rescue package unlocked."}, "letUsAssistYou": {"lead": "There can be thousands of reasons why your campaigns didn’t perform as you expected. But we’re here to give you a helping hand. Let us show you during personalized 1:1 call how to get the most out of your OptiMonk subscription to get more sales while increasing your ROI:", "title": "Let us assist you in!"}, "letUsAssistYouTechnical": {"lead": "Everything is possible with OptiMonk! Have you spoken with our Support Team already? Contact us and get prompt help straight away.", "title": "Let us assist you in!"}, "stay": {"lead": "Facing economic challenges? We’re here to help.</br>Unlock our rescue package to increase sales and ROI instantly. Keep your next month’s subscription free and receive a complimentary 1-hour consultation with a CRO expert.", "title": "We’re here to give you a hand - if you need it"}}, "reasons": {"anotherProvider": "I'm going on with another provider", "notSatisfied": "I'm not satisfied with the results", "other": "Other", "saveMoney": "I'm trying to save money", "technicalProblem": "I have a technical problem that I've been unable to solve"}}, "cannon": "<PERSON>", "awesome": "Awesome", "cannotChangePayment": "Payment method change not available!", "cannotChangePaymentNoMethod": "Currently, you don’t have a payment method as you don’t have an active subscription.", "cartCustomOne": "in cart: The value of", "cartCustomTargeting": "Target visitors based on the items in their cart", "cartCustomTwo": "variable, which is a", "cartTargeting": "Target visitors based on cart contents and value", "category": "Category", "categoryId": "Category ID", "center": "Center", "challengerVariants": "Challenger variants", "chanceToWin": "Chance to win", "change": "Change", "looksGood": "Looks good", "changeColor": "Change color", "changeEditor": "Switch to old Editor", "changeEditorOld": "Let’s try the refreshed OptiMonk editor 🚀", "emailNoti": {"title": "Email notifications", "settings": {"alertsAndReports": {"title": "Alerts and reports", "desc": "Receive e-mails about alerts and reports concerning your campaigns."}, "productUpdates": {"title": "Product updates and news", "desc": "Receive emails about product updates, our newest features and improvements."}, "tips": {"title": "Tips, educational contents and best practices emails ", "desc": "Get educational content, articles and case studies, videos, webinars, community insights, and best practices to make the most of OptiMonk."}}}, "betterEmail": {"title": "Campaign <PERSON><PERSON>", "desc": "We'll send campaign alerts if something is blocking your campaigns. They will arrive to all users.<br/> Users can unsubscribe from alerts in their own account.", "placeholder": "Your email", "checkLabel": "Account owner email address", "label": "Account owner email address", "emailAlreadyExists": "Email already exists!", "successChange": "Your email has been changed.", "weWillSend": "We will send you a verification email", "tryAgainLater": "You can't change your email address so often. Please try again later!", "change": "Change"}, "changeEmail": {"address": "Change email address", "alert": "You won’t be able to use this <b> %{email} </b> email address to log in to your OptiMonk account until it is verified.", "description": "Add your new email address and we'll send you an email to verify that you are its owner.", "emailAlreadyExists": "Email already exists!", "emailAlreadyRegistered": "Email address already registered!", "label": "Change email", "newEmail": "New email address", "newEmailConfirm": "Confirm new email address", "successChange": "Your email has been changed. You can log in with your new email: %{email}", "tryAgainLater": "You can't change your email address so often. Please try again later!"}, "changeImage": "Replace image", "changePassword": "Change password", "changePlan": "Change Plan", "chartTooltips": {"assistedRevenue": "Revenue that is generated after an OptiMonk conversion that led a visitor to purchase on your website. We use a 5-day attribution window, meaning that if the visitor places her order within 5 days of the OptiMonk conversion, her purchase will be attributed to our Assisted Revenue calculation. Please note that Assisted revenue data is updated hourly.", "conversionRate": "The number of conversions divided by the number of impressions. It shows what percent of your visitors actually take the desired action.", "conversions": "When a visitor fills out a form, clicks a button or performs any action that is considered as a conversion in your campaign.", "impressionValue": "How much extra revenue is generated on average by each OptiMonk impression. It is calculated by dividing Assisted Revenue with the total number of Impressions within a given time period. Please note that Impression value data is updated hourly.", "impressions": "An impression is a display of any pop-up, fullscreen, sidebar or sticky bar, regardless of whether the visitor converted or not."}, "chatFuel": "Chatfuel", "checkOutGallery": "Check out our gallery", "checkbox": "Checkbox", "checkboxAlign": "Checkbox align", "choose": "<PERSON><PERSON>", "chooseATemplate": "Choose a template", "chooseDomain": "Choose domain", "chooseFile": "<PERSON><PERSON>", "choosePreviouslySaved": "Create a new API integration for your campaign with previously saved credentials:", "chooseTemplate": "Choose template", "choosedPage": "Chosen page", "circle": "Circle", "city": "City", "clean": "Clean", "clear": "Clear", "clearAll": "Clear all", "clickAction": "Action", "clickHelp": "Use the following rule for the popup to be displayed when a visitor clicks on a particular part of your page — a box, link, banner, etc. At this point the popup behaves like a \"mini landing page\".\n    You can enter any type of value of any HTML element, CSS classes and IDs, and their combination.</br>\n    </br>\n    IMPORTANT NOTE: When a visitor clicks a child element, the event will not be triggered for the parent element. For example, the event will not be triggered for a #container value when we click on one of its links or any other element.", "close": "Close", "closeButton": "Close button", "closeButtonGlobal": "Close button", "closeOnEsc": "Close when \"Esc\" is pressed", "closeOnOverlayClick": "Close when overlay is clicked", "closePopup": "Close", "codeEditor": "Code", "codeInsert": {"checkNow": "Check now", "custom1": "To install OptiMonk, you need to insert the OptiMonk JavaScript code in the <head> section of each page of your website.", "custom21": "In the event you don't feel comfortable changing the code on your site, you can click the button below to send an email containing the code to your system administrator or web developer and he or she will know what to do!", "custom22": "If you haven't got a developer in your team, we can help you to insert the JavaScript code, just contact us. This service is absolutely free.", "custom23": "In case you don't feel comfortable installing the {plugin} to your site, you can click the button to send an email containing the instructions to your system administrator or web developer and he or she will know what to do!", "custom24": "If you haven't got a developer in your team, we can help you to install the {plugin}, just contact us. This service is absolutely free.", "magento": "<p>In order to display the campaigns you need to install the OptiMonk extension on your Magento application.</p>\n      <p>When it's installed you need to use your OptiMonk ID, which is: <strong>{userId}</strong></p>\n      <p>You can find more details about the extensions on the following links:</p>\n      <a target=\"_blank\" href=\"https://marketplace.magento.com/wse-optimonkm1.html\">Magento 1.x</a></br>\n      <a target=\"_blank\" href=\"https://marketplace.magento.com/wse-optimonk.html\">Magento 2.x</a>", "omDetected": "You've successfully installed OptiMonk to your site.", "omNotDetected": "Unfortunately, we couldn't detect OptiMonk on your site.", "serviceUnavailable": "Sorry, error on our end.", "shopify": "<p>In order to display the campaigns you need to install the OptiMonk plugin on your Shopify site.</p>\n      <p>You can find more details about the app on the following link: <a target=\"_blank\" href=\"https://apps.shopify.com/optimonk\">https://apps.shopify.com/optimonk</a></p>", "wordpress": "<p>In order to display the campaigns you need to install the OptiMonk plugin on your WordPress site.</p>\n      <p>When it's installed you need to use your OptiMonk ID which is: <strong>{userId}</strong></p>\n      <p>You can find more details about the plugin on the following link: <a target=\"_blank\" href=\"https://wordpress.org/plugins/exit-intent-popups-by-optimonk/\">https://wordpress.org/plugins/exit-intent-popups-by-optimonk/</a></p>"}, "codeInsertV2": {"alreadyConnected": "Your website already connected", "bigcommerce": {"description": "In order to display your campaigns first you will need to integrate OptiMonk with your BigCommerce e-commerce platform", "name": "Bigcommerce", "steps": [{"texts": ["1. To install OptiMonk, you need to copy and add the following OptiMonk JavaScript code to your BigCommerce website"], "title": "Get your unique JavaScript code from OptiMonk"}, {"texts": ["2.1 Open a new webpage and log in to the admin page of your BigCommerce website", "2.2 Select Storefront on the left", "2.3 Next, select <PERSON><PERSON><PERSON> Manager", "2.4 Click on C<PERSON> a <PERSON>rip<PERSON>", "2.5. On the next page give a name to your Script", "2.6. Then select the following options", "Location on page: Header", "Select pages where script will be added: All pages", "Script category: Essential", "Script type: Script", "2.7. The last step is to paste the code you have copied from your OptiMonk admin", "<PERSON>lick Save in the bottom right corner to Save the new <PERSON><PERSON><PERSON>.", "You should see a message <PERSON><PERSON><PERSON> created and you can find the new <PERSON>ript under Installed Scripts"], "title": "Add our code to your BigCommerce e-commerce website"}]}, "buttons": {"anotherPlatform": "Do you use another platform?", "help": "Do you need help?"}, "domainCheckInProgress": "Domain recognition in progress. This may take a few seconds", "header": {"connectLater": "Connect later"}, "help": {"askUsForHelp": {"description": "\tAlternatively, you can also share the login details of your website and our colleagues will connect your site with OptiMonk. If it's possible, create a temporary user for us that you can easily delete later. We will notify you via email when the connection is ready.", "inputUrl": "URL of your website's admin login page", "problem": "Chat not available right now", "startChat": "Start chat", "text1": "Not sure how to connect your website?", "text2": "Start a chat with our support team and they'll help you make it work.", "text3": "In working hours we reply within minutes.", "title": "Ask us for help"}, "sendToDev": {"text": "Send an email containing the instructions to your system administrator or web developer and he or she", "title": "Send the instructions to my admin or developer"}}, "iCanInsert": "I can insert the JavaScript code", "insertJS": "Insert the OptiMonk JavaScript code in the <head> section of each page of your website.", "magento": {"description": "OptiMonk has direct integration with Magento, so you can use the OptiMonk application (available in the Magento Marketplace) to integrate the two systems.", "name": "Magento", "steps": [{"texts": ["1.1. First, log in to your Magento Marketplace account at <a href=\"https://marketplace.magento.com/\" target=\"_blank\">https://marketplace.magento.com</a>. Click on your name in the top right corner, then click on My Profile.", "1.2. Click on Marketplace then Access Keys under My Products:", "1.3. Select your Magento version, then click on Create A New Access Key:", "1.4. Give a name to your access key then click OK. This will create a Public and a Private Key. We will need these Keys for our Magento Admin page:", "1.5. Open the Magento Admin page in a new browser window, and click on System, then Web Setup Wizard:", "1.6. Select Component Manager on the next page:", "1.7. Select Sign in to sync your Magento Marketplace purchases:", "1.8. You will need the previously created Public and Private keys from the Magento Marketplace. Copy them, then click Sign in:"], "title": "Create a new access Key"}, {"texts": ["2.1. Go back to Magento Marketplace, click on the Search bar at the top, and search for OptiMonk:", "2.2. Select the correct version of the OptiMonk application, then click on Add to Cart:", "2.3. Complete the checkout steps. The OptiMonk application is free in the Magento Marketplace.", "2.4. Go back to your Magento Admin page / Component Manager, and click on Sync:", "2.5. <PERSON><PERSON> on Install under New Purchases:", "2.6. <PERSON>lick on Start readiness check:", "2.7. Once it is finished, click on Next. In the next step, you will be able to create a backup. Click Next once you have selected the options you want to use", "2.8. In the next step, you will be able to create a backup. Click Next once you have selected the options you want to use.", "2.9. <PERSON><PERSON> on <PERSON><PERSON><PERSON> to finish the setup:"], "title": "Install the OptiMonk application"}, {"texts": ["Copy your OptiMonk ID <strong>{userId}</strong> and paste it into the Magento Admin page to finish the setup."], "title": "Paste your OptiMonk ID"}]}, "manual": {"connect": "Connect your website to make your campaign live", "gtag": "or Install with Google tag manager", "link": "For further installation guides you may visit our", "supportPage": "support page", "whatType": "What e-commerce platform do you use?"}, "messages": {"error": "The connection has not been properly created yet.<br>Please check the instructions and make sure that your setup meets all the requirements.", "loading": "Checking our OptiMonk script on your website"}, "platforms": {"bigcommerce": "Bigcommerce", "custom": "Custom website", "gtag": "Google tag manager", "magento": "Magento", "other": "Other platforms", "shopify": "Shopify", "shoprenter": "Shop<PERSON>er", "unas": "<PERSON><PERSON>", "wordpress": "Wordpress / Woocommerce", "gomag": "Gomag"}, "selectDomain": "Select the domain on which you want to use OptiMonk.", "sendToDeveloper": {"message": "Dear {name},\n\nPlease add this JavaScript code to every page of {domain} in the <head> tag to install {brandName}. You only need to include the code once per page.\n\n{script}\n\nFor further installation guides you may visit our support page:\n\nhttps://support.optimonk.com/hc/en-us/categories/4405601748754-Integrations\n", "placeholders": {"name": "{{name}}"}}, "shopify": {"description": "In order to display the campaigns you need to enable the OptiMonk App Embeds on your Shopify store.", "messages": {"detectionError": "We can not check the connection between OptiMonk and your website as long as you use the free plan of Shopify. Please come back to validate the connection once you upgraded your Shopify plan.", "error": "The connection has not been properly created yet.<br>The OptiMonk App Embed may not be enabled on your Shopify store or your store might be password protected.", "loading": "Verifying that OptiMonk App Embed is enabled and saved in Shopify"}, "name": "Shopify", "steps": [{"texts": ["1.1 Visit the <a href=\"https://apps.shopify.com/optimonk\">OptiMonk app page</a> in the Shopify store and click on the Add app button to install OptiMonk on your Shopify site", "1.2 On the next screen, click on Install app to finish the installation."], "title": "Install OptiMonk app on your Shopify store"}, {"texts": ["2.1 Visit <a class=\"cursor-pointer\" target=\"_blank\" href=\"{link}\">this Shopify settings page</a> and make sure that the OptiMonk switch on the left is turned on", "2.2 Once enabled, press “Save”", "A few minutes of processing time is required after the app embed setting.<br>During this time, you're free to close this window and keep on working in OptiMonk.", "If you change the Theme in the shopify store, you have to enable the OptiMonk app embed again."], "title": "Enable the OptiMonk app embed on your Shopify theme"}]}, "shoprenter": {"description": "Az OptiMonk fiókunkat egyszerűen összeköthetjük a Shoprenter oldalunkkal. Ehhez nem is kell mást tennünk, mint telepíteni az OptiMonk alkalmazást a Shoprenter oldalunk admin felületén.", "name": "Shop<PERSON>er", "steps": [{"texts": ["1.1. <PERSON><PERSON><PERSON><PERSON><PERSON> is lépj be az oldalad admin felületére, majd válaszd az Alkalmazások menüpontot:", "1.2. <PERSON><PERSON>d meg az OptiMonkot a list<PERSON><PERSON><PERSON><PERSON>, <PERSON>s kattints a Bővebben gombra:", "1.3. A következő oldalon kattints a Telepítés gombra:", "1.4. Ha nem jelentkeztél még be az OptiMonk fiókdoba, úgy meg fog jelenni egy ablak ahol ezt meg tudod tenni, és kész is az integráció! Elkezdheted használni az OptiMonkot a Shoprenter oldaladon"], "title": "Telepítsd az OptiMonk alkalmazást"}]}, "success": {"congratulation": "Congratulation!", "connected": "Your website connected successfully"}, "tagmanager": {"description": "In order to display your campaigns first you will need to integrate OptiMonk with your website, We will show you how you can do this with Google Tag Manager", "name": "Google Tag Manager", "steps": [{"texts": ["1. To install OptiMonk, you need to copy and add the following OptiMonk JavaScript code to Google Tag Manager"], "title": "Get your unique JavaScript code from OptiMonk"}, {"texts": ["2.1 Open a new webpage and log in to your Google Tag Manager account and select your website you wish to implement OptiMonk on", "2.2 Select Add a new Tag", "2.3 Click on Tag Configuration, then select Custom HTML", "2.4 Click inside the HTML box and paste the OptiMonk code you have copied previously with the shortcut Ctrl + V, or right-click with your mouse and select Paste", "2.5. <PERSON><PERSON> on <PERSON><PERSON><PERSON>", "2.6. <PERSON><PERSON> on All Pages", "2.7. Once you are done, this is what you will see. Finalize the changes with clicking Save in the top right corner", "2.8. Do not forget to click Submit to finish the setup", "2.9. You do not have to change anything on the next page, you can give a name to the changes, then click Publish in the top right corner"], "title": "Add our code to your website via Google Tag Manager"}]}, "titles": {"almostThere": "You’re almost there!", "connect": "Connect your website", "connected": "You’re all set, the OptiMonk code is inserted", "installWith": "Install with {platform}", "installationGuideTo": "Installation guide to {platform}"}, "unas": {"description": "After a simple integration, you can use the most effective OptiMonk campaigns in your UNAS webstore just like in any other common webshop platform.", "name": "<PERSON><PERSON>", "apiKeyLabel": "API key", "save": "Save", "delete": "Revoke", "requiredApiKey": "API key is required", "invalidApiKey": "Invalid API key", "revokeDialog": {"title": "Are you sure you want to revoke your UNAS API key?", "text": "If you revoke it, we cannot update your product catalog.", "cancel": "Cancel", "confirm": "Revoke"}, "steps": [{"texts": ["1.1. <PERSON><PERSON> the next code:"], "title": "Get your unique JavaScript code from OptiMonk"}, {"texts": ["2.1. In a new browser window, log in to the admin interface of your UNAS webstore. You’ll find the Script Insertion menu under Settings > Design > Appearance.", "2.2. Click the Add button in the top right corner.", "2.3. On the next page, after enabling the Active field, fill in the other fields as follows:", "Type: src, head", "Script loading mode: Async", "Title: OptiMon<PERSON>", "SCRIPT src: paste the code snippet copied in Step 3 here", "2.4. Once you're done, click the Save button in the top right corner to save the settings. Your OptiMonk campaigns will now be able to appear on the UNAS website."], "title": "Add the OptiMonk code in the UNAS interface"}, {"texts": ["3.1. Log in to the admin interface of your UNAS web store. To create an API key, navigate to Settings > External Connection > API Connection.", "3.2. If you haven't created an API key yet or want to create one, click the 'Create API Key' button in the top right corner.", "3.3. Give it a name and save it. Then copy the API key from the first column and paste it into the field above, and save it."], "title": "Provide your UNAS API key for advanced features."}]}, "wordpress": {"description": "In order to display the campaigns you need to install the OptiMonk plugin on your WordPress site.", "name": "Wordpress / Woocommerce", "steps": [{"texts": ["1.1. Log in to your WordPress site and click on Plugins on the left, then select Add new. Click on the Search bar and search for the word OptiMonk. Click on Install Now.", "1.2. <PERSON><PERSON> on Activate.", "1.3. After activation has been completed successfully, you’ll find the OptiMonk plugin in the list of Installed Plugins."], "title": "Install OptiMonk plugin on your Wordpress site"}, {"texts": ["2.1. Copy your OptiMonk ID: <strong>{userId}</strong>", "2.2. Go back to the WordPress admin page, navigate to Appearance on the left and select OptiMonk. Insert your OptiMonk ID Number into your OptiMonk WordPress Plugin.", "2.3. <PERSON><PERSON> on Save Changes to finish the setup."], "title": "Insert your OptiMonk ID number: {userId}"}, {"texts": ["OptiMonk may not work immediately on WordPress sites due to the presence of a cache plugin. These plugins help speed up websites by compressing and generating HTML files, which reduces server resources. To make sure that OptiMonk runs smoothly and your campaigns appear on your website, we strongly recommend the following steps:", "Add the following domains to the cache ignore list:", "https://onsite.optimonk.com", "https://front.optimonk.com", "Purge the cache on your website", "If you are not sure how to do this, please take a look at our guide for setting up different types of WordPress cache plugins to work with OptiMonk which you can <a href=\"https://support.optimonk.com/hc/en-us/articles/202963241-Installing-OptiMonk-on-a-WordPress-Website#h_01GZH5TCH3VB4NWZMZZRGYH8W5\" target=\"_blank\">find here</a>."], "title": "Make sure your cache plugin doesn't interfere with the OptiMonk plugin"}, {"texts": ["4.1. Click the \"Login\" button, then enter your username and password."], "title": "Log in to WordPress with OptiMonk", "button": "<PERSON><PERSON>"}]}}, "color": "Color", "color2": "Color2", "colorModalNamePlaceholder": "Color {index}", "colorName": "Color name", "colorPickerSettings": {"tabs": {"name": {"gradient": "Gradient", "image": "Image", "none": "None", "solid": "Solid"}}}, "colors": "Colors", "column": "Column", "columnBackground": "Background", "columnCount": "Column count", "columnCountWarning": "The contents of the blocks will also be removed. Are you sure you'd like to continue?", "columnLeft": "<PERSON><PERSON><PERSON> left", "columnMiddle": "Column middle", "columnRight": "Column right", "columnSplit": "Column split", "columns": "Columns", "columnsWithinRow": "Columns within the block", "components": "Components", "conditionsFinish": "Next to Integrations", "confetti": "Confetti", "confidence": "Confidence", "confidenceClick": "Click here to learn more<br>about the confidence.", "confirmationDialog": "Are you sure you want to delete it?", "confirmedManuallyEmbedded": "You confirmed that the code was embedded manually.", "connect": {"existingLogin": "Login to my OptiMonk account", "hi": "Hi {firstName}", "newAccount": "Create new OptiMonk account", "shopify": "Shopify", "shoprenter": "ShopRenter", "successfulConnect": "Great job! Your account is successfully connected to your {type} store.", "welcomeToOptiMonk": "Welcome to OptiMonk!", "whatToDo": "If you’ve already registered earlier, just login to your account. If not, create a new one."}, "connectShopify": {"connect": "Connect", "connectAnother": "I would like to connect with another OptiMonk account", "doYouWantToConnect": "Do you want to connect your store with this account?", "failed": "Something went wrong. Please try to reconnect Shopify again.", "goToAgencyDashboard": "Go to Partner Dashboard", "hiOM": "Hi {firstName}!", "noSubAccount": "You don't have a sub-account yet, create one, then reinstall the app.", "selectSubAccount": "Please select which sub-account you would like to connect your store with.", "selectSubAccountDropDown": "Select a sub-account", "success": "Successfully connected with {domain}."}, "contact": "Contact", "contactUs": "Contact us", "contactUsModal": {"btn": "Get my Quote!", "lead": "To get access to a higher pageview limit and enjoy our extended services, you’ll need to fill out this form and a member of the OptiMonk team will reach out with all the details once you’ve submitted", "title": "Get Your OptiMonk Custom Quote"}, "contain": "Contain", "containerBackground": "Container background", "containerSelect": "Container select", "contains": "contains", "contentAlignment": "Content alignment", "contentPosition": "Content position", "contentSize": "Content size", "contentWidth": "Content width", "continue": "Continue", "control": "Control", "controls": "Controls", "conversion": "Conversion", "conversionGoals": {"addNewText": "Create new goal", "builtInConversionGoals": "Built-in conversion goals", "builtInConversionGoalsTooltip": "Built-in conversion goals are only available on the Shopify and Shoprenter platforms.", "createModal": {"createTitle": "Create a new conversion goal", "editTitle": "Edit conversion goal", "eventPropertiesTitle": {"eventProperties": "Event properties", "pageView": "Where the URL meets the following criteria"}, "eventType": "Event type", "selectType": "Select type"}, "createNewGoal": "Create new goal", "customConversionGoals": "Custom conversion goals", "dcFlow": {"addToCartTitle": "Visitors with carts", "createGoal": "Create or select a conversion goal", "createGoalAction": "Create new conversion goal", "description": "For some campaigns, it is advisable to create a conversion goal so that the results can be measured.", "descriptionModule": {"addToCart": "Visitors with carts", "blogPageVisit": "Blog page visit", "description": "Conversion goals offer greater flexibility for measuring performance based on specific metrics, allowing you to define conversion events beyond sign ups (e.g. pageview or add to cart) that align perfectly with your business goals.", "giveFeedback": "Give us feedback", "purchase": "Visitors with orders", "readMore": "Read more", "title": "Choose relevant conversion goals for your campaigns", "watchVideo": "Watch a video"}, "purchaseTitle": "Visitors with orders", "toGoalSelection": "Next to Goal selection"}, "defaultGoal": "Campaign conversion", "defaultGoals": {"addToCart": "The visitor has added a product to the cart", "purchase": "The visitor has placed an order"}, "defineConversionGoal": "Define conversion goal", "eventType": "Select type", "eventTypes": {"addToCart": "Visitors with carts", "campaignClosed": "Campaign closed", "campaignConversion": "Campaign conversion", "campaignImpression": "Campaign impression", "eoc": "Campaign closed", "eof": "Campaign conversion", "eoi": "Campaign impression", "pageView": "Page view", "purchase": "Visitors with orders", "custom": "Custom"}, "failedToSaveGoal": "Failed to save goal", "fieldNames": {"campaignId": "Campaign ID", "cartTotal": "Value of cart", "price": "Price of the added product", "quantity": "Quantity of the added product", "salesValue": "Sales value", "totalNumberOfProducts": "The total number of products", "url": "url"}, "customEventNamePlaceholder": "Name of the event", "customEventFieldNamePlaceholder": "Property name (optional)", "customEventAllowedCharacters": "Only letters a-z, numbers and underscores are allowed.", "customEventMaxLength": "Maximum length is 64 characters.", "customEventFieldMaxLength": "Maximum length is 128 characters.", "customEventValueMaxLength": "Maximum length is 256 characters.", "goalSaved": "Goal saved successfully", "rulesExplanation": {"cartTotal": "Value of cart", "contains": "contains", "endsWith": "ends with", "equals": "is equal to", "greaterThan": "is greater than", "greaterThanEquals": "is greater than or equal to", "interval": "is between", "itemCount": "The total number of products ", "lessThan": "is less than", "lessThanEquals": "is less than or equal to", "notContains": "does not contain", "notEndsWith": "does not end with", "notEquals": "is not equal to", "notStartsWith": "does not start with", "price": "Price of the added product", "quantity": "Quantity of the added product", "startsWith": "starts with", "total": "Sales value", "url": "url", "custom": "'{field}' property of '{event}' event", "customExists": "'{event}' event exists"}, "selectLabel": "Conversion", "title": "Conversion goals", "tooltip": "The conversion numbers show the number of unique visitors who reached the selected conversion goal. You can manage conversion goals on the Settings / Conversion goals page.", "supportArticle": "You will find more help about custom events here"}, "conversionRate": "Conversion rate", "conversionThenRedirect": "Redirect", "conversions": "Conversions", "convertTeaser": {"oldToPage": "Migrate to new version", "pageToOld": "Revert to old version", "toggleVersion": "Change version (in editor)"}, "cookieCustomTwo": "cookie, which is a", "copy": "Copy", "copyInsertCodeTitle": "Copy insert code to clipboard", "copyStyle": {"tooltip": "Click on the painter to copy styling of an element. Simply click on a similar element (button —> button) to apply styling."}, "copyVariant": "Create a copy of the first variant", "corner": "Corner", "countdown": "Countdown", "countdownLabels": {"days": "Days label", "hours": "Hours label", "minutes": "Minutes label", "seconds": "Seconds label"}, "countdownType": "Type", "country": "Country | Countries", "countryLabel": {"first": "Your campaign will", "last": "for visitors from"}, "coupon": "Discount code", "couponCode": "Coupon code", "couponCopy": {"helper": "The visitor will see this after they've copied the coupon code.", "title": "Successful copy message", "missing": {"title": "In case of missing", "tooltip": "If the user clicks too fast on the button, the code might not be ready in case of automatically generated codes"}}, "couponInfo": {"discounted3Month": "The first 3 months are discounted.<br>The price will be {price} / month"}, "couponModal": {"fileUpload": "File upload", "formats": "Formats: "}, "couponNextPage": "Success action", "couponPlaceholder": "Paste codes here... (ex.: coupon1, coupon2 etc.)", "couponRedeem": "Redeem coupon", "couponResetWarning": "You will delete your coupons from all the variants in this campaign. Are you sure?", "couponTitle": "Coupon title", "cover": "Cover", "create": "Create", "createCampaign": "Create Campaign", "createFirstCampaign": "Create your first campaign", "createNew": "Create new", "createNewCampaign": "Create a new campaign", "createNewOption": "Create new", "createNewTag": "Create", "createOne": "Create one", "createTempAccount": "If it's possible please create a temporary user for us.", "created": "Created", "creditCard": "Credit card", "crop": "Crop", "cssEditor": "CSS editor", "cssSelector": "CSS selector", "currentChampion": "Current champion", "currentPage": "Current page", "clickableImage": "Clickable image", "sizeAndSpacing": "Size and spacing", "currentPassword": "Current password", "currentlyActive": "Currently active", "customClass": "Custom class", "customFieldNewTooltip": "If you are integrating with a 3rd party mail system and want this field to appear in it you should make sure that the name given here matches the custom field name given in your mail sender.", "customFields": "Custom fields", "customFontErrors": {"extensionMismatch": "Please upload a zip file!", "extensionMismatchV2": "Please upload a valid file!", "missingFile": "Missing file.", "missingFiles": "%{missingExtensions} extensions are missing from the zip file!", "nameIsRequired": "Name is required.", "nameIsUsed": "Name is already in use.", "nameIsUsedV2": "This font name is already taken, please enter another name.", "selectAFile": "Please select a file.", "unseccesfulConvert": "Unsuccessful font converting!"}, "customFontExtensions": "To use a custom font please upload a zip file which contains the following font files: eot, svg, ttf, woff, woff2.", "customFontExtensionsV2": "Uploadable file formats: ttf, otf, woff, woff2", "customFontUpload": "Custom font upload", "customHTMLHelp": "Paste your HTML code here. Any <code>&lt;style&gt;</code> and <code>&lt;script&gt;</code> tags will affect the whole campaign.", "customPeriod": "Custom period", "customSource": "Custom source", "customStep": "Custom step", "customTheme": {"chooser": {"block": {"default": {"title": "Default themes"}, "empty": {"description": "With your themes, you can create campaigns with a uniform style faster. Create a campaign from our themes and we will create your first unique theme.", "link": "Select a template from the themes", "title": "You don't have your own themes yet"}, "title": "Your themes"}, "moreTemplates": "More templates", "templateCount": "{count} templates", "upcomingSeasons": "Upcoming seasons"}, "modal": {"deleteConfirm": "Are you sure you want to delete this <strong>{style}</strong> style?", "deleteCustomStyle": "Delete custom style", "editCustomStyle": "Edit custom style", "inputName": "Style name", "inputPlaceholder": "Add name", "title": "Save new {elementType} style"}, "modified": "(modified)", "notifications": {"alreadyExists": "This theme style already exists", "themeAlreadyExists": "Theme with this name already exists", "themeTooLong": "Theme name can be maximum 30 character long", "themeTooShort": "Theme name does not reach the minimum (3) character length", "tooShort": "The style name does not reach the minimum (3) character length", "unableToDelete": "Unable to delete this style", "updateFailure": "Unable to save custom style", "updatedSuccess": "Custom style updated successfully!"}, "saveAsNew": "Save as new", "savedStyle": "Saved style", "update": "Update"}, "customUrl": "Custom url", "customWebsite": "Custom website", "customhtml": "HTML", "dangerZone": "Danger zone", "dashboard": "Home", "dashboardAlerts": {"unas": {"missingApiKey": {"title": "Unas API key is missing", "description": "Provide your Unas API key for advanced features.", "link": "View details"}}, "woocommerce": {"missingApiKey": {"title": "WooCommerce API keys is missing", "description": "Provide your WooCommerce API keys for advanced features.", "link": "View details"}}}, "dashboardSections": {"onboarding": {"doneTitle": "Congratulation, you have launched your success story! 🎉", "steps": {"activate": {"description": "Almost there! Launch your first campaign to boost your conversions.", "readingTime": "~1 mins", "title": "Activate your first campaign", "cta": "Go to campaigns", "errorText": "Please create a campaign and connect OptiMonk first"}, "connect": {"description": "Add OptiMonk to your site to display campaigns.", "readingTime": "~2 mins", "title": "Connect OptiMonk", "cta": "Connect"}, "create": {"description": "Get started by creating your captivating campaign with Optimonk.", "readingTime": "~10 mins", "title": "Create your first campaign"}}, "title": "Take your last step to start your success story! | You are only {count} steps away from start your success story!", "supportSection": {"title": "Do you need help?", "text": "Get in touch with our customer support", "startChat": "Start chat", "visitKnowledgeBase": "Visit knowledgebase portal", "reka": "<PERSON><PERSON><PERSON>", "customerSupportTeamLead": "Customer Support Team Lead"}}}, "aiDashboardSections": {"onboarding": {"doneTitle": "Congratulations, you have unlocked your success story! 🎉", "steps": {"register": {"description": "Setup an account for access our services", "title": "Register Your OptiMonk Account"}, "connect": {"description": "Connect your website and enable Optimonk to supercharge your online presence.", "readingTime": "~5 mins", "title": "Connect Optimonk - Power up your website!"}, "book": {"description": "Turn visitors into customers effortlessly with the power of AI", "title": "Request AI Early Access"}}, "title": "{count} out of 3 steps are already done to start your success story!"}, "pupupTitle": "How about boosting conversion with popups?", "pupupDescription": "Get started by creating a captivating popup campaign", "createPopup": "Create a Popup", "learnMore": "Learn more", "show": "Show more", "hide": "Show less"}, "DashboardWizardTaskList": {"onboarding": {"getStarted": {"title": "Start popup wizard", "subTitle": "Answer a few questions and get tailored campaigns designed for you in minutes.", "cta": "Get started"}, "recommendation": {"title": "Choose from the popup campaigns recommended for {businessName}", "subTitle": "Based on your answers, we’ve identified the best-suited campaigns for you.", "cta": "Show recommendations"}, "steps": {"browseTemplates": {"title": "Browse templates", "description": "Browse 300+ pre-built templates for different business goals"}, "createPopup": {"title": "Create a popup", "description": "Browse our best templates to build your first campaign"}, "optimize": {"title": "Optimize your website", "description": "Run A/B tests, personalize, etc."}, "browseLibrary": {"title": "Browse tactic library", "description": "Not sure? Get inspired by 50+ tactics."}}}}, "dashed": "Dashed", "dataFromOptimonk": "Data from {brand}", "date": "Date", "dateCreated": "Date created", "dateFormat": "Format", "dateIsAfter": "is after", "dateIsAtLeast": "is at least", "dateIsBefore": "is before", "dateIsBetween": "is between", "dateIsBetweenDates": "is between dates", "dateIsInLast": "is in the last", "dateRange": {"allTime": "All time", "last1Year": "Last 1 year", "customDateRange": "Custom", "last3Months": "Last 3 months", "last30days": "Last 30 days", "last7days": "Last 7 days", "lastMonth": "Last month", "yesterday": "Yesterday"}, "dateTime": {"dateFrom": "From", "dateTo": "To"}, "days": "Days", "daysShort": {"friday": "<PERSON><PERSON>", "monday": "Mon", "saturday": "Sat", "sunday": "Sun", "thursday": "<PERSON>hu", "tuesday": "<PERSON><PERSON>", "wednesday": "Wed"}, "dblClickToCrop": "Double click to crop", "deactivate": "Deactivate", "default": "<PERSON><PERSON><PERSON>", "sameAsDesktop": "Same as desktop", "defaultCountry": "Default country", "defaultHTML": "<p>Paste your HTML code here.</p>", "defaultPage": "by button action", "defaultTabText": "Type here", "delay": "Delay", "delete": "Delete", "deleteCampaign": "Delete campaign", "deleteFieldModal": {"text": "Are you sure you want to delete this field?", "themeColorText": "Are you sure you want to delete this theme color?", "title": "Delete field "}, "deleteTemplate": "Delete template", "deny": "<PERSON><PERSON>", "deprecatedAnimation": "We introduced new animations and discontinue the old ones. Please select a new animation for your campaign.", "deselectAll": "Clear selection", "design": "Design", "designVariants": "Design variants", "desktopDisplay": "Hide element on desktop", "desktopImageByDefault": "Desktop image is displayed by default", "details": "Details", "device": {"all": "All", "both": "Both devices", "desktop": "Desktop", "desktopOnly": "Desktop only", "desktopView": "Desktop view", "desktop_and_mobile": "Desktop or mobile", "mobile": "Mobile", "mobileOnly": "Mobile only", "mobileView": "Mobile view", "unknown": "Unknown"}, "deviceWord": "<PERSON><PERSON>", "devices": "Devices", "dial": "Call", "digit": "Digit", "digitStyle": "Digit style", "dimensions": "Dimensions", "direct": "Direct", "disableTeaser": "Disable teaser", "disabled": "disabled", "discard": "Discard", "discount": "Discount", "discountPane": {"automatic": {"afterDisplayExpiration": "Expiration after displaying the code", "days": "day(s)", "expiration": "Expiration", "expiresIn": "Expires in", "fixed": "Amount", "fixedExpiration": "Specific expiration date and time", "fixedSubTitle": "The time is based on your timezone (GMT{offset}). The discount code will expire globally at <strong>the same time.</strong>", "fixedValue": "Discount value ({currency})", "hours": "hour(s)", "minutes": "minute(s)", "percentage": "Percentage", "percentageValue": "Amount of discount (percentage)", "prefix": "Prefix", "prefixTooltip": "Optional fixed characters at the beginning of the unique discount code. E.g. use the prefix SALE to generate codes like SALE3L1T3.", "shopifyNotActive": "This feature is only available for Shopify stores", "subTitle": "We will generate unique single-use discount codes and add them to your Shopify account automatically.", "type": "Type"}, "fixed": {"allCountries": "All countries", "appliesOncePerCustomer": "Once per customer", "buyAmountgetY": "Spend {x}, get {y} item(s) free", "buyAmountgetYDiscounted": "Spend {x}, get {y} item(s) {discount} off", "buyXgetY": "Buy {x} items, get {y} item(s) free", "buyXgetYDiscounted": "Buy {x} items, get {y} item(s) {discount} off", "couponAdditionalSettings": "We will create this discount code in Shopify. You will be able to add more settings or modify this code later in <a class=\"cursor-pointer\" href=\"{link}\" target=\"_blank\">Shopify.</a>", "couponCreateError": "Something went wrong while creating your discount code. <a class=\"cursor-pointer\" href=\"{link}\" target=\"_blank\">Please create it in your Shopify account.</a>", "couponCreateRetry": "Something went wrong while creating your discount code.<br>Please try to create it again.", "couponCreated": "New discount code has been created.<br>You can add more settings or modify this discount code in <a class=\"cursor-pointer\" href=\"{link}\" target=\"_blank\">Shopify's admin.</a>", "couponDoesNotExist": "This discount code does not exist. You can create it here before saving your campaign.", "couponExists": "This is an existing discount code.<br>You can edit the settings in <a class=\"cursor-pointer\" href=\"{link}\" target=\"_blank\">Shopify.</a>", "couponExpired": "This is an existing but expired discount code. Go to your <a class=\"cursor-pointer\" href=\"{link}\" target=\"_blank\">Shopify settings</a> to fix the problem.", "couponLookupError": "Something went wrong while searching for the discount code. You can check the discount settings in <a class=\"cursor-pointer\" href=\"{link}\" target=\"_blank\">Shopify.</a>", "couponUsed": "The discount code exists in Shopify but it has reached its usage limit. Go to your <a class=\"cursor-pointer\" href=\"{link}\" target=\"_blank\">Shopify settings</a> to fix the code", "createDiscountCode": "Create this discount code", "discount": "{value} discount", "editInEditor": "You can change the text in the Editor", "expiration": "Expiration", "fixedExpiration": "Specific expiration date and time", "freeShipping": "Free shipping", "maximumShippingPrice": "Maximum shipping price: {shippingMax}", "minimumPurchase": "{purchaseValue} minimum purchase", "specificCountries": "Specific countries", "specificCustomers": "Specific customers", "specificProducts": "Specific products", "subTitle": "Display the same code to every visitor.", "usageCount": "Used {value} times", "usageLimit": "Usage limit: {value}", "validFrom": "Valid from: {startsAt}", "validUntil": "Valid until: {endsAt}"}, "fixedOrUnique": {"subTitle": "Don't forget: you need to add the discount code to your shop as well"}, "followup": {"expirationEnds": "the campaign expiration date ends.", "limit": "Limit time period", "rulesText": "The discount code reminder campaign will appear automatically on the next page view if a visitor has an active discount code they haven’t redeemed yet. The reminder campaign ends if", "rulesTitle": "How does the reminder campaign work?", "subTitle": "With a reminder campaign, you can remind your users to redeem their discount code", "userClose": "deliberately closes the campaign or", "userGuide": "You can read more about this in our <a class=\"cursor-pointer\" href=\"{link}\" target=\"_blank\">User Guides.</a>", "userRedeem": "the visitor redeems their coupon,"}, "title": "Discount code type", "types": {"automatic": "Unique codes", "fix": "Fixed code", "followup": "Reminder", "gamification": "Gamification code", "unique": "Uploaded unique codes"}}, "display": "Display", "displayName": "Display name", "displayRules": "Display rules", "displayTab": "Display teaser", "distanceFromTheEdge": "Distance from the edge", "divider": "Divider", "domain": "Domain", "domainInactivationWarningModal": {"inactivateBtn": "Inactivate domain", "lead": "By inactivating this domain the following campaigns will be inactivated too:", "title": "Are you sure you inactivate this domain?"}, "domainLevelSettings": {"analytics": {"title": "<span class=\"font-weight-bold\">Analytics</span> event reporting", "statuses": {"disabled": "Disabled", "enabled": "Enabled"}}, "insertCode": {"appEmbedTitle": {"disabled": "OptiMonk App Embed in your Shopify store", "enabled": "OptiMonk JavaScript code"}, "help": "How to insert the code", "howToEnable": "How to enable app embed?", "howToInsertCode": "How to insert the code", "statuses": {"disabled": "Disabled", "enabled": "Enabled", "inserted": "Inserted", "notInserted": "Not inserted"}, "title": "Installation code"}, "shopifyAppExtension": {"active": "The <b>Shopify app embed mode</b> enabled.", "inactive": "The <b>Shopify app embed mode</b> disabled."}, "tagManager": {"container": "Download Container", "lead": "To track your campaign-specific events in Google Analytics via Tag Manager, you need to import the following Tag Manager container we created for you.", "title": "Do you use Google Analytics with Tag Manager?"}}, "domainManagementTitle": "Where would you like to run this campaign?", "domainPlaceholder": "Your domain", "domainTypeInNew": "Type in a new domain", "domainUsed": "This domain is used in %{count} campaigns. If you remove it those campaigns will be inactivated and you have to assign new domains to them. Are you sure you'd like to continue?", "domainUsedRename": "This domain is used in %{count} campaigns. If you rename it those campaigns will be modified. Are you sure you'd like to continue?", "domains": "Domains", "done": "Done", "dontCountAsConversion": "Don't count as conversion", "dotted": "Dotted", "double": "Double", "downgradeModal": {"cancelBtn": "Keep using my current plan", "changeToPlan": "Change to:", "changesAbTestingCampaigns": "The following campaign(s) will be <b>deactivated</b> due to using <b>A/B testing</b>:", "changesDomainInactivationCampaigns": "The following campaign(s) will be <b>deactivated</b> due to deactivating domain names:", "changesPageViewsRemaining": "After the change your account will have only <b>{remainingPageViews}<b> pageviews", "changesProRuleCampaigns": "The following campaign(s) will be <b>deactivated</b> due to using <b>Pro Rules</b>:", "changesTitle": "In your account after the plan change:", "chooseDomainsLead": "From the next month you will have only <b>{newDomainsCount} active domains</b>. To go on, please, choose which ones you wish to keep active:", "chooseDomainsTitle": "Choose the domains you want to keep", "currentPlan": "Current plan:", "downgrade": "Confirm", "downgradeBtn": "I want to lose functionality and change to {planName} plan", "downgradeConfirmationAltText": "Are you sure you want to change your plan?", "downgradeConfirmationTitle": "Change next period plan", "headerTitle": "Are you sure you want to lose your benefits?", "infoBar": "Your plan will be downgraded to %{downgradedPackage} at the end of your billing period on %{nextBilling}", "infoBarAfterExpires": "Your subscription will be changed to %{downgradedPackage} within 24 hours", "infoBarWithoutDowngrade": "Your plan will be changed to %{downgradedPackage} at the end of your billing period on %{nextBilling}", "lostFeaturesTitle": "With this plan change you will lose:", "needSomeHelp": "Need some help? ", "scheduleACall": "Schedule a call.", "shopifyApproveInfo": "You also need to approve this change on your Shopify store. You will be redirected there.", "unlimitedDomains": "unlimited domains"}, "download": "Download", "draftTemplates": "Draft templates", "dragAndMove": "Drag and move this card", "dragToEdit": "Drag & Drop to edit", "dropElementHere": "Drop Element Here", "dropFileHere": " or drop your file here", "dropRowHere": "Drop row here", "dropdown": "Dropdown", "duplicate": "Duplicate", "duplicateTemplate": "Duplicate template", "durationOfTheVisit": "Length of the visit", "dynamicContent": "Dynamic Content", "edit": "Edit", "editButton": "Edit button", "editCampaign": "Edit campaign", "editCampaignName": "Edit campaign name", "editCheckbox": "Edit checkbox", "editCloseButton": "Edit close button", "editCol": "Edit {column}", "editControl": "Edit control", "editCountdown": "Edit countdown", "editCountry": "Edit dropdown", "editCoupon": "Edit coupon", "editCoupons": "Upload & edit coupons", "editCustomHTML": "Edit HTML Element", "editDivider": "Edit divider", "editDropdown": "Edit dropdown", "editField": "Edit field", "editFloatingImage": "Edit floating image", "editGeneralSettings": "Edit general settings", "editGlobalButtons": "Edit global buttons", "editGlobalControls": "Edit global controls", "editGlobalInputs": "Edit global inputs", "editGlobalTexts": "Edit global texts", "editHTML": "Edit HTML", "editImage": "Edit image", "editInput": "Edit input", "editInputField": "Edit input", "editMessenger": "Edit Messenger", "editMode": "Edit mode", "editOverlay": "Edit overlay", "editPage": "Edit frame", "editRadio": "Edit radio", "editRow": "Edit block", "editRules": "Edit settings", "editSchedule": "Edit schedule", "editSettings": "Edit settings", "editSocial": "Edit social", "editSpacer": "Edit spacer", "editStyles": "Edit styles", "editSurvey": "Edit survey", "editTab": "Edit teaser", "editTeaserCloseButton": "Edit teaser close button", "editTemplate": "Edit template", "editText": "Edit text", "editTextarea": "Edit textarea", "editThemeColor": "Edit Theme colors", "editVideo": "Edit video", "editingOnlyInDesktopMode": "Editing is only allowed in Desktop mode", "editor": {"addElement": "Add elements", "customTheme": "Theme", "devMode": "Code", "layers": "Elements", "preview": "Preview", "themeAndDisplay": "Display"}, "editorBeta": {"components": {"Button": {"title": "<PERSON><PERSON>"}, "CloseButton": {"title": "Close button"}, "Col": {"title": "Column"}, "Countdown": {"title": "Countdown"}, "Coupon": {"title": "Discount"}, "CustomHTML": {"title": "Custom HTML"}, "Divider": {"title": "Divider"}, "Dropdown": {"title": "Dropdown"}, "Feedback": {"title": "<PERSON><PERSON><PERSON>"}, "FloatingImage": {"title": "Floating image"}, "Image": {"title": "Image"}, "Input": {"title": "Input"}, "InputPicker": {"OmCheckbox": {"title": "Checkbox"}, "OmRadio": {"title": "Radio"}}, "LuckyWheel": {"title": "Lucky wheel"}, "Messenger": {"title": "<PERSON>"}, "Overlay": {"title": "Overlay"}, "PickAPresent": {"title": "Pick a present"}, "Product": {"title": "Product card"}, "Row": {"title": "Block"}, "ScratchCard": {"title": "Scratch card"}, "Social": {"title": "Social"}, "Spacer": {"title": "Spacer"}, "Survey": {"title": "Survey"}, "Teaser": {"title": "Teaser"}, "TeaserCloseButton": {"title": "Teaser close button"}, "Text": {"title": "Text"}, "Textarea": {"title": "Textarea"}, "Video": {"title": "Video"}}, "help": {"errorMessageInput": "The visitor will see this message if the field is filled in incorrectly", "onlyNewLead": "The visistor will see this if they are already subscribed"}, "manageFonts": "Manage fonts", "overlayAnimation": "Overlay animation"}, "editorHeader": {"displayRules": "Display rules"}, "editorView": "View", "elementId": "Element ID", "elementIdTooltip": "", "elements": "Elements", "email": "Email", "emailAddress": "Business email address", "emailFallback": {"button": {"label": "Button text", "text": "Subscribe"}, "floatingLabel": "Messenger log in", "placeholder": {"label": "Placeholder text", "text": "Email address"}, "preview": {"label": "Preview", "options": {"email": "Logged out of <PERSON>", "messenger": "Logged in to <PERSON>"}}, "switch": {"label": "Smart display", "tooltip": "Subscribe to email if the user is not logged in to Messsenger"}, "title": "E-mail settings"}, "emailInputValidations": "Allow / Block", "emailMismatch": "Emails doesn't match.", "emailPlaceholder": "<EMAIL>", "embedCode": {"button": "Get embed code", "code": "<div class=\"om-embedded-campaign\" data-campaign-id=\"{campaign}\"></div>", "desc": "Add this HTML to your site where you want your form to appear.", "title": "Embed code"}, "embedded": "Embedded", "embeddedManually": "Embedded manually", "emptyTags": "No tags added yet", "enableAppEmbed": {"activate": "Activate", "confirmation": {"description": "Please enable OptiMonk App embed in your Shopify theme and save it, if you don't enable it, your campaigns won't appear. Once you activate the Shopify app embed mode, you can no longer inactivate it.", "title": "OptiMonk App Embed"}, "disabled": "The OptiMonk App Embed is disabled on your Shopify store", "enableLater": "Enable later", "enableScript": " Once there, enable the OptiMonk App Embeds for OptiMonk Onsite Script.", "goToSettings": "Navigate to your {0}.", "goToSettings_link": "Theme Settings in Shopify", "goToShopify": "Go to Shopify", "next": "Next", "plsEnable": "Please enable the OptiMonk App Embed", "saveAndClose": "Once enabled, press “Save” and close the tab and return to this page.", "status": {"danger": "The OptiMonk App Embed is disabled on your Shopify store", "info": "Verifying that OptiMonk App Embed is enabled and saved in Shopify", "noShop": "We was unable to find a shop associated with your account, please ask our support team.", "success": "The OptiMonk App Embed is enabled on your Shopify store"}, "sure": "Yes, I'm sure", "tryAgain": "Try again", "warning": {"description": "Are you sure you don't enable the OptiMonk App Embed in your Shopify store? If you continue, you will not be able to start your campaign.", "title": "OptiMonk App Embed"}}, "enabled": "enabled", "endDate": "End date", "endless": "Endless", "endsWith": "ends with", "english": "English", "equals": "equals", "error": {"field": {"email": "Invalid email address!", "euVatRequired": "VAT number is required!", "minLength": "Input length is too short!", "required": "This field is required!", "url": "Invalid url, valid example: https://myshop.myshopify.com"}, "oldLastRequestDate": "There was no recent traffic measured on your site. If OptiMonk is not integrated properly, the editor might not be displayed."}, "errorMessage": "Error message", "errorTermsAndConditions": "Please accept the terms to proceed!", "errors": {"passwordReset": {"missingEmail": "Missing email address", "tokenAlreadyRequested": "Token already requested"}, "rateLimited": "Too many attempts. Try again %{time}."}, "euVatNumber": "VAT number", "eventLead": {"click": "<span class=\"text-primary font-weight-bold\">{selector}</span> element on <span class=\"text-primary font-weight-bold\">{device}</span>", "exitIntent": "{p}is about to leave your site", "inactivity": "{p}is inactive for <span class=\"text-primary font-weight-bold\">{delay} seconds</span>", "javascriptEvent": {"listing": "on <span class=\"text-primary font-weight-bold\">{eventName}</span> event activation <span class=\"text-primary font-weight-bold\">{device}</span>", "prefix": "When one of the following JavaScript events have occurred"}, "prefix": "When a visitor ", "scrollDown": "{p}scrolls down <span class=\"text-primary font-weight-bold\">{percent}%</span>", "timed": "{p}has been on the page for at least <span class=\"text-primary font-weight-bold\">{delay} seconds</span>"}, "events": {"click": {"desc": "Use the following rule for the popup to be displayed when a visitor clicks on a particular part of your page — a box, link, banner, etc. At this point the popup behaves like a \"mini landing page\". You can enter any type of value of any HTML element, CSS classes and IDs, and their combination.\n        <div>IMPORTANT NOTE: The “Frequency” rule does not apply to this trigger. A click will always trigger your campaign if the other rules are met.</div>\n        <div>For example, the event can be triggered when a visitor clicks on the following elements:</div>\n        - a (any link element)<br>\n        - div.container > a (any link that is a child of the DIV element that has the CSS class container)<br>\n        - table > a.delete (any link that has a delete CSS class and is a child of a table element)<br>\n        - #container (an element which has a container ID)<br>\n        - #container > * (any element, which is a child of an element possessing container ID)<br>\n        <br>\n        IMPORTANT NOTE: When a visitor clicks a child element, the event will not be triggered for the parent element. For example, the event will not be triggered for a #container value when we click on one of its links or any other element.", "descWL": "Use the following rule for the popup to be displayed when a visitor clicks on a particular part of your page — a box, link, banner, etc. At this point the popup behaves like a \"mini landing page\". You can enter any type of value of any HTML element, CSS classes and IDs, and their combination. For example, the event can be triggered when a visitor clicks on the following elements:<br>\n        - a (any link element)<br>\n        - div.container > a (any link that is a child of the DIV element that has the CSS class container)<br>\n        - table > a.delete (any link that has a delete CSS class and is a child of a table element)<br>\n        - #container (an element which has a container ID)<br>\n        - #container > * (any element, which is a child of an element possessing container ID)<br>\n        <br>\n        IMPORTANT NOTE: When a visitor clicks a child element, the event will not be triggered for the parent element. For example, the event will not be triggered for a #container value when we click on one of its links or any other element.", "lead": "When a visitor clicks on a specific part of your page.", "title": "On click to a specific area"}, "exitIntent": {"desc": "Use this event when you want to display the popup to visitors who are about to leave your site. It grabs the attention of visitors who are about to leave your site without converting, on <a href=\"https://support.optimonk.com/hc/en-us/articles/360011129080-Exit-intent-trigger-on-mobile-devices\" target=\"_blank\">mobile devices</a> and PC as well.", "lead": "When a visitor is about to leave your site.", "title": "On exit-intent"}, "inactivity": {"desc": "Use this event when you want to show the popup to visitors who have been inactive on your page for at least X seconds. This catches the attention of inactive visitors. When you set this to 0 seconds, it will function as an \"entry popup” – it will appear immediately when a visitor lands on your page.", "lead": "When a user has stopped all activity (clicking, scrolling).", "title": "After x seconds of inactivity"}, "javascriptEvent": {"desc": "Use this rule when you want to show the popup after a visitor has completed any predefined action that is not handled by standard methods. You can enter any event that is used to track interactions that occur on your website. For example, your campaign can be displayed when a registered visitor logins to his/her account and the 'user_login' event is occurred.", "lead": "When a specific JavaScript event has occurred.", "title": "After a JavaScript event"}, "scrollDown": {"desc": "Use this event when you want to show the popup after a visitor has scrolled down on your page at least X percent. This catches the attention of active visitors.", "lead": "After a visitor has scrolled X% down on the current page.", "title": "After x percent scrolling"}, "timed": {"desc": "Use this event when you want to show the popup to visitors who have been reading your page for at least X seconds. When you set this to 0 seconds, it will function as an \"entry popup\" – it will appear immediately when a visitor lands on your page.", "lead": "When a visitor has been on the page for at least X seconds.", "title": "After x seconds"}}, "experiences": {"addVariantToExperienceModal": {"header": "Which previous variant do you want to duplicate?"}, "deleteExperienceModal": {"cancel": "Cancel", "continue": "Continue", "description": "Are you sure you want to delete {experienceName}? <br/> If you delete it, all variants and rules will be lost.", "dontShowAgain": "Dont show this message again", "title": "Delete {experienceName}"}, "frontendRuleModal": {"activeRulesTitle": "Personalize existing campaign targeting rules for this experience", "personalize": "Personalize", "personalizedRuleTitle": "Personalize {experienceName} based on {ruleName} rule", "subtitle": "Add an extra rule to this experience", "title": "Who should see {experienceName}"}, "list": {"addNewRule": "Add new rule", "confidence": "Confidence", "conversions": "Conversions", "conversionsRate": "Conversions rate", "frontendRulesCount": "{frontendRulesCount} extra targeting rule", "impressions": "Impressions", "personalizeFor": "Personalize for", "variantCount": "{variantCount} Variant{postfix}"}, "modal": {"description": "Personalized Experiences allows you to create unique experiences for sub-groups of your campaign's target audience. <a href=\"{learnMoreLink}\" target=\"_blank\">Learn more</a>", "header": "How it works", "leftSide": {"afterDescription": "Different messages for sub-groups", "afterTitle": "<span class=\"orange-500\">After</span> personalization", "beforeDescription": "The same message for every visitor", "beforeTitle": "<span class=\"orange-500\">Before</span> personalization"}, "skip": "<PERSON><PERSON>", "startTour": "Start tour", "videoDescription": "Watch the video or start a 5-step tour so we can quickly show you around."}, "module": {"addNew": "Add new experience", "howItWorks": "How it works", "title": "Personalized experiences"}, "renameExperience": {"btn": "Apply", "placeholder": "Type name here", "title": "Rename experience"}, "ruleIsPartOfExperienceWarning": {"description": "If you continue, we will remove the <b>{ruleName}</b> rule from the campaign experiences.", "dontShowAgain": "Dont show this message again", "title": "The rule is part of an experience"}}, "experimental": {"campaignsAbTest": {"activeTestsHeading": "Active tests", "deleteModal": {"subTitle": "Are you sure you wan to delete {testCase} test case?", "title": "Delete test case"}, "errors": {"alreadyAdded": "This case is already added", "emptyName": "This field is required", "invalidRatio": "The ratio can't bigger then 100% and it must be bigger then 1%", "invalidValue": "min: 2 - max: 5", "jsSyntaxError": "Found SyntaxE<PERSON>r in JS snippet"}, "heading": "Campaign A/B test(s)", "newTestCase": {"editHeading": "Modify test case", "heading": "Add new test", "jsSnippetLabel": "JS snippet", "label": "New test case name", "placeholder": "Summer 2021"}, "noActiveTests": "No active tests running at the moment.", "numberOfSplit": {"label": "Number of splits", "placeholder": "Min: 2 - Max: 5"}, "splitRatio": "Split ratio "}, "flashingBrowserTab": {"cookieTargeting": "<PERSON><PERSON> targeting", "cookieTargetingAltTitle": "Browsers with this cookie will see the flashing tab notification. Use it for A/B testing or targeting reasons", "heading": "Browser tab notification", "inputAltTitle": "Leave blank to use your original page title", "inputLabel": "Browser tab text", "toggle": "Use browser tab notification", "useCookieTargeting": "Use cookie targeting"}, "globalFrequencyCap": {"errors": {"inTime1-24": "Az idő legalább 1 és legfeljebb 24 lehet", "inTime1-59": "Az idő legalább 1 és legfeljebb 59 lehet", "invalidExceptions": "Invalid format", "noUnit": "<PERSON> kell adni az időegységet, perc vagy <PERSON>", "wrongCount": "A kampányok számának legalább 1-nek kell lennie"}, "heading": "Global frequency cap", "texts": {"between": "campaign(s) will appear in", "exceptCampaigns": "except these campaigns (campaign ids separated by comma)", "post": "", "pre": "Maximum"}}, "goals": {"heading": "Goals"}, "heading": "Labs", "menu": "Labs", "domainContext": {"heading": "Domain context", "domainSelectLabel": "Domain", "domainSelectPlaceholder": "Select a domain"}, "smartPersonalization": {"heading": "Smart personalization", "addParameter": "Add personalization parameter", "setupPersonalization": "Setup smart personalization", "columns": {"parameter": "Personalization parameter", "context": "Personalization context", "actions": "Actions"}, "deleteConfirmModal": {"title": "Are you sure?", "content": "Are you sure you want to remove the parameter <strong>{parameter}</strong>?"}, "managerModal": {"title": "Manage personalization parameter", "parameter": "Personalization parameter", "advancedSettings": "Advanced settings", "prompt": "Prompt", "value": "Value", "context": "Personalization context", "newValue": "Add new value", "autoPersonalization": "Auto personalization"}}}, "splitUrlTestConfirm": {"description": "Modifying a running A/B test may affect its results. Are you sure you want to proceed?", "confirm": "Yes"}, "splitUrlTestEdit": {"setupADomain": "Setup a Domain", "urlVariants": "URL Variants", "excludeURLs": "Exclude URLs ", "control": "Control", "controlInfo": "The original URL you want to test:", "trafficShare": "Traffic share", "urlMatches": "The URL matches:", "modal": {"addJsSnippet": "Add JS Snippet"}, "alternativeUrlInfo": "The alternative URL you want to redirect part of the traffic to:", "delete": "Delete", "newVariant": "Add new variant", "exclude": "Exclude", "excludeInfo": "Enter the URL of the web pages you want to exclude from the test.", "whereUrl": "Where the URL", "cancel": "Cancel", "save": "Save"}, "splitUrlTestDetails": {"edit": "Edit", "urlVariants": "URL Variants", "control": "Control", "trafficShare": "Traffic share", "exclude": "Exclude", "start": "Start", "draft": "Draft", "duration": "Duration", "started": "Started", "finished": "Finished", "end": "End", "running": "Running", "excludeURLs": "Exclude URLs", "variant": "<PERSON><PERSON><PERSON>", "url": "URL", "visitors": "Visitors", "pageViews": "Pageviews", "conversions": "Conversion", "conversionRate": "Conversion Rate", "totalOrder": "Total Order", "totalRevenue": "Total Revenue", "deleteSplitTest": {"title": "Delete Test", "confirmation": "Are you sure you want to delete this Split URL A/B test?", "cancel": "Cancel", "confirm": "Yes, I'm sure"}}, "trafficShare": {"cancel": "Cancel", "done": "Done", "evenlySplit": "Evenly split", "evenlySplitDescription": "Equally distribute weight percentage across all groups", "group": "Group", "sum": "Sum", "title": "Traffic share", "weight": "Weight", "error": {"sum": "The sum of all traffic shares must be 100%"}}, "experiments": {"activeOnly": "Active only", "addCampaign": "Add campaign", "addNewGroup": "Add new group", "campaignPartOfExperiment": "The campaign is part of the following multi-campaign A/B test: {experimentName}", "campaignVariantsInDraftExperiment": "The campaign is part of the <a style=\"font-weight: 700\" href=\"{experimentLink}\">{experimentName}</a> draft multi-campaign A/B test. To manage the status remove the campaign from it.", "campaignVariantsInRunningExperiment": "The campaign is part of the <a style=\"font-weight: 700\" href=\"{experimentLink}\">{experimentName}</a> running multi-campaign A/B test. To manage the status end the A/B test.", "days": "Day | Days", "deleteGroup": "Delete group", "description": {"subhead1": "Multi-Campaign A/B Test", "subhead2": "Split URL test", "text1": "Test campaign groups against each other, compare campaigns against no campaign at all, or evaluate campaigns with different targeting settings.", "text2": "Evaluate the performance of two distinct webpage versions to determine which design, layout, or content structure to see which one performs better.", "title": "Test Campaigns & Webpages with A/B Testing"}, "testTypes": {"multiCampaign": {"title": "Multi-campaign A/B test", "description": "Test campaign groups against each other, compare campaigns against no campaign at all, or evaluate campaigns with different targeting settings."}, "splitUrl": {"title": "Split URL A/B test", "description": "Evaluate the performance of two distinct webpage versions to determine which design, layout, or content structure performs better."}}, "type": {"SplitUrl": "Split URL", "Experiment": "Multi-campaign"}, "device": "<PERSON><PERSON>", "draft": "Draft", "duration": "Duration", "editTrafficShare": "Edit traffic share", "end": "End", "finished": "Finished", "loadingError": "Some error occurred while loading experiments", "modal": {"abtest": {"chooseTestType": "What kind of A/B test do you want to create?"}, "addCampaign": {"activeDescription": "The <b>{campaignName}</b> campaign will be inactivated.<br/> When you start the A/B test, the campaign will automatically be activated.", "cancel": "Cancel", "ok": "Ok", "scheduledDescription": "The <b>{campaignName}</b> campaign will not appear until you start the A/B test", "title": "Add campaign"}, "addJsSnippet": "Add JS snippet", "archive": {"cancel": "Cancel", "description": "The campaign is part of an experiment.<br> Are you sure you want to archive it?<br> Archiving the campaign can affect the result of the experiment. | Some of these campaigns are part of an experiment.<br> Are you sure you want to archive them?<br> Archiving the campaigns can affect the result of the experiment.", "ok": "Yes, I'm sure", "title": "Archive campaign"}, "delete": {"cancel": "Cancel", "description": "The campaign is part of an experiment.<br> Are you sure you want to delete it?<br> Deleting the campaign can affect the result of the experiment. | Some of these campaigns are part of an experiment.<br> Are you sure you want to delete them?<br> Deleting the campaigns can affect the result of the experiment.", "ok": "Yes, I'm sure", "title": "Delete campaign"}, "deleteExperiment": {"cancelBtn": "Cancel", "description": "Are you sure you want to delete the experiment?<br/> Campaigns will be inactivated and you can activate them manually.<br/>", "title": "Delete experiment", "yesBtn": "Yes, I'm sure"}, "deleteSplitUrl": {"cancelBtn": "Cancel", "description": "Are you sure you want to delete this Split URL A/B test?", "title": "Delete test", "yesBtn": "Yes, I’m sure"}, "end": {"cancelBtn": "Cancel", "description": "Are you sure you want to end the experiment?<br/> Campaigns will be inactivated and you can activate them manually.<br/> The experiment cannot be restarted.", "title": "End experiment", "yesBtn": "Yes, I'm sure"}, "newExperiment": {"chooseDomain": "On which domain do you want to run the A/B test?"}, "trafficShare": {"cancel": "Cancel", "done": "Done", "evenlySplit": "Evenly split", "evenlySplitDescription": "Equally distribute weight percentage across all groups", "group": "Group", "sum": "Sum", "title": "Traffic share", "weight": "Weight"}}, "newExperiment": "New A/B test", "readMore": "Read more", "running": "Running", "searchCampaigns": "Search campaigns", "selectCampaigns": "Select campaigns from {domain}", "start": "Start", "started": "Started", "title": "A/B test center", "trafficShare": "Traffic share", "visitorGroups": "Visitor groups", "watchVideo": "Watch a video"}, "expired": "Expired", "expiredEmailVerification": {"paragraph": "If you would like to change the email address on your account, please go to <a class=\"change-email-link\" href=\"/login?redirect=change_email\"> Change email<a/> settings.", "title": "Expired verification link"}, "expiryDate": "Expiry date", "export": "Export", "extend": "Extend", "extra": "extra", "fade": "Fade", "fadeHorizontal": "Fade horizontal", "fadeIn": "Fade In", "fadeVertical": "Fade vertical", "fall": "Vibrate", "fallbackToDesktop": "These values are inherited from the desktop values.", "campaignConversion": {"title": "Count as campaign conversion", "tooltip": "If this switch is on, we record the action as a conversion in our system."}, "featureDetails": {"aBlock": {"description": "Target your visitors depending on whether they are using Ad-blocker or not.", "title": "Adblock-based targeting"}, "abTesting": {"description": "Create unlimited versions of a campaign to test which changes result in better conversions.", "title": "Test multiple design variations"}, "basicFeatures": {"description": "Exit-intent on desktop and mobile, Timed-display control, Scroll-based triggering, OnClick triggering, Monitoring inactivity.", "description2": "Target your visitors based on various variables related to their background & history, visited pages while timing your message perfectly.", "description3": "All types of messages, Drag&Drop editor, All templates, Dynamic Text (Element) Replacement, Built-in analytics.", "title": "Basic triggering options", "title2": "Basic targeting options", "title3": "Design & Customization"}, "cartRules": {"description": "Reduce your cart abandonment rate more effectively and sell more by targeting your visitors based on cart content or cart value.", "title": "Cart-based targeting"}, "cookie": {"description": "Targeting cookie-specific segments of your audience helps you to differentiate the customers based on tailored website experience.", "title": "Segment based on any cookie"}, "customVariable": {"description": "Segment your audience based on any variable you want (e.g. gender, age, etc. - it depends on your unique needs).", "title": "Customize based on any criteria"}, "ipBlock": {"description": "Exclude visitors with certain IP addresses from your OptiMonk campaigns.", "title": "Block IP addresses"}, "jsEvent": {"description": "Display your message when a visitor has completed any predefined action that is not handled by standard methods.", "title": "Triggering after a JavaScript event"}, "linkOptiMonkCampaigns": {"description": "Show unique offer to visitors who have (or haven't) seen or filled in specific OptiMonk campaigns.", "title": "Turn your campaigns into a sales funnel"}, "pageViews": {"description": "The total number of pages viewed on your site per month."}, "prioritySupport": {"description": "Priority support means that we provide faster ticket resolution through priority handling.", "title": "Get the highest level of support that OptiMonk offers."}, "sites": {"description": "The total number of websites where OptiMonk can be used."}, "unbranded": {"description": "If you don't want your visitors to know that you use OptiMonk, make your campaigns unbranded.", "title": "Remove the 'Made with ♥️ by OptiMonk' link"}}, "featureForFeedback": {"devMode": {"content": "Please, request access to the custom CSS and JS editor via chat and we’ll enable this feature for you.", "error": "Unfortunately, we can’t sent message. Please, try again later.", "heading": "Enable custom CSS and JS editor", "sendMessage": "Send message"}, "poweredBy": {"content": "Please, confirm your request via chat quickly and we’ll remove the branding for free.", "error": "Unfortunately, we can’t sent message. Please, try again later.", "heading": "Remove OptiMonk branding", "sendMessage": "Send message"}}, "feedback": "<PERSON><PERSON><PERSON>", "feedbackBar": {"reviewUs": "Write a quick review", "thankYou": {"shopifyLead": "It’d make us really happy if you left a 30-second review of OptiMonk on Shopify", "title": "Thanks for your feedback"}, "title": "How would you like to rate your experience with OptiMonk?"}, "feedbackModal": {"appreciate": "we really appreciate your thoughts.", "improve": {"lead": "Please help us improve our product by leaving your feedback below.", "title": "Help us improve OptiMonk"}, "placeholder": "Describe your experience", "review": {"lead": "Big fat thank you for your feedback in advance 🙂", "title": "Review"}, "reviewPosted": "Your review will be posted to our Customer Service Team", "send": "Send my feedback", "thanks": "Thanks,", "validation": "Please, write a valid review for us!"}, "feedbackOptions": {"1": "1 stars", "2": "2 stars", "3": "3 stars", "4": "4 stars", "5": "5 stars", "bad": "Bad", "good": "Good", "neutral": "Neutral", "no": "No", "yes": "Yes"}, "feedbackPane": {"byRating": "By rating", "displayTypes": {"smiley": "<PERSON><PERSON>", "stars": "5 stars", "yesno": "Yes-No"}, "editFeedback": "Edit feedback", "iconType": "Icons", "iconTypes": {"image": "Image", "vector": "Default icons"}, "jumpTo": "Jump to", "question": "Question", "specificPage": "Specific page"}, "feedbackStatistics": {"answers": "answer(s)", "selectVariant": "Variants", "smiley": {"bad": "Bad", "good": "Good", "neutral": "Neutral"}, "stars": {"1": "1 star", "2": "2 stars", "3": "3 stars", "4": "4 stars", "5": "5 stars"}, "title": "Feedbacks", "yesno": {"no": "No", "yes": "Yes"}}, "fieldIdentifier": "Field identifier in {name}", "fieldMustBeInFuture": "The date must be in the future", "fieldMustBePositive": "The value of this field must be greater than 0", "fieldNotPercentage": "The value of this field must be between 0 and 100", "fieldRequired": "This field is required", "fieldSettings": "Field settings", "fieldUsed": "This field is used in %{count} campaigns. To delete this field please remove it from every campaign first.", "fields": "Fields", "file": "File", "fileDimensionToLargeMsg": "File dimensions are larger than allowed ({height}px x {width}px)", "fileSizeToLargeMsg": "File size is larger than allowed ({size}kb)", "fill": "Fill", "filterBy": "Filter by", "filterOne": "The value of", "finish": "Finish", "fireworks": "Fireworks", "firstName": "First name", "firstNameTooLong": "First name must be maximum {length} characters long.", "fit": "Fit", "fitToContent": "Fit to content", "fitToScreen": "Fit to screen", "fix": "Fix", "fixedDate": "Fixed date", "fixedTime": "Fixed time", "flash": "Flash", "flexiPay": "FlexiPay", "flipHorizontal": "3D Flip horizontal", "flipVertical": "3D Flip vertical", "floatingimage": "Floating image", "fluid": "Fluid", "follow": "Follow", "followup": "Follow-up", "followupCampaign": {"addAFollowupCampaign": "Add a reminder campaign", "addFollowup": "Add a reminder", "addFollowupCampaign": "Add discount reminder campaign", "explanationText": "With a discount reminder campaign, you can remind your users to redeem their discount code.<br><br>The faster your users redeem their discount codes the higher the chance that they purchase in your store.<br><br>Read more about this in our <a class=\"cursor-pointer\" href=\"{link}\" target=\"_blank\">User Guides.</a>", "learnMore": "Learn more", "promoTitle": "Discount code reminder", "tooltip": "Add a reminder campaign to increase your discount campaign efficiency."}, "font": "Font", "fontColor": "Font color", "fontEdit": "Font", "fontFamily": "<PERSON>ont family", "fontLicense": "Please make sure you have the appropriate license to use this font on the web.", "fontManager": {"availableTitle": "Available fonts", "customTitle": "Custom fonts", "example": {"text": "Quick brown fox jumps over the lazy dog", "title": "Example"}, "installedTitle": "Installed fonts", "notifications": {"install": {"fail": "Font '{family}' cannot be installed right now", "success": "Font '{family}' installed"}, "modified": {"fail": "Font '{family}' cannot be saved right now", "success": "Font '{family}' saved"}, "noSubset": "Please choose at least one character subset", "uninstall": {"fail": "Font '{family}' cannot be deleted right now", "success": "Font '{family}' deleted"}}, "preInstalledTitle": "System fonts", "search": {"placeholder": {"font": "All fonts", "subsets": "All languages"}}, "subsets": {"cyrillic": "Cyrillic", "cyrillic-ext": "Cyrillic extended", "greek": "Greek", "greek-ext": "Greek extended", "latin": "Latin", "latin-ext": "Latin extended", "vietnamese": "Vietnamese", "vitnamese-ext": "Vietnamese extended"}, "subsetsText": "Languages", "title": "Font manager", "variantsText": "Sizes"}, "fontName": "Font name", "fontSize": "Font size", "forAgencies": "Ambassador program", "frame": "<PERSON>ame", "frday": "day(s)", "freemiumPlanDescription": "Unlimited campaigns<br/>\n{noOfPageViews} pageviews / month<br/>\nMax {maxSites} site", "frequency": "Frequency", "frequency1": "The popup will appear a maximum number of", "frequency2": "times per visitor", "frequency21": ", with at least", "frequency3": "between two appearances.", "frhour": "hour(s)", "frminute": "minute(s)", "from": "From", "frontendRuleGroups": {"pages": "Pages", "pagesAndCustom": "Pages & Custom Rules", "pro": "Pro", "time": "Time", "timeAndContext": "Time & Context", "visitors": "Visitors"}, "frontendRuleLead": {"aBlock": "{p}to those who <span class=\"text-primary font-weight-bold\">{value}</span> AdBlock, AdBlock Plus, etc.", "campaignProgressState": {"filledForm": "If the visitor has <span class=\"text-primary font-weight-bold\">{state}</span> in a campaign, where the value of <span class=\"text-primary font-weight-bold\">{field}</span> field <span class=\"text-primary font-weight-bold\">{operator}</span> <span class=\"text-primary font-weight-bold\">{attributeValue}</span>.", "text": "<span class=\"text-primary font-weight-bold\">{campaign}</span> campaign <span class=\"text-primary font-weight-bold\">{state}</span> by visitors.", "turbo": "If the visitor has <span class=\"text-primary font-weight-bold\">{state}</span> <span class=\"text-primary font-weight-bold\">{campaign}</span> <span class=\"text-primary font-weight-bold\">{messageType}</span> <span class=\"text-primary font-weight-bold\">{visitType}</span>."}, "cookie": "cookie <span class=\"text-primary font-weight-bold\">{name}</span> {operator} <div class=\"card-text-truncate\">{value}</div>", "country": " for visitors from ", "countryPrefix": "Your campaign will", "ipBlock": "<span class=\"text-primary font-weight-bold\">{ips}</span>", "klaviyoSegmentDisplay": "<span class=\"text-primary font-weight-bold\">Display </span>to visitor in any of these segments and lists: <span class=\"text-primary font-weight-bold\">{items}</span>", "klaviyoSegmentDontDisplay": "<span class=\"text-primary font-weight-bold\">Don't display</span> to visitors in any of the following segments and lists: <span class=\"text-primary font-weight-bold\">{items}</span>", "klaviyoSegmentExistingProfiles": "<span class=\"text-primary font-weight-bold\">Don’t display</span> to existing Klaviyo profiles", "loggedIn": "{p}to those who <span class=\"text-primary font-weight-bold\">{value}</span>", "maximumPopupDisplay": "{p}a maximum number of <span class=\"text-primary font-weight-bold\">{value}</span> times per visitor", "maximumPopupDisplay1": ", with at least <span class=\"text-primary font-weight-bold\">{time} {unit}</span> between two appearances", "minimumPageVisit": "{p}when a visitor has opened <span class=\"text-primary font-weight-bold\">{operator} {value}</span> pages", "more": " more", "negation": " not appear", "notViewedPageRecent": "URL of the exluded site <span class=\"text-primary font-weight-bold\">{operator}</span>: {value} - {secondsToBlock}", "notViewedPageRecentDefaultRuleset": {"noBlockingTime": "<span class=\"text-primary font-weight-bold\">forever</span>", "text": "URL of the excluded site <span class=\"text-primary font-weight-bold\">{operator}</span>: {value}. Blocking time: ", "withBlockingTime": "<span class=\"text-primary font-weight-bold\">{secondsToBlock}</span> seconds"}, "onAllCategoryPages": "on all <span class=\"text-primary font-weight-bold\">{category}</span> pages", "onAllProductPages": "on all <span class=\"text-primary font-weight-bold\">product</span> pages", "pageViewerType": "{p}to <span class=\"text-primary font-weight-bold\">{type}</span>", "prefix": "The popup will appear ", "previouslyViewedPage": "URL of one of the previously visited pages <span class=\"text-primary font-weight-bold\">{operator}</span>: {value}", "source": "{p}to those who are coming from one of the following sources:", "sourceCustom": "URL of the site <span class=\"text-primary font-weight-bold\">{operator}</span>: {value}", "subscribers": {"orTo": " or to ", "text": "<span class=\"text-primary font-weight-bold\">{type}</span> if the visitor has already subscribed to {lists}"}, "the": "the ", "timeBasedActualPage": "{p}to visitors who spent a minimum of <span class=\"text-primary font-weight-bold\">{value}</span> seconds on the current subpage", "timeBasedSession": "{p}to visitors who spent at least <span class=\"text-primary font-weight-bold\">{value}</span> seconds on the site", "viewedCategoryPage": "on all {category} pages where <span class=\"text-primary font-weight-bold\">{operand} {operator} {value}</span>", "viewedPage": "on all pages where the URL <span class=\"text-primary font-weight-bold\">{operator}</span> {value}", "viewedProductPage": "on all product pages where {the}<span class=\"text-primary font-weight-bold\">{operand} {operator} {value}</span>", "viewedProductPageWithCategory": "on all product pages where the <span class=\"text-primary font-weight-bold\">{category}</span> of the product is in <span class=\"text-primary font-weight-bold\">{value}</span>", "viewedSpecificCategoryPage": "on all {category} pages where {category} is in <span class=\"text-primary font-weight-bold\">{value}</span>", "visitorAttribute": "<span class=\"text-primary font-weight-bold\">{name}</span> {operator} <span class=\"text-primary font-weight-bold\">{value}</span>", "visitorAttributeCustomAttribute": "custom variable, <span class=\"text-primary font-weight-bold\">{name}</span> {operator} <span class=\"text-primary font-weight-bold\">{value}</span>", "willAppear": " appear", "onHomepage": "on <span class=\"text-primary font-weight-bold\">homepage</span>"}, "frontendRuleWarning": "Narrow the target audience by adding some conditions to convert more visitors with a more relevant message.", "frontendRules": {"aBlock": {"desc": "Use this option if you want to target AdBlock users with a specific message. This way, you can let the ad blocker users know that it’s worth switching off the AdBlock for a better user experience. (As a result, all of your contents will be available for AdBlock users, too.)", "lead": "Visitors who use AdBlock will see your message", "title": "AdBlock detection"}, "campaignProgressState": {"desc": "Use this rule when you want to display the campaign based on any other campaign interaction like filling, displaying, or closing a campaign, or specific form information for example answering a survey.", "lead": "Visitors who have seen or filled in specific {brand} campaigns will see your message", "supportArticle": "https://support.optimonk.com/hc/en-us/articles/360018530880-Engaged-with-OptiMonk-Campaigns-rule ", "title": "Link {brand} campaigns", "turbo": {"any": "any", "currentCampaign": "this", "deleted": "(deleted)", "deletedCampaign": "{campaign} [Deleted Campaign]", "desc": "Use the following option to display a campaign depending on other campaigns. For example, once a popup is filled in, you can display the offer in a progress bar too.", "lead": "Visitors who have seen or filled in specific {brand} campaigns will see your message", "messageType": {"campaign": "campaign", "embedded": "embedded", "nanobar": "sticky bar", "popup": "popup", "sidebar": "sidemessage"}, "operators": {"contains": "contains", "equals": "equals", "notContains": "doesn't contain", "notEquals": "doesn't equal"}, "selectInput": "Select input", "states": {"closed": "closed", "filled": "converted in", "filledForm": "filled a form", "notClosed": "not closed", "notFilled": "not converted in", "notShowed": "not seen", "showed": "seen"}, "title": "Engaged with {brand} campaigns", "visitType": {"ever": "ever before", "visit": "in this session"}}, "video": ""}, "cookie": {"desc": "Use the following option to filter your visitors based on their cookies.", "lead": "Segment your audience based on any cookie you want", "title": "Cookie segmentation"}, "country": {"add": "Add country", "appear": "appear", "desc": "With this condition, you can exclusively show or hide your campaigns for visitors who are coming from a specified country. If you want to show your campaign to every visitor just remove all the countries from the selection.", "lead": "You can select from which specific countries your users will see your message", "noResult": "No elements found. Consider changing the search query", "notAppear": "not appear", "title": "Country"}, "ipBlock": {"desc": "Use this option if you want to exclude visitors with a specific IP address or range.", "lead": "Visitors with the following IP addresses will not see your message", "title": "Block IP addresses"}, "klaviyoSegment": {"addPrivateKey": "Add private API key", "addPublicKey": "Add public API key", "chooseAtLeastOneSegment": "Choose at least one list or segment", "connect": "Connect", "desc": "This rule will allow you to display your campaign based on <PERSON><PERSON><PERSON><PERSON>’s list and segment information. This targeting rule relies on <PERSON><PERSON><PERSON><PERSON>’s cookies. The system considers every visitor a new anonymous visitor if they have recently cleared their cookies, are using a new device to browse, or visit your site using an incognito/private browser window.", "displayLists": "Display to visitors in any of the following lists or segments", "displayProfiles": "Target visitors in a list or segment", "dontDisplayLists": "Don't display to visitors in any of the following lists or segments", "dontDisplayProfiles": "Don't display to existing Klaviyo profiles", "findApiKeys": "Here you can find your API keys", "integration": "Klaviyo integration", "invalidApikey": "The API key is invalid, please try a different key.", "lead": "Visitors who are (or not) in a specific Klaviyo list or segment will see your message", "privateKeyLabel": "Klaviyo API private API key", "publicKeyLabel": "Klaviyo API public API key", "select": "Select a segment", "selectAtLeastOneSegment": "Select at least one list or segment", "selectIntegration": "Select an integration", "title": "Klaviyo lists and segments", "tooltip": {"displayLists": "The campaign will appear to cookied visitors who are members of any of the lists or segments you select.", "dontDisplayLists": "The campaign will only appear to visitors who are not known to be members of any of the lists or segments you select, including both anonymous visitors and visitors who are cookied, but not known to be a member of any of the selected groups.", "dontDisplayProfiles": "The campaign will only appear to visitors who are unknown to <PERSON><PERSON><PERSON><PERSON>."}}, "loggedIn": {"desc": "Use this option to show the popup only to visitors who have logged in on your Shopify store.", "lead": "Visitors who have logged in to your Shopify store will see your message", "title": "Shopify logged in user"}, "maximumPopupDisplay": {"desc": "Use this field when you want to show the popup more than one time to a visitor.<br>\n        <br>\n        Important:<br>\n        - After a visitor completes the form in the popup, the popup won't be displayed to them again.<br>\n        - When a visitor closes the popup, it won't de displayed until the page is refreshed or opened again (loaded again).", "lead": "Set how many times your message can appear per visitor", "title": "Frequency"}, "minimumPageVisit": {"desc": "Use this field when you want to show the popup only to visitors who have some activity on your site.<br>\n        <br>\n        Example: When you put '4' in the field, the popup will appear when a visitor has opened 4 pages before they leave.", "lead": "Visitors who opened ‘X’ number of pages will see your message", "operators": {"atLeast": "minimum", "maximum": "maximum"}, "title": "Number of visited pages"}, "notViewedPageRecent": {"desc": "Use this field when you don't want to show the popup to visitors who have visited a specific part of your site, ie- visitors who have signed up somewhere or purchased from your store.<br>\n        <br>\n        Example: You don't want to show the popup to visitors who have purchased something, set the fields using these entries:<br>\n        - URL of the site: 'equals'<br>\n        - /index.php?route=checkout/success<br>\n        - Blocking time: 86400 (in seconds) (If you assign 0, the popup will never be shown again)<br>\n        <br>\n        Important: The URL given is the URL of a typical Thank You page after purchase, please see notes in the 'Show popup' section above, ie- do not include your domain name. The Blocking time is measured in seconds, when you assign '0', the popup will never be shown to these visitors.", "lead": "Exclude visitors who have already visited a specific page", "title": "Exclude page visitors"}, "pageViewerType": {"desc": "Use this field when you want your popup to display only for new – or, only for returning – visitors. (Important: Right after you insert the code every visitor will be detected as a new visitor on their first visit.)<br>\n        <br>\n        Example: If you choose 'new visitors' the popups will appear to visitors who have visited your site in the past 24 hours. A 'returning visitor' is a visitor who has visited your site more than 24 hours ago.", "lead": "Only new or only returning visitors will see your message", "title": "Returning / New"}, "previouslyViewedPage": {"desc": "Use this option to show the popup only to visitors who have visited one or more subpages on your site.<br>\n        <br>\n        For example: To display the popup only when a visitor has already visited your 'Packages' page AND your 'Contact' page OR has visited your 'Discounts' page, use the following settings:<br>\n        <br>\n        - URL of the site: equals /index.php?route=information/packages AND<br>\n        - URL of the site: equals /index.php?route=information/contact<br>\n        OR<br>\n        - URL of the site: equals /index.php?route=information/discounts<br>\n        <br>\n        Important: Do not include your domain name here, only the page or URL that follows your domain name. Let's say the full address you see in your browser is 'http://www.mydomain.com/index.php?route=information/contact', include only '/index.php?route=information/contact'. You can reference pages other than your 'Packages', 'Contact' and 'Discounts' pages, we use these as examples and you should confirm these URLs match your pages before using them.", "lead": "Visitors who have visited any specific pages of your site will see your message", "title": "Visited URL"}, "schedule": {"desc": "desc", "lead": "lead", "title": "Schedule"}, "source": {"desc": "Use this field when you want to show the popup to visitors coming from a given source.", "lead": "Visitors coming from a specific traffic source will see your message", "title": "Source"}, "subscribers": {"addList": "Add another list", "anyList": "any list", "defaultList": "Default list", "desc": "With this condition you can exclude visitors from an OptiMonk campaign who have already subscribed to one of your lists through another OptiMonk campaign. If you need to exclude all subscribers, we recommend to keep the default “any list” option. If you prefer to exclude visitors who subscribed to a specific list, please select the name of the list from the dropdown menu.\n        <br><br>\n        Alternatively, with this condition you can also target visitors who have previously subscribed through your OptiMonk campaigns. In this case you should select the “appear only” option first, then select the required lists.\n        ", "label": "if the visitor has already subscribed to", "lead": "Include or exclude visitors who have already subscribed through OptiMonk", "listFrom": "", "selectAList": "Select a list", "title": "Subscribers / Non-subscribers", "types": {"exclude": "not appear", "include": "appear only", "lead": {"exclude": "Doesn't appear", "include": "Appears only"}}, "yourCampaignWill": "Your campaign will"}, "timeBasedActualPage": {"desc": "Use this field to give visitors a certain amount of time to view the subpage before displaying the popup.", "lead": "Visitors who have spent at least ‘X’ seconds on the current subpage will see your message", "title": "Spent on pages"}, "timeBasedSession": {"desc": "Use this field to give visitors a certain period of time to browse your website before displaying the popup, irrespective of the number of subpages viewed.", "lead": "Visitors who have spent at least ‘X’ seconds on the entire website will see your message", "title": "Spent on site"}, "viewedCategoryProductFilter": {"lead": "Shows up on each category page displaying their respective products.", "title": "Current category page"}, "viewedPage": {"addWhere": "Add conditions", "category": "{category} pages", "desc": "Use this rule when you want to display the campaign only to those visitors who are browsing a specific page of your site.", "intro": "This campaign will appear", "lead": "Visitors who are (or not) currently browsing a specific page will see your message", "operands": {"categoryCreationDate": "{category} creation date", "categoryDescription": "{category} description", "categoryHandle": "{category} handle", "categoryName": "{category} name", "numberOfProductsInCategory": "number of products in {category}", "productAvailability": "product availability", "productCategory": "product {category}", "productDescription": "product description", "productName": "product name", "productPrice": "product price", "productTag": "product tag", "productType": "product type", "productVendor": "product vendor", "specificCategory": "specific {category}"}, "page": "pages", "homepage": "homepage", "product": "product pages", "supportArticle": "https://support.optimonk.com/hc/en-us/articles/360011254219-Current-page-URL-rule", "title": "Current page / URL", "video": "https://www.youtube.com/watch?v=h_u3hPWmyCU"}, "viewedPageV2": {"desc": "Use this rule when you want to display the campaign only to those visitors who are browsing a specific page of your site.", "include": "Include", "exclude": "Exclude", "introInclude": "Show campaign only", "introExclude": "Never show this campaign", "lead": "Visitors who are (or not) currently browsing a specific page will see your message", "title": "Current page / URL", "includeDesc": "Show campaign only on these pages.", "excludeDesc": "Don’t show this campaign on these pages.", "supportArticle": "https://support.optimonk.com/hc/en-us/articles/360011254219-Current-page-URL-rule", "video": "https://www.youtube.com/watch?v=h_u3hPWmyCU", "deleteRule": "Delete rule", "addPages": "Add pages", "filter": "Filter", "whereTheUrl": "where the URL"}, "visitorAttribute": {"desc": "Segment your audience based on multiple first-party information like OS type, Browser type, UTM params, First landing page etc. To set up custom attributes, a special JavaScript code should be inserted into the source code of your site. Please find more details about the settings <a href=\"https://support.optimonk.com/hc/en-us/articles/204743981-Custom-Variables\" target=\"_blank\">here.</a>", "lead": "Target visitors based on attributes like OS type, timezone, browser language, or other first-party attributes.", "operators": {"browserLanguage": "Browser Language", "browserType": "Browser Type", "campaignLastSeen": "Campaign last seen", "currentMonth": "Current Month", "customAttribute": "Custom attribute", "firstLanding": "First Landing", "firstVisitDate": "First visit date", "is": "is", "isNot": "is not", "osType": "OS Type", "timezone": "Timezone", "utmCampaign": "UTM campaign", "utmMedium": "UTM medium", "utmSource": "UTM source", "deviceType": "<PERSON><PERSON>"}, "title": "Other visitor attributes", "warning": "Shopify customer tag targeting is not avaiable."}, "visitorCartV3": {"desc": "Use this rule when you want to display the campaign based on the cart content of the visitors.", "descWL": "To setup targeting based on cart contents and value, you have to insert a special JavaScript code into the source code of your site.", "lead": "Visitors with specific cart content will see your message", "supportArticle": "https://support.optimonk.com/hc/en-us/articles/4403091845010-Cart-rules", "title": "Cart rules", "video": "https://www.youtube.com/watch?v=018wYqE2TSc"}}, "frsecond": "second(s)", "full": "Full", "fullHeightOnMobile": "Full height on mobile", "fullHeightWithOverlay": "With overlay", "gamification": "Gamification", "generalSettings": "General settings", "getInspired": "Get inspired by 100+ customers' campaigns", "globalButton": "Global button", "globalFrequencyCap": {"back": "Back", "description": "It will make sure that your popups will not overwhelm and annoy visitors even if you create a hundred different campaigns with the boldest targeting settings.", "heading": "User Experience Protector", "next": "Next", "predefinedCaps": {"normal": {"description": "It will result in the best combination of conversions and user experience.", "title": "Normal"}, "off": {"description": "Smart protection is completely disabled. Your campaigns will appear exactly as you set them up.", "title": "Off"}, "safe": {"description": "We’ll optimize for user experience, compromising on conversions if necessary.", "title": "Safe"}}, "subTitle": "User Experience Protector", "title": "Never worry about annoying<br> your visitors again"}, "globalInput": "Global input", "globalIntegrationRemove": "If you remove this global integration, the integrations in the following campaigns will also be removed:", "globalIntegrations": "Global integrations", "globalPage": "Global page", "globalText": "Global text", "go": "Go", "goAnnual": "Go annual & <span class=\"cursor-pointer color-brand-primary mx-1\">save {saving}</span>", "goalDetails": {"capture_email": "Most of your visitors aren’t ready to buy yet… Turn these ‘early-stage’ visitors into email or Messenger subscribers - and convert them into sales later.", "collect_email": "Most of your visitors aren’t ready to buy yet. Entice them to subscribe to your newsletter and convert them into sales later. Offering irresistible incentives make visitors more than happy to share their contact details with you.", "collect_feedback": "Improve your business by listening to your customers. Run a survey or get visual feedback to better understand your clients.", "collect_messenger": "Build and grow social communities, and reach your prospects through social media by gathering more social followers and shares.", "collect_phone": "Most of your visitors aren’t ready to buy yet – collect phone numbers from them and reach them later via phone or SMS. Offer a callback, custom quotes, free shipping or coupon codes.", "faciliate_social_sharing": "Build and grow social communities, and reach your prospects through social media by gathering more social followers and shares.", "gamify": "Make your visitors subscribe to your newsletter and Messenger list with fun and engaging gamification popups.", "increase_cart_value": "Make more money on the same amount of traffic. Promote your best deals and get more first-time and even repeat buyers.", "increase_form_submission": "Focus on boosting your form submissions in order to start a real conversation with your visitors and connect with them.", "promote_spec_offers": "Turn new visitors into first-time buyers and existing customers into repeat buyers by promoting your best offers and incentives. Encourage your buyers to complete checkout with coupons, discounts, free shipping or launch seasonal offers.", "promote_special_offers": "Turn your website into a ‚personalized digital assistant’ who makes the navigation smoother and helps the visitors find the desired product.", "recommend_products": "Turn new visitors into first-time buyers and existing customers into repeat buyers by promoting your best offers and incentives. Encourage your buyers to complete checkout with coupons, discounts, free shipping or launch seasonal offers.", "reduce_cart_abandonment": "On average, 7 out of 10 visitors who add an item to their cart will leave without buying. Detect these visitors and convince them with a secondary offer."}, "goalsLabel": "Goals", "goalsListHeading": "This template can help you...", "goalsTitle": "Goals", "googleAnalytics": "You can measure the effectiveness of OptiMonk in a more efficient way by OptiMonk Dashboard for Google Analytics.", "gradient": "Gradient", "gradientCenter": "Gradient center", "gradientDirection": "Gradient direction", "gradientType": "Gradient type", "greaterThan": "greater than", "greaterThanEquals": "greater than or equal to", "hasNoEmbedPoistionAlertChoose": "Place the embedded content by either using the ", "hasNoEmbedPoistionAlertEmbedCode": "embed code", "hasNoEmbedPoistionAlertFinish": " to your site’s code", "hasNoEmbedPoistionAlertOr": " or manually pasting the ", "hasNoEmbedPoistionAlertPointClick": "Point & Click placement tool", "headerDomainUnlimited": "Unlimited", "headerDomains": "{used} of {max}", "headerVisitors": "{used} of {max}", "headerVisitorsUnlimited": "{used}", "headline": "Headline", "hearPage": {"hearOptions": {"agency_expert": "Agency or expert", "friends_colleagues": "Friends or colleagues", "g2_capterra": "G2 / Capterra", "google": "Google / Search engine", "other": "Other", "podcast": "Podcast", "shopify_app_store": "Shopify App Store", "social_media": "Social media"}, "title": "How did you first hear about us?"}, "heartBeat": "Heartbeat", "height": "Height", "help": "Help", "helpCenter": {"readArticle": "Read support articles", "recommended": "Recommended", "title": "Help center", "watchVideo": "Watch ‘Getting started’ videos", "learnMore": "Learn more"}, "here": "here", "hex": "Hex", "hidden": "Hidden", "horizontal": "Horizontal", "horizontalAlignment": "Horizontal alignment", "horizontalLength": "Horizontal length", "horizontalPadding": "Horizontal padding", "hours": "Hours", "hover": "Hover", "hoverBar": {"clickToEditDragToMove": "Click to edit, drag to move", "justEdit": "Click to edit", "justEditWithPageName": "Click to edit {pageName}"}, "hoverColor": "Hover color", "hoverGradient": "Hover: Gradient", "hoverImage": "Hover: Image", "hoverNone": "Hover: None", "hoverSolidFill": "Hover: Solid color", "howItWorks": "How it works?", "howItWorksTitle": "How it works", "hungarian": "Hungarian", "icon": "Icon", "iconSize": "Icon size", "iconStyle": "Icon style", "ifCouponLimitReached": "If coupon limit reached", "image": "Image", "imageAlignDisableTooltip": "Based on your image and the container size you can not set one of them alignment.", "imageCantUse": "Can't use '.{ext}' here", "imageCropped": "Image (crop)", "imageManager": "Image library", "imageManagerModals": {"replace": {"title": "Shall we replace this image everywhere?", "buttons": {"currentPage": "No, just here", "allPages": "Yes, everywhere"}}, "library": {"toggle": {"label": "Replace everywhere"}}}, "imageMaxSize": "Max. {sizeMB}MB ({size}Kb)", "imageMaxPxSize": "Max. {width}px x {height}px", "imagePosition": "Image position", "imageRepeat": "Image repeat", "imageSettings": "Image setting", "imageUploadMessage": "Drag and drop your files here or ", "imageUploadNew": "Upload new image", "imageUse": "Use image", "images": "Use images", "impersonateUpgrade": "This feature is not available on your current plan. Only the account owner can upgrade your plan.", "import": "Import", "impressionValue": "Impression value", "impressions": "Impressions", "in": "in", "inactive": "Inactive", "inactiveShopifyStatus": "({domain} inactive)", "inactiveVariants": "Currently there are no active variants for this campaign. To display the popup, at least one variant should be activated.", "inactivity1": "The campaign will appear after", "inactivity2": "inactive second(s).", "infoBox": {"addElementTitle": "Drag and drop", "addElementText": "Drag elements to the page to add them to your campaign", "teaserLearnMoreUrl": "https://support.optimonk.com/hc/en-us/articles/************-Teaser", "teaserText": "Sticky teasers follow your visitors all the way through your website (taking into account the other display and targeting settings, of course), and don't let them forget about your message.", "teaserTitle": "Sticky teasers", "themeLearnMoreUrl": "https://support.optimonk.com/hc/en-us/articles/*************-Themes", "themeText": "Themes are account-level collections of styles and layouts you can re-use in your campaigns.", "themeTitle": "Themes"}, "input": "Input", "inputFieldBindings": "Input field bindings", "inputFieldBindingsStaticTitle": "STATIC FIELDS", "inputModifyWarning": "Please note if you modify this input, it will change everywhere where this input is used.", "inputOptionalError": "Inappropriate format.", "inputRequired": "Required?", "inputSettings": "Input settings", "inputValidationsModal": {"allowBlockToggle": "Allow/Block", "allowSubtitle": "Allowed email addresses", "allowToggle": "Allow", "blockSubtitle": "Blocked email addresses", "blockToggle": "Block", "description": "For email validation, there are two logic types. You can either define ‘whitelisted’ a.k.a. allowed email addresses or define email addresses that are blocked or ‘blacklisted’. Based on the logic you select, you can define the error message displayed.", "hint": "Hit Enter after defining a rule", "title": "Allow/Block email addresses"}, "emptyState": {"campaignList": {"text1": "Campaign List", "text2": "Here you can manage your campaigns and see the most important statistics.", "button": "Create your first campaign", "emptyFilter": "No campaigns found with the selected filters."}, "leads": {"text1": "Leads", "text2": "Here you’ll see all the leads you’ve collected with your OptiMonk campaigns. You’ll be able to transfer your leads to your email or SMS tools, or export them as XLSX or CSV files.", "link": "Learn more", "linkUrl": "https://support.optimonk.com/hc/en-us/articles/************-How-to-manage-the-leads-within-my-OptiMonk-account", "emptyFilter": "No campaigns found with the selected filters."}, "segments": {"text1": "Custom segments", "text2": "If you want to use the same targeting rule in several campaigns, saved segments can save you a ton of time. Simply save your setting as a custom segment and add it to any future campaigns.", "link": "Learn more", "linkUrl": "https://support.optimonk.com/hc/en-us/articles/*************-Segments#h_01GF655J11AAAGB04CJ13GAA4P"}, "campaignAnalytics": {"text": "Access your campaign performance stats here.", "button": "Create a campaign"}}, "inputs": "Input", "insertCode": "Insert code", "insertCodeModal": {"done": {"custom": "I’ve installed OptiMonk"}, "installLater": "Install later", "question": {"custom": "Do you feel intimidated by the thought of handling code?", "install": "Do you feel unprepared to handle the installation?"}, "title": {"custom": "Install OptiMonk"}}, "insertCodeReady": "Well done, now insert your<br>Javascript code and you are ready.", "insertJavaScriptCode": "Install OptiMonk", "install": "Install", "installOptiMonk": "Install OptiMonk", "integration": "Where you would like to send the subscribers and campaign data?", "integrationBindingErrorMessages": {"missingRequiredFieldAtLeast": "This integration requires at least one of the following fields: ", "missingRequiredFieldsEvery": "This integration requires all of the following fields: ", "requiredFieldsInList": "The following fields are required in your {integration} list: <b>{fieldList}</b>"}, "integrationBindingFieldTypes": {"email": "Email", "phoneNumber": "Phone number"}, "integrationError": "integration error", "integrationConnectFail": "Integration issue", "integrationConnectFailDetails": "Please reconnect integration", "integrationErrorDetailsModal": {"retryBtn": "Retry", "support": "<a target=\"_blank\" href=\"https://support.optimonk.com/hc/en-us/search?utf8=%E2%9C%93&query=%{integrationName}&commit=Search\">contact support</a>", "text": "Optimonk experienced an error when synchronizing data with your <b>%{integration}</b> account. If you think, the error is not permanent, please try to resync. Optimonk will also attempt to resync data once a day for 3 days. When the error looks permanent, please check <b><a href=\"%{integrationLink}\">your integration settings</a></b> for errors. If you need help to proceed please <b>%{support}</b>.", "title": "Integration error"}, "integrationFields": {"accessToken": "Access token", "account": "Account", "accountId": "Account ID", "apiId": "API ID", "apiKey": "API key", "apiPassword": "API password", "apiUrl": "API URL", "apiUserId": "API felhasználói azonosító", "appId": "App ID", "applicationId": "Application ID", "applicationName": "Name of application", "authorizationToken": "Authorization token", "baseUrl": "Admin Domain", "callbackUrl": "URL", "categoryId": "Category ID", "charset": "Charset", "clientFolderId": "Client Folder ID", "clientId": "Client ID", "clientSecret": "Client Secret", "client_id": "Client ID", "client_secret": "Client Secret", "groupListId": "Csoport (lista) ID", "host": "Host URL", "is360User": "I use GetResponse360", "lastNameField": "Last name field", "legacyApiKey": "Legacy API key", "name": "Name", "password": "Password", "postUrl": "<PERSON>'s address", "privateKey": "Private key", "publicApiKey": "Public API key", "publicKey": "Public key", "refreshToken": "Refresh <PERSON>", "secret": "Secret", "secretKey": "Secret key", "shopUrl": "Shop URL", "shopifyDomain": "Shop URL", "subscribeId": "Subscribe ID", "systemId": "System ID", "uniqueURL": "Unique domain", "url": "URL", "userName": "Username", "userToken": "Token", "username": "Username"}, "integrationFlow": {"activeCampaign": {"doi": "Double Opt-in", "formHelper": "Select a form to confirm your subscribers in case of Double Opt-in. <a href=\"https://help.activecampaign.com/hc/en-us/articles/115000853310-Double-opt-in-vs-single-opt-in\" target=\"_blank\">Learn more</a>", "formSelectInfo": "If you want your subscribers to confirm their subscription, you need to add a Form. Forms can be added through the Forms menu.", "notifications": {"apiConnectError": "There was a problem communicating with the API, please check your ActiveCampaign API key and API URL."}, "selectTags": "Select tag(s)", "tags": "ActiveCampaign tags"}, "addIntegration": "Add {type}", "combinedMultiselect": {"deselect": "Click to deselect", "placeholder": "Select or enter value", "tagPlaceholder": "Add this as new value"}, "doiInfo": "A double opt-in adds an additional step to the email subscription opt-in process, requiring a user to verify their email addresss and confirm interest.", "finishSetup": "Finish setup", "highLevel": {"authBtn": "HighLevel authentication", "authError": "Authentication error", "authSuccessful": "Authentication successful", "authentication": "Authentication", "tag": "tag", "tags": "Tags"}, "hubSpotV2": {"authBtn": "HubSpot authentication", "authError": "Authentication error", "authSuccessful": "Authentication successful", "authentication": "Authentication"}, "selzy": {"optInTypes": {"always": "Always send an invitation", "never": "Add to list without sending invitation", "onlyForNew": "Invitation letter for new users only"}, "optInTypeHelpers": {"0": "The contact is considered to only have expressed a desire to subscribe, but has not yet confirmed the subscription. In this case, the contact will be sent an invitation letter to subscribe.", "3": "It is considered that the contact's consent has been obtained, and the contact is added with the «new» status.", "4": "The system checks the presence of a contact on your lists. If the contact is already present on your lists with the «new» or «active» status, the address will be just added to the list you have specified. If the contact is not present on your lists or its status is other than «new» or «active», an invitation letter to subscribe will be sent to the contact."}, "tags": "Tags in Selzy", "selectTags": "Select tag(s)"}, "integration": "integration", "integrationModal": {"fieldMapping": "Field mapping", "general": {"addCustomValue": "Add more custom value", "addName": "Add name", "additionalData": "Send additional data", "additionalDataHelper": "Use Custom values to send additional data with each new signup such as Campaign name, URL of appearance or any other fix value.", "apiKeyError": "Paste your API key here", "apiUrlError": "Paste the API URL here", "copyApiKey": "Copy your API key", "createAndCopyApiKey": "Create and copy your API keys", "customValue": "Custom value", "fieldMappingAltTitle": "Check the corresponding field names in {type} to ensure that the campaign data we send to {type} will be handled correctly.", "goToApiKey": "Click here to copy your API Key", "howToGetApiKey": "Click here to find out how to get your API Key", "inputHelp": "Name will help you differentiate between the same type of integrations.", "integrationFields": "Properties in {type}", "linkToKeys": "Click here to create your private and public API keys.", "listHelper": "Which {type} list would you like to integrate with?", "namePlaceholder": "Integration 1", "omInputFields": "OptiMonk input fields", "pasteApiKey": "Paste your API Key", "pasteApiUrl": "Paste your API URL", "privateApiKey": "{type} private API key", "privateApiKeyError": "Paste the private API key here", "invalidList": "Invalid list", "apiKeyInvalid": "Incorrect API Key", "apiKeyFixed": "It looks like you already fixed the API key, proceed with the resync and click Next", "checkFields": "Please check the Properties in {integration}", "publicApiKey": "{type} public API key", "publicApiKeyError": "Paste the public API key here", "enterEmailAndPassword": "Enter email and password", "email": "Email", "password": "Password", "emailError": "Email is required", "passwordError": "Password is required", "optInTypeHelper": "Choose an opt-in type", "listGetError": "Error while getting integration data, please check your credentials"}, "mapping": {"campaignFillUrl": "URL of campaign fill", "variantName": "Variant name"}, "modalTitle": "{type} integration", "notifications": {"klaviyo": {"apiConnectError": "<a href=\"https://www.klaviyo.com/account#api-keys-tab\" target=\"_blank\">There was a problem communicating with the API, please check your Klaviyo API key.</a>"}}, "selectAForm": "Select a form", "selectAList": "Select a list", "setupIntegration": "Setup integration"}, "klaviyo": {"apiKey": "API key: ", "helpUrl": "https://support.optimonk.com/hc/en-us/articles/************-Integrating-OptiMonk-Campaigns-with-<PERSON><PERSON><PERSON><PERSON>", "notifications": {"apiConnectError": "<a href=\"https://www.klaviyo.com/account#api-keys-tab\" target=\"_blank\">There was a problem communicating with the API, please check your Klaviyo API key.</a>"}, "publicApiKey": "Public API key:", "fullAccess": "Make sure to create a 'Full Access Key'."}, "klaviyoOAuth": {"authentication": "Authenticate with <PERSON>lav<PERSON><PERSON>", "authBtn": "Authenticate", "authSuccessful": "Authentication successful", "authError": "Authentication failed", "helpUrl": "https://support.optimonk.com/hc/en-us/articles/************-Integrating-OptiMonk-Campaigns-with-<PERSON><PERSON><PERSON><PERSON>", "multiListCampaign": "Multi-list campaign", "multiListCampaignTooltip": "If you enable this option, you can select different lists for email-only, phone-only, and email+phone subscribers.", "emailOnly": "Email only", "phoneOnly": "Phone only", "emailAndPhone": "Email and phone", "pasteApiKey": "Paste your API Key", "APIKeyHere": "Your API Key is here"}, "mailChimp": {"chooseGroups": "Choose which groups will be associated with the subscriber. (Optional) <a href=\"https://support.optimonk.com/hc/en-us/articles/*********-Integrating-OptiMonk-campaigns-with-MailChimp\" target=\"_blank\">Learn more</a>", "doi": "Double Opt-in", "groupMultiselectPlaceholder": "Select group(s)", "helpUrl": "https://support.optimonk.com/hc/en-us/articles/*********-Integrating-OptiMonk-campaigns-with-Mail<PERSON>himp", "notifications": {"apiConnectError": "<a href=\"https://admin.mailchimp.com/account/api/\" target=\"_blank\">There was a problem communicating with the API, please check your MailChimp API key.</a>"}}, "select": "Select", "shopify": {"tags": "Shopify customer tags"}, "webhook": {"error": {"callbackUrl": "URL is required"}, "helpUrl": "https://support.optimonk.com/hc/en-us/articles/*********-Integrating-the-OptiMonk-Campaigns-with-Webhook", "insertUrl": "Insert URL", "label": {"insertUrl": "The data (firstname, lastname, e-mail address, etc.) will be sent to this URL."}}, "dotmailer": {"apiUser": "If you haven't got an API user yet, then on Dotdigital's admin interface at Account menu > Settings > Access > API users you can add one. The system will generate an e-mail address. (ex.: <EMAIL>)", "email": "Generated email of the API user", "password": "Password of the API user", "listTooltip": "(Optional) Choose a list to add contacts to.", "optInTypeTooltip": "Choose an opt-in type", "optInTypeHelpers": {"Single": "No confirmation email. <a href=\"https://support.dotdigital.com/en/articles/8198810-email-opt-in-types#h_2de8842ef3\">Learn more</a>", "Double": "You must send the confirmation email. <a href=\"https://support.dotdigital.com/en/articles/8198810-email-opt-in-types#h_7ba6876404\">Learn more</a>", "VerifiedDouble": "Dotdigital sends the confirmation email. <a href=\"https://support.dotdigital.com/en/articles/8198810-email-opt-in-types#h_d722830234\">Learn more</a>"}}, "theMarketer": {"copyCustomerAndRestID": "Copy Customer ID and REST ID", "linkToIDs": "Click here to get your IDs.", "pasteIDs": "Paste your IDs", "customerId": "Customer ID", "customerIdError": "Paste your Customer ID here", "restId": "REST ID", "restIdError": "Paste your REST ID here", "tags": "Tag(s)"}, "postscript": {"listTooltip": "Select a keyword to add contacts to.", "selectAList": "Select a keyword"}, "listamesterV2": {"userId": "User ID", "pasteApiKeyAndUserId": "Paste your API key and user ID", "pasteUserId": "Paste your user ID", "userIdRequired": "User ID is required", "listTooltip": "Select a group to add contacts to.", "selectAList": "Select a group"}, "zapier": {"error": {"callbackUrl": "URL is required"}, "insertUrl": "Insert URL", "label": {"insertUrl": "The data (firstname, lastname, e-mail address, etc.) will be sent to this URL."}}, "convertKit": {"formAndSequenceHelper": "Either a form or a sequence must be selected. You can select both.", "formHelper": "Choose a form to add contacts to.", "courseHelper": "Choose a sequence to add contacts to", "selectAForm": "Select a form", "selectACourse": "Select a sequence", "tags": "Tag(s)", "selectTags": "Select tag(s)", "copyApiKey": "Copy your V3 API key."}}, "integrationLead": "Define where you would like to keep the list of subscribers", "integrationRecommendationState": {"add": "{name} is popular among our customers and we detected it on your website. Add and set up for your campaign", "finish": "If you want to use {name} for this campaign, finish its setup", "select": "If you want to use {name} for this campaign, select one from this list"}, "integrationSettingLead": "Define where you would like to keep the list of subscribers", "integrationSettings": "Integration settings", "integrationWarning": "To store your new subscribers in your newsletter or CRM system, we recommend adding an integration.", "integrationWarnings": {"app_inactive": "Subscriber might not be forwarded, because Our app seems inactive or uninstalled in your shop", "experiencing_problems": "Currently we are experiencing communication problems with the integration.", "firstname_lastname_email_sent_automatically": "Firstname, Lastname and Email fields are automatically sent.", "name_forwarded": "First name and last name are automatically forwarded", "only_phone_and_email": "Only Email and Phone fields are supported. For phone numbers, <a href=\"https://help.attentivemobile.com/hc/en-us/articles/16948569849492-Send-messages-internationally-with-Attentive\" target=\"_blank\">check</a>!", "only_firstname_lastname_email": "This integration only supports Firstname, Lastname and Email fields.", "only_firstname_lastname_email_phone": "This integration only supports Firstname, Lastname, Email, Phone fields and Customer tags.", "only_phone": "This integration only supports Phone fields.", "ping_error": "Could not establish a connection. Your authentication data is probably outdated.", "salesforce_mandatory_fields": "This integration mandatory fields: Firstname, Lastname and Email fields.", "salesforce_premium": "You need a Salesforce Premium Account to use this service!", "validations": {"at_least_one_field_binded": "Please correctly set at least one field: {fieldList}", "no_$_sign": "You can use these fields: <br><strong>first_name, last_name, phone_number, title, organization, city, region, country, zip, image</strong><br> or any custom identifier"}}, "integrations": {"IContact": {"fields": {"sourceInputName": "Source field name"}, "name": "iContact", "tooltip": {"accountId": "You find your Application ID after the Account information header on this site: <a href=\"https://app.icontact.com/icp/core/externallogin\" target=\"_blank\">https://app.icontact.com/icp/core/externallogin</a> Example: 1489252", "apiPassword": "This is the password you used when you created the API.", "applicationId": "You can find your \"API-AppId\" here: <a href=\"https://app.icontact.com/icp/core/registerapp/\" target=\"_blank\">https://app.icontact.com/icp/core/registerapp/</a>", "clientFolderId": "You find your Client Folder ID at the following URL: <a href=\"https://app.icontact.com/icp/core/externallogin\" target=\"_blank\">https://app.icontact.com/icp/core/externallogin</a> Example: 4958", "listId": "(Optional) Under the List menu (Contacts -> Lists) you can see the lists that you have created already. You can get the list ID from the subscribers list's page URL. This is the number that follows the 'lists/edit' part of the URL. Example: https://app.icontact.com/icp/core/mycontacts/lists/edit/7249/ -> you would type in: 7249", "sourceInputName": "(Optional) You are able to use '{brand}' as the value in a custom field - this way you will know which subscribers were from your {brand} popups. You can find the name of the custom field at <a href=\"https://app.icontact.com/icp/core/settings/customfields\" target=\"_blank\">https://app.icontact.com/icp/core/settings/customfields</a>, in the Field name column.", "userName": "This is your username."}, "warnings": ["only_firstname_lastname_email"]}, "aWeber": {"binding": {"title": "Field identifier in AWeber"}, "link": "https://auth.aweber.com/1.0/oauth/authorize_app/abdfb534?oauth_callback=%{redirectUrl}", "name": "<PERSON><PERSON><PERSON><PERSON>", "warnings": ["name_forwarded"]}, "acerCCDB": {"campaignCode": "Campaign code", "name": "Acer CCDB"}, "acerCCDBV2": {"campaignCode": "Campaign code", "name": "Acer CCDB"}, "actOn": {"fields": {"emailField": "Email field", "firstNameField": "First name field", "lastNameField": "Last name field", "sourceInputName": "Source field name", "sourceText": "Source field text"}, "name": "Act-On", "tooltip": {"emailField": "You can choose a field to store the subscriber e-mail address. If you don't choose a field, the default 'E-mail Address' column will be used. If the selected field does not exists, or it is an incorrect type, the subscriber will not be added to ActOn", "firstNameField": "The subscriber's first name will be stored in this field (column)", "lastNameField": "The subscriber's last name will be stored in this field (column)", "listId": "Subscribers will automatically be added to this list.", "password": "Act-On Password (Your password will not be stored, it is only used for authentication.)", "sourceInputName": "(Optional) The name of the source field at ActOn, in which you will mark the subscribers you receive from {brand}.", "sourceText": "(Optional) The value that will be transmitted to the source field at ActOn, ie - '{brand} Campaign'. The default value '{brand}' will be transmitted if this field is empty.", "username": "Act-On Username (Your username will not be stored, it is only used for authentication.)"}, "warnings": ["only_firstname_lastname_email"]}, "activeCampaign": {"fields": {"email": "Email", "firstname": "First name", "formName": "Form name (In case of Double Opt-in):", "lastname": "Last name", "phone": "Phone", "tags": "Tags:"}, "name": "ActiveCampaign", "tooltip": {"apiKey": "You can also find the API key at Settings > Developer.<br>Example: 0d8c99190cd379125fef6419f3c4f87f22f632b49bdc2b4188ff9a2bda2e7aa06a67d567", "apiUrl": "You can find the API URL field at Settings > Developer.<br>Example: https://test.api-us1.com", "formName": "(Optional) If you want your subscribers to confirm their subscription, you need to add a Form. Forms can be added through the Forms menu. Please note: A list is required.", "listName": "(Optional) Your visitors' data will be added to the following list. Lists can be added through the Lists menu.", "tags": "(Optional) With multiselect you can choose which tags apply to a subscriber."}}, "attentive": {"name": "Attentive", "noPhoneBinding": "No binding for phone type input!", "tooltip": {"authorizationToken": "You could ask the authorization token from Attentive support"}, "warnings": ["only_american_phone"]}, "attentiveV2": {"fields": {"signUpSourceId": "Sign up source ID:"}, "name": "Attentive V2", "noPhoneBinding": "No binding for phone type input!", "tooltip": {"authorizationToken": "You could ask the authorization token from Attentive support", "signUpSourceId": "Please check this <a href=\"https://docs.attentive.com/pages/graphql/subscribe/\" target=\"_blank\">website</a> for more information"}, "warnings": ["only_phone_and_email"]}, "automizy": {"fields": {"customField": "Source field name", "customFieldText": "Source field value", "firstNameField": "First name field", "formName": "Form name", "lastNameField": "Last name field", "listId": "List", "urlField": "Source URL field"}, "link": "https://api.automizy.com/oauth/authorize?client_id=1c323234d2323fd3115a054c40d8d55ef0ef9174b6c84837b2ccc7&redirect_uri=%{redirectUrl}", "name": "Automizy", "tooltip": {"accessToken": "Settings -> API Token -> NEW TOKEN, then copy the token and paste here", "customField": "(Optional) You can choose a \"custom field\" to mark subscribers you receive from {brand}.", "customFieldText": "(Optional) The value that will be stored in the Source field. The default value \"{brand}\" will be used if this field is left empty.", "firstNameField": "(Optional) The value of the first name will be stored here.", "formName": "(Optional) Subscribers will be added through this Automizy form.", "lastNameField": "(Optional) The value of the last name will be stored here.", "urlField": "(Optional) The URL of the source site will be passed to this field. The source site is where the visitor subscribed through the popup."}, "warnings": ["only_firstname_lastname_email"]}, "bizzy": {"name": "Sendgrid - v1", "tooltip": {"apiKey": "To locate your API key: Log in to your Sendgrid dashboard. Click on the Settings then API Keys. You can create a new API key for OptiMonk, then copy that key here."}, "warnings": ["only_firstname_lastname_email"]}, "campaignMonitor": {"binding": {"title": "Field identifier in Campaign Monitor"}, "fields": {"clientName": "<PERSON><PERSON>'s name:"}, "name": "Campaign Monitor", "tooltip": {"apiKey": "Account Settings -> API Key", "clientName": "Client's name as it appears under the list of Clients.", "listName": "Name of the list that people signing up will be subscribed to."}}, "cleverReach": {"fields": {"formId": "Form name", "optIn": "Double opt-in"}, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tooltip": {"apiKey": "Your API Key can be found under Account -> Extras -> API. If you haven't created an API key, you can create one under Create API Key."}, "warnings": ["only_firstname_lastname_email"]}, "conversio": {"binding": {"title": "Field name in CM Commerce"}, "name": "CM Commerce", "tooltip": {"apiKey": "To locate your API key: Log in to your CM Commerce dashboard. Click on your account name in the top right corner of the site and select the \"Account Settings\" option. Or use <a href=\"https://commerce.campaignmonitor.com/profile\" target=\"_blank\">this link</a>.", "listId": "The subscribers will be added to this list. You can create a new list on <a href=\"https://commerce.campaignmonitor.com/customerLists\" target=\"_blank\">https://commerce.campaignmonitor.com/customerLists</a>"}, "warnings": ["firstname_lastname_email_sent_automatically"]}, "convertKit": {"name": "<PERSON>", "fields": {"email": "Email", "firstname": "First name"}}, "copernica": {"fields": {"databaseId": "Database name:", "emailField": "Forward email here:", "firstnameField": "Forward first name here:", "lastnameField": "Forward last name here:", "sourceFieldName": "Source field name:", "sourceFieldValue": "Source value:"}, "link": "https://www.copernica.com/en/authorize?client_id=d25cb79a7c249dc7e9c2898ceab30d12&response_type=code&state=%{randomStr}&redirect_uri=%{redirectUrl}", "name": "Copernica", "tooltip": {"databaseId": "The data of subscribers will be transmitted into this database.", "emailField": "(Optional) The value of the email will be transmitted here.", "firstnameField": "(Optional) The value of the first name will be transmitted here.", "lastnameField": "(Optional) The value of the last name will be transmitted here.", "sourceFieldName": "(Optional) The name of the source field where you will mark the subscribers you receive from {brand}.", "sourceFieldValue": "(Optional) The value that will be transmitted to the Source field."}}, "deleted": {"name": "Deleted integration"}, "dotmailer": {"name": "Dotdigital", "tooltip": {"username": "Email"}, "notifications": {"apiConnectError": "There was a problem communicating with the API, please check your email and password.</a>"}}, "selzy": {"name": "<PERSON><PERSON><PERSON>", "tooltip": {"apiKey": "API key"}, "notifications": {"apiConnectError": "There was a problem communicating with the API, please check your API key.</a>"}}, "emarsys": {"fields": {"doubleOptin": "Opt-in status:", "listId": "List name:"}, "name": "<PERSON><PERSON><PERSON>", "tooltip": {"secret": "To generate or view your API users and keys: Login to your dashboard at Emarsys and click on Admin -> Security Settings -> API users tab.", "username": "To generate or view your API users and keys: Login to your dashboard at Emarsys and click on Admin -> Security Settings -> API users tab."}}, "getResponse": {"fields": {"campaignName": "Campaign name:", "is360User": "I use GetResponse360:", "sourceField": "Source field:", "sourceValue": "Source value:"}, "name": "GetResponse", "tooltip": {"apiKey": "Account Details -> API & OAuth: You can obtain the key from the My GetResponse API key section.", "campaignName": "You can choose a campaign from your Campaign List. Subscribers will be added to this campaign.", "is360User": "Turn this On if you use GetResponse360.", "uniqueURL": "The unique GetResponse360 URL provided to you by your Account Manager."}}, "highLevel": {"name": "HighLevel"}, "hirlevelmanager": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tooltip": {"categoryId": "You can specify the category id from <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s system. (e.g.: 14094).", "lastNameField": "(Optional) By default the lastname field is saved to the VEZETEKNEV field. You can change it here."}}, "hubSpot": {"binding": {"title": "Field name in HubSpot"}, "name": "HubSpot", "reIntegration": {"alert": {"altTitle": "HubSpot is introducing a breaking change in its integrations, and it needs your intervention.", "campaignAlertTitle": "The following issues could prevent your campaign from running properly:", "description": "You are required to reconnect your OptiMonk and your HubSpot accounts, as the API keys used for this purpose are being sunsetted.<br><br>Starting November 30, 2022, HubSpot API keys will no longer be able to be used as an authentication method to access HubSpot APIs. Upon expiration you will need to use OAuth to authenticate API calls.<br><br>We provide a 1-click solution to reconnect your OptiMonk campaigns to your HubSpot accounts. If you want to connect one of your campaigns to another HubSpot account, you have to do this individually in the settings of the specific campaign.", "link": "View details", "title": "A re-integration of {imageTag} HubSpot became necessary"}}, "tooltip": {"apiKey": "Please visit the following URL to check or create your API key: https://app.hubspot.com/keys/get", "listName": "(Optional) The user's data will be added to the following list.", "permission": "You don't have permission to inquire the contact lists with the provided API key. Please contact the HubSpot support to upgrade to a higher plan."}}, "hubSpotV2": {"name": "HubSpot", "notifications": {"apiConnectError": "There was a problem communicating with the API, please check your HubSpot authentication.</a>"}}, "infusionSoft": {"binding": {"title": "\"Database Names\" in InfusionSoft"}, "fields": {"doubleOptin": "Send verification e-mail by Infusionsoft:", "tags": "Tags:"}, "name": "InfusionSoft", "tooltip": {"apiKey": "The API key can be found with the help of the main navigation menu next to the logo. Go to: Admin -> Settings -> Application Settings -> Application. Here you will create an \"API Passphrase”. After creating the Passphrase, the \"Encrypted Key” appears. (E.g.- 7b8f951cfb604bcd87ab507d5b55bace)", "applicationName": "This is the subdomain of the url. Example: https://abc123.infusionsoft.com -> you would type in: abc123", "tags": "(Optional) With multiselect you can choose which tags apply to a subscriber."}}, "interspire": {"fields": {"contactListName": "Contact list", "firstNameField": "First name field", "lastNameField": "Last name field", "sourceField": "Source field"}, "name": "Interspire", "tooltip": {"apiUrl": "To retrieve the information needed for authentication, login to your Interspire account, select 'Users & Groups' in the menu above, and then click on 'View User Accounts'. On the User Accounts page, click 'Edit' in the line of the account you use to access Interspire. On the 'Advanced User Settings' tab, enable the XML API, and here you can find the authentication information: XML Path URL, XML Token, and XML Username. Your username and token will not be stored, these are only used for authentication.", "contactListName": "Subscribers will automatically be added to this list.", "firstNameField": "(Optional) You can specify the Custom Fields in which the first and last name of the user will be stored in your Interspire Contact List.", "lastNameField": "(Optional) You can specify the Custom Fields in which the first and last name of the user will be stored in your Interspire Contact List.", "sourceField": "(Optional) Select a Custom Field to track subscribers generated by {brand}. The value \"{brand}\" will be stored in this field.", "userToken": "To retrieve the information needed for authentication, login to your Interspire account, select 'Users & Groups' in the menu above, and then click on 'View User Accounts'. On the User Accounts page, click 'Edit' in the line of the account you use to access Interspire. On the 'Advanced User Settings' tab, enable the XML API, and here you can find the authentication information: XML Path URL, XML Token, and XML Username. Your username and token will not be stored, these are only used for authentication.", "username": "To retrieve the information needed for authentication, login to your Interspire account, select 'Users & Groups' in the menu above, and then click on 'View User Accounts'. On the User Accounts page, click 'Edit' in the line of the account you use to access Interspire. On the 'Advanced User Settings' tab, enable the XML API, and here you can find the authentication information: XML Path URL, XML Token, and XML Username. Your username and token will not be stored, these are only used for authentication."}, "warnings": ["only_firstname_lastname_email"]}, "keap": {"fields": {"doubleOptin": "Send verification e-mail by Keap:", "tags": "Tags:"}, "name": "Keap"}, "klaviyo": {"binding": {"title": "Field identifier in Klaviyo", "tooltip": "You can use these fields: <br><strong>first_name, last_name, phone_number, title, organization, city, region, country, zip, image</strong><br> or any custom identifier"}, "fields": {"optin": "Send verification email by <PERSON><PERSON><PERSON><PERSON>:"}, "helpUrl": "https://support.optimonk.com/hc/en-us/articles/************-Integrating-OptiMonk-Campaigns-with-<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "notifications": {"apiConnectError": "<a href=\"https://www.klaviyo.com/account#api-keys-tab\" target=\"_blank\">There was a problem communicating with the API, please check your Klaviyo API key.</a>"}, "tooltip": {"apiKey": "To retrieve your API key, log in to your Klaviyo account. Click on 'Account' -> 'Account Settings' -> 'API Keys' tab. Create a new API Key, then copy and paste it here.", "optin": "Choose this for a double opt-in, once via {brand} and once via the verification email by <PERSON><PERSON><PERSON><PERSON>.", "publicApiKey": "To retrieve your public API key, log in to your Klaviyo account. Click on 'Account' -> 'Account Settings' -> 'API Keys' tab."}}, "klaviyoOAuth": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "ladyBird": {"name": "LadyBird", "tooltip": {"postUrl": "The value in the Cím field. (e.g. system.ladybird.hu/public/savefromapi/hash/11111111111111111111111111111111)"}}, "listamester": {"name": "Listamester", "tooltip": {"apiPassword": "<PERSON><PERSON><PERSON> <PERSON>, ahol az API felhasználó azonosító (pl: b342e87647fbfd8131b8706b51fcea12)", "apiUserId": "A Listamester oldalán az alábbi helyen található az azonosító: Beállítások -> Programozói interfész (pl: 7191)", "groupListId": "Itt adható meg, hogy melyik listára k<PERSON>üljön fel a feliratkozó. A lista ID-je: Csoportjaim -> Tagokra kattintás -> Az URL-ből kell kimásolni az utolsó / jel utáni számsort. (pl: 23938)"}}, "mailChimp": {"binding": {"title": "Field name in MailChimp"}, "fields": {"doubleOptin": "Double opt-in:", "groups": "Groups:"}, "name": "MailChimp", "tooltip": {"apiKey": "You can view or generate API keys in your MailChimp account. Go to [Account Name] -> Account Setting -> Extras -> API keys (eg.: ************************************)", "groups": "(Optional) You can choose which groups will be associated with the subscriber. <a href=\"http://kb.mailchimp.com/lists/growth/combine-lists-to-a-master-list-that-uses-groups#Create-Groups-in-Your-Master-List\">http://kb.mailchimp.com/lists/growth/combine-lists-to-a-master-list-that-uses-groups#Create-Groups-in-Your-Master-List</a>", "reqFields": "Please note! The following fields are required for this list:", "reqFields1": "These fields must be included in your popup as they are marked as \"Required\" in MailChimp."}}, "mailEngine": {"fields": {"affiliateId": "Affiliate id:", "emailFieldName": "E-mail field's id:", "firstnameFieldName": "First name field's id:", "hidden": "Send MailEngine confirmation e-mail:", "lastnameFieldName": "Last name field's id:", "uniqueFieldName": "Unique filter's name:", "uniqueFieldValue": "Unique filter's id:"}, "name": "MailEngine", "tooltip": {"clientId": "Id requested from MailEngine", "subscribeId": "Menü -> Beállítások -> Általános be<PERSON>llít<PERSON>ok"}}, "mailUp": {"fields": {"group": "Group", "sourceId": "Source field", "sourceValue": "Source field value"}, "link": "http://services.mailup.com/Authorization/OAuth/LogOn?client_id=%{client_id}&client_secret=%{client_secret}&response_type=code&redirect_uri=%{redirectUrl}", "name": "MailUp", "tooltip": {"client_id": "You can view or generate Client ID in your MailUp account. Go to Settings -> Advanced settings -> Develo<PERSON>'s corner", "client_secret": "You can view or generate Client Secret in your MailUp account. Go to Settings -> Advanced settings -> Dev<PERSON><PERSON>'s corner", "group": "(Optional) The subscriber will be added to this group too.", "sourceId": "(Optional) You can choose a \"source field\" to mark subscribers you receive from {brand}.", "sourceValue": "(Optional) The value that will be stored in the Source field. The default value \"{brand}\" will be used if this field is left empty."}}, "mailWizz": {"binding": {"title": "Field name in MailWizz"}, "fields": {"firstName": "First name field:", "hiddenInputName": "Source field name:", "hiddenInputValue": "Source value:", "lastName": "Last name field:", "updateExisting": "Forwarding changes:"}, "name": "MailWizz", "tooltip": {"apiUrl": "To locate your API URL: Login to your MailWizz dashboard. Click on API keys in the left sidebar menu and then click on the Info button in the top right corner to retrieve your API URL.", "hiddenInputName": "(Optional) You can choose a \"source field\" to mark subscribers you receive from {brand}.", "hiddenInputValue": "(Optional) The value that will be stored in the Source field. The default value \"{brand}\" will be used if this field is left empty.", "privateKey": "To locate your Private key: Login to your MailWizz dashboard. Click on API keys in the left sidebar menu and then choose Create New to generate a key for {brand}.", "publicKey": "To locate your Public key: Login to your MailWizz dashboard. Click on API keys in the left sidebar menu and then choose Create New to generate a key for {brand}."}}, "maileon": {"fields": {"customFieldInput": "Source field name:", "customFieldValue": "Source value:", "doiKey": "Double Optin key", "doubleOptin": "Opt-in permission:", "sendDoi": "Double Optin e-mail", "sourceUrlFieldInput": "Submit source URL to this field:"}, "name": "Maileon", "tooltip": {"apiKey": "Please find the API key within Maileon account, under Settings then API keys options. ( ex.: 9b6e40f4-db7b-4059-8d4a-2db082d709df)", "customFieldInput": "(Optional) The name of the source field where you will mark the subscribers you receive from {brand}.", "customFieldValue": "(Optional) The value that will be transmitted to the Source field.", "doubleOptin": "Select the permission to be assigned to the subscriber. You will find more information about the permissions on the Maileon interface.", "sourceUrlFieldInput": "(Optional) The URL of the source site will be submitted to this field."}}, "mailerLite": {"fields": {"customFieldName": "Source field name:", "customFieldText": "Source value:", "groupId": "List name:"}, "name": "MailerLite", "tooltip": {"apiKey": "To locate your API key: Log in to your MailerLite dashboard. Click on your account name in the top right corner of the site and select the \"Integrations\" option. Then select \"Developer API\". Your API key will be listed under the \"API Key\" label.", "customFieldName": "(Optional) You can choose a \"source field\" to mark subscribers you receive from {brand}.", "customFieldText": "(Optional) The value that will be stored in the Source field. The default value '{brand}' will be used if this field is left empty.", "groupId": "Subscribers will be added to this list."}, "warnings": ["firstname_lastname_email_sent_automatically"]}, "mailigen": {"fields": {"doubleOptin": "Send verification e-mail by Mailigen:", "updateExisting": "Update existing records:"}, "name": "Mailigen", "tooltip": {"apiKey": "You can view or generate API keys in your Mailigen account. Go to Settings -> API keys.", "updateExisting": "If the subscriber's record already exists in your Mailigen list, the data will be updated."}}, "mailjet": {"fields": {"firstnameField": "First name field:", "lastnameField": "Last name field:", "sourceField": "Source field name:", "sourceValue": "Source value:"}, "name": "Mailjet", "tooltip": {"apiKey": "You can find your API key and Secret key within Mailjet, under My Account -> Master API Key option.", "firstnameField": "(Optional) The value of the first name will be transmitted here.", "lastnameField": "(Optional) The value of the last name will be transmitted here.", "list": "The subscriber's information will be added to the following list.", "secretKey": "You can find your API key and Secret key within Mailjet, under My Account -> Master API Key option.", "sourceField": "(Optional) The name of the source field where you will mark the subscribers you receive from {brand}.", "sourceValue": "(Optional) The value that will be transmitted to the Source field. The default value \"{brand}\" will be transmitted if this field is empty."}}, "marketo": {"fields": {"sourceFieldName": "Source field name", "sourceFieldValue": "Source field value"}, "name": "Marketo", "tooltip": {"clientId": "The Client ID of the Rest Service you create in Marketo can be found here: Admin -> Integration -> LaunchPoint, then on the line of the previously created service, View Details -> Client ID", "clientSecret": "You can also find the Client Secret of the Rest Service here: Admin -> Integration -> LaunchPoint, View Details -> Client Secret", "host": "You can find the host address of the Marketo REST API endpoint here: Admin -> Integration -> Web Services -> REST API Endpoint. Be sure to remove the trailing \"/rest\" when entering the URL in the field. Example: https://123-ABC-456.mktorest.com", "listId": "(Optional) Your subscribers can be added to a list. When you don't specify a list, leads will simply be added to your general lead database or a Smart List you have set up in Marketo.", "sourceFieldName": "(Optional) The name of the source field where you will mark the subscribers you receive from {brand}.", "sourceFieldValue": "(Optional) The value that will be passed to the Source field. The default value \"{brand}\" will be sent if this field is left empty."}}, "miniCrm": {"binding": {"title": "Field in MiniCRM"}, "fields": {"checkExistence": "Check duplicates:", "moduleName": "Module name:", "statusName": "Subscriber status:", "userName": "MiniCRM user:"}, "name": "MiniCRM", "tooltip": {"apiKey": "API key requested from Mini CRM (e.g: 1OEFjzXCtVNe7hihjTgsfstKhs3QlA)", "checkExistence": "If true, existing contacts won't be transmitted to MiniCRM.", "moduleName": "The subscribers will be saved to this module.", "statusName": "(Optional) The subscribers will be given this status.", "systemId": "System identifier requested from Mini CRM (e.g: 345)", "userName": "(Optional) The subscribers will be assigned to this user."}}, "moosend": {"fields": {"mailingList": "Mailing list:"}, "name": "Moosend", "tooltip": {"apiKey": "To retrieve the API Key needed for authentication, log in to your Moosend account and go to: \"Account (Settings)\" -> \"API Key\". You can generate a new API key with the \"Generate New API Key\" button. Please note, this will invalidate any keys which were created previously.", "mailingList": "New subscribers will automatically be added to this list"}}, "ontraport": {"fields": {"sequence": "Sequence:", "tagName": "Tag name:"}, "name": "Ontraport", "tooltip": {"apiKey": "Sign in to your Ontraport account and click on the Administration option within the drop-down menu in the top right side of the page. On the next page, under Integrations within the ONTRAPORT API INSTRUCTIONS AND KEY MANAGER option you can add the new API key and ID (ex. OM api key)", "appId": "Sign in to your Ontraport account and click on the Administration option within the drop-down menu in the top right side of the page. On the next page, under Integrations within the ONTRAPORT API INSTRUCTIONS AND KEY MANAGER option you can add the new API key and ID (ex. OM api key)", "sequence": "(Optional) You can choose which sequence to add to your new Contact", "tagName": "(Optional) You can manage tags within Contacts->Settings from the menu on the left side of the Ontraport dashboard. You can add new tags under the Manage Tags option. (ex. You can set up tags to label visitors who signed up via {brand})"}}, "postscriptLegacy": {"fields": {"keyword": "Keyword*"}, "name": "Postscript (legacy)", "noPhoneBinding": "No binding for phone type input!", "tooltip": {"legacyApiKey": "Select your Shop Name in the side menu of your Postscript dashboard, then select API. Locate your Legacy API Key at the top of the page. It's highlighted in blue."}}, "postscript": {"fields": {"keyword": "Keyword"}, "name": "Postscript"}, "listamesterV2": {"fields": {"userId": "User ID"}, "name": "Listamester"}, "robly": {"fields": {"doubleOptin": "Send verification e-mail by <PERSON><PERSON>:", "includeAutoresponder": "Autoresponder:", "sourceFieldName": "Source field name:", "sourceFieldValue": "Source field value:", "welcomeEmail": "Welcome e-mail:"}, "name": "<PERSON><PERSON>", "tooltip": {"apiId": "You <NAME_EMAIL> to request your API key and API ID.", "apiKey": "You <NAME_EMAIL> to request your API key and API ID.", "includeAutoresponder": "Choose this option to send an autoresponder from your Robly account.", "listId": "(Optional) The user's data will be added to the following list. You can create a list on https://www.robly.com/email/lists/summary page. 'Create New'", "sourceFieldName": "(Optional) The name of the source field where you will mark the subscribers you receive from {brand}. You can add a field on https://www.robly.com/email/field_tags page.", "sourceFieldValue": "(Optional) The value that will be transmitted to the Source field. The default value \"{brand}\" will be transmitted if this field is empty.", "welcomeEmail": "<PERSON><PERSON> will send a 'Welcome' email, when a visitor subscribed."}, "warnings": ["only_firstname_lastname_email"]}, "salesAutopilot": {"binding": {"title": "Field Id in SalesAutopilot", "tooltip": "Field Ids are to be found in SalesAutopilot → E-mail lists → [CHOSEN LIST] → Forms → Sign-up form. Adding new values will be displayed in the Field Id column."}, "fields": {"formId": "Form Id of a list:"}, "name": "Sales Autopilot", "tooltip": {"formId": "The form identifier is also located at the mentioned location in the previous point. (e.g., 12345)", "listId": "Email list Id is to be found in SalesAutopilot → E-mail lists → [CHOSEN LIST] → Forms → Sign-up form. Scroll down a bit for the Email list id (e.g.: 12345)", "password": "Password is the API key itself found in Settings → Intergations → API keys like 4ac32444c04412bfd5d68f2e7d17dd44863", "username": "Generate (or view existing) API keys in Settings → Intergations → API keys. API keys have a username that is also displayed on this page."}}, "salesforce": {"fields": {"listType": "List type:"}, "link": "https://eu6.salesforce.com/services/oauth2/authorize?response_type=code&client_id=3MVG98_Psg5cppybqUoiIXVuHeC0CUZU8q7Egav3bmnrucSFQLgL_DsvG1Hmrbg_8QK4tlt2yRmLqwdBTTKjV&state=%{state}&redirect_uri=%{redirectUrl}", "name": "Salesforce", "warnings": ["salesforce_mandatory_fields"]}, "sendGrid": {"name": "Sendgrid", "tooltip": {"apiKey": "To locate your API key: Log in to your Sendgrid dashboard. Click on the Settings then API Keys. You can create a new API key for OptiMonk, then copy that key here."}}, "sendinblue": {"name": "Brevo (Sendinblue)", "notifications": {"apiConnectError": "<a href=\"https://app.brevo.com/settings/keys/api\" target=\"_blank\">There was a problem communicating with the API, please check your Brevo API key.</a>"}, "onlySpecificAttributeTypes": "We only support text and date type attributes."}, "shopRenter": {"binding": {"title": "Field in ShopRenter"}, "labels": {"account": "Shop", "listLanguage": "Newsletter language"}, "name": "ShopRenter", "tooltip": {"listLanguage": "If you leave this field empty the shop's default language will be set", "password": "A ShopRenter adminisztrációs felületén az Beállítás -> Integrációk -> Egyéb -> OptiMonk menüpont alatt található a jelszó.", "shopUrl": "The site's url where your webshop is available. (eg: http://demo.shoprenter.hu)", "username": "A ShopRenter adminisztrációs felületén az Beállítás -> Integrációk -> Egyéb -> OptiMonk menüpont alatt található a felhasználói név."}}, "shopifyCustomer": {"binding": {"title": "Field identifier in Shopify"}, "multiselect": {"placeholder": "Create tags"}, "name": "Shopify", "tagFields": {"tags": "Customer tags"}, "warnings": ["only_firstname_lastname_email_phone"]}, "silihost": {"name": "Silihost", "tooltip": {"account": "A fenti HTML kódban keresd ki, hogy az első \"input\" formban milyen érték szerepel a \"value\" után, pl.: name=\"account\" value=\"44\">. A példában a 44 -et kell itt megadni.", "charset": "<PERSON>z átadott szöveg karakterkódolása adható itt meg. (Alapértelmezett értéke: utf-8)", "url": "(pl.: http://autoresponder.hu/demo/register.cgi). Az URL megkereséséhez a Silihost adminisztrációs felületén ki kell választani a kívánt hírlevél csoportot, majd a \"HTML űrlap\" menüpontban található HTML szöveg elején olvasható a keresett URL. Innen ki kell másolni az action=\" után álló URL-t a záró \" tagig."}}, "silverpop": {"name": "Silverpop", "tooltip": {"baseUrl": "The domain of your Silverpop admin page (e.g. engage3.silverpop.com).", "clientId": "To generate a Client ID and Client Secret, you will need to create an “Application”. Login to your Silverpop account and go to “Settings” -> “Organization Settings”, then follow the directions to create an “Application” in the \"Application Account Access\" section.", "clientSecret": "To generate a Client ID and Client Secret, you will need to create an “Application”. Login to your Silverpop account and go to “Settings” -> “Organization Settings”, then follow the directions to create an “Application” in the \"Application Account Access\" section.", "refreshToken": "To generate a Refresh Token, you will need to setup “Account Access”. Login to your Silverpop account and go to “Settings” -> “Organization Settings”, then follow the directions to setup “Account Access” in the \"Application Account Access\" section. The Refresh Token will be sent to the email address you provide."}, "warnings": ["only_firstname_lastname_email"]}, "slack": {"binding": {"title": "Field identifiers"}, "name": "<PERSON><PERSON>ck", "tooltip": {"callbackUrl": "The data (firstname, lastname, e-mail address, etc.) will be sent to this URL.", "callbackUrlWL": "The data (firstname, lastname, e-mail address, etc.) will be sent to this URL."}}, "smsBump": {"link": "%{redirectUrl}", "name": "<PERSON><PERSON><PERSON> (SMSBump)"}, "soundest": {"binding": {"title": "Field identifiers in Omnisend", "tooltip": "Built-in identifiers: firstName, lastName, phone, country, state, city, address, postalCode. Other than these will be set as custom property."}, "fields": {"sendWelcomeEmail": "Send welcome email:", "sourceInputValue": "Source field value:"}, "name": "Omnisend", "tags": {"placeholder": "Start typing to add new tags"}, "tooltip": {"apiKey": "To retrieve your API key, log in to your Omnisend account. Click on 'Store settings' -> 'Integrations & API' tab. Create a new API key, then copy and paste it here.", "listId": "The user's data will be added to the following list.", "sendWelcomeEmail": "Omnisend will send a 'Welcome' email, when a visitor has subscribed. Please note, to use this feature you need a Omnisend Premium package.", "sourceInputValue": "(Optional) The value that will be transmitted to the source field at Omnisend. The default value \"{brand}\" will be transmitted if this field is empty.", "tags": "Tags are labels you create to organize and manage your audience in Omnisend. If you want to assign a tag to a subscriber who signed up through OptiMonk, just select it from the dropdown or create a new one by typing in it."}}, "verticalResponse": {"fields": {"sourceInputName": "Source field name", "sourceInputValue": "Value of the Source Field"}, "link": "%{redirectUrl}", "name": "Vertical Response", "tooltip": {"sourceInputName": "(Optional) Name of custom field.", "sourceInputValue": "(Optional) You can set up the custom field value."}, "warnings": ["only_firstname_lastname_email"]}, "webGalamb3": {"name": "Webgalamb 3", "tooltip": {"url": "Webgalamb subscribe URL"}}, "webGalamb4Plus": {"binding": {"title": "Field name in WebGalamb4Plus"}, "name": "Webgalamb 4+", "tooltip": {"url": "Webgalamb subscribe URL"}}, "webhook": {"binding": {"title": "Field identifiers"}, "name": "Webhook", "tooltip": {"callbackUrl": "The data (firstname, lastname, e-mail address) will be sent to this URL. You can find more information about webhook <a href=\"https://support.optimonk.com/hc/en-us/articles/*********-How-to-integrate-with-Webhook\" target=\"_blank\">here.</a>", "callbackUrlWL": "The data (firstname, lastname, e-mail address) will be sent to this URL."}}, "recart": {"name": "<PERSON><PERSON><PERSON>", "warnings": ["only_phone"]}, "unas": {"name": "<PERSON><PERSON>"}, "theMarketer": {"name": "theMarketer"}, "zapier": {"name": "Zapier"}}, "integrationsFinish": "Next to Campaign summary", "integrationsTitle": "Integrations", "interstitial": "Interstitial", "fullscreen": "Fullscreen", "fullscreen_old": "Fullscreen old", "fullscreen_new": "Fullscreen new", "interval": "between", "invalidCustomFields": {"placeholder": "What’s this {type} about?", "title": "{type} name"}, "invalidEmail": "Invalid email address", "invalidFormat": "Invalid format", "invalidPhoneNumber": "Invalid phone number entered", "invalidUrl": {"default": "This field is required, please provide the proper URL", "protocol": "Protocol is missing from the URL", "invalid": "Please provide a valid URL"}, "invite": "Invite", "sure": "Sure", "inviteByEmailPlaceholder": "Invite by email address", "inviteFirstUser": "Invite your first user", "inviteMessage": "invited you to his/her account as a user.", "inviteMessageRegister": "To accept this invitation please register.", "inviteUsers": "Invite users", "invited": "Invited", "invoice": "Invoice", "invoiceHistory": "Invoice history", "ip": {"errors": {"notValid": "Please, enter a valid IP address", "notValidRange": "Please, enter a valid IP range"}, "inRange": "Within IP range", "notInRange": "not within IP range", "notSpecific": "not from IP address", "specific": "Specific IP address"}, "is": "is", "isNot": "is not", "isSet": "is set", "isThatYou": "Is that you?", "italic": "Italic", "itemSelected": "{num} selected", "items": "Items", "itsNotMe": "It's not me", "joinBeta": "Join B<PERSON>", "jsDocsLink": "https://support.optimonk.com/hc/en-us/articles/*********-Custom-Popup-HTML-Guide#Handling%20conversion%20with%20your%20custom%20method", "jsEditor": "JS editor", "jsEvent": "Event", "jsEvents": {"pageLoad": "Page load", "popupFill": "Popup fill", "popupReject": "<PERSON><PERSON> reject", "popupShow": "Popup shown"}, "jsEventsLabel": "JS events", "jumpTo": "Jump to", "jumpToPage": "Jump to ", "jumpToPageAfter": " page", "keepQueryParams": "Keep URL parameters", "keepQueryParamsTooltip": "If the referrer of the page has URL parameters like https://www.example.com/product?utm_source=facebook, then the parameters are automatically added to the link of the button, in this case the utm_source=facebook.", "keepWinnerRunning": "Keep only the winner variant running", "klaviyoDetection": {"btn": "Add Klaviyo integration", "description": "<p>Connect your Klaviyo account easily, automatically sync your subscribers and send relevant e-mails for your audience. <a href=\"https://support.optimonk.com/hc/en-us/articles/************-Integrating-OptiMonk-Campaigns-with-Klaviyo\">Read more</a></p>", "title": "We see that you are using Klaviyo! "}, "label": "Label", "language": "Language", "large": "Large", "lastLeads": "Last leads", "lastMonth": "Last 30 days", "lastName": "Last name", "lastNameTooLong": "Last name must be maximum {length} characters long.", "lastWeek": "Last 7 days", "latestCampaigns": "Latest", "layout": "Layout", "layoutTooltip": "Layout components are the building blocks of your popups. Each layout component represents a row divided into columns. You need to place elements into columns in your layout.", "layouts": "Layouts", "leads": "Leads", "learnMore": "Learn more", "left": "Left", "leftVisitors": "Left", "lessThan": "less than", "lessThanEquals": "less than or equal to", "letsChat": "Let's chat", "letterSpacing": "Letter spacing", "light": "Light", "limitModify": "modify", "limitPlease": "Please ", "limitReachedAlert": {"non-paying-over-50p": {"line1": {"with_PageViewBasePackage": "You've received {used}/{max} pageviews so far in this month.", "with_VisitorBasePackage": "You've received {used}/{max} unique visitors so far in this month."}, "line2": {"with_PageViewBasePackage": "Upgrade now if you want to extend your pageview limit!", "with_VisitorBasePackage": "Upgrade now if you want to extend your unique visitor limit!"}, "button": "Upgrade"}, "non-flexipay-over-100p": {"line1": {"with_PageViewBasePackage": "You've reached the maximum number of monthly pageviews included in your plan. Your campaigns are now paused.", "with_VisitorBasePackage": "You've reached the maximum number of monthly unique visitors included in your plan. Your campaigns are now paused."}, "line2": {"with_PageViewBasePackage": "Upgrade now if you want to extend your pageview limit and convert more of your visitors!", "with_VisitorBasePackage": "Upgrade now if you want to extend your unique visitor limit and convert more of your visitors!"}, "button": "Upgrade"}, "non-flexipay-over-100p-without-upgrade": {"line1": {"with_PageViewBasePackage": "You've reached the maximum number of monthly pageviews included in your plan. Your campaigns are now paused.", "with_VisitorBasePackage": "You've reached the maximum number of monthly unique visitors included in your plan. Your campaigns are now paused."}, "line2": {"with_PageViewBasePackage": "Please contact us to receive your custom plan with a higher limit.", "with_VisitorBasePackage": "Please contact us to receive your custom plan with a higher limit."}, "button": "Contact us"}, "flexipay-over-capped-amount": {"line1": {"with_PageViewBasePackage": "You've reached the maximum number of monthly pageviews (with FlexiPay) included in your plan. Your campaigns are now paused.", "with_VisitorBasePackage": "You've reached the maximum number of monthly unique visitors (with FlexiPay) included in your plan. Your campaigns are now paused."}, "line2": {"with_PageViewBasePackage": "Please contact us to receive your custom plan with a higher limit.", "with_VisitorBasePackage": "Please contact us to receive your custom plan with a higher limit."}, "button": "Contact us"}}, "limitResetInfo": "reset on ", "limitedIntegrations": "Limited integrations available", "noInputField": "This campaign has no input field", "limitedIntegrationsLead": "The chosen template has no email and/or phone number field. Only a limited number of integrations are available.", "line": "Solid", "lineHeight": "Line height", "linear": "Linear", "link": "Link", "linkText": "Link text", "listName": "List name", "locale": "Account language", "loggedIn": {"no": "not logged in", "yes": "logged in"}, "loggedInAs": "You are currently logged in to <span class=\"font-weight-bold\">{accountName}</span> subaccount.", "loggedInAsAffiliate": "You are currently logged in to <span class=\"font-weight-bold\">{accountName}</span>.", "login": {"avonTestimonial": {"company": "AVON Cosmetics Hungary", "lead": "\"We use OptiMonk on our site since 2015. We can show a popup to every single visitor that helps the user experience or supports the purchase decision.\"", "name": "<PERSON><PERSON><PERSON>", "title": "E-commerce Supervisor"}, "createOne": "Create one", "dontHaveAccount": "Don’t have an account?", "emailPlaceholder": "Email address", "liveTraning": {"cta": "Save your seat", "desc": "Learn how to create your popup campaign from scratch in 30 min and ask all your questions from our popup expert.", "everyThuesday": "Every Tuesday with <PERSON><PERSON>", "live": "Live", "omTraning": "OptiMonk Training"}, "login": "Log in", "loginWithEmail": "Or log in using your OptiMonk account:", "loginWithShopRenter": "Log in through ShopRenter:", "loginWithSocial": "Login with", "passwordPlaceholder": "Enter your password", "privacy": "Privacy", "reset_password": "Forgot password?", "terms": "Terms", "trustedBy": "Trusted by"}, "loginName": "Login name", "loginUrl": "Login url", "luckyWheelInit": {"prizeCodes": ["FREESHIP", "", "50OFF", "", "10OFF", ""], "prizeTitles": ["Free shipping", "So close", "5% off", "No luck today", "10% off", "Spin again"]}, "luckywheel": "Lucky wheel", "luckywheelPane": {"addPrizes": "Add more prizes", "lessPrizes": "Less prizes"}, "mainColor": "Main color", "mainPage": "Main page", "maintenance": "Sorry, we are currently maintaining.<br>Please come back later.", "makeSeasonal": "Make seasonal", "manageInputFields": "Manage input fields", "manageInputFieldsModalsTitle": "In this list, you can see all the input type fields you have created in your account.", "manageUsers": "Manage users", "managedAccounts": "Managed accounts", "manual": "Manual", "manyChat": "ManyChat", "margin": "<PERSON><PERSON>", "marginBottom": "Bottom margin", "marginLeft": "Left margin", "marginRight": "Right margin", "marginTop": "Top margin", "markAsRead": "<PERSON> as read", "markFilledThenRedirect": "<PERSON> filled", "masterPlanBox": {"altTitle": "Ask about Master", "campaigns": "Unlimited campaigns", "domains": "Unlimited domains", "pageView": "Custom amount of pageview", "title": "Need a larger plan?"}, "matchRegex": "matches the regex", "maxDimensions": "Max. dimensions: 650px x 650px", "medium": "Medium", "members": "Members", "menu": {"myPlan": "My plan", "settings": "Settings", "inviteYourTeam": "Invite your team", "signOut": "Sign out", "switchAccount": "Switch account", "upgrade": "Subscription"}, "message": "Message", "messageType": "Message type", "messenger": "<PERSON>", "messengerLoginOff": "Messenger: not logged in", "messengerLoginOn": "Messenger: logged in", "messengerSubscribe": "Messenger subscribe", "migrateRule": "The rule has been upgraded. Please delete and add it again.", "minHeight": "Min-height", "minWidth": "Min-width", "minutes": "Minutes", "mirrorHorizontal": "Mirror horizontal", "mirrorVertical": "Mirror vertical", "mobileAlign": "Align on mobile", "mobileDisplay": "Hide element on mobile", "mobileDisplayTooltip": "With the hide on mobile switch on your elements \n in this row/column won't appear on mobile devices", "mobileMargin": "<PERSON><PERSON>", "mobileOnboarding": {"industries": {"agency": "Agency", "blog": "Blog", "ecommerce": "eCommerce", "saas": "SaaS / Services"}, "inputs": {"labels": {"clients": "How many clients do you have?", "phone": "Phone number", "phoneAgency": "Agency phone number", "type": "What describes your agency?", "website": "Website URL", "websiteAgency": "Agency website"}, "placeholders": {"clients": "e.g. 600", "type": "Select the most fitting option", "website": "www.mywebsite.com", "websiteAgency": "e.g. www.mywebsite.com"}}, "stages": {"established": "I'm above <strong>$20M</strong> annual revenue", "grow": "I'm under <strong>$2M</strong> annual revenue", "scaling": "I'm between <strong>$2-20M</strong> annual revenue", "started": "I just started (no revenue)"}, "subtitles": {"contactMid": "We want your OptiMonk experience to be perfect for your needs. Please, answer a few questions to help us get to know you better.", "welcome": "Please, answer a few questions to help us get to know you better."}, "titles": {"agencyContact": "Where would you like to use OptiMonk?", "contactDesc2": "Just in case we need to help you on your journey", "contactMid2": "Can we get your phone number?", "goals": "What would you like to use OptiMonk for?", "income": "What stage are you in?", "industry": "First of all, please select what describes your business", "welcome": "We want your OptiMonk experience to be perfect for your needs"}}, "mobilePadding": "Padding", "mobilePosition": "Mobile position", "mobileRedirect": {"lead": "Tap on the button below to get notified<br><strong>in email</strong> to return on desktop later", "send": "Continue on desktop", "success": {"cta": "Browse case studies", "lead": "Please continue exploring OptiMonk once you are on desktop. Till then, check out our case studies to see how you can utilize OptiMonk to make the most out of your site.", "title": "Your reminder email is on its way!"}, "title": "OptiMonk's editor<br>is best experienced<br><span style=\"color:#ED5A29\">on desktop</span>"}, "mobileSetting": "Mobile settings", "mobileSettingsWarning": ["To see the effect of these settings ", "switch", " to Mobile view."], "mobileSpacing": "Desktop values applied in mobile version is shown in grey. Setting new values will affect only the mobile version.", "mobileStyles": "Mobile styles", "mockLayout": "View", "mode": "Mode", "modify": "Modify", "modifyProduct": "Modify product", "month": "month", "monthlyPageViews": "Monthly Pageviews", "months": {"april": "April", "august": "August", "december": "December", "february": "February", "january": "January", "july": "July", "june": "June", "march": "March", "may": "May", "november": "November", "october": "October", "september": "September"}, "more": "more", "multiCampaigns": {"selectDevice": "Select device", "selectDomain": "Select domain", "selectStatus": "Select status", "selectStatusPopperText": "Select the status to quickly retrieve your archived campaigns.", "selectStatusPopperTitle": "Looking for the archives?", "tooltip": {"statistics": "Statistics"}}, "multiSelectLimitText": "and {count} more", "multiselect": {"labels": {"deselect": "Remove", "placeholder": "Select option", "selected": "Selected", "tag": "Create tag"}, "slot": {"noOptions": "List is empty"}}, "myCustomClass": "my-class", "myOption1": "My option %{num}", "name": "Name", "nanobar": "Sticky bar", "needHelp": {"first": {"cta": "Save your seat", "lead": "Join us for a live demo and learn to create high-converting popups in 10 minutes.", "title": "OptiMonk Training + Live Q&A"}, "second": {"cta": "Watch course", "lead": "Learn how to optimize ecommerce conversions with A/B testing, popups, and personalization. ", "title": "Conversion Rate Optimization Course"}, "third": {"cta": "Watch course", "lead": "Watch our on-demand getting started course if you need a quick overview on OptiMonk in just 15 minutes.", "title": "Getting Started Course"}, "title": "Need help?"}, "needSomeHelp": "Need some help with the setup? >", "new": "New", "new-user-password": {"askNewLink": "Ask a new one", "emailPlaceholder": "Email", "emailRequired": "Valid email address is required!", "enterYourEmail": "Enter your email address to reset your password", "forgotYourPassword": "Forgot your password?", "identicalPasswords": "Passwords must be identical.", "invalidToken": "Your reset link has probably expired.", "passwordPlaceholder": "Password (8+ characters)", "repeatPasswordPlaceholder": "Repeat password", "sendResetLink": "Send reset link", "setNewPassword": "Set your new password", "setPassword": "Set password", "startUsingText": "Set your password and start using OptiMonk right away!", "welcome": "Welcome, ", "yourEmailIs": "Your email address is"}, "newCampaign": "New campaign", "step": "Step %{step}", "requestFreeTrial": "Get a demo", "freeTrialRequested": "De<PERSON> requested", "newCampaignFlow": {"accountReady": "Your account is ready, <br> now you can add your first campaign", "add": "Add", "addCampaignName": "Give your campaign a name", "campaignNamePlaceholder": "My campaign", "categories": "Seasonal", "choosePlan": "Choose plan", "content": "Content", "dcDomainSelect": "Enter URL or select a domain", "domain": {"add": "Press Enter or click here", "canChange": "You can change it later", "empty": "Domain input cannot be empty", "invalid": "Invalid domain", "placeholder": "Type in your domain or insert an url", "select": "Select or type in a new domain"}, "domainSelect": "What domain would you like to use {brand} on?", "domainSelectForNewComers": "Add a new domain", "getStarted": "Get started", "hasCampaignsTitle": "Create your new campaign", "lead": "Choose a domain where you'd like to run this campaign and a campaign name. </br> Don't worry, you can change it later.", "needsPlanUpdate": {"withNewPrice": "Continue with my current plan", "withNewPriceShort": "Continue"}, "preview": "Preview", "select": "Downgrade", "selectAnExistingOne": "Select an existing one", "selectCampaignType": "Select campaign type", "selectCurrent": "Select", "selectGoal": "Goal", "selectTemplate": "Done, go to select a template", "templateLead": "Filter our high converting templates by goals, </br> then choose the message type that best fits your website.", "templateTitle": "Select a template and customize it", "upGrade": "Upgrade plan", "upGradeAndActivateOvercharge": "Upgrade and activate FlexiPay", "upGradeWithOvercharge": "Activate FlexiPay", "branching": {"title": "What would you like to do?", "options": {"popup": {"title": "Create a Popup", "description": "Popup, sticky bar, sidemessage, etc."}, "optimize": {"title": "Optimize a Website", "description": "Run A/B tests, personalize, etc."}, "library": {"title": "Browse Tactic Library", "description": "Not sure? Get inspired by 50+ tactics."}}}, "websiteOptimization": {"title": "How would you like to optimize your website?", "upgrade": "Upgrade", "common": {"video": {"preHeading": "What is it"}, "why": {"preHeading": "Why AI?", "title": "Find the best converting headlines & copy<br>without any hassle"}, "how": {"preHeading": "How it works", "title": "Set it up in %{minutes} minutes.<br>Enjoy the benefits forever."}}, "optimize": {"title": "Optimize", "abTest": {"title": "A/B test", "description": "A/B test two or more versions of a page against each other. "}, "splitUrl": {"title": "Split URL test", "description": "Test two different versions of your landing page."}, "sab": {"title": "Smart A/B test", "description": "Find the best converting headlines automatically.", "modal": {"preHeading": "Smart A/B testing", "title": "Save time with fully automated AI-powered A/B testing", "description": "Run automatic A/B tests for any headline, description or CTA text. Just define the versions you want to test, and then let our AI run sequential A/B tests for all these headlines - fully automatically.", "images": ["automated-ab-testing.png"], "video": {"title": "Fully automated, AI-powered A/B testing<br>for your landing pages"}, "why": [{"icon": "window-gauge", "title": "Save time & boost sales", "description": "Use AI to write benefit-driven product descriptions. Save a ton of time for you and your team."}, {"icon": "window-idea", "title": "No CRO expertise needed", "description": "Gain access to advanced CRO tactics effortlessly, without getting bogged down in technicalities."}, {"icon": "continues-deploy", "title": "Set up & forget", "description": "Just set it up once, and let OptiMonk AI find the best performing variant automatically."}], "how": {"image": "how-it-works.jpg", "options": [{"title": "Choose elements to A/B test", "description": "Choose the text you want to optimize in our no-code Visual Editor and let our AI come up with new copy for any test element."}, {"title": "Set up variations", "description": "Review the AI-generated content and edit as needed - shorten it or adjust the tone to match your brand."}, {"title": "Launch automated A/B test", "description": "Watch the AI take care of running A/B tests consecutively while you sit back. Track how each version impacts your conversions and sales."}]}}}, "sppo": {"title": "Product Page Optimizer", "description": "Optimize your product pages with AI-powered copy.", "modal": {"preHeading": "Product Page Optimizer", "title": "Optimize thousands of product pages effortlessly in minutes", "description": "Leverage AI to optimize headlines, descriptions, and benefits, and conduct A/B tests for ideal product pages on a large scale, simultaneously.", "images": ["product-page-optimizer.jpg"], "video": {"title": "Turn product pages into high-converting<br>sales pages - at scale"}, "why": [{"icon": "window-gauge", "title": "Reliable significance", "description": "OptiMonk takes care of everything, guaranteeing that your tests are always reaching statistical significance."}, {"icon": "window-idea", "title": "No CRO expertise needed", "description": "Gain access to advanced tests effortlessly, without getting bogged down in technicalities."}, {"icon": "continues-deploy", "title": "Set up & forget", "description": "Just set it up once, and let OptiMonk AI find the best performing variant automatically."}], "how": {"image": "how-it-works.jpg", "options": [{"title": "Insert dynamic headlines and benefit lists", "description": "Let our AI come up with new Smart Elements. Insert them into your product page in our no-code Visual Editor. No coding, just a few clicks."}, {"title": "Review AI generated content", "description": "Review the AI-generated content and edit as needed - shorten it or adjust the tone to match your brand."}, {"title": "Launch automated A/B test", "description": "Watch the AI take care of optimizing your product pages while you sit back. Track how each version impacts your conversions and sales."}]}}}, "smartPopup": {"modal": {"preHeading": "Smart Popups", "title": "No more generic popups, show a personalized popup for each visitor", "description": "Monetize your traffic better by tailoring the messaging of your welcome and exit popups to each visitor's interest automatically.", "images": ["smart-popup.jpg"], "video": {"title": "Display personalized popups without any<br>manual work and maximize conversions"}, "whyTitle": "Forget about<br/>one-size-fits-all popups", "why": [{"icon": "popup-conversion", "title": "Double popup conversion rates", "description": "Unlock the true potential of your website traffic."}, {"icon": "scaleable-landing", "title": "Scaleable to hundreds of landing pages", "description": "Personalize your popup for hundreds of landing pages at once."}, {"icon": "automated-personalization", "title": "Fully automated personalization", "description": "Use the power of AI & experience the future of personalization."}], "how": {"video": "how-it-works.mp4", "title": "Set it up once.<br>Enjoy the benefits forever.", "options": [{"title": "Insert the smart elements to your popup", "description": "Decide where you want to insert the smart elements on your popup. You can add interest-based headlines and subheadlines as well."}, {"title": "Set up the targeting and triggering conditions", "description": "Configure the targeting and triggering conditions of your popup the same way as you would do with a non-personalized popup."}, {"title": "Launch your campaign and watch your results grow", "description": "Watch the magic unfold as personalization takes place automatically and measure the impact of these changes on your conversions easily."}]}}}, "multiCampaignABTest": {"title": "Multi-campaign A/B test", "description": "Test campaign combinations against each other."}}, "personalize": {"title": "Personalize", "personalizer": {"title": "Personalization", "description": "Create a personalized version of a page for specific segments."}, "smartPersonalizer": {"title": "Auto Personalization", "description": "Automatically personalize your landinge pages for your ads.", "modal": {"preHeading": "Auto Personalization", "title": "Boost PPC results with real-time ad - landing page synchronization", "description": "Tailor landing pages to ads in real-time to boost relevance and increase PPC conversion rates. – 100% automatically.", "images": ["auto-personalizer.jpg"], "video": {"title": "Tailor landing page headlines dynamically to ad keywords<br>and boost your ROAS by up to 200%"}, "why": [{"icon": "window-dashboard", "title": "Boost ROAS by up to 200%", "description": "Boost relevancy & conversions by matching messaging to Google Ads automatically,"}, {"icon": "window-gauge", "title": "No CRO expertise needed", "description": "Gain access to advanced CRO tactics effortlessly, without getting bogged down in technicalities."}, {"icon": "continues-deploy", "title": "Set up & forget", "description": "Just set it up once, and let OptiMonk AI find the best performing variant automatically."}], "how": {"image": "how-it-works.jpg", "options": [{"title": "Choose elements to personalize", "description": "Let our AI come up with new Smart Elements. Insert them into your product page in our no-code Visual Editor. No coding, just a few clicks."}, {"title": "Preview personalized version", "description": "Review the AI-generated content and edit as needed - shorten it or adjust the tone to match your brand."}, {"title": "Launch auto personalization", "description": "Watch the AI take care of optimizing your product pages while you sit back. Track how each version impacts your conversions and sales."}]}}}}, "book": {"steps": [{"title": "De<PERSON> requested"}, {"title": "Find a time to meet", "details": {"title": "Schedule a personalized discovery call to explore OptiMonk AI", "subtitle": "We’ll meet you over screen share for 30 minutes to:", "options": ["Understand your business needs", "Give you a personalized walkthrough of the features", "Show how our AI-powered features can grow your business"]}}]}}, "codeInsert": {"sab": {"fallback": "To set up an Automated A/B Test you need to connect your website", "shopify": "In order to set up an Automated A/B Test you need to enable the OptiMonk App Embeds on your Shopify store.", "wordpress": "In order to set up an Automated A/B Test you need to install the OptiMonk plugin on your WordPress site."}}, "dc": {"verification": {"title": "Domain verification", "inflight": {"description": "We check that the OptiMonk JavaScript code is inserted into %{domain}<br>This may take a few seconds"}, "fail": {"description": "In order to use this campaign type first you will need to integrate OptiMonk with %{domain}"}, "done": {"description": "All set!<br>We are creating your campaign"}}}}, "newField": "New field", "newGlobalSetting": "New global setting", "newIntegration": "Add other integration", "newNamePlaceholder": "Type name here", "newPassword": "New password", "newPasswordAgain": "New password again", "newSubAccount": "New sub-account", "newTemplate": "New template", "newVariant": "Add A/B test variant", "next": "Next", "nextPayMent2": "Next payment", "nextPayment": "Next payment", "nextPopup": "Next page", "copyCoupon": "Copy coupon code", "no": "No", "noActiveABTest": "There's no active A/B test running.", "noFeedbackAction": "Please select an after click action or insert a button.", "noInputFields": "The chosen template has no input fields.", "noLeads": "You have no leads yet", "noLimit": "No limit", "noRepeat": "No repeat", "noResults": "No results", "noStatistics": "Your statistics will be seen here right<br> after you installed Optimonk", "noStatisticsShopify": "Your statistics will be seen here right after<br> launching your first campaign.", "nonWizardFilter": {"displays": {"dynamicContent": "Dynamic content", "embedded": "Embedded", "full_screen": "Full screen", "gamification": "Gamification", "popup": "Popup", "sidemessage": "Sidemessage", "sticky_bar": "Sticky bar", "survey": "Survey"}, "goals": {"build_your_list": {"desc": "Turn your early-stage visitors into email, phone or messenger leads.", "title": "Build your list"}, "collect_feedback": {"desc": "Improve your website experience with valuable customer insights.", "title": "Collect feedback"}, "guide_your_visitors": {"desc": "Help your visitors find the best offers or the right product.", "title": "Guide your visitors"}, "promote_special_offers": {"desc": "Turn website visitors into buyers by promoting your best deals.", "title": "Promote special offers"}, "recommend_products": {"desc": "Personalize visitor's experience by recommending relevant products.", "title": "Recommend products"}, "stop_cart_abandonment": {"desc": "Show a secondary offer to visitors who are about to leave their cart.", "title": "Stop cart abandonment"}}, "title": {"display": "Choose your campaign type", "goal": "Choose your goal"}}, "none": "None", "noneOfTheItems": "none of the items in the cart", "normal": "Normal", "notAllowedToModifyToggle": "This function is not available, because the campaign is scheduled. To change the status of the campaign please delete the campaign schedule.", "notContains": "doesn't contain", "notEndsWith": "doesn't end with", "notEnoughData": "Unfortunately, we don't have enough data yet", "notEquals": "doesn't equal", "notMatchRegex": "Doesn't match (regular expression)", "notSet": "isn't set", "notStartsWith": "doesn't start with", "notSufficient": "Not sufficient", "notValidFormat": "Not valid format.", "note": "Note", "nothingToShow": "Nothing to show, yet :(", "notifications": {"activeDomainLimitReached": "You reached the maximum number of active domains, please inactivate one at least.", "addError": "Error while adding", "addSuccess": "Added successfully", "affiliateMinimumPayout": "Minimum payout not reached!", "alreadyInvited": "User has already been invited", "alreadyInvitedOrAdded": "User already invited or added", "archiveError": "Error while archiving!", "archiveSuccess": "Archived successfully!", "generationStarted": "Generation started", "generationNotStarted": "Generation not started", "publishStarted": "Publish flow started", "publishNotStarted": "Publish flow not started", "alreadySelected": "Deletion is not possible because it is selected!", "apiConnectError": "Could not establish connection with the API", "campaignActivationDomainLimitReached": "The domain of this campaign is currently inactive. You need to inactivate another domain first to activate this campaign.", "campaignDomainChangeDomainLimitReached": "The selected domain is currently inactive, please inactivate the campaign or activate the domain first.", "cancelConfirm": "We will be very sad if you leave us.<br/>Will you continue your success story with us?", "clipboardCopyError": "Error while copying to clipboard.", "controlVariantAddError": "Error while creating control variant.", "copyVariantError": "Error while copy variant.", "couponActivated": "Coupon successfully activated", "couponInvalid": "Coupon cannot be applied!", "dailyLimit": "Daily email send limit reached.", "deleteError": "Error while deleting!", "deleteSuccess": "Deleted successfully!", "domainActivationFailed": "An error ocurred while changing domain active status.", "domainAlreadyExists": "This domain already exists. Please select it from the list, or add new by typing.", "domainLimitReached": "Active domain limit reached. Additional domains will be added with inactive status.", "domainRemoveError": "Error while removing domain", "domainRemoveSuccess": "Domain successfully removed", "domainUsed": "Could not remove. Domain is used in %{count} campaigns", "editError": "Error while editing", "editSuccess": "Edited successfully", "elementIdCopied": "Element ID copied to clipboard", "emailSent": "Email successfully sent", "errorWhileImageProcessing": "An error occurred while processing image.", "errorWhileUploadingImage": "An error occurred while uploading image.", "feedbackSendSuccess": "Thank you for your review!", "fileSizeLimit": "File size limit reached", "fillAllFields": "Please fill all fields", "htmlValidationError": "HTML is invalid", "incorrectCurrentPassword": "Current password isn't correct", "insertCodeCopyError": "Error while copying to clipboard", "insertCodeCopySuccess": "Copied to clipboard", "invitationError": "Error while inviting", "invitationResendError": "Error while resending invitation", "invitationResendSuccess": "Invitation successfully resent", "invitationSuccess": "Invited successfully", "limitReached": "Limit reached, please try again later", "loginError": "Wrong email or password", "loginSuccess": "Successful log in", "masterRequest": "Thank you! We will contact you as soon as possible!", "newIntegrationFlow": {"missingRequiredFields": "Please bind the required fields"}, "newPasswordSetAndLogin": "You have successfully changed your password.", "noAccountFound": "No account found with this email address", "noDataInRange": "No data in the given range", "noTrigger": "You haven't added any trigger event to the campaign, so it won't appear. Are you sure you want to continue?", "oldCampaignCopyError": "Cannot duplicate old campaign", "onlyOwnerCanInvite": "Only the owner of the account can invite additional users.", "payoutSuccess": "Payout request successfully submitted!", "recentlySent": "Recently used this feature, try again later.", "registerError": "Something went wrong. Please try again later", "somethingWentWrong": "Something went wrong. Please try again later", "removeError": "Error while removing", "removeSuccess": "Removed successfully", "renameDomainAlreadyExists": "This domain has already been added", "renameError": "Error while renaming", "renameSuccess": "<PERSON><PERSON> successfully", "saveError": "Error while saving", "saveSuccess": "Saved successfully", "segments": {"nameExists": "Segment name exists"}, "selectOrAddDomain": "Please select or add a domain", "sendChangeEmail": "We sent an email to %{email} to verify it.", "sendError": "Error while sending", "sendSuccess": "Successfully sent", "shopifyAppExtension": {"domainAccountMismatch": "Cannot toggle app extension.", "failed": "Cannot toggle app extension.", "nonShopifyDomain": "Cannot toggle app extension on non Shopify domain.", "success": "Successfully toggle app extension."}, "subscriptionRetriedNoSuccess": "Subscription error still exists, please resolve the issues.", "subscriptionRetryError": "An error ocurred when we tried to resend this subscription.", "subscriptionRetrySuccess": "Successfully resent this subscription", "successCancel": "Successful cancellation :(", "successDowngrade": "Successfully downgraded", "successShopify": "Successful pay via Shopify", "successfulOrder": "Successful order", "tagUsed": "Tag is used in %{count} templates.", "unsuccessfulUsageReport": "Unsuccessful report sending", "unsupportedFileFormat": "Unsupported file format", "updateBilling": "Please edit your billing information!", "urlCopied": "URL copied to clipboard!", "validationError": "Please correct the marked fields", "domainContext": {"saveSuccessful": "Domain context saved successfully", "saveError": "Unexpected error, could not save domain context", "generateSuccessful": "Domain context generated successfully", "generateError": "Error while generating domain context"}, "campaignShare": {"error": "Unexpected error during campaign share", "success": "Campaign shared! Link copied to your clipboard!"}, "leads": {"exportSuccess": "Export is processing. You’ll receive an email to {email} once it’s complete.", "exportError": "Leads export failure"}, "campaignRestore": {"restoreFailed": "Campaign restore failed", "restoreSuccess": "You have successfully restored the campaign"}, "klaviyoInstallSuccess": "Klaviyo integration successfully installed!", "klaviyoInstallError": {"unauthorized": "Klaviyo install error: Unauthorized"}}, "notifyMe": {"accepted": "You have accepted the invite", "goToSales": "Go to OptiMonk.com", "label": "Get lead notification via email when someone subscribes", "notification": "The notifications should start arriving soon.", "status": {"pending": "Pending"}, "inputPlaceholder": "Add new email"}, "nrOfProducts": "No. of products", "number": "Number", "numberColor": "Number color", "numberOfPages": "Number of pages", "oauth": {"connect": "The following app is requesting access to your account:", "permission": "It will be able to:", "permissions": {"readSubscribers": "read your campaign subscribers", "writeSubscribers": "write your campaign subscribers"}}, "off": "Off", "ok": "Ok", "okItsDone": "Ok it's done", "oldEditorTermination": {"backToCampaign": "Back to your campaign", "callout": "For a faster, richer and more enjoyable design experience start using our new Drag’n’Drop editor today.", "contact": "We will contact you within 1 business day.", "createNew": "Create a new campaign", "features": {"1": "Drag’n Drop", "2": "100% customizable", "3": "Responsive design", "4": "150+ prebuilt templates", "5": "Brand new features"}, "goOnUsing": "Go on using the old editor in the meantime", "lead": "You’re using an outdated version of our editor which won’t be supported soon. For you, it means that your campaign will be displayed but cannot be edited after 15 July.", "lead1": "You’ve created this campaign with an outdated version of our editor which is not supported after July 15, 2019. For you, it means that your campaign will be displayed but cannot be edited anymore.", "letUsDo": "Let us do it for you", "thankYou": "Thank You for your interest!", "title": "Your campaign is not up-to-date.", "viki": "<PERSON><PERSON> from OptiMonk"}, "on": "On", "onAll": "On all", "onClick": "On click", "onDevice": {"desktop": "Desktop", "desktop_and_mobile": "Desktop or mobile", "mobile": "Mobile"}, "onExistingDomain": "On an existing one:", "onInactivity": "On inactivity", "onLoad": "On load", "onNewDomain": "On a new domain:", "onScroll": "On scroll", "onboarding": {"aiSuccess": {"title": "You successfully requested the access to the new OptiMonk AI. 🎉", "text": "We will get back to you on the next workday, don’t forget to check your emails."}, "affiliate": {"cards": {"phone": "Let me talk to my<br> dedicated partner manager", "referral": "I want to see the <br><PERSON> dashboard", "ui": "I’d like to test OptiMonk first,<br> show me the user interface"}, "lead": "Can’t wait to chat with you!<br>Please select a time that works best for you:", "title": "Hi, I’m <PERSON><PERSON><PERSON>, your dedicated partner manager."}, "afterQualificationLead": "Awesome! Let me show you what you're going to be able to do with OptiMonk", "agency": [{"children": [{"lead": "Access, manage, and analyze all your clients campaigns from one single OptiMonk account.", "title": "Manage multiple accounts"}, {"lead": "Promote your own, branded template collection landing page, where you can highlight your favorite templates and introduce yourself.", "title": "Personalized landing page & popup collection"}, {"lead": "Create your own private templates and use cases only your clients can access. Modify permissions if you want to share templates with specific clients or everyone in your agency.", "title": "Provide private VIP templates"}], "title": "Special features just for agencies"}, {"children": [{"lead": "Promote OptiMonk subscriptions with your unique referral link and get up to 30% share of our revenue.", "title": "Ambassador Program", "url": "https://www.optimonk.com/ambassador-partner/"}, {"lead": "Expand your expertise by helping your clients to grow their business and manage their accounts. Resell or refer OptiMonk to your clients and earn 30% life-time recurring commissions.", "title": "Become a Certified Partner", "url": "https://www.optimonk.com/certified-partner/"}], "title": "Two ways to become an OptiMonk partner"}, {"children": [{"lead": "Collect email, messenger or sms list effectively. Make visitors sign up and convert them into sales later.", "title": "List building"}, {"lead": "Detect potential buyers who are about to leave without making a purchase and show a secondary offer to prevent cart abandonment.", "title": "Stop cart abandonment"}, {"lead": "Use OptiMonk's dynamic algorithm to personalize recommendations so visitors see products they're actually interested in.", "title": "Product recommendation"}], "lead": "Impress your clients with higher-conversion rates and impressive revenue growth.", "title": "3 Ways you can use OptiMonk to help clients grow their business"}, {"children": [{"buttonLabel": "Book a call", "title": "Learn more about the Agency account and Partner Program", "url": "https://www.optimonk.com/certified-partner/", "urlLabel": "More info about our Certified Partner Program"}, {"buttonLabel": "Test Optimonk", "title": "Test OptiMonk first as a regular user"}], "title": "Ready? Here are two ways to get started:"}], "agencySubpage": {"booking": {"backButton": "Did you change your mind?", "title": "Great, please select the time that works best for you!"}, "otherPageText": "Please tell us what best describes your business.", "subTitle": "Select the most fitting option.", "title": "Which best describes your agency?"}, "aiFeature": {"title": "What would you like to do first?", "aiTitle": "Explore automated conversion<br>optimization with OptiMonk <span class=\"ai\">AI</span>", "aiButton": "Discover OptiMonk AI", "popupTitle": "Create your first<br>popup campaign", "seeHowItWorks": "See how it works", "requestEarlyAccess": "Request early access", "notInterested": "I'm not interested", "requestConfirmed": "Request confirmed", "smartPersonalizer": {"title": "Smart Personalizer", "heading": "Paid ads supercharged with real-time, 1-to-1 personalization for any landing page", "description": "Personalize your landing pages for each and every visitor. Tailor any headline, description or text based on the interest of your visitor – 100% automatically."}, "smartAbTesting": {"title": "Smart A/B Testing", "heading": "Boost website conversions with fully automated, AI-assisted A/B testing", "description": "Forget time-consuming A/B testing. Just pick the elements on your website you want to test, and let our AI do the rest of the job."}, "smartProductPageOptimizer": {"title": "Smart Product Page Optimizer", "heading": "Turn product detail pages into high-converting sales pages with AI-powered copy", "description": "Use the power of AI to create better headlines, descriptions, and benefit lists, and run A/B tests to tailor the ideal product page. And do it for thousands of product pages – parallelly."}, "smartPopups": {"title": "Smart Popups", "heading": "No more generic popups, show a personalized popup for each visitor", "description": "Forget about one-size-fits-all popups. Tailor the messaging of your popups to each visitor’s interest automatically with the help of AI and monetize your website traffic better."}}, "blog": [{"lead": "Setup a pop-up to suggest visitors read your latest or most popular posts", "title": "Promote Any Piece of Content "}, {"lead": "Capture the email or Messenger contact details of visitors not ready to buy, then nurture them into a sale later", "title": "Turn ‘Lookers’ into ‘Leads’"}, {"lead": "Let your visitors spread the word by asking them to follow your social sites or sharing your contents", "title": "Convert 'Strangers' into 'Friends'"}, {"lead": "Take the guesswork out of conversions - automate customer feedback and optimize your store based on their answers", "title": "Get Customer Feedback"}], "customTheme": {"customize": {"buttons": {"reset": "Reset settings", "use": "Use this theme"}, "description": "Don't worry - all settings can be customized later in the editor.", "labels": {"color": "Color", "corners": "Corners", "font": "Font", "name": "Name", "secondaryFont": "Secondary font", "logo": "Logo", "language": "Template language"}, "title": "Quick-tune your brand settings", "goToEditor": "Go to editor"}, "mainColor": "Main color", "pickTheme": {"description": "Themes allow you to showcase your brand and product imagery in different templates. You are not choosing a template at this point.", "title": "Pick a theme you like"}, "pickThemeV2": {"description": "You can customize colors, fonts, and other style settings in the next step.", "title": "Pick a popup theme you like"}, "start": {"description": "Select a color that fits your brand or one that stands out on your store for clear visibility.", "title": "Choose the color of your brand."}, "themeName": "{name} theme"}, "autoPersonalize": {"title": "Should we auto-personalize the branding?", "template": {"original": {"title": "Original", "footer": "No, keep original style"}, "branded": {"title": "Branded", "footer": "Yes, use branded version"}}}, "ecommerce": [{"lead": "Display a special offer to customers who leave the shopping cart without buying", "title": "Rescue Lost Sales"}, {"lead": "Capture the email or Messenger contact details of visitors not ready to buy, then nurture them into a sale later", "title": "Turn ‘Lookers’ into ‘Leads’"}, {"lead": "Take the guesswork out of conversions - automate customer feedback and optimize your store based on their answers", "title": "Get Customer Feedback"}, {"lead": "Show product recommendations based on the interest of your visitor", "title": "Increase the avg. Order Value of Each Customer"}], "goal": {"awesome": "Awesome!", "awesomeLead": "Let me show you the way to launching your first OptiMonk campaign.", "inAHurry": "We understand, you’re in a hurry.", "inAHurryLead": "You can create your first campaign right now, and start saving visitors.", "lead": "But enough about us. Your first campaign is waiting for you, so let’s get down to business...", "options": {"blog": {"facilitate-social-sharing": "Facilitate social sharing", "feedback": "Get valuable feedback", "list-building": "Get more leads", "promote-content": "Promote content"}, "ecommerce": {"cart-abandonment": "Reduce cart abandonment", "feedback": "Get valuable feedback", "list-building": "Get more leads", "upselling": "Sell more products"}, "saas": {"feedback": "Get Customer Feedback", "list-building": "Get More Leads", "promote-landing-pages": "Promote Landing Pages"}}, "question": "What would you like to use OptiMonk for?", "skip": "I’m good for now, take me to my Dashboard", "soundsGood": "Sounds good, right?"}, "nextStep": "Next", "normalLead": "Here’s what you gonna be able to do with OptiMonk", "qualification": {"business": {"options": {"agency": "Agency", "blog": "Blog", "ecommerce": "eCommerce", "saas": "SaaS / Service"}, "question": "First, tell me what best describes your business:"}, "guide": "We want your OptiMonk experience to be perfect for your needs. Answer a few questions to help us get to know you better.", "hi": "Welcome {firstName},", "stage": {"blogOptions": {"established": "My blog is more than 6-year-old", "grow": "My blog is less than 3-year-old", "scaling": "My blog is running between 3 to 6 years", "started": "I just got started"}, "options": {"established": "I’m above $20M annual revenue", "grow": "I’m under $2M annual revenue", "scaling": "I’m between $2-20M annual revenue", "started": "I just got started (no revenue)"}, "question": "What stage are you in?"}, "whyUs": {"options": {"improveConversionRate": "I want to improve my conversion rate", "notSatisfiedWithPopupTool": "I’m not satisfied with my popup tool", "other": "Other"}, "question": "What brings you to OptiMonk today?"}}, "agencyBranching": {"title": "Who are you optimizing this website for?", "subTitle": "Please choose the option which best describes your relationship with {businessName}", "options": {"my-business": {"title": "My business", "description": "This is a website of mine or my business"}, "work-for": {"title": "A company I work for", "description": "I'm an employee of {businessName}"}, "client": {"title": "A client", "description": "I'm an agency/freelancer working for {businessName}"}}}, "agencyContact": {"title": "Whose contact have you registered this account with?", "subTitle": "Please choose either option, so we'll know what kind of messages we should send to this email address:", "options": {"client-contact": {"title": "This is my client's contact info", "description": "Please don't send agency related materials."}, "own-contact": {"title": "These are my own contact details", "description": "You can message me with agency related content."}}}, "agencyDetails": {"title": "Thanks! Please also provide your own contact details", "subTitle": "You'll receive an email with the option to create an agency account and access all your clients from one account.", "modalTitle": "Provide agency contact details", "modalSubTitle": "You'll receive an email with the option to create an agency account and access all your clients from one account.", "firstName": "Your first name", "lastName": "Your last name", "email": "Your email address", "emailPlaceholder": "Email address", "agencyName": "What's the name of your agency?", "agencyNamePlaceholder": "Super Agency", "skip": "Skip for now"}, "saas": [{"lead": "Capture the email or Messenger contact details of visitors not ready to buy, then nurture them into a sale later", "title": "Turn ‘Lookers’ into ‘Leads’"}, {"lead": "Get your latest deals or best-converting free giveaways in front of visitors who are interested in it to drive more sales", "title": "Drive Traffic to Your Top Landing Pages"}, {"lead": "Take the guesswork out of conversions - automate customer feedback and optimize your store based on their answers", "title": "Get Customer Feedback"}], "settingsDone": "Well done. This is gonna be a great campaign :)", "settingsDoneLead": "Let's move on to the final step (you can edit these settings anytime in the future).", "tactic": {"moreTactics": "Check more tactics", "title": "Level up your conversion", "placeholder": "Here you will get tactics to help you boost conversions", "requestAccess": "Get a demo", "subscription": {"title": "You're on the List!", "description": "Your request for early access has been registered.", "description2": "Stay tuned for updates!"}, "smart_a_b_test": {"title": "Smart A/B testing", "description": "Fully automated A/B testing for your homepage or landing pages.", "url": "https://www.optimonk.com/ai"}, "smart_personalization": {"title": "Smart Personalization", "description": "Increase your ROAS with automatic website personalization", "url": "https://www.optimonk.com/ai"}, "smart_popup": {"title": "Smart Popups", "description": "No more generic popups, show a personalized popup for each visitor", "url": "https://www.optimonk.com/ai"}, "product_page_optimizer": {"title": "Product page optimizer", "description": "Turn product pages into high-converting sales pages", "url": "https://www.optimonk.com/ai"}}, "seasonal": {"title": "Seasonal offers", "description": "Increase your sales with seasonal offers", "showTemplates": "Show templates", "black_friday": {"title": "Black Friday templates", "description": "Make the most of Black Friday with stunning, high-converting popup templates."}, "cyber_monday": {"title": "Cyber Monday templates", "description": "Capitalize on the online shopping frenzy with high-converting Cyber Monday popup templates."}, "halloween": {"title": "Halloween templates", "description": "Make your Halloween campaigns impossible to ignore with spooky, high-converting popup templates."}, "christmas": {"title": "Christmas templates", "description": "Capture the festive spirit and boost holiday sales with Christmas-themed popup templates."}, "new_year": {"title": "New Year templates", "description": "Ring in the new year with high-converting popup templates that help you boost engagement, clear out year-end inventory, and kick off January with strong sales."}, "valentin": {"title": "Valentine’s Day templates", "description": "Make hearts (and sales) flutter with romantic, high-converting Valentine’s Day popup templates."}, "super_bowl": {"title": "Super Bowl templates", "description": "Ring in the new year with high-converting popup templates that help you boost engagement, clear out year-end inventory, and kick off January with strong sales."}, "st_patricks": {"title": "<PERSON>’s Day templates", "description": "Get lucky this Saint <PERSON>’s Day with engaging, high-converting popup templates designed to boost sales and maximize engagement."}, "easter": {"title": "Easter templates", "description": "Celebrate the season with engaging Easter popup templates that help you capture leads and drive spring sales."}, "childrens": {"title": "Children’s Day templates", "description": "Make shopping fun and engaging with playful, high-converting Children’s Day popup templates."}, "mothers": {"title": "Mother’s Day templates", "description": "Show appreciation for moms everywhere with beautiful, high-converting Mother’s Day popup templates."}, "summer": {"title": "Summer templates", "description": "Get ready for the sunny season with engaging, high-converting summer popup templates."}, "fathers": {"title": "Father’s Day templates", "description": "Celebrate dads with high-converting Father’s Day popup templates that help you increase engagement and drive sales."}, "july_4": {"title": "4th of July templates", "description": "Celebrate Independence Day with patriotic, high-converting 4th of July popup templates."}, "autumn": {"title": "Autumn templates", "description": "Embrace the season with beautiful, high-converting autumn popup templates designed to capture leads and increase sales."}, "back_to_school": {"title": "Back to school templates", "description": "Help customers gear up for the school season with high-converting back-to-school popup templates."}}, "usage": {"options": [{"icon": "ob-blog", "subtitle": "List building popups, exit popups, fullscreens, sticky bars, gamification & more", "title": "Popups"}, {"icon": "ob-personalization", "subtitle": "Personalize your website or landing page to any customer segment", "title": "Website personalization"}, {"icon": "ob-ab-tests", "subtitle": "Test landing page headlines, popup offers or experiment with complete journeys", "title": "A/B testing"}], "skip": "Not sure, just exploring", "subtitle": "Your answer will help us tailor your onboarding experience.<br>You can select multiple options.", "title": "What do you plan to use OptiMonk for?"}, "welcome": {"btn": "Let's get started", "email": "Your email address", "emailPlaceholder": "Email address", "firstName": "Your first name", "firstNamePlaceholder": "First name", "lastName": "Your last name", "lastNamePlaceholder": "Last name", "text1": "Let’s begin with a few questions to help you get started with OptiMonk.", "title": "Welcome to OptiMonk 🙌", "whatBusiness": "What’s the name of this website?", "whatBusinessPlaceholder": "Awesome Brand", "whatPhoneNumber": "What’s your phone number?", "whatDomain": "Which website would you like to optimize?", "whatDomainPlaceholder": "www.awesomebrand.com", "whatStore": "What’s the name of your store?", "whatStorePlaceholder": "Awesome Brand", "referralSourceLabel": "How did you hear about us?", "referralSourcePlaceholder": "Select one", "referralSources": {"google": "Google search", "shopifyWordpress": "Shopify/WordPress", "chatGPT": "ChatGPT", "youtube": "YouTube", "socialMedia": "Instagram/Facebook", "linkedin": "LinkedIn", "friendColleague": "Friend/colleague", "partnerAgency": "Partner/agency", "alreadyUsedBefore": "Already used before", "other": "Other"}, "pleaseSpecify": "Can you please specify?"}, "wizard": {"bottom": {"button": "Browse our template library", "question": "Can't find the right campaign for you?"}, "choosePath": {"buttons": {"start": "Let's do it"}, "left": {"description": "Just answer a few questions and get tailored campaigns designed for you in minutes", "title": "Let our AI wizard suggest solutions for you"}, "rightBottom": {"description": "Jump straight to your dashboard to explore and create campaigns on your own", "link": "Go to dashboard"}, "rightTop": {"title": "Browse templates", "description": "Browse our best campaign templates and tactics to build your first campaign"}, "expert": {"title": "Do you need any help?", "description": "In case you need some technical support or wish to seek professional advice, our experts are happy to assist, free of charge.", "button": "Request consultation", "confirmTitle": "Request Confirmed:<br>Your consultation is on the way!", "confirmDescription": "Check your email for next steps to book your expert session. While you wait, feel free to browse templates, set up integrations, and explore the dashboard."}, "title1": "You’re all set!  🙌🏻"}, "loading": {"title": "Your campaign plan is being generated<br/> specifically for you"}, "pages": {"buildList": {"description": "Are you engaging customers through email, SMS or both?", "options": {"email": "Email", "emailAndSms": "Email + SMS", "messenger": "<PERSON>", "none": "None", "sms": "SMS"}, "title": "What type of list are you building? (select one)"}, "contentMarketing": {"description": "Do you engage with your audience through social media, blogging, or podcasts?", "title": "Do you do content marketing?"}, "discounts": {"description": "Offering incentives makes it easier for you to get subscribers. Deals, discounts, and free shipping are great ways to motivate visitors to subscribe to your mailing lists.", "title": "Do you offer any discounts?"}, "goals": {"greeting": "Great, let’s get started! 🙌", "title": "First of all, what are your goals?"}, "recommendation": {"title": "Your tailored solution plan for<br>{businessName} is ready! 🙂", "description": "Based on your answers we suggest the following optimization tactics.", "blocks": {"easy": {"title": "Popup campaigns we recommend for {businessName}:"}, "optimize": {"title": "Website optimization campaigns we recommend for {businessName}:"}}, "advanced": {"description": "Maximize your website conversions with our expert Tips & Tricks!", "title": "Recommended advanced tactics"}, "select": "Select", "tacticLibrary": {"button": "Check our Tactic library", "textLine1": "For more tactics visit the Tactic library page", "textLine2": "Maximize your website conversions with our expert Tips & Tricks!"}, "ai": {"blockTitle": "Want to try our new AI powered solutions?", "title": "Personalized popups", "description": "Tailoring the messaging of your welcome and exit popups to each visitor'sinterest automatically."}}, "recurringCustomers": {"description": "Do your customers come back for repeat purchases or do they buy from you once during the course of a year?", "title": "Do you have repeat customers throughout the year?"}, "seasonalSales": {"description": "Seasonal events like Christmas and Black Friday are the perfect way to increase your sales.", "options": {"fewTimes": "Yes, few times a year", "no": "No", "yes": "Yes, regularly"}, "title": "Do you do seasonal sales?"}}, "progress": {"customize": {"title": "Customize", "tooltip": "Finalize your campaign with the drag and drop editor."}, "themeKit": {"title": "Theme setup", "tooltip": "With Themes, you’ll have a template ready for all possible campaign goals right when you need it."}, "useCase": {"title": "Campaign plan", "tooltip": "Select a campaign type eg.: Conversational popup, Cart abandonment stopper, Welcome back recommender and many more."}, "yourBusiness": {"title": "Your business", "tooltip": "Simple questions about your business."}}, "skipToDashboard": "Skip to dashboard", "skipToTemplates": "Skip to templates"}}, "oneFeedbackPerPage": "One feedback element is allowed per page.", "onlyNeedThese": "I only need these", "onlyNewLead": "Block existing subscribers", "opacity": "Opacity", "openInNewTab": "Open in new tab", "openIt": "Open", "optInFeatures": {"label": "Optional features"}, "optimizeYourCampaign": "Optimize your campaign, test multiple variants or versions!", "option": "Option", "optionAlign": "Options align", "optionHeight": "Option height", "optionWidth": "Option width", "optional": "optional", "options": "Options", "or": "or", "orAddANewOne": "or add a new one", "orConnection": ", or", "orOperation": "OR", "orSignOut": "or sign out", "order": "Order", "orderId": "Order ID", "orderSummary": "Order summary", "orderedPackage": "Ordered package", "orientation": "Orientation", "original": "Original", "other": "Other", "otherBusinessPlaceholder": "Please tell us what it is if none of the above", "others": "Others", "ourInvoiceDetails": {"address": "4028 Debrecen, Kassai út 129.", "name": "Optimonk International Zrt.", "title": "OptiMonk invoicing information", "vat": "26335498-2-09"}, "overallCampaign": "Overall campaign statistics", "overflow": "Overflow", "overlay": "Overlay", "overlayColor": "Overlay color", "owner": "Owner", "package": "Package", "padding": "Padding", "page": "Page", "pageElements": "Page elements", "pageId": "Page ID", "pageRename": "Rename page", "pageSize": "Page size", "pageStructure": {"columnCenter": "Center column", "columnLeft": "Left column", "columnRight": "Right column"}, "pageUrl": "Page url(s)", "pageViewTotal": "* 'Pageviews' is the total number of pages viewed on your site", "pageViewerType": {"firstTimeVisitor": "New visitors", "recurrentVisitor": "Returning visitors"}, "pageViews": "Pageviews", "pageWithIndex": "Page {index}", "pages": "Pages", "palette": "Palette", "partnerInfo": "Partner Information", "partnerUrl": "URL slug", "password": "Password", "passwordLength": "Password must have at least {length} characters.", "passwordMismatch": "Password doesn't match.", "passwordStrength": "Password strength", "passwordTooLong": "Password must be maximum {length} characters long.", "pay": "Pay", "payment": {"amountMayChange": "The amount may change until the last day of the billing month", "bank_transfer": "Bank transfer", "banktransferWithPrice": "Bank transfer", "basicPv": "Basic pageview", "braintree": "Credit/Debit card", "braintreepaypal": "PayPal", "deleteFailed": "Error occurred when deleting payment methods!", "deleteInactiveCards": "Delete inactive card(s)", "deleteSuccess": "Deleted successfully!", "error": {"customer-payment-error": "Card authorization error occurred ({message}). Please select other payment option!"}, "existingBankTransfer": "With this transaction we will charge you with the amount of your plan. Because of your existing subscription your next payment is due {dateExpiresOn}", "modal": {"bankTransferChangePlan": "Bank transfer", "changePaymentAndPlan": "Bank card", "changePlanTitle": "The automatic plan change is only possible with credit card payment. Do you want to change your payment method?", "thankYou": "Thank you, we are going to contact you shortly"}, "none": "None", "notification": {"deprecatedProforma": "If you have pending proforma invoice, please note that it became deprecated as you are paying with card."}, "shopify": "Shopify", "shopifyDiscount1": "Please note that you already accepted the one-time payment for our offer.", "shopifyDiscount2": "Here you can allow us to automatically renew your subscription:", "uniqueVisitor": "Unique visitor"}, "paymentDetails": "Payment details", "paymentFee": "Payment fee", "paymentMethod": "Payment method", "paymentProblem": {"bankTransfer": {"default": "According to our records, your wire transfer did not arrive yet, please check your records.", "modalTitle": "Payment problem"}, "braintree": {"2000": "There was a problem with your card payment.<br/>Please retry the transaction or use a different payment method.", "2001": "There was a problem with your card payment.<br/>Please check your balance, limits or use a different payment method.", "2004": "There was a problem with your card payment.<br/>Your card is expired. Please use a different payment method.", "2047": "There was a problem with your card payment.<br/>Please use a different payment method.", "2099": "There was a problem with your card payment.<br/>Please retry the transaction or use a different payment method.", "default": "There was a problem with your card payment.<br/>Please check your balance, limits or use a different payment method.", "modalTitle": "Payment failed"}, "button": {"downloadProforma": "Download proforma", "updatePaymentMethod": "Update payment method"}, "shopify": {"default": "There was a problem with the payment regarding your Shopify shop, please check it on Shopify Pay.", "modalTitle": "Payment problem"}}, "paypal": "PayPal", "pending": "Pending", "pendingCoupons": "Pending coupons", "percentage": "Percentage", "performance": "Performance", "personalDetails": "Personal details", "personalizedExperiences": {"buttons": {"readMore": {"link": "https://support.optimonk.com/hc/en-us/articles/8805751837202", "title": "Read more"}, "start": "Start personalization", "watch": {"link": "https://www.youtube.com/watch?v=bCs8dIeHru8", "title": "Watch a video"}}, "details": "Create unique experiences for sub-segments of your campaign's target audience, for example, by changing the language depending on the country, or showing a higher discount to visitors with higher cart values.", "title": "Personalized experiences"}, "phoneCountryAutoDetectTooltip": "Detecting country of the visitor. Fallback is USA.", "phoneNumber": "Phone number", "pickAPresent": {"colors": {"box": "Box color", "ribbon": "Ribbon color", "shadow": "Shadow color"}, "defaults": {"coupon": "COUPON100", "subtitle": "Give your email address to active the presents.", "title": "Want to win a GIFT?"}, "mobile": {"subtitleFontSize": "Subtitle font size", "titleFontSize": "Title font size"}, "noButton": "You have to insert a button for the correct behaviour.", "presentCount": "Present count", "presentSize": "Present size", "settings": {"button": "Coupon settings", "title": "Pick a present coupon settings"}, "subtitle": "Subtitle", "test": {"button": "Test", "title": "Pick a present demo"}, "title": "Title"}, "pickATemplate": "Pick a template", "pickATemplateByGoal": "Pick a template to {goal}", "pickapresent": "Pick a present", "placeContent": "Place content", "placeholder": "Placeholder", "plan": {"annual": "Annual billing", "cancel": "Cancel subscription", "current": "Current", "details": "Plan details", "extraPv": "extra pageview", "extraUniqueVisitor": "extra unique visitor", "masterEnterprise": {"contact": "To modify your plan please contact our ", "support": "support"}, "month": "mo", "monthly": "Monthly billing", "monthlyShort": "month", "nextBillingDate": "Next billing date", "activateCouponInShopify": "Activate in Shopify", "notifications": {"expiredAccount": {"button": "GO TO CHECKOUT", "message": "Your account expired on {datePaid} so we cannot display your campaigns since then. Please reactivate your subscription!"}, "expiredPlan": {"button": "GO TO CHECKOUT", "message": "Your subscription has expired on {datePaid}! Please, reactivate your subscription within {remainingDays} days!"}, "maximumTotalUpdate": {"button": "Update", "message": "Please, update the App spending limit (to {maximumTotal}) of your current subscription for FlexiPay: "}, "overChargeUpgrade": {"button": "Activate", "message": "Please, activate FlexiPay with your current subscription:"}, "nextBillingCoupon": {"message": "Good news! We've applied a special discount to your subscription, lowering your {billingCycle} payment.", "approved": {"message": "Your <b>{couponName}</b> discount will be applied starting from your next payment.", "period": "Discount validity: {couponStart} to {couponEnd}."}, "pending": {"message": "Please, review the updated payment details and then approve the changes.", "button": "Activate discount"}}}, "oneTimePayment": "One-time payment", "plan": "Plan", "plan2": "plan", "reason": "Reason", "recurringPayment": "Recurring payment", "select": "Select", "success": "Thank you for your order, your prepayment request has been sent to your email!", "twoMonthsFree": "2 months free with annual", "unlimited": "Unlimited", "whyLeave": "Why are you leaving us?", "year": "year", "yearly": "Annual (20% off)", "yearlyShort": "year", "pvVariantOptionText": "{pageViews} pageviews / month", "recPvVariantOptionText": "{pageViews} pageviews / month *"}, "planDescription": "Unlimited campaigns<br/>\n{noOfVisitors} visitors / month<br/>\nMax {maxDomains} domain", "planDetailTitles": {"aBlock": "AdBlock detection", "abTesting": "A/B testing", "basicFeatures": "Basic features", "cartRules": "Cart rules", "cookie": "Cookie segmentation", "customVariable": "Custom variable", "ipBlock": "Block IP addresses", "jsEvent": "JavaScript event trigger", "linkOptiMonkCampaigns": "Link OptiMonk campaigns", "pageViews": "Pageviews", "price": "Price", "prioritySupport": "Priority support", "sites": "Domains", "totalPageViews": "Total pageviews", "totalUniqueVisitor": "Total unique visitors", "unbranded": "Unbranded", "visitor": "Unique visitors", "allFeatures": "All features", "unlimitedCampaigns": "Unlimited campaigns"}, "planMisc": {"campaign": "campaign", "domains": "domains", "domain": "domain", "increasedPrice": "{price} starting November 2nd", "max": "<PERSON>.", "notSufficientDescription": "Based on your traffic in the previous months, you would be running out of pageviews with this plan in about <b>%{remainingDays}</b> days.", "notSufficientDescriptionPassed": "Based on your traffic in the previous months, you would be running out of pageviews with this plan.", "sitesDescription": "Every domain or subdomain counts as a \"domain\". For instance, if your main domain is company.com, then blog.company.com and shop.company.com will both be a subdomain to that and they qualify as 3 separate domains in this case."}, "planRecommendationItems": {"datePassed": "With %{numPageviews} pageviews in the last %{spentDays} days <b>you reached your pageview limit of your current plan</b>.Therefore we recommend upgrading to a plan that better fits your needs.", "text": "In the last %{spentDays} days you had %{numPageviews} pageviews which means <b>you'll reach the pageview limit of your current plan in %{remainingDays} days</b>. Therefore we recommend upgrading to a plan that better fits your needs."}, "planSetting": {"notSet": "Not set"}, "planUpgradeInfoBox": {"description": "Don't worry, we'll save you the time-proportional price difference.", "title": "Switching from an existing plan?"}, "pleaseContact": "Please contact with the support team.", "popup": "Popup", "position": "Position", "postCode": "Postcode", "postalCode": "Postal code", "prefix": "Prefix", "preview": "Preview", "previewAndSave": "Preview & Save", "previewError": {"missing": {"content": "There was no measured traffic on your site, OptiMonk is most likely not integrated and the preview won't be displayed."}, "old": {"content": "There was no recent traffic measured on your site. If OptiMonk is not integrated properly, the preview might not be displayed."}, "openSite": "Open my site anyway", "title": "Warning"}, "previewModal": {"sliders": {"advancedTactics": "Advanced tactics", "moreFromTactics": "More templates from this tactic", "moreFromTheme": "More templates from this theme", "moreFromUseCase": "More templates from this use case"}, "whenToUseIt": "When to use it?", "skipToEditor": "Skip to Editor", "customizeTemplate": "Customize theme", "goToEditor": "Go to Editor"}, "previewMode": "Preview mode", "previous": "Previous", "previousMonth": "Previous month", "previousOrders": "Previous orders", "price": "Price", "priceChangeAlert": {"gracePeriodForPriceChange": {"line1": "Your package price have changed on November 2nd, and you need to accept it to continue using your OptiMonk plan. You will be charged the new amount at your next billing cycle.", "redirect": "Continue with my current plan"}, "gracePeriodForPriceChangeExpired": {"line1": "You did not accept the price changes of November 2nd yet, and your account is restricted at the moment, but the campaigns did not stop running. To lift restrictions and continue using OptiMonk, please click on “Continue with my current plan”. You will be charged the new amount at your next billing cycle."}, "gracePeriodForPriceChangeExpiredForUserWithFuturePackage": {"line1": "You did not accept the price changes, and your account is restricted at the moment, but the campaigns did not stop running. To accept the price change, lift restrictions and continue using OptiMonk, please scroll down and click on the orange \"Select\" button."}, "priceIncreaseDashboardAlert": {"headline1": "On November 2, we’ll be introducing new pricing plans that will reflect the needs of our clients better while allowing us to keep improving the service and developing new features. <a class=\"cursor-pointer\" href=\"{viewDetailsLink}\">View details</a>", "title": "Updating prices to bring you more"}, "shopify": {"line1": "The prices of OptiMonk plans have changed on November 2nd, 2022. To accept the price change, please click on “Continue with my current plan”. You will be charged the new amount at the beginning of your next billing cycle.", "redirect": "Continue with my current plan"}, "shopify2024": {"line1": "The prices of OptiMonk plans have changed on December, 2024. To accept the price change, please click on “Continue with my current plan”. You will be charged the new amount at the beginning of your next billing cycle.", "redirect": "Continue with my current plan"}}, "privacyPolicyLink": "Make it a link", "product": "Product", "productCatalog": {"cartRuleTitle": "Target visitors based on the products in their cart", "changeVariant": "Change Variant", "remove": "Remove", "select": "Select", "selectButton": "Select product", "selectVariant": "Select Variant", "title": "Choose product"}, "productFilterLabels": {"most-viewed": "Most popular of"}, "productFilters": {"most-viewed": {"category": "Given category", "none": "Whole store", "viewed-category": "Each category page"}}, "productModes": {"last-visited": "Recently viewed", "manual": "Manual", "most-viewed": "Most popular", "smart-recommendation": "Shopify recommender", "smart-recommendation-related": "Shoprenter recommender related", "smart-recommendation-similar": "Shoprenter recommender similar", "optimonk-recommender": "OptiMonk recommender", "products-in-the-cart": "Products in the cart"}, "productPane": "Product card", "productPaneTooltips": {"abscenceOfButton": "In the abscence of a button the product component redirects to the corresponding product page by default.", "notDisplayedIfNoEnoughProducts": "If there aren't enough products to recommend, the popup will not be displayed to the visitor.", "showOutOfStock": "Visitors might see popups that contain out-of-stock products when the products were set manually in the campaign. However, when all products are out-of-stock the pop-up will not be displayed."}, "productTexts": {"clickActions": {"addToCart": "Add to cart", "outOfStock": "Out-of-stock", "redirectToProduct": "Redirect to product", "redirectToCart": "Redirect to cart"}, "example": {"cta": {"addToCart": "Add to cart", "redirect": "See details", "redirectToCart": "Go to cart"}, "name": "Product name", "price": {"new": "$19", "old": "$25"}, "priceNumeric": {"new": "19", "old": "25"}, "sku": "SKU"}, "info": {"noProduct": "Please choose a product!", "outOfStock": "This product cannot be added to cart, because it is out of stock. Please, choose another one."}, "label": {"ctaText": "Button text", "image": "Product image", "noResult": "Nothing to display", "onlyInStockAreShown": "Only in stock products are shown", "outOfStock": "Out of stock", "preOrder": "Pre-order", "search": "Search products by name or SKU", "searchBySku": "Search products by SKU", "url": "Link"}, "title": {"image": "Image", "name": "Name", "oldPrice": "Old price", "openInNewTab": "Open link in new tab", "price": "Price", "cta": "<PERSON><PERSON>", "showOutOfStock": "Visitors can see out-of-stock products", "sku": "SKU"}}, "productTour": {"addElement": "Add element", "addElementText": "You can easily add elements from this panel. Just simply drag and drop the elements to the place you would like to.", "brandKit": "Theme settings", "brandKitText": "Customize the campaign theme to match your brand. We’ll save the style and suggest new campaigns based on it in the future.", "congrats": "Congrats!", "congratsText": "You're on your way to harnessing the power of OptiMonk.", "customizeElements": "Customize elements", "customizeElementsText": "After selecting an element, this panel will allow you to customize that element. Each element has a unique set of customizable settings. Most elements have General, Style, and Advanced settings.", "editContent": "Edit content", "editContentText": "Templates can be fully customized. You can select and edit any element.", "experiences": {"steps": {"addRule": {"description": "Here you can set the targeting of an Experience and define the group of visitors within the total audience of your campaign.<br>The original targeting settings of the campaign apply to each Experience. There's no need to define them again here.", "title": "Who should see this<br>Experience?"}, "addVariant": {"description": "Here you can decide what message you want to show this group of visitors.<br>You can create and A/B test more variants within one Experience.", "title": "Create at least one variant<br>for each Experience"}, "howItWorks": {"description": "By clicking on 'How it works', you can\n            <ul>\n            <li>access written instructions,</li>\n            <li>watch the intro video,</li>\n            <li>or start this tour again.</li>\n            </ul>", "title": "Are you stuck? Help is always<br>close at hand"}, "name": {"description": "With Experiences, you can define which version of your campaign you want to show to each sub-group of the target audience.<br><br>You must set up at least two different Experiences for personalization (but the visitor will always meet only one of them).", "title": "Welcome to the<br>Experience View"}, "priority": {"description": "If a visitor could see more than one Experience based on the targeting rules, they meet the Experience of the higher priority.", "title": "Show the most relevant<br>Experience to each visitor"}}}, "gettingStarted": "Getting started with OptiMonk editor", "goAhead": "Go ahead, continue editing.", "layers": "Elements", "layersText": "Element gives you a unique overview of your campaign, helps you quickly select elements and change their backgrounds.", "letsGo": "Let's go", "mobileView": "Mobile view", "mobileViewText": "If you want to run your campaign on mobile devices too, then you should check the mobile view. You can set the campaign device-specific appearance at a later step", "page": "{current} of {all}", "selectDevice": "Mobile and desktop view", "selectDeviceText": "Here you can switch between the mobile and desktop view.", "selectPages": "Select pages", "selectPagesText": "Your campaign can have multiple pages if you want to communicate more messages. You can switch between these pages and add new pages at the top of the editor.", "selectTeaserText": "Next to the pages, you can find the campaign teaser.", "showTeaser": "Show teaser", "showTeaserText": "This panel will allow you to set when the teaser appears.", "takeTour": "Take a 2 minute tour to discover the editor.", "teaser": "Teaser", "teaserText": "The Teaser is a complementary page for your campaign, which helps grab visitor's attention before or after seeing you popup."}, "productsMissing": "Product missing", "productsMissingText": "You added fewer products than the layout requires. You can either change the layout to fit the added number of products or you can add more.", "profile": "My profile", "proforma": "<PERSON><PERSON><PERSON>", "properties": "Properties", "publish": "Publish", "pulse": "Pulse", "quarter": "quarter", "question": "Question", "questionFontEdit": "Question font", "questionTooltip": "If you provide a question to your element, the statistics of the user’s answers will be visible on your campaign page.", "radial": "Radial", "radio": "Radio", "radioAlign": "Radio align", "range": "Range", "reIntegration": {"deprecatedIntegrations": "Deprecated integrations", "deprecatedText": "({all}/{active} active campaign)", "error": {"subTitle": "Unfortunately, an error occurred while connecting your Optimonk campaigns to your HubSpot account. Please try to reconnect again.", "title": "Reconnection failed"}, "noNeedReIntegration": "{integrationType} is not deprecated!", "notUsedInCampaign": "You don't have any old {integrationType} integration", "pageTitle": "{integrationType} reintegration", "reconnect": "Reconnect", "reconnected": "Reconnected", "success": "Nice work! All your {type} integrations are up to date."}, "readMore": "Read more", "recart": "<PERSON><PERSON><PERSON>", "recartSettings": {"buttonPlacement": {"label": "<PERSON><PERSON>", "options": {"after": "After checkbox", "before": "Before checkbox"}}, "noButtonActionMessage": "Please set the Campaign goal reached option under the button settings for the correct behaviour", "noButtonMessage": "Please, insert a button."}, "recentlyUsedColors": "Recently used colors", "recommended": "Recommended", "recommendedIntegration": "Recommended", "recommendedPlan": "Recommended plan", "recommendedUseCases": "Recommended use cases", "rectangle": "Rectangle", "redirect": "Redirect", "redirectLink": "Redirect link", "redo": "Redo", "registration": {"acceptPrivacyPolicy": "You must agree Our Terms & Conditions and Privacy Policy before continue", "agencyAffiliateInvite": "You've been invited by {name}", "agencyAffiliateSubtitle": "{name} will get access to your account, and will be able to manage your campaigns.", "allFeatures": "All features included", "allIntegrations": "All integrations included", "emailAlreadyExists": "Email already in use, click <a href=\"/login/en/?email=%{email}\" class=\"mx-1 text-blue\"> here </a> to log in", "getStarted": "Get Started!", "haveAccount": "Do you have an account?", "lead": "Advanced conversion tools", "login": "Log in", "passwordLength": "8+ characters", "privacyPolicy": "By clicking the button I agree to Terms & Conditions and Privacy Policy.", "signUpWitEmail": "Sign up with your email", "signUpWith": "Sign up with {provider}", "subtitle": "No credit card required. No surprises. Just results.", "termsAndConditionsAndPrivacyLinkNew": "By submitting this form, you agree to the <a href=\"{link}\" title=\"Terms of Service\" target=\"_blank\"> Terms of Service</a> and <a href=\"https://www.optimonk.com/privacy-policy\" title=\"Privacy Policy\" target=\"_blank\"> Privacy Policy</a>", "title": "Everything you need to stop losing customers", "titleAffiliate": "Create Your Account for Free & Start Growing Your Business", "titleAgency": "Get Your 14 Days Trial & Start Boosting Your Business", "unlimitedCampaigns": "Unlimited campaigns", "emailInvalid": "This email address is invalid. Give a valid email address or <a href=\"mailto:<EMAIL>\">contact our Support Team</a>", "emailInfo": "You'll receive important alerts and notifications about your account and popups."}, "rejectRecommendedIntegration": {"fail": {"text": "Please try again later", "title": "Recommendation cannot be rejected"}, "success": {"text": "We will no longer recommend this integration for this campaign", "title": "Recommendation rejected"}}, "rejected": "Closed", "remove": "Remove", "removeFromSeasonal": "Remove from seasonal", "rename": "<PERSON><PERSON>", "renameDomain": "Rename domain", "renameVariant": "Rename variant", "repeat": "Repeat", "repeatHorizontally": "Repeat horizontally", "repeatVertically": "Repeat vertically", "repeats": "Repeat on", "repeatsOnEvery": "Repeats on every", "reportAs": "Report as", "reportAsTooltip": {"conversion": "Reports this to Google Analytics as \"filled\". Is used when the visitor successfully reaches your goal within the popup.", "none": "Sends no data to Google Analytics.", "rejected": "Reports this to Google Analytics as \"no\". Is used when the visitor wants to quit the popup without further interaction."}, "couponTooltip": "You can insert a button to copy the discount code if you like, although clicking or tapping the discount code element also copies the code.", "analyticsPage": {"totalRevenue": "Total revenue", "nonstandardOrders": "Order type used by domains", "deleted": "Deleted", "title": "Analytics", "dateRange": "Date range", "devices": {"desktop": "Desktop view", "desktop_and_mobile": "Desktop & Mobile", "mobile": "Mobile view", "unknown": "Unknown"}, "last30days": "Last 30 days", "last7days": "Last 7 days", "lastMonth": "Last month", "lastWeek": "Last week", "yesterday": "Yesterday", "visitors": "Visitors", "visitorsWithOrders": "Visitors with orders", "conversionRate": "Conversion rate", "totalOrdersHeader": "Total orders", "totalOrders": "Total orders", "ordersByCurrency": "Revenue", "aov": "Average order value", "revenue": "Revenue", "ga4Notification": "The order data displayed in the table is auto-tracked from Google Analytics 4. Please note that in some cases only the number of orders is available, furthermore, there may be differences between the measured data and the actual data. <a href=\"https://support.optimonk.com/hc/en-us/articles/17025263113618-Tracking-campaign-performance-in-Campaign-Analytics-with-Google-Analytics-4-Orders\" target=\"_blank\">You’ll find more information here.</a>", "campaignPerformance": "Campaign performance", "noReports": "There is no analytics data available<br> for your campaigns in the selected date range.", "loadingError": "Some error occurred while loading campaign analytics", "campaignConversion": "Assisted revenue", "campaignName": "Campaign Name", "impressions": "Impressions", "conversions": "Conversions", "uplift": "Uplift", "chanceToWin": "Chance to win", "assistedOrders": "Assisted orders", "assistedRevenue": "Assisted revenue", "revenueUplift": "Revenue uplift", "conversionRateHeader": "CR%", "aovHeader": "AOV", "top-level-explanation-visitors": "The number of unique visitors who visited your site during the selected period.", "top-level-explanation-visitorsWithOrders": "The number of visitors who placed an order at least once during the selected period.", "top-level-explanation-conversionRate": "The number of visitors who placed at least one order is divided by the number of Visitors. It shows what percent of your visitors ordered at least once in the selected period.", "top-level-explanation-totalOrdersHeader": "The number of total orders placed in the selected period.", "top-level-explanation-revenue": "The revenue generated in the selected period.", "top-level-explanation-aov": "The average value of orders placed in the selected period.", "header-explanation-visitors": "The number of unique visitors who saw the campaign.", "header-explanation-impressions": "The number of campaign impressions.", "header-explanation-conversions": "The number of campaign conversions. That means when a visitor fills out a form, clicks a button, or performs any action that is considered a conversion in your campaign.", "header-explanation-conversionRate": "The number of conversions is divided by the number of impressions. It shows what percent of your visitors take the desired action.", "header-explanation-uplift": "Uplift is the measured difference between the conversion rates of the campaign variants considered in the test. We calculate it only in the case of an A/B test.", "header-explanation-chanceToWin": "Chance to win means the probability of the variant outperforming the baseline, in terms of conversion rates.", "header-explanation-assistedOrders": "The number of orders placed within 5 days after a campaign conversion.", "header-explanation-assistedRevenue": "The revenue generated within 5 days after a campaign conversion.", "header-explanation-visitorsWithOrders": "The number of visitors who placed an order at least once within 5 days after they saw the campaign.", "header-explanation-conversionRateVisitorsWithOrders": " The number of visitors who placed at least one order is divided by the number of Visitors. It shows what percent of your visitors ordered at least once within 5 days after they saw the campaign.", "header-explanation-totalOrders": "The number of orders placed within 5 days after a campaign variant was triggered.", "header-explanation-revenue": "The revenue generated within 5 days after a campaign variant was triggered.", "header-explanation-aov": "The average value of orders placed within 5 days after a campaign impression.", "header-explanation-revenueUplift": "Revenue uplift is the measured difference between the revenue of the campaign variants considered in the test. We calculate it only in the case of an A/B test.", "header-explanation-goalConversion": "The number of visitors who reached the goal at least once within 5 days after they saw the campaign.", "header-explanation-conversionRateForOtherGoals": "The number of visitors who reached the goal at least one is divided by the number of Visitors. It shows what percent of your visitors reached the goal at least once within 5 days after they saw the campaign.", "baseline": "Baseline", "custom": "Custom period", "lastUpdated": "Last updated: ", "noDeviceSpecificData": "Please note that we have been collecting device-specific data since February 21st, so we can display device-specific data after this day.", "noOMDetected": "We haven`t detected your OptiMonk code on this site yet. Please go to the <a target=\"_blank\" href=\"https://app.optimonk.com/settings/code-insert\">Insert code</a> page and follow the installation instructions.", "noCodeDetected": {"title": "Analyze the performance of your campaigns", "description": "See how much revenue or other conversions you get from each campaign.", "learnMore": "Learn more"}, "generalError": "Oops! Something went wrong. We’re on it.<br>Please check back later.", "assistedRevenueExplanation": "Revenue generated by visitors who made a purchase after converting in a campaign. <a target=\"_blank\" href=\"https://support.optimonk.com/hc/en-us/articles/5098278551186-Campaign-Analytics#h_01HXE5SYGASAY0RW9FCM9FTYVR\">Learn more</a>", "totalRevenueExplanation": "Revenue generated by visitors who’ve seen a campaign. <a target=\"_blank\" href=\"https://support.optimonk.com/hc/en-us/articles/5098278551186-Campaign-Analytics#h_01HXE5TXXM91M64PERGF7KC4JJ\">Learn more</a>"}, "requiredField": "This field is required.", "resend": "Resend", "reset": "Reset", "resetCoupons": "Reset coupons", "resetToDefault": "Reset to default", "responsive": "Responsive", "restore": "Rest<PERSON>", "reverseOrderMobile": "Reverse order on mobile", "ribbon": "Ribbon", "right": "Right", "rotateLeft": "3D Rotate left", "rotateLeft15": "Rotate left 15°", "rotateRight15": "Rotate right 15°", "rotateTop": "3D Rotate right", "row": "Row", "rowBackground": "Background", "runningAbTest": "A/B test is running.", "samePageForAllOptions": "Same page for all options", "sameRedirectForAllOptions": "Same redirect url for all options", "save": "Save", "saveAndExit": "Save & Exit", "saveAndNext": "Save & Next", "saveAsNew": "Save as new", "saveOriginal": "Save", "saveOriginalTooltip": "This will overwrite your existing picture", "saveSegment": "Save segment", "schedule": "Schedule", "scheduleInterval": "Period", "scheduleModal": {"dailySchedule": "Daily schedule", "deleteConfirmation": "Are you sure want to delete the campaign schedule?", "deleteSchedule": "Delete schedule", "endDate": "End date", "everyDay": "Every day", "learnMoreSection": {"desc": "Use the campaign schedule to specify in advance how long your campaign can run, and to specify the specific days and times your campaign can run. Depending on the timing, the campaign will start and stop automatically."}, "never": "Never", "on": "On", "specificDay": "Specific day", "startDate": "Start date", "subtitle": "All times are local times based on the visitor's time zone.", "title": "Set up your schedule", "timezone": "Time zone", "utc": "GMT+00:00 UTC", "visitorBased": "Based on the visitor's time zone"}, "scheduleOr": "or, ", "scheduleStr": "Scheduled", "scheduled": "Scheduled", "scratchCard": {"bg": {"lose": "Losing background", "scratch": "Scratchable background", "win": "Winning background"}, "editTitle": "Edit scratch card", "message": "Sign up", "settings": {"basics": "Design", "title": "Scratch card settings"}, "test": {"showButton": "Test", "title": "Scratch card demo"}}, "scratchcard": "Scratch card", "scrollDown1": "The campaign will appear after scrolling down", "scrollDown2": "percent.", "search": "Search", "searchInputPlaceholder": "Search for campaign name...", "searchUsersPlaceholder": "Search users", "seasonal": "Seasonal", "seasonals": "Seasonals", "second": "second | second | seconds", "secondaryColors": "Secondary colors", "seconds": "Seconds", "seeAll": "See all", "seeAllThemes": "See all themes", "seeAllLeads": "See all leads", "segments": "Segments", "select": "Select", "selectAList": "Select a list", "selectAll": "Select all", "selectAnAccount": "Select an account", "selectDate": "Select a date", "selectDomainPlaceholder": "Select a domain", "selectItems": "Select products", "selectOne": "Select option", "selectOptionOrCreateNew": "Select an option or create a new one", "selectPlan": "Select plan", "selectProduct": "Select product", "selectRecentFields": "Select recent fields", "selectTemplate": "Use this template", "selectedPlan": "Selected plan", "send": "Send", "sendCredentials": "Let us do it for you for free", "sendCredentialsModalTitle": "Login details of your website", "sendEmailAddress": "Email address", "sendInsertCode": "Send to Developer", "sendInsertCodeModalText": {"code": "Dear {name},\n\nTo install OptiMonk, you need to insert the OptiMonk JavaScript code in the <head> section of each page of {domain}.\n\n{code}", "plugin": "Dear {name},\n\nPlease install the following {plugin} to our website:\n\n{link}\n\nIf you prefer using a JavaScript code, please add this code to every page of our website in the <head> tag to install OptiMonk. You only need to include the code once per page.\n\n{code}"}, "sendInsertCodeModalTitle": "Send email instructions and code snippet to your developer", "sendLink": "Send this link to any of your clients", "sendLinkLead": "If someone registers through this link, his account will be connected to your partner account, and you'll be able to manage these accounts.", "sendName": "Name", "separateCoupons": "Separate each coupon with a comma", "separatorColor": "Separator color", "service": "Service", "serviceUrl": "Service URL", "services": "Services", "setElementDefaults": {"all": "Update all %{type} and its' defaults", "onlyDefault": "Update %{type} defaults", "title": "Default values"}, "setVisitorLimit": "Set visitor limit ", "settingGroups": {"account": "Account", "billing": "Billing", "contactInformation": "Contact information"}, "settings": "Settings", "settingsSummary": {"eventTitle": "When will the popup show up", "frontendRuleTitle": "Who should see the popup", "frontendRuleTitleCampaign": "Who should see the campaign", "pointAndClick": {"above": "above", "below": "below", "embeddedUrlInfo": "The campaign will display on all URLs where the selector can be found. You can refine it in the current URL rule.", "whereIsItInserted": "Inserted {position} {selector} selector"}, "pointClickTitle": "Point & Click placement", "positionsTitle": "Where to place", "title": "Settings summary", "frequencyTitle": "How Often Can It Appear", "frequencyShowAgainTitle": "How many times", "frequencyShowAgainMaximumTimes": "Maximum {times} times", "frequencyAlways": "Always", "withFrequency": "How frequently", "frequencyUnlimitedTimes": "Unlimited Times", "frequencyAsap": "As soon as possible", "frequencyDelayTimes": "Min {times} {unit} between two impressions", "frequencyStopShowing": "Stop Showing", "frequencyStopAfterClosed": "After a visitor has closed the campaign", "frequencyStopAfterConverted": "After a visitor has converted in this campaign"}, "settingsFrequency": {"title": "How many times can this campaign appear?", "showAgainDescription": "<b style=\"color: #505763\">How many times</b> can this campaign appear to a visitor?", "showAgainUnlimitedTimes": "Unlimited Times", "showAgainMaximum": "Maximum", "showAgainTimes": "times", "withFrequencyTitle": "With Frequency", "withFrequencyDescription": "<b style=\"color: #505763\">How frequently</b> can this campaign appear again after a visitor has closed it?", "withFrequencyAsap": "As soon as possible", "withFrequencyMinimum": "<PERSON>.", "withFrequencyBetweenImpressions": "between two impressions", "stopShowingTitle": "Stop Showing", "stopShowingDescription": "<b style=\"color: #505763\">When should</b> the campaign <b style=\"color: #505763\">stop showing</b> to the visitors?", "stopAfterClosed": "After a visitor has closed the campaign", "stopAfterConverted": "After a visitor has converted in this campaign", "nextButton": "Next to Targeting", "clickEventNotification": {"hasOnlyClickTrigger": {"title": "\"On click to a specific area\" trigger bypasses frequency and display limits.", "text-row-1": "When a visitor triggers the popup by clicking on a specific area, it will always appear regardless of frequency and appearance settings.", "text-row-2": "To enable these settings, add another trigger or replace the \"On click to a specific area\" trigger with a different one."}, "hasClickTrigger": {"title": "\"On click to a specific area\" trigger bypasses frequency and display limits.", "text": "When a visitor triggers the popup by clicking on a specific area, it will always appear regardless of frequency and appearance settings."}}}, "setupADomain": "Add a domain", "shadow": "Shadow", "shadowColor": "Shadow color", "shake": "Shake", "share": "Share", "shareSettings": "Share settings", "shopMessageSettings": {"hasButtonMessage": "Currently selected type doesn't need button, please remove for the correct behaviour", "noButtonMessage": "Currently selected type needs a button"}, "shopifyAccount": "Shopify account", "shopifyFeedbackModal": {"cta": "Write a quick review", "lead": "Please support us with your Shopify app review to help us keep our FREE plan.", "title": "Write a review"}, "shopifyRemoveModal": {"lead": "Shopify integration is mandatory for Shopify shops, if you want to send your customers emails to another shop, please add another Shopify integration below before removing this.", "title": "Cannot remove Shopify integration"}, "show": "Show", "showLess": "Show less", "showManuallyEmbeddedCode": "Show embedding code", "showMore": "Show more", "showPopupOnClick": "Show popup on click to specific element(s)", "showTeaser": "Show teaser", "showTemplates": "Show templates", "showText": "Show text", "sidebar": "Sidemessage", "sidebarSettings": {"elements": {"group": {"title": {"basic": "Basic elements", "form": "Form elements", "other": "Other", "product": "Product offers", "structure": "Structural elements"}}}, "infoBox": {"stepInfo": {"content": "Click on any element in your campaign to edit it.", "heading": "How to edit content?"}}, "layout": {"description": "Below you'll find a list of all the elements in this step. Click on any of them and edit them on the right side."}, "tabs": {"name": {"advanced": "Advanced", "content": "Content", "elements": "Add element", "general": "General", "layout": "Layout", "step": "Step", "style": "Style"}}}, "sign": "3D Sign", "signUpFreeTrial": "Sign up for free trial", "similarTemplate": "Similar template", "similarTemplates": "Similar templates", "simple100": "100%", "single": "Single", "singleClickToChange": "Single click to change", "sites": "Sites", "size": "Size", "sizingPosition": "Sizing & position", "skip": "<PERSON><PERSON>", "sliceColor1": "Space 1", "sliceColor2": "Space 2", "sliceColor3": "Space 3", "sliceColor4": "Space 4", "dots": "Dots", "sticks": "Sticks", "radius": "Circular arc", "ticker": "Ticker and middle circle", "slide": "Slide", "slideInLeft": "Slide in left", "slideInTop": "Slide in bottom", "slit": "3D Slit", "slugAlreadyExists": "Partner URL already exists. Please choose another one!", "slugTooltip": "The last part of your unique partner url.", "smartAbTest": {"page": {"title": "Smart A/B testing", "testRunningText": "The test is running. The results are updated every hour.", "firstUpdate": "First update in an hour", "lastUpdated": "Last updated", "editTest": "Edit test", "conversion": "Conversion", "uplift": "Uplift", "upliftDescription": "Implementing changes of the winning variants would most likely result in a minimum {upliftPercentage} uplift in conversions. 🎉", "columns": {"tests": "Tests", "visitors": "Visitors", "conversion": "Conv.", "conversionTooltip": "The conversion numbers show the number of unique visitors who reached the selected conversion goal.", "conversionRate": "CR", "uplift": "Uplift", "chanceToWin": "Chance to win", "upliftTooltip": "Uplift is the measured difference between the conversion rates of the two variants considered in the test.", "chanceToWinTooltip": "Chance to win means the probability of the variant outperforming the control one, in terms of conversion rates."}, "accordion": {"element": "Element #{number}", "original": "Original", "variant": "Variant #{number}", "showAll": "Show all test rounds", "showOnly": "Show only the current round", "testsAreRunning": "{runningTests}/{allTests} test round ran {days} days since {date}", "testRound": "{runningTests}/{allTests} test round", "testsFinished": "Test finished {date}", "notFindClearWinner": "During this test round no clear winner emerged, so we'll continue with the previous champion in the test.", "historyRuntime": "{actualTest}/{allTests} test round ran {days} days between {intervalDate}. "}}}, "smartHeadline": {"description": "Let our AI suggest new versions for this text! Works best when you already include some text and your website contains a good meta-description of your business. The whole text element content will be replaced.", "generateSuggestions": "Generate suggestions", "limitReached": "You’ve reached your daily limit for generating headlines. Check back later.", "noInput": "We could not generate a suggestion, please add text to the selected element", "reset": "Revert text to original", "suggestions": "Suggestions", "title": "Smart headline generator", "unknownError": "An error has occured while trying to generate suggestions. Please try again later."}, "smartTags": {"input": {"cartValueCountdownLabel": "Enter the value for the countdown", "label": "If {label} is not available"}, "label": "⚡ Smart tags", "modal": {"edit": {"cart-value-countdown": "Edit cart value countdown value", "general": "Edit default value"}, "new": {"cart-value-countdown": "Add cart value countdown value", "general": "Add default value"}}, "tagGroups": {"history": "History", "location_and_time": "Location and time", "personal_data": "Personal data", "product_and_cart": "Product and cart", "system": "System", "smart_product_tags": "Smart product tags"}, "tags": {"browser_name": "Browser name", "cart_num_item_kinds": "Cart number of kinds", "cart_num_items": "Cart number of items", "cart_total": "Cart total", "cart_value_countdown": "Cart value countdown", "country_de": "Country in DE", "country_en": "Country in EN", "country_hu": "Country in HU", "curr_month_en": "Current month in EN", "curr_month_hu": "Current month in HU", "email": "Email", "first_name": "First name", "landing_page": "Landing page", "last_name": "Last name", "os_name": "Operating system name", "product_category": "Product category", "product_name": "Product name", "tz_offset": "Timezone offset", "utm_campaign": "UTM campaign", "utm_content": "UTM content", "utm_medium": "UTM medium", "utm_source": "UTM source"}}, "snowing": "Snowing", "social": "Social", "socialError": {"facebookAccountNotExist": "This account doesn't exist. Are you signed in to the correct Facebook account?", "googleAccountNotExist": "This account doesn't exist. Are you signed in to the correct Google account?"}, "socialFollow": "Social follow", "socialPlatform": "Social platform", "socialShare": "Social share", "socialTypeTooltip": "Choose if you want your visitors to share your content or follow your page.", "solid": "Solid", "solidColor": "Solid color", "sortBy": "Sort by", "source": "Source", "spaceBetweenOptions": "Space between options", "spacer": "Spacer", "spacing": "Spacing", "spamProtection": "Spam protection", "specific": "Same", "spreadRadius": "Spread radius", "startDiagnosticate": "Campaign diagnostics", "disabledDiagnosticate": "You need to activate the campaign first in order to use it.", "startFromScratch": "Popup from scratch", "startsWith": "starts with", "staticValue": "Field values", "status": "Status", "stickyUp": "Sticky up", "stillOnMobile": {"desc": "Let's be honest: no one wants to do their best work on mobile. That's why we've optimized OptiMonk for desktop to ensure the best possible user experience.", "end": "Please finalize creating your campaign once you are on desktop.", "title": "Sorry, You are still on mobile..."}, "stopCampaign": "Stop campaign", "street": "Street", "stretch": "<PERSON><PERSON><PERSON>", "strike": "Strike", "string": "Text", "strong": "Large", "style": "Style", "styleGuide": "Style Guide", "styleGuideCodeContent": "{elementType}({fixProperties}{selectedProperties})", "styles": "Styles", "subAccountName": "Sub-account name", "subAccounts": "Sub-accounts", "subTotal": "Sub-total", "submit": "Submit", "subscribe": "Subscribe", "subscribedCustomers": "Subscribed Customers", "subscriberSelected": "1 subscriber selected | {count} subscribers selected", "subscribers": "Subscribers", "subscribersPage": {"notCollectingLeads": "We are not collecting subscriber data", "filters": {"syncFailed": "Failed"}}, "sum": "Sum", "superScaled": "Super scaled", "survey": "Survey", "switchAccount": {"agency": "Agency", "normal": "Normal", "sub": "Sub-account"}, "switchFromCampaign": "Switch from campaign", "syncStatus": {"completed": "Completed", "failed": "Integration issue", "new": "New", "pending": "Pending", "title": "Sync status", "unknown": "Unknown", "IntegrationErrorAPIKey": "Incorrect API Key", "IntegrationErrorList": "Invalid list", "IntegrationErrorField": "Field mapping issue", "IntegrationErrorAccountIssue": "Account issue", "SubscriberErrorExisting": "Subscriber already exists", "SubscriberErrorInvalidEmail": "Invalid email", "SubscriberErrorInvalidPhone": "Invalid phone number", "SubscriberErrorUnsubscribed": "Already unsubscribed", "SubscriberServerError": "Server error"}, "syncToIntegration": {"label": "Sync to integration", "rightNow": {"label": "Right now", "tooltip": ""}, "smartSend": {"label": "Smart sync", "tooltip": "Smart sync send data to integrations either when there are no possible input fields for the visitor to fill, or when the visitor intends to quit the popup by closing it or navigating away from the page, closing the browser, or after 5 minutes of inactivity since last interaction."}}, "tab": "Teaser", "tabSize": "Size", "tabTitleTooltip": "Teasers let you minimize your popups and access them before/after they actually show.", "tabType": "Teaser type", "tactic": {"feature": {"abTesting": "A/B testing", "dynamicContent": "Dynamic content", "embedded": "Embedded", "experiences": "Experiences", "experiments": "Experiments", "multiStep": "Multi-step", "productRecommender": "Product recommender", "smartTag": "Smart Tags", "sourceBasedTargeting": "Source-based targeting", "urlTargeting": "URL targeting"}, "goal": {"listBuilding": "Build your list", "stopCartAbandonment": "Stop cart abandonment", "growAOV": "Grow average order value", "collectFeedback": "Collect feedback", "guideYourVisitors": "Guide your visitors", "boostConversions": "Boost conversions", "increaseSales": "Increase sales"}, "industry": {"any": "Any", "ecommerce": "Ecommerce", "media": "Media", "saas": "SAAS", "service": "Service"}}, "tada": "<PERSON><PERSON>", "tagManagerHelp": "In your admin dashboard on OptiMonk, you can easily configure your campaigns to report the following events to your Google Tag Manager account.<br>\n    Download the Tag Manager container, which is already created for you from <a href=\"https://payment.optimonk.com/tag_manager/OptiMonk_Tag_Manager_Tags.json?_ga=2.*********.*********.**********-**********.**********\">here</a>.<br>\n    Import the container to your Tag Manager<br>\n    The following object will be added to your data layer when the popup actions – shown, filled, close(x), no – are reported<br>\n    where<br>\n    <ul>\n      <li>category: OptiMonk</li>\n      <li>action: shown, filled, close(x), no</li>\n      <li>label: OptiMonk campaign name</li>\n    </ul>", "tagTemplates": "Tag templates", "tags": "Tags", "tax": "Tax", "teaser": {"device": {"all": "Both devices", "desktop": "Desktop", "mobile": "Mobile"}, "errors": {"onlyInt": "This setting only be an integer", "smallerThenZero": "Can't be smaller then 0"}}, "teaserCloseButton": "Teaser close button", "teaserDeviceChooser": "Appears on these device(s)", "teaserDisabledWarning": "The teaser is turned off.", "teaserMobileSwitch": "Show in mobile view", "teaserPosition": "Teaser position", "teaserSec": "sec", "teaserTriggering": {"appearAfter": "Appear after", "appearAfterPageLoad": "Appear after first page load"}, "templateChooser": {"search": {"resultTitle": "Search: \"{term}\"", "resultTitleCount": "{count} results: \"{term}\""}, "advanedTactics": {"moreLink": "Browse tactic library"}, "banner": {"startEditing": "Start editing", "whatAreThemesDescription": "Themes allow you to customize your popups with your own design—adjusting colors, fonts, and layout—and save those settings for easy reuse in future campaigns.", "whatAreThemesReadMore": "https://support.optimonk.com/hc/en-us/articles/*************-Themes", "whatAreThemesTitle": "What are Themes?"}, "browseByTheme": "Browse by theme", "changePreferences": "Change preferences", "customTemplates": "Custom templates", "emptyStateBaseTitle": "Unfortunately, none of our templates meet the filter criteria you have set", "filter": {"embedded": "Embedded content", "nanobar": "Sticky bar", "pageABtest": "Page A/B test", "pagePersonalization": "Page personalization", "popup": "Popup", "sidebar": "Sidemessage", "gamification": "Gamification", "imagePopup": "Image popup"}, "learnMore": "Get more out of it", "home": "Home", "themes": "Themes", "useCases": "Use cases", "brandedThemes": "Branded themes", "moreRecommendation": "See all recommendations", "personalization": {"abTest": {"cards": {"card-1": {"description": "Test landing page headlines with different value propositions", "title": "Landing Page A/B Test"}, "card-2": {"description": "Boost your landing page conversion rates with AI", "title": "Smart A/B testing"}}, "description": "Test headlines, offers, designs & CTAs easily against each other or a control to find the highest-performer variant.", "footer": {"step-1": {"description": "Modify headlines, text elements, CTA buttons etc. on your page"}, "step-2": {"description": "Define the audience for this test. It can be every visitor, or only a sub-segment"}, "step-3": {"description": "Launch and track performance of your variants"}}, "heading1": "Run simple A/B tests for landing pages in seconds", "heading2": "Get started with proven & tested A/B testing tactics"}, "common": {"footerHeading": "How does it work?", "getStarted": "Get started", "learnMore": "Learn more", "stepTitle": "Step {index}"}, "page": {"cards": {"card-1": {"description": "Personalize landing page headlines based on ad copy", "title": "Ad-Landing Page Synchronizer"}, "card-2": {"description": "Personalize your email landing pages with the visitor’s name", "title": "Personal Greetings"}, "card-3": {"description": "Tailor product page messaging to align with your ad copy", "title": "Product Page Personalizer"}}, "description": "Tailor messaging, offers, visuals and layout of your store to suit each individual shopper with a simple, no-code editor.", "footer": {"step-1": {"description": "Make changes in the visual editor"}, "step-2": {"description": "Define the segment you want to personalize for"}, "step-3": {"description": "Track campaign performance"}}, "heading1": "Personalize landing pages without coding", "heading2": "Get started with proven, step-by-step personalization tactics"}, "multiCampaignABTest": {"heading1": "Run A/B tests to determine the best campaigns on your site", "description": "The tactics you rely on need to be backed by data so that you know what combination of campaigns, incentives and segments work best for you.", "doNotShowAgain": "Don’t show it again", "footer": {"step-1": {"description": "Create or select the participating campaigns"}, "step-2": {"description": "Set up campaign groups"}, "step-3": {"description": "Launch and track performance"}}}}, "seasonalTemplates": "Seasonal templates", "themeArchiveQuestion": "Archived themes will not be available for your new campaigns. Are you sure you would like to archive this theme?", "title": "What will you create today?", "fromScratchModal": {"title": "Popup from scratch", "blank": "Blank canvas", "fromElements": "Build your campaign from elements", "image": "Image popup", "predesignedImage": "Use your pre-designed image"}, "seasonal": {"upcomingSeasons": "Upcoming seasons", "seeAll": "See all seasonal themes"}, "themeWithName": "{theme} theme"}, "useCases": "Use cases", "templateCollection": {"labels": {"active": "Active", "description": "Description", "inactive": "Inactive", "private": "Private", "public": "Public", "slug": "Slug", "status": "Status", "visibility": "Visibility"}, "titles": {"edit": "Edit template collection", "new": "Create template collection"}, "tooltips": {"slug": "This will be the URL part on which users can reach the collection."}}, "templateEmbedded": "Embedded template?", "templateFilter": {"categories": {"autumn": "Autumn", "back_to_school": "Back to school", "black_friday": "Black Friday", "childrens": "Children's Day", "christmas": "Christmas", "corona": "COVID-19/Coronavirus", "cyber_monday": "Cyber Monday", "easter": "Easter", "fathers": "Father's Day", "halloween": "Halloween", "july_4": "4th of July", "kata": "KATA changes", "labor_day": "Labor Day", "mothers": "Mother's Day", "new_year": "New Year", "st_patricks": "Saint Patrick's Day", "summer": "Summer", "super_bowl": "Super Bowl", "valentin": "Valentine's Day", "pride": "Pride"}, "goals": {"capture_email": "Build your list", "collect_email": "Collect email addresses", "collect_feedback": "Collect feedback", "collect_messenger": "Collect Messenger subscribers", "collect_phone": "Collect phone numbers", "faciliate_social_sharing": "Increase social engagement", "gamify": "Gamify your messages", "increase_cart_value": "Increase cart value", "increase_form_submission": "Increase form submission", "make_announcement": "Make announcement", "promote_spec_offers": "Promote special offers", "promote_special_offers": "Guide your visitors", "recommend_products": "Recommend products", "reduce_cart_abandonment": "Stop cart abandonment"}, "contents": {"coupon": "Discount", "luckyWheel": "Lucky wheel", "product": "Product recommendation", "countdown": "Countdown", "survey": "Survey", "feedback": "<PERSON><PERSON><PERSON>", "pickAPresent": "Pick a present", "scratchCard": "Scratch card"}, "sortBys": {"conversionRate": "Conversion rate", "createdAt": "Most recent", "order": "Featured", "popularity": "Most popular"}, "types": {"embedded": "Embedded | Embeddeds", "gamification": "Gamification", "interstitial": "Fullscreen | Fullscreens", "nanobar": "Sticky bar | Sticky bars", "popup": "Popup | Popups", "sidebar": "Sidemessage | Sidemessages", "survey": "Survey"}}, "templateManager": {"addToFeatured": "Add to Featured", "all": "Everyone", "deleteTemplate": "Delete template", "dragAndMove": "Drag to change position", "duplicate": "Copy and edit", "editSettings": "Edit settings", "editTemplate": "Edit template", "errors": {"noTheme": "Please add a theme for the template", "noUseCase": "Please add a use-case for the template", "widgetNotSelected": "Please, select a type"}, "filter": {"all": "All", "normalTemplates": "Normal templates", "universalTemplates": "Universal templates"}, "modals": {"edit": {"title": "Settings"}, "new": {"title": "New template settings"}}, "new": "+ New template", "newCollection": "+ New collection", "newSegment": "+ New segment", "newUseCase": "+ New use case", "noDraft": "No draft template", "noUnlisted": "No unlisted template", "openLink": "Open in Template Library", "placeholders": {"categories": "Select categories", "goals": "Select goals", "templateKind": "Template kind", "theme": "Select theme", "useCase": "Select use case", "users": "Select subaccounts", "useCases": "Select tactics"}, "publish": "Activate", "removeFromFeatured": "Remove from Featured", "segments": {"showFor": {"all": "All", "non-shopify": "Non-shopify", "shopify": "Shopify"}}, "selectWidgetType": "Select message type", "shareWith": "Share with", "sharedWith": "Shared with", "tabs": {"active": "Active", "collections": "Collections", "draft": "Draft", "partnerFeatured": "Featured", "segments": "Segments", "universal": "Universal", "unlisted": "Unlisted", "useCase": "Tactics", "favorite": "<PERSON> as favorite", "myTemplates": "My templates"}, "title": "Template Manager", "agencyTitle": "Templates", "titles": {"theme": "Theme", "useCase": "Use case"}, "tooltips": {"categories": "Select the categories that the template belongs to!", "goals": "Choose what goals the template belongs to!", "shareWith": "Specify which sub-accounts you want to share the template with! If you leave it empty then it will be available in all sub-accounts.", "theme": "Select the theme that the template belongs to!", "thumbnail": "Upload a template preview if generated isn't good for you", "useCase": "Select the use case that the template belongs to!"}, "unlist": "Make it unlisted", "unpublish": "Make it draft", "useCases": {"promote": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "warnings": {"deleteTemplate": "This is permanently. Are you sure?"}}, "templateName": "Template name", "templateNameExists": "Template name already exists", "templateRecommendation": {"carousel": {"closeButtonText": "Don't show recommendations", "moreFromYourStyle": "More from your style", "title": "Recommended for you"}}, "templateTags": "Template tags", "templateThemes": {"banner": {"cta": "See themes", "lead": "Use the same style for all your campaigns and create a smooth experience", "title": "Check out our new themes"}, "details": {"color": "Check them out in other<br> colors:", "ownColor": "Or pick your own:", "title": "Templates in this theme"}, "lead": {"default": "Showcase your brand and product imagery in these<br> vibrant and colorful templates."}}, "templates": "Templates", "termsAndConditions": "I have read and accept the <a href=\"{link}\" target=\"_blank\">Terms</a>", "termsAndConditionsLink": "https://www.optimonk.com/wp-content/uploads/OptiMonk_ASZF_202403_EN.pdf", "testingGuide": "Cannot make a decision on what to test? Please read Our testing guides!", "text": "Text", "textAlign": "Text align", "textColor": "Text color", "textRotation": "Text rotation", "textSettings": "Text settings", "textShadow": "Text shadow", "textarea": "Textarea", "texts": "Texts", "thankYou": "Thank you", "thankYouAfterText": "Your plan was upgraded!", "thankYouText": "Thank You!", "the": "The", "theme": "theme", "themeColors": "Theme colors", "lessOptions": "Less options", "moreOptions": "More options", "themeKit": {"primaryFont": "Main font", "primaryFontForQuill": "{font} (Main font)", "secondaryFont": "Secondary font", "secondaryFontForQuill": "{font} (Secondary font)", "uploadLogo": "Upload your logo"}, "themes": "Themes", "templateFamilies": "Template families", "featuredThemes": "Featured themes", "thisMonth": "This month", "thoseVisitorsWho": "The visitors who are", "timePeriods": "Time periods", "timeUnitLabels": "Time unit labels", "timed": "Timed", "timed1": "The campaign will appear after", "timed2": "second(s).", "timedBasedActualPage1": "Visitors who have spent a minimum of", "timedBasedActualPage2": "on the current subpage.", "timer": "Timer", "title": "Title", "titles": {"login": "Login to your OptiMonk Account", "register": "Everything you need to stop losing customers"}, "to": "To", "top": "Top", "topBottom": "Top bottom", "topCampaigns": "Top", "topLeft": "Top left", "topRight": "Top right", "total": "Total", "transition": "Transition", "transparent": "Transparent", "trialEnds": "Trial ends", "triggersFinish": "Next to Targeting", "triggersFinishFrequency": "Next to Frequency", "triple": "Triple", "type": "Type", "typeHere": "Type here", "typeSettings": "Type settings", "underline": "Underline", "undo": "Undo", "unique": "Unique", "unitColor": "Unit label color", "unpublish": "Unpublish", "upgrade": "Upgrade", "upgradeAndUnlock": "Upgrade & Unlock", "upgradePlan": "Upgrade your plan", "upgradePlanModal": {"changeActiveDomain": "or change your active domain "}, "upgradePlanPage": {"currentPlan": "Current plan:", "features": "Features", "mostPopular": "Most popular", "needSomeHelp": "Need some help?", "scheduleACall": "Schedule a call.", "upgradeTo": "Upgrade to:"}, "upgradePlanTitles": {"aBlock": {"subtitle": "Upgrade your plan if you want to target AdBlock users with a specific message", "title": "Want to detect AdBlock users?"}, "abTesting": {"subtitle": "Upgrade your plan to run an A/B test", "title": "Want to compare different design variants?"}, "cartRules": {"subtitle": "Upgrade your plan to target visitors with specific cart content and value", "title": "Want to segment your buyers based on their cart?"}, "cookie": {"subtitle": "Upgrade your plan to segment your audience based on any cookie you want", "title": "Want to use cookies for segmentation?"}, "default": {"subtitle": "Upgrade your plan to get more pageviews and more advanced features", "title": "Continue running your campaigns!"}, "ipBlock": {"subtitle": "Upgrade your plan if you want to exclude visitors with a specific IP address", "title": "Want to block one or more IP addresses?"}, "jsEvent": {"subtitle": "Upgrade your plan to show your message when a JavaScript event has occurred", "title": "Want to show your campaign at a JavaScript event?"}, "linkOptiMonkCampaigns": {"subtitle": "Upgrade your plan to build your automated sales funnel by linking campaigns", "title": "Want to connect your OptiMonk campaigns?"}, "pro": {"subtitle": "Upgrade your plan to target visitors with specific cart content and value", "title": "Want to use custom variables for segmentation?"}, "sites": {"subtitle": "Upgrade your plan to add more domains and get more advanced features", "title": "Want to use OptiMonk on more domains?"}, "unbranded": {"subtitle": "Upgrade your plan to make your messages unbranded", "title": "Want to remove the ’Made with ♥️ by OptiMonk’ link?"}}, "upgradePrice": "Upgrade price", "upload": "Upload", "uploadCoupons": "Please upload coupons.", "uploadFont": "Upload font", "uploadLogo": "Custom logo", "uploadNew": "Upload new", "uploadSquaredLogo": "Custom squared logo", "uploadedImages": "Uploaded images", "uppercase": "Uppercase", "url": "URL", "urlOfAppearance": "URL of appearance", "urlOfTheSite": "URL of the site", "useCase": "Use case", "useCaseManager": {"difficulty": {"high": "Advanced", "low": "Easy", "medium": "Intermediate"}, "goals": {"connectVisitors": "Connect with visitors", "increaseSales": "Increase sales", "listGrow": "Grow your list"}, "industries": {"blog": "Publishers", "ecommerce": "Ecommerce", "saas": "SaaS / Services"}, "labels": {"difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editRules": "Edit rules", "industries": "Industries", "ojoStages": "OJO Stages", "svgPreview": "<PERSON><PERSON><PERSON>", "tactics": "Tactics", "targeting": "Recommended targeting", "thumbnail": "Preview", "whyShouldYouUseIt": "Why should you use it"}, "ojoStages": {"all": "All Stages", "completelyUnaware": "Completely Unaware", "fullyAware": "Fully Aware", "orderPlaced": "Order Placed", "problemAware": "Problem Aware", "productAware": "Product Aware", "solutionAware": "Solution Aware"}, "placeholders": {"difficulty": "Select difficulty", "goals": "Select goals", "industries": "Select industries", "name": "Start typing", "ojoStages": "Select OJO stage(s)", "tactics": "Select tactics", "templates": "Select templates", "thumbnail": "Select preview"}, "tactics": {"collectEmail": "Collect email addresses", "collectMessengerSubscribers": "Collect Messenger subscribers", "collectPhone": "Collect phone numbers", "gamify": "Gamify your messages", "getFeedback": "Get feedback", "increaseFormSubmissions": "Increase form submissions", "increaseSocialEngagement": "Increase social engagement", "makeAnnouncement": "Make announcement", "promoteSpecialOffers": "Promote special offers", "recommendProducts": "Recommend products", "redirectVisitors": "Redirect visitors", "reduceCartAbandonment": "Reduce cart abandonment"}, "titles": {"edit": "Edit tactic", "new": "Create tactic"}, "tooltips": {"industries": "Select which industry is the most specific", "tactics": "Select which approach is the most specific", "targeting": "Each line is one bullet point in \"Recommended targeting\" box", "templates": "Select which templates are used for this use case", "thumbnail": "Select which template preview shows up on use cases listing page"}}, "useDesktopImage": "Use desktop image", "useField": "Use field", "usePermanentTeaser": "Use permanent teaser", "usePointAndClick": "Use the Point & Click placement tool to directly define the position of the embedded content", "useTemplate": "Create campaign", "usedAssignedStat": "used / assigned unique visitors", "usedManualEmbed": "Placement is defined by embedding the code", "usedPointAndClick": "Point & Click placement is selected", "usedVisitors": "Used", "userSegment": {"buttons": {"load": "Load segment", "save": "Save as segment"}, "modals": {"choose": {"tabs": {"copy": {"empty": "You don't have a campaign yet.", "search": "Search campaigns", "title": "<PERSON><PERSON> from previous campaign"}, "predefined": {"custom": "Custom targeting", "title": "Predefined segments"}, "userdefined": {"empty": "You don't have a saved segment yet.", "title": "Saved segments"}}, "title": "Select who should see the campaign"}, "save": {"label": "Segment name", "title": "Save segment"}, "summary": {"alertTitle": "The following rules are not available for this campaign", "personalizedTitle": "The following rules are already added to at least one experience. By loading this segment we will move these rules from experiences to the campaign.", "title": "Segment summary"}}}, "userSuggestionsPlaceholder": "User suggestions", "username": "Username", "users": "Users", "validEmail": "Give a valid email address.", "validPhoneNumber": "Give a valid phone number.", "validations": {"domain": {"invalid": "Invalid domain format"}}, "value": "Value", "valuePlaceholder": "Value", "variable": "variable", "variantName": "Name", "variantPage": {"analytics": "<span class=\"text-primary font-weight-bold\">Analytics integration</span> is {status} for the domain <span class=\"text-primary font-weight-bold\">{domain}</span>", "appear": "The popup will appear on <span class=\"text-primary font-weight-bold\">{device}</span>,", "campaignDoesNotCollectLeads": "This campaign does not collect leads.", "noStat": "There were no conversions yet.", "noStatInPeriod": "No statistics for this variant in the given period.", "subscribers": "The subscribers will be saved in:", "when": " when a visitor:"}, "variantTable": {"controlDesc": "No variant will be shown.", "controlTitle": "Control", "dynamicContentDesc": "The original content will be displayed."}, "variants": "Variants", "vatAmount": "Amount of VAT", "verify": "Verify", "vertical": "Vertical", "verticalAlignment": "Vertical alignment", "verticalLength": "Vertical length", "verticalPadding": "Vertical padding", "video": "Video", "videoSettings": "Video settings", "view": "View", "viewPerPage": "View", "vimeo": "Vimeo", "visibilityDelay": "Visibility delay", "visibleOnlyOn": "Visible only on: ", "visitAmbassadorDashboard": "Visit Affiliate Dashboard", "visitorCartV3": {"addRule": "Add another rule", "appearIf": "Visitors will see your campaign if", "any": "any of the following products", "whereV2": "where the name {operator}", "where": "where", "which": "which is a", "operators": {"include": "include", "exclude": "not include"}, "type": {"string": "text", "number": "number"}, "cartValue": "Cart value", "totalNumberOfProducts": "Total number of products", "productsInCart": "Products in the cart", "productOperators": {"name": "products matching a name rule", "id": "any products selected manually"}, "attributeOperators": {"contains": "contains", "equals": "equal to", "greaterThan": "greater than", "greaterThanEquals": "greater than or equal to", "interval": "between", "isSet": "set", "lessThan": "less than", "lessThanEquals": "less than or equal to", "notContains": "doesn't contain", "notEquals": "not equal to", "notSet": "not set"}, "placeholders": {"number": "e.g 1234", "property": "e.g. SKU", "value": "e.g. abc123", "value100": "e.g. 100", "value50": "e.g. 50"}}, "visitorLimit": "Visitor limit", "visitorLimitPlaceholder": "Set visitors limit", "visitors": "Visitors", "vistia": "Vistia", "w100": "100% Width", "h100": "100% Height", "wantToLearnMore": "Learn more about this", "watchVideo": "Watch a video", "websiteOverlay": "Website overlay", "websiteUrl": "Website URL", "weeks": "Weeks", "weight": "Weight", "weightingFactor": "Chance weight", "welcome": "Welcome", "welcomePage": {"answerQuestions": "Answer a few questions to help us get to know you better.", "business": "Please tell us about your business:", "businessOptions": {"ecommerce": "eCommerce", "saas": "SaaS/Software", "service": "Service", "media": "Media", "other": "Other"}, "businessType": "Your business type", "howToGetStarted": "So, please tell us: How would you like to get started?", "lead": "Please tell us about your business.", "stage": "What stage are you in?", "stageOptions": {"established": "I’m above $20M annual revenue", "grow": "I’m under $2M annual revenue", "scaling": "I’m between $2-20M annual revenue", "started": "I just got started (no revenue)"}, "title": "What best describes {businessName}? "}, "welcomeUser": "Welcome, {firstName}", "whatsNew": "What's New", "wheelJumpTo": "jump to ...", "wheelSettings": "Wheel settings", "whenToShow": "When to Show", "whenToShowLead": "Determine the user behavior that will trigger your campaign and display your message.", "whenToShowLong": "When would you like the popup to show up?", "where": "where", "whereToPlaceEmbeddedContent": "Where would you like to place the embedded content?", "whiteLabelSettings": "White-label settings", "whoToShow": "Who to Show", "whoToShowCampaign": "Select who should see the campaign", "whoToShowEmbedded": "Select who should see the embedded content", "whoToShowLead": "Segment your visitors and specify the audience that should see your message.", "whoToShowLong": "Select who should see the popup", "widgetId": "Widget ID", "width": "<PERSON><PERSON><PERSON>", "win": "Win", "withoutOverlay": "Without overlay", "withoutOverlayOnMobile": "Without overlay on mobile", "xColor": "X color", "yes": "Yes", "you": "You", "yourDomains": "Your domains", "youtube": "YouTube", "youtubeUrl": "YouTube URL", "zoom": "Zoom", "couponDetails": {"couponItemName": "Discount", "activatedCouponTitle": "Coupon", "validityPeriod": "Validity period", "couponInputBoxTitle": "Coupon", "couponInputLabel": "Coupon code (optional)", "activationButton": "Apply", "cancelCoupon": "Remove", "alert": {"invalidForPlan": "The current coupon is not valid for the selected package!", "failedActivation": {"activation-ineligible-coupon": "Invalid coupon!"}}}, "translateFeature": {"title": "Templates in 5 new languages!", "description": "All our templates are now available in your preferred language.", "templateLanguage": "Template language", "languages": {"en": "English", "hu": "Hungarian", "de": "German", "fr": "French", "pt": "Portuguese", "ro": "Romanian", "es": "Spanish"}}, "ai": {"menuTitle": "AI features", "preHeadTitle": "Introducing Optimonk AI", "pageTitle": {"ecommerce": "Turn visitors into customers<br>efforlessly with the power of AI", "saas": "Automate, personalize and conver<br>your SaaS traffic better", "agency": "Save full-time CRO expert's time with<br>AI-powered CRO"}, "blockTitles": {"ecommerce": "Stop burning more $$$ on ads. Optimize for real results and conversion automatically.", "saas": "Stop hacking. Start converting your SaaS traffic better.", "agency": "Maximize PPC results with real-time, AI-powered personalization."}, "requestAccess": "Request Early Access", "requestConfirmed": "Request confirmed", "video": "", "rates": {"title": {"normal": "Since we started", "fancy": "OptiMonk AI"}, "subtitle": "Based on overall client statistics", "left": {"fancy": "+12.4%", "details": "Increase in leads"}, "right": {"fancy": "+78.5%", "details": "Increase in email & SMS subscribers"}}, "blocks": {"productPageOptimizer": {"preHeading": "Product Page Optimizer", "title": "A single setup, thousands of optimized product pages", "description": "Increase product page conversions with better headlines, descriptions, and benefit lists. Do it for thousands of product pages – at the same time.", "learnMoreLink": "https://www.optimonk.com/optimizer/", "video": "sppo_animation.mp4"}, "smartABTesting": {"preHeading": "Smart A/B Testing", "title": "Fully automated, AI-assisted A/B testing", "description": "Forget time-consuming A/B testing. Pick the elements on your website you want to test, and let our AI run one test after another.", "learnMoreLink": "https://www.optimonk.com/testing/", "video": "smart_abtesting_animation.mp4"}, "smartPopups": {"preHeading": "Smart Popups", "title": "One popup, endless personalized variants", "description": "Tailor the messaging of your welcome and exit popups to each visitor’s interest automatically.", "learnMoreLink": "https://www.optimonk.com/smart-popups/", "video": "smart_popups_animation.mp4"}, "smartPopups_b": {"preHeading": "Smart Popups", "title": "One popup, endless personalized variants", "description": "Don’t settle for generic popups on high-traffic pages attracting a lof of visitors. Tailor the messaging of your welcome and exit popups to each visitor’s interest automatically.", "learnMoreLink": "https://www.optimonk.com/smart-popups/", "image": "smart_popups_b.png"}, "smartABTesting_b": {"preHeading": "Smart A/B Testing", "title": "Fully automated, AI-assisted A/B testing", "description": "Feeling your SaaS messaging isn't landing well? Start A/B testing on scale. Pick the elements on your website you want to test, and let our AI run one test after another.", "learnMoreLink": "https://www.optimonk.com/testing/", "image": "smart_ab_testing_b.png", "button": "Try for free"}, "smartPersonalizer": {"preHeading": "Smart Personalizer", "title": "Automatic ad - landing page synchronization", "description": "Tailor landing pages to ads in real-time to boost relevance and increase PPC conversion rates. – 100% automatically.", "learnMoreLink": "https://www.optimonk.com/personalizer/", "image": "smart_personalizer.png"}, "smartPersonalizer_b": {"preHeading": "Smart Personalizer", "title": "Automatic ad - landing page synchronization", "description": "Tailor landing pages to ads in real-time to boost relevance and increase PPC conversion rates. – 100% automatically.", "learnMoreLink": "https://www.optimonk.com/personalizer/", "image": "smart_personalizer_b.png"}}, "blocksEnd": {"title": "Try out the new OptimMonk AI"}, "book": {"title": "Skip the learning curve.<br>Fast track your way with a guided walkthrough.", "details": {"preHeading": "60-min setup walkthrough + live Q&A", "title": "First steps with AI-powered CRO", "description": "To start your OptiMonk AI journey, join <PERSON> for a feature walkthrough. She’ll show you how to set up your first Smart A/B tests and guide you through the rest of the setup process with other AI features.", "cta": "Book now"}}}, "maybeLater": "Maybe later", "campaignShare": {"prepare": {"title": "A campaign has been shared with you", "subtitle": "Please wait! You will be redirected soon", "notifications": {"invalidToken": "Invalid token", "found": "Campaign data found"}}, "button": {"shareCampaign": "Share this campaign"}}, "generate": "Generate", "optimization": {"sab": {"start-phase": {"heading": "Create a new Smart A/B Test", "subHeading": "Which page would you like to optimize?", "inputPlaceholder": "Enter URL", "validations": {"required": "Domain is required", "localhost": "Localhost is not accepted", "missing-protocol": "Protocol (http, https) is missing", "invalid": "Domain is invalid"}}, "branching": {"left-section": {"heading": "Let our AI wizard suggest A/B test opportunities for you", "subHeading": "After a quick analysis, our AI will offer recommendations to optimize your website.", "ctaText": "Get recommendations"}, "right-section": {"heading": "Set up your own tests", "subHeading": "Choose any headline, description, or CTA text.", "ctaText": "Set up a custom Smart A/B Test"}}, "loading": {"heading": "Your Smart A/B test plan is being generated", "subHeading": "This may take a few minutes"}, "recommendations": {"heading": "Page Optimization Plan", "subText": "We analysed your website and suggest optimizing the following <b>{count} headlines</b>. Conducting A/B tests to find better alternatives could significantly improve your conversion rates.", "tableTitle": "Headlines to be optimized", "tableSubText": "In the next step, you can run an A/B test on any text content in addition to the suggestions above.", "alternativeToTest": "Alternatives to test:", "editAndPreview": "Edit and Preview"}}, "abTest": {"start-phase": {"heading": "Create a new A/B Test", "subHeading": "Which page would you like to optimize?"}}, "page": {"start-phase": {"heading": "Create a new personalized version", "subHeading": "Which page would you like to personalize?"}}, "notifications": {"generationError": "An error occured while generation"}}, "failedIntegration": {"notification": {"title": "You have {count} Sync failure", "resolve": "Fix & resync"}, "modal": {"title": "The following integration errors cause sync failures", "dateTooltip": "Showing errors from the last 1 year.", "allDone": "All of the integration errors were successfully resynced!", "failure": "Failure", "fixAndResync": "Fix & resync", "resyncInProgress": "Resync in progress", "done": "Done", "integrationError": "Integration error", "missingIntegration": "Integration missing from campaign", "tooltip": {"integrationDeleted": "This integration has been deleted from OptiMonk, you have to configure it from the ground up", "integrationError": "Solve the integration error either by editing the existing integration or by adding a new one"}}, "leadSyncModal": {"title": "Lead sync", "readyToSync": {"subTitle": "Ready to sync {leadsCnt} failures.", "resyncLeads": "This may take a few minutes.<br/>We will notify you of the result at the end of the synchronization.", "ctaText": "Start resync"}, "syncStarted": {"subTitle": "Lead sync started.", "resyncLeads": "You can follow the progress on the Audience page.", "ctaText": "Check progress"}}, "resyncPoll": {"notification": "Integration resync finished! Click here to check the results."}}, "onboardingModal": {"skip": "Skip intro", "goToDashboard": "Go to dashboard", "startWizard": "Start wizard", "page1": {"title": "Welcome aboard, {firstName}! 🙌", "text": "Join me for a quick tour, and discover what OptiMonk has to offer in just 1 minute!"}, "page2": {"title": "Easily create high-converting popups  that don’t annoy your visitors."}, "page3": {"title": "Optimize your website for maximum conversions with AI-assisted A/B testing"}, "page4": {"title": "Unsure where to start? Use our wizard and get personalized recommendations in 2 minutes."}}, "mobileResizer": {"zoomIn": "Zoom in", "viewWholeScreen": "View whole screen"}, "featurePromotions": {"title": "Level up your conversion", "experiences": {"title": "Personalize manually", "description": "Create unique experiences for sub-segments of your campaign's target audience"}, "couponFollowUp": {"title": "Discount Code Reminder", "description": "Remind users of their coupons to encourage redemption"}, "aipersonalization": {"title": "AI-Personalization", "description": "Automatically customize pop-up messages to match every visitor's unique interests"}}, "promoBanners": {"teammateInvite": {"title": "Invite your teammates", "description": "Get more out of OptiMonk by inviting your team for free", "button": "Invite your team"}, "agencyPromo": {"title": "Finish setting up your agency contact data", "description": "Manage all of your brands under one account. ", "button": "Set up agency account"}}, "sideNav": {"more": "More"}, "backgroundAndBorder": "Background and border", "backgroundHover": "Background hover", "borderHover": "Border hover", "browseUseCases": "Browse use cases", "changeHistory": {"kebabOption": "Change history", "title": "change history", "user": "User", "change": "Change", "date": "Date", "noHistory": "No logged changes yet.", "changeTypes": {"modifiedVariantDesign": "Modified {variantName} design", "modifiedDynamicContent": "Modified {variantName} design", "modifiedTargetingAndTriggering": "Modified targeting and triggering", "modifiedIntegration": "Modified integration", "modifiedFrequency": "Modified frequency", "campaignActivated": "Activated the campaign", "campaignInactivated": "Inactivated the campaign", "scheduleModified": "Modified schedule", "addedToExperiment": "Added to multi-campaign A/B test", "removedFromExperiment": "Removed from multi-campaign A/B test ", "addedVariantToExperience": "Added {variantName} variant to experience", "removedVariantFromExperience": "Removed {variantName} variant from experience", "modifiedExperienceTargeting": "Modified experience targeting and triggering", "addedExperience": "Experience added", "removedExperience": "Experience removed", "variantInactivated": "Inactivated {variantName} variant", "variantActivated": "Activated {variantName} variant", "variantDeleted": "Deleted {variantName} variant", "variantCreated": "Created {variantName} variant", "embeddedPlacementUpdated": "Modified targeting and triggering"}}, "resellerTemplates": "{brand} templates", "elementResizePopover": {"title": "Spacing and resizing are much easier", "description": "In addition to setting spacing, it’s now possible to resize button, text, input and image elements right in the workspace."}, "analyzingYourDomain": "Analyzing your domain", "newDiagFeaturePopover": {"new": "New", "title": "Campaign diagnostics", "desc": "Use the diagnostic tool to uncover the reasons your campaign isn’t appearing on your devices."}, "tiptap": {"alignAndIndent": "Align & indent", "themeFonts": "Theme fonts", "allFonts": "All fonts", "addNewFont": "Add new font", "lineHeight": "Line spacing", "searchEmoji": "Search emojis", "list": "List", "link": "Link", "emoji": "<PERSON><PERSON><PERSON>", "copyStyle": "Copy style", "copyElement": "Copy element", "removeElement": "Delete", "fontFamily": "<PERSON>ont family", "fontSize": "Font size", "fontWeight": "Font weight", "fontColor": "Font color", "style": "Style", "emojiModal": {"typeMore": "Type more...", "empty": "Try different..."}}}