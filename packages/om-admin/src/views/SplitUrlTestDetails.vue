<template lang="pug">
.brand-wrapper.split-ur-test-details-page(v-if="splitUrlTest")
  .container-fluid.px-2
    split-url-test-header(
      :splitUrlTest="splitUrlTest"
      @newDomain="splitUrlTest.domain = $event"
      @newTitle="splitUrlTest.name = $event"
    )
    .details
      .col-12.split-url-ab-test-start-stop.bordered-content(
        @mouseleave="closeKebabMenu({ _id: splitUrlTest._id })"
      )
        .d-flex.split-url-status
          om-chip.m-0(:color="chipColor" :icon="chipIcon(splitUrlTest.status)" large) {{ splitAbTestStatus }}
          .d-flex.flex-column(v-if="splitUrlTest.status !== 'DRAFT'")
            om-body-text.duration(bt400sm) {{ `${$t('splitUrlTestDetails.duration')}: ${getDuration(splitUrlTest)}` }}
            .d-flex.status-text
              om-body-text(bt400sm) {{ splitAbTestStarted }}
              om-body-text.ml-3(bt400sm v-if="splitUrlTest.status === 'FINISHED'") {{ splitAbTestFinished }}
        .actions
          om-button#start-split-url-ab-test(
            v-if="splitUrlTest.status !== 'FINISHED'"
            secondary
            icon="edit"
            @click="redirectToEdit"
          )
            span {{ $t('splitUrlTestDetails.edit') }}
          om-button#start-split-url-ab-test.mr-4(
            primary
            icon="play-circle"
            v-if="splitUrlTest.status === 'DRAFT'"
            :disabled="!isStartEnabled"
            @click="startSplitURLABTest"
          )
            span {{ $t('splitUrlTestDetails.start') }}
          om-button#end-split-url-ab-test.mr-4(
            primary
            icon="stop-circle"
            v-if="splitUrlTest.status === 'RUNNING'"
            @click="stopSplitURLABTest"
          )
            span {{ $t('splitUrlTestDetails.end') }}
          om-kebab-menu.p-1(
            :ref="`kebab_${splitUrlTest._id}`"
            :rowData="{ row: { _id: splitUrlTest._id } }"
            @delete="deleteExperiment(splitUrlTest._id, { type: 'SplitUrl', isDetailsPage: true })"
          )
            template(slot="delete") {{ $t('delete') }}
            template(slot="custom")
              .kebab-option(@click="updateTrafficShare") {{ $t('experiments.editTrafficShare') }}

      .col-12.p-0
        .split-url-ab-test-table.bordered-content
          om-table(
            :items="variants"
            :columns="columns"
            :allItemsCount="10"
            :paginate="false"
            :selectable="false"
            @selectedIdsChange="$emit('selectedIdsChange', $event)"
            @sortingChange="$emit('sortingChange', $event)"
            @paginationChange="$emit('paginationChange', $event)"
            :columnClasses="columnClasses"
          )
            template(slot="url" slot-scope="{ rowData }")
              om-tooltip(:delay="rowData.row.url.length > 83 ? 0 : 99999" transition="fade")
                span.text-break {{ rowData.row.url }}
                template(slot="trigger")
                  span.truncate.font-weight-normal {{ urlToDisplay(rowData.row.url) }}
            template(slot="table-embedded-content")
              .d-flex.justify-content-between.mb-5
                om-heading.font-weight-normal(h3) {{ $t('splitUrlTestEdit.urlVariants') }}
                .goals.d-flex
                  om-select#goal-select.mr-2.goal-select(
                    v-model="activeGoal"
                    :options="allGoals"
                    optionKey="_id"
                    optionText="name"
                    :label="$t('conversionGoals.selectLabel')"
                    labelPosition="fill"
                    extendable
                    :addNewText="$t('conversionGoals.addNewText')"
                    @addNew="openCreateGoalModal(splitUrlTest.domainId)"
                    :disabled="!activeGoal"
                  )
                  om-tooltip.d-flex.align-items-center(placement="top-end")
                    span {{ $t('conversionGoals.tooltip') }}

            template(slot="visitorCount" slot-scope="{ rowData }")
              vue-skeleton-loader.skeleton-goals(
                v-if="loadingGoals"
                type="rect"
                width="50%"
                height="24px"
                :rounded="true"
              )
              div(v-else data-testid="visitorCountCell") {{ rowData.row.visitorCount | thousandSep }}
            template(slot="pageViewCount" slot-scope="{ rowData }")
              vue-skeleton-loader.skeleton-goals(
                v-if="loadingGoals"
                type="rect"
                width="50%"
                height="24px"
                :rounded="true"
              )
              div(v-else data-testid="pageViewCountCell") {{ rowData.row.pageViewCount | thousandSep }}
            template(slot="conversionCount" slot-scope="{ rowData }")
              vue-skeleton-loader.skeleton-goals(
                v-if="loadingGoals"
                type="rect"
                width="50%"
                height="24px"
                :rounded="true"
              )
              div(v-else data-testid="conversionCountCell") {{ rowData.row.conversionCount | thousandSep }}
            template(slot="conversionRate" slot-scope="{ rowData }")
              vue-skeleton-loader.skeleton-goals(
                v-if="loadingGoals"
                type="rect"
                width="50%"
                height="24px"
                :rounded="true"
              )
              div(v-else data-testid="conversionRateCell") {{ rowData.row.conversionRate | dec2 }}
            template(slot="orderCount" slot-scope="{ rowData }")
              vue-skeleton-loader.skeleton-goals(
                v-if="loadingGoals"
                type="rect"
                width="50%"
                height="24px"
                :rounded="true"
              )
              div(v-else data-testid="orderCountCell") {{ rowData.row.orderCount | thousandSep }}
            template(slot="totalRevenue" slot-scope="{ rowData }")
              vue-skeleton-loader.skeleton-goals(
                v-if="loadingGoals"
                type="rect"
                width="50%"
                height="24px"
                :rounded="true"
              )
              div(v-else data-testid="totalRevenueCell")
                template(v-if="rowData.row.totalRevenue.length > 1")
                  om-tooltip(transition="fade")
                    .right-text-align(
                      v-for="totalRevenue in getMoreItemsFromRevenues(rowData.row.totalRevenue)"
                    ) {{ totalRevenue }}
                    template(slot="trigger")
                      span {{ getFirstItemFromRevenues(rowData.row.totalRevenue) }}
                template(v-else)
                  span {{ getFirstItemFromRevenues(rowData.row.totalRevenue) }}

          om-heading.exclude-heading.font-weight-normal.mb-4(h3 v-if="excludeUrls.length") {{ $t('splitUrlTestEdit.exclude') }}
          .p-0.d-flex.exclude-urls(
            v-for="(url, index) in excludeUrls"
            :key="`exclude-variant-${index}`"
          )
            .mr-3.where-url-text
              span(v-if="index === 0") {{ $t('splitUrlTestEdit.whereUrl') }}
            .d-flex.w-100.mb-2
              om-tooltip(:delay="url.length > 135 ? 0 : 99999" transition="fade")
                span.text-break {{ url }}
                template(slot="trigger")
                  span.truncate.exclude-url {{ url }}
        create-edit-goal(@goals:createGoal="createNewGoal")
        delete-experiment
        TrafficShare(@change="onTrafficChange")
</template>

<script>
  import {
    UilPlusCircle,
    UilTrashAlt,
    UilPlayCircle,
    UilStopCircle,
    UilEdit,
    UilStopwatch,
    UilCheckCircle,
  } from '@iconscout/vue-unicons';
  import { mapGetters, mapMutations } from 'vuex';
  import splitUrlTestMixin from '@/mixins/splitUrlTest';
  import SplitUrlTestHeader from '@/components/SplitUrlTest/SplitUrlTestHeader.vue';
  import closeKebabMenu from '@/components/Elements/KebabMenu/closeKebabMenu';
  import trafficShare from '@/mixins/trafficShare';
  import experimentsMixin from '@/mixins/experiments';
  import moment from 'moment';
  import ssrMixin from '@/mixins/ssr';
  import dateFormat from '@/mixins/dateFormat';
  import goalsMixin from '@/mixins/goals';
  import formatStat from '@/mixins/formatStat';
  import CreateEditGoal from '@/components/Modals/CreateEditGoal.vue';
  import GET_GOALS from '@/graphql/GetGoals.gql';
  import GET_SPLIT_URL_AB_TEST_STATS from '@/graphql/GetSplitURLABTestStatistics.gql';
  import STOP_SPLIT_URL_AB_TEST from '@/graphql/StopSplitURLABTest.gql';
  import START_SPLIT_URL_AB_TEST from '@/graphql/StartSplitURLABTest.gql';
  import DeleteExperiment from '@/components/Experiments/Modal/DeleteExperiment.vue';
  import SkeletonLoader from '@/components/SkeletonLoader/SkeletonLoader.vue';
  import TrafficShare from '@/components/Modals/TrafficShare.vue';
  import UPDATE_SPLIT_URL_AB_TEST from '@/graphql/UpdateSplitURLABTest.gql';

  const _clone = (v) => JSON.parse(JSON.stringify(v));

  export default {
    name: 'SplitAbTestDetails',
    components: {
      TrafficShare,
      DeleteExperiment,
      UilPlusCircle,
      UilTrashAlt,
      UilPlayCircle,
      UilStopCircle,
      UilEdit,
      UilStopwatch,
      UilCheckCircle,
      SplitUrlTestHeader,
      CreateEditGoal,
      SkeletonLoader,
    },
    mixins: [
      splitUrlTestMixin,
      ssrMixin,
      closeKebabMenu,
      dateFormat,
      goalsMixin,
      trafficShare,
      experimentsMixin,
      formatStat,
    ],
    data() {
      return {
        splitUrlTest: null,
        splitUrlTestStatistics: null,
        columnClasses: {
          url: 'font-weight-bold split-url-ab-test-url',
          name: 'split-url-ab-test-variant',
          visitors: 'font-weight-bold',
          pageViews: 'font-weight-bold',
          conversions: 'font-weight-bold',
          totalOrder: 'font-weight-bold',
          totalRevenue: 'font-weight-bold',
        },
        loadingGoals: true,
        goals: [],
        columns: [
          {
            header: this.$t('splitUrlTestDetails.trafficShare'),
            key: 'trafficSharePercentage',
            sortable: false,
          },
          { header: this.$t('splitUrlTestDetails.variant'), key: 'name', sortable: false },
          { header: this.$t('splitUrlTestDetails.url'), key: 'url', sortable: false },
          { header: this.$t('splitUrlTestDetails.visitors'), key: 'visitorCount', sortable: false },
          {
            header: this.$t('splitUrlTestDetails.pageViews'),
            key: 'pageViewCount',
            sortable: false,
          },
          {
            header: this.$t('splitUrlTestDetails.conversions'),
            key: 'conversionCount',
            sortable: false,
          },
          {
            header: this.$t('splitUrlTestDetails.conversionRate'),
            key: 'conversionRate',
            sortable: false,
          },
          { header: this.$t('splitUrlTestDetails.totalOrder'), key: 'orderCount', sortable: false },
          {
            header: this.$t('splitUrlTestDetails.totalRevenue'),
            key: 'totalRevenue',
            sortable: false,
          },
        ],
      };
    },
    computed: {
      ...mapGetters(['domains']),
      variants() {
        return this.splitUrlTest.urlVariants.map((variant) => {
          const statistics = this.splitUrlTestStatistics?.find(
            (variantInStats) => variantInStats._id === variant._id,
          );
          return {
            ...variant,
            trafficSharePercentage: `${this.getTrafficShareInPercentage(
              variant.trafficShare,
              this.splitUrlTest.urlVariants.length,
            )}%`,
            url: `${this.splitUrlTest.domain}${variant.url}`,

            visitorCount: statistics?.visitorCount ?? '-',
            pageViewCount: statistics?.pageViewCount ?? '-',
            conversionCount: statistics?.conversionCount ?? '-',
            conversionRate:
              statistics?.conversionCount &&
              statistics?.visitorCount &&
              statistics?.visitorCount > 0
                ? (statistics?.conversionCount / statistics?.visitorCount) * 100
                : '-',
            orderCount: statistics?.orderCount ?? '-',
            totalRevenue: statistics?.totalRevenue ?? [],
          };
        });
      },
      excludeUrls() {
        return this.splitUrlTest.excludeURLs.map((url) => `${this.splitUrlTest.domain}${url}`);
      },
      chipColor() {
        return this.splitUrlTest.status === 'RUNNING' ? 'green' : 'secondary';
      },
      splitAbTestStatus() {
        const status = this.splitUrlTest.status;
        return this.$t(`splitUrlTestDetails.${status.toLowerCase()}`);
      },
      splitAbTestStarted() {
        return `${this.$t('splitUrlTestDetails.started')}: ${moment(
          this.splitUrlTest.startedAt,
        ).format(this.dateFormat)}`;
      },
      splitAbTestFinished() {
        return `${this.$t('splitUrlTestDetails.finished')}: ${moment(
          this.splitUrlTest.finishedAt,
        ).format(this.dateFormat)}`;
      },
      isStartEnabled() {
        return this.splitUrlTest.urlVariants.length >= 2;
      },
      activeGoal: {
        get() {
          return this.selectedGoal;
        },
        set(goal) {
          this.selectedGoal = goal;
          this.saveDefaultGoalToLS(
            goal._id,
            this.splitUrlTest._id,
            this.DEFAULT_GOAL_BY_SPLIT_URL_TEST_KEY,
          );

          this.fetchSplitURLABTestStatistics();
        },
      },
    },

    async created() {
      this.showAdminLoader(true);
    },

    async mounted() {
      const id = this.$route.params.testId;

      this.splitUrlTest = await this.getSplitUrlTest(id);
      if (this.splitUrlTest) {
        this.splitUrlTest = {
          ...this.splitUrlTest,
          domain:
            this.domains.find((domain) => domain._id === this.splitUrlTest.domainId)?.domain || '',
        };
        this.$nextTick(() => {
          this.updateDimensions();
        });

        this.showAdminLoader(false);

        this.initStatistics();
      }
    },

    methods: {
      ...mapMutations(['showAdminLoader']),
      chipIcon(status) {
        switch (status) {
          case 'DRAFT':
            return 'edit';
          case 'RUNNING':
            return 'stopwatch';
          case 'FINISHED':
            return 'check-circle';
        }
      },
      updateTrafficShare() {
        this.$modal.show('traffic-share', {
          trafficGroups: this.splitUrlTest.urlVariants,
        });
      },
      async initStatistics() {
        await this.getGoals();
        this.setDefaultGoal();
        return this.fetchSplitURLABTestStatistics();
      },
      async onTrafficChange(trafficGroups) {
        this.splitUrlTest.urlVariants.forEach((group) => {
          group.trafficShare = trafficGroups.find(
            (newGroup) => newGroup._id === group._id,
          )?.trafficShare;
        });

        try {
          const { data } = await this.$apollo.mutate({
            mutation: UPDATE_SPLIT_URL_AB_TEST,
            variables: {
              _id: this.splitUrlTest._id,
              settings: {
                urlVariants: this.splitUrlTest.urlVariants,
              },
            },
          });

          if (data.result) {
            this.$notify({
              type: 'success',
              text: this.$t('notifications.saveSuccess'),
            });
            this.$router.push({
              name: 'split-url-test-details',
              params: { testId: this.splitUrlTest._id },
            });
          } else {
            this.$notify({
              type: 'error',
              text: this.$t('notifications.saveError'),
            });
          }
        } catch (error) {
          this.$notify({
            type: 'error',
            text: this.$t('notifications.saveError'),
          });
          console.error('Error save split URL test:', error);
        }
      },
      setDefaultGoal() {
        const defaultConversionGoal = _clone({
          _id: 'default_purchase',
          name: this.$t('conversionGoals.defaultGoals.purchase'),
        });

        const defaultFromLs = this.getDefaultGoalFromLS(
          this.splitUrlTest._id,
          this.DEFAULT_GOAL_BY_SPLIT_URL_TEST_KEY,
        );
        if (defaultFromLs) {
          const lastViewedGoal = this.goals.find((goal) => goal._id === defaultFromLs);
          if (lastViewedGoal) {
            this.selectedGoal = lastViewedGoal;
            return;
          }
        }

        this.selectedGoal = defaultConversionGoal;
      },
      async fetchSplitURLABTestStatistics() {
        this.loadingGoals = true;

        const goal = this.selectedGoal;

        await this.createMaterializedViewIfNecessary(goal, this.goals, this.splitUrlTest?.domainId);

        try {
          const response = await this.$apollo.query({
            query: GET_SPLIT_URL_AB_TEST_STATS,
            variables: {
              _id: this.splitUrlTest._id,
              goalId: goal._id,
            },
          });
          this.splitUrlTestStatistics = response?.data?.result || null;
        } catch (err) {
          console.error('Caught error during  get Split Url Test statistics', {
            msg: err.message,
            stack: err.stack,
          });
          this.splitUrlTestStatistics = null;
        }

        this.loadingGoals = false;
      },
      async getGoals() {
        const {
          data: { goals },
        } = await this.$apollo.query({
          query: GET_GOALS,
          variables: {
            domainId: this.splitUrlTest.domainId,
            includeRules: false,
          },
        });
        this.goals = [...goals.goals];
      },
      async createNewGoal(goal) {
        await this.upsertGoal(goal);
        if (this.campaign.domainId === goal.domainId) {
          this.setSelectedGoalToNew();
        }
      },
      redirectToEdit() {
        this.$router.push({
          name: 'split-url-test-edit',
          params: { testId: this.splitUrlTest._id },
        });
      },
      async startSplitURLABTest() {
        try {
          const response = await this.$apollo.mutate({
            mutation: START_SPLIT_URL_AB_TEST,
            variables: {
              _id: this.splitUrlTest._id,
            },
          });
          const result = response.data.result;
          if (result) {
            this.splitUrlTest = await this.getSplitUrlTest(this.splitUrlTest._id);
            this.splitUrlTest = {
              ...this.splitUrlTest,
              domain:
                this.domains.find((domain) => domain._id === this.splitUrlTest.domainId)?.domain ||
                '',
            };
          } else {
            this.$notify({
              type: 'error',
              message: this.$t('notifications.somethingWentWrong'),
            });
          }
        } catch (error) {
          this.$notify({
            type: 'error',
            message: this.$t('notifications.somethingWentWrong'),
          });
          console.error(error);
        } finally {
          this.showAdminLoader(false);
        }
      },
      async stopSplitURLABTest() {
        try {
          const response = await this.$apollo.mutate({
            mutation: STOP_SPLIT_URL_AB_TEST,
            variables: {
              _id: this.splitUrlTest._id,
            },
          });
          const result = response.data.result;
          if (result) {
            this.splitUrlTest = await this.getSplitUrlTest(this.splitUrlTest._id);
            this.splitUrlTest = {
              ...this.splitUrlTest,
              domain:
                this.domains.find((domain) => domain._id === this.splitUrlTest.domainId)?.domain ||
                '',
            };
          } else {
            this.$notify({
              type: 'error',
              message: this.$t('notifications.somethingWentWrong'),
            });
          }
        } catch (error) {
          this.$notify({
            type: 'error',
            message: this.$t('notifications.somethingWentWrong'),
          });
          console.error(error);
        }
      },

      getFirstItemFromRevenues(revenues) {
        const totalRevenue = revenues.length ? revenues[0] : null;
        if (!totalRevenue) return '-';
        return this.formatNumber({
          value: totalRevenue.total,
          currency: totalRevenue.currency,
          key: 'assistedRevenue',
          usedOnAnalyticsPage: true,
        });
      },

      getMoreItemsFromRevenues(revenues) {
        const totalRevenues = revenues.slice(1);
        return totalRevenues.map((totalRevenue) => {
          return `+${this.formatNumber({
            value: totalRevenue.total,
            currency: totalRevenue.currency,
            key: 'assistedRevenue',
            usedOnAnalyticsPage: true,
          })}`;
        });
      },

      urlToDisplay(url) {
        const maxLength = 83;

        return url.length < maxLength
          ? url
          : `${url.slice(0, maxLength - maxLength / 2)} ... ${url.slice(-(maxLength / 2))}`;
      },
    },
  };
</script>

<style lang="sass" scoped>
  @import '@/sass/variables/_colors.sass'

  .split-url-ab-test-start-stop
    display: flex
    justify-content: space-between
    .actions
      display: flex
      align-items: center
    .exclude-heading
      margin-top: 32px
  #start-split-url-ab-test
    margin-right: 12px
  .bordered-content
    padding: .75rem
    border: 1px solid $om-gray-300
    border-radius: 4px
    margin-bottom: 2.5rem
  .split-url-ab-test-table
    padding: 16px

  .exclude-urls
    max-width: 900px
    display: flex
    gap: 16px
    .exclude-url
      line-height: 1.5
  .split-url-status
    .status-text,.duration
      margin-left: 20px
  .where-url-text
    font-weight: 600
    font-size: 14px
    white-space: nowrap
    min-width: 80px
    line-height: 1.5
    color: $om-gray-800
</style>
<style lang="sass">
  @import '@/sass/variables/_colors.sass'

  .split-ur-test-details-page
    .goal-select .select-wrapper
      min-width: 20rem
    .th
      color: $om-gray-700
    .td
      color: $om-gray-800
    .truncate
      white-space: normal
      overflow: hidden
      text-overflow: ellipsis
      display: -webkit-box
      -webkit-line-clamp: 2
      -webkit-box-orient: vertical
      max-width: 100%
    .text-break
      word-wrap: break-word

  .skeleton-goals
    margin-left: auto

  .right-text-align
    text-align: right
</style>
