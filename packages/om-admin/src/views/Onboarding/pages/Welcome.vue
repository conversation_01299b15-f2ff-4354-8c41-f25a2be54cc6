<template lang="pug">
.om-onboarding-ecommerce-welcome.mb-6.mb-md-0
  wizard-top(:show-progress="false")
  .container(:style="'margin-top: 2.75rem'")
    .row
      .d-none.d-lg-flex.col-lg-1
      .col-3
        om-monks.mb-4.mb-md-0(monk="hi-user2")
      .col-9.col-lg-6.pb-5(:style="{ 'padding-right': '4.25rem' }")
        .welcome-input-container
          wizard-title.mb-md-3.text-center.text-md-left.mb-6 {{ $t('onboarding.welcome.title') }}
          om-body-text.mb-3.pb-2 {{ $t('onboarding.welcome.text1') }}
        .welcome-input-container
          template(v-if="showNameInputs")
            .form-group.mb-2
              .row
                .col
                  om-input#firstName(
                    block
                    v-model="firstName"
                    :placeholder="$t('onboarding.welcome.firstNamePlaceholder')"
                    :label="$t('onboarding.welcome.firstName')"
                  )
                    template(slot="error" v-if="$v.firstName.$error && !$v.firstName.required")
                      span {{ $t('requiredField') }}
                .col
                  om-input#lastName(
                    block
                    v-model="lastName"
                    :placeholder="$t('onboarding.welcome.lastNamePlaceholder')"
                    :label="$t('onboarding.welcome.lastName')"
                  )
                    template(slot="error" v-if="$v.lastName.$error && !$v.lastName.required")
                      span {{ $t('requiredField') }}
            .form-group.mb-2
              om-input#email(
                block
                v-model="email"
                type="email"
                :label="$t('onboarding.welcome.email')"
                :placeholder="$t('onboarding.welcome.emailPlaceholder')"
              )
                template(slot="error" v-if="$v.email.$error && !$v.email.required")
                  span {{ $t('requiredField') }}
                template(slot="error" v-if="$v.email.$error && !$v.email.isCoolEmail")
                  span {{ $t('invalidEmail') }}
                template(slot="error" v-if="hasEmailError")
                  span {{ $t('changeEmail.emailAlreadyExists') }}
          .form-group.mb-2.business-domain-group(v-if="showDomainInput")
            om-input#businessDomain(
              block
              v-model="domain"
              :label="$t('onboarding.welcome.whatDomain')"
              :placeholder="$t('onboarding.welcome.whatDomainPlaceholder')"
            )
              template(slot="error" v-if="$v.domain.$error && !$v.domain.required")
                span {{ $t('requiredField') }}
              template(slot="error" v-if="$v.domain.$error && !$v.domain.validUrl")
                span {{ $t('invalidUrl.default') }}
          .form-group.mb-2.welcome-form-group
            om-input#businessName(
              block
              v-model="businessName"
              :label="hasShopify ? $t('onboarding.welcome.whatStore') : $t('onboarding.welcome.whatBusiness')"
              :placeholder="hasShopify ? $t('onboarding.welcome.whatStorePlaceholder') : $t('onboarding.welcome.whatBusinessPlaceholder')"
            )
              template(slot="error" v-if="$v.businessName.$error && !$v.businessName.required")
                span {{ $t('requiredField') }}
          .form-group.mb-2.welcome-form-group(v-if="showPhoneNumber")
            om-input#phoneNumber(
              block
              v-model="phoneNumber"
              prefix="+36"
              phonePrefix
              :label="$t('onboarding.welcome.whatPhoneNumber')"
            )
              template(slot="error" v-if="$v.phoneNumber.$error && !$v.phoneNumber.required")
                span {{ $t('requiredField') }}
              template(slot="error" v-if="$v.phoneNumber.$error && !$v.phoneNumber.isPhoneNumber")
                span {{ $t('notValidFormat') }}
          .form-group.mb-2.welcome-form-group(v-if="hasReferralSourceFeature")
            om-select#referralSource.w-100(
              v-model="selectedReferralSource"
              :options="referralSourceOptions"
              :label="$t('onboarding.welcome.referralSourceLabel')"
              :placeholder="$t('onboarding.welcome.referralSourcePlaceholder')"
              :error="$v.selectedReferralSource.$error"
            )
            .form-text.text-danger(
              v-if="$v.selectedReferralSource.$error && !$v.selectedReferralSource.required"
            )
              span {{ $t('requiredField') }}
          .form-group.mb-2.welcome-form-group(v-if="isOtherSource")
            om-input#otherReferralSource(
              block
              v-model="otherReferralSource"
              :label="$t('onboarding.welcome.pleaseSpecify')"
            )
              template(
                slot="error"
                v-if="$v.otherReferralSource.$error && !$v.otherReferralSource.required"
              )
                span {{ $t('requiredField') }}
          .form-group.mt-5
            om-button(primary @click="saveData()" :loading="nextIsLoading") {{ $t('onboarding.welcome.btn') }}
      .d-none.d-lg-flex.col-lg-2
</template>
<script>
  import SAVE_EMAIL from '@/graphql/SaveEmail.gql';
  import UPDATE_LOGIN from '@/graphql/UpdateLogin.gql';
  import UPDATE_BUSINESS_NAME_AND_PHONE_NUMBER from '@/graphql/UpdateBusinessNameAndPhoneNumber.gql';
  import SAVE_PREFERRED_BY_DOMAIN from '@/graphql/SavePreferredLangByDomain.gql';
  import GENERATE_SITE_EXTRACTIONS from '@/graphql/GenerateSiteExtractions.gql';
  import GENERATE_AUTO_THEME from '@/graphql/GenerateAutoTheme.gql';
  import SAVE_REFERRAL_SOURCE from '@/graphql/SaveReferralSource.gql';
  import { required, email, requiredIf } from 'vuelidate/lib/validators';
  import { mapState, mapGetters } from 'vuex';
  import {
    validateDomain,
    removeProtocolFromDomain,
    removeWWWFromDomain,
    getPureDomain,
    cleanDomain,
  } from '@/util';
  import WizardTop from '@/components/Wizard/Top.vue';
  import WizardTitle from '@/components/Wizard/Title.vue';
  import { ERROR_MESSAGE_KEY, REFERRAL_SOURCES } from '@/utils/constant';
  import { FEATURE_CONSTANTS } from '@/utils/features';
  import { keyInErrors } from '@/utils/error';
  import { track } from '@/services/xray';
  import { apolloClient } from '@/apollo';
  import ADD_DOMAIN from '@/graphql/AddDomain.gql';
  import navigationMixin from '../accountSetup-navigation';

  import 'intl-tel-input/build/js/utils';
  import 'intl-tel-input/build/css/intlTelInput.css';

  export default {
    components: { WizardTop, WizardTitle },
    mixins: [navigationMixin],
    data() {
      return {
        nextIsLoading: false,
        businessName: '',
        phoneNumber: '',
        firstName: '',
        lastName: '',
        email: '',
        domain: '',
        originalEmail: '',
        selectedReferralSource: '',
        otherReferralSource: '',
        hasEmailError: false,
        hasDomainError: false,
        comingBack: false,
        hasValidName: true,
      };
    },
    computed: {
      ...mapGetters(['hasShopify', 'getLocale', 'hasAccountFeature']),
      ...mapState(['account']),
      isHungarianAccount() {
        return this.getLocale === 'hu';
      },

      showNameInputs() {
        return this.hasShopify || !this.hasValidName;
      },

      showPhoneNumber() {
        return this.isHungarianAccount && !this.getPhoneNumber;
      },

      showDomainInput() {
        return !this.account.settings?.domains.length;
      },

      getPhoneNumber() {
        return this.account.login.phoneNumber || this.account.settings.onboarding.additional.phone;
      },

      hasReferralSourceFeature() {
        return this.hasAccountFeature(FEATURE_CONSTANTS.REFERRAL_SOURCE);
      },

      referralSourceOptions() {
        const sources = REFERRAL_SOURCES[this.getLocale].slice();
        return [
          ...sources.filter((s) => s !== 'other').sort(() => Math.random() - 0.5),
          'other',
        ].map((source) => ({
          key: source,
          value: this.$t(`onboarding.welcome.referralSources.${source}`),
        }));
      },

      isOtherSource() {
        return this.selectedReferralSource?.key === 'other';
      },
    },
    watch: {
      email() {
        this.hasEmailError = false;
      },
    },
    validations: {
      businessName: {
        required,
      },
      phoneNumber: {
        required: requiredIf(function () {
          return this.showPhoneNumber;
        }),
        isPhoneNumber(v) {
          if (!this.showPhoneNumber) {
            return true;
          }
          const match = /^\+?(1|[2-9][0-9])\d{6,7}$/.test(v);

          return match;
        },
      },
      domain: {
        required: requiredIf(function () {
          return this.showDomainInput;
        }),
        validUrl(value) {
          if (!value || !this.showDomainInput) {
            return true;
          }
          const domain = getPureDomain(value);
          if (!domain) {
            return false;
          }
          return validateDomain(domain);
        },
      },

      email: {
        required: requiredIf(function () {
          return this.showNameInputs;
        }),
        isCoolEmail(v) {
          if (!this.showNameInputs) return true;

          return email(v.toLowerCase());
        },
      },
      firstName: {
        required: requiredIf(function () {
          return this.showNameInputs;
        }),
      },
      lastName: {
        required: requiredIf(function () {
          return this.showNameInputs;
        }),
      },
      selectedReferralSource: {
        required: requiredIf(function () {
          return this.hasReferralSourceFeature;
        }),
      },
      otherReferralSource: {
        required: requiredIf(function () {
          return this.isOtherSource;
        }),
      },
    },
    mounted() {
      this.initFormData();
    },
    methods: {
      initFormData() {
        this.firstName = !this.hasShopify ? this.account.login.firstName : '';
        this.lastName = !this.hasShopify ? this.account.login.lastName : '';
        this.email = '';

        this.originalEmail = this.account.login?.emailChange?.to ?? this.account.login.email;
        this.businessName = this.account.businessName;
        this.phoneNumber = this.getClearedPhoneNumber();
        this.hasValidName = this.firstName && this.lastName;
      },
      getClearedPhoneNumber() {
        const phoneNumber =
          this.$i18n.locale === 'hu' && this.getPhoneNumber
            ? this.getPhoneNumber.split('+36')[1]
            : '';
        return phoneNumber.replace(/\s/g, '');
      },
      async saveEmail() {
        if (this.originalEmail.toLowerCase() === this.email.toLowerCase()) {
          return;
        }

        try {
          const result = await this.$apollo.mutate({
            mutation: SAVE_EMAIL,
            variables: {
              newEmail: this.email.toLowerCase(),
              originalShopifyEmail: this.originalEmail.toLowerCase(),
            },
          });
          const { errors } = result;
          if (errors?.length) {
            if (keyInErrors(ERROR_MESSAGE_KEY.EMAIL_ALREADY_REGISTERED, errors)) {
              this.hasEmailError = true;
              this.$notify({
                type: 'error',
                text: this.$t('changeEmail.emailAlreadyExists'),
              });
            } else if (keyInErrors(ERROR_MESSAGE_KEY.RATE_LIMIT, errors)) {
              this.$notify({
                type: 'error',
                text: this.$t('changeEmail.tryAgainLater'),
              });
            } else {
              this.$notify({
                type: 'error',
                text: this.$t('changeEmail.saveError'),
              });
            }
            throw new Error('EMAIL_SAVE_ERROR');
          } else {
            this.hasEmailError = false;
          }
        } catch (e) {
          console.error('Error during change email', e);
          this.nextIsLoading = false;
          if (e.message === 'EMAIL_SAVE_ERROR') {
            throw e;
          }
        }
      },
      async redirectToNextPage() {
        if (this.hasShopify && this.hasEmailError) {
          return;
        }
        await this.navigateToNext({});
      },
      async updateBusinessNameAndPhoneNumber() {
        let variables = {
          businessName: this.businessName,
        };

        if (this.showPhoneNumber) {
          variables = {
            ...variables,
            phoneNumber: `+36${this.phoneNumber}`,
          };
        }

        await this.$apollo
          .mutate({
            mutation: UPDATE_BUSINESS_NAME_AND_PHONE_NUMBER,
            variables,
          })
          .then(() => {})
          .catch(() => {
            this.nextIsLoading = false;
            this.$notify({
              type: 'error',
              title: this.$t('notifications.saveError'),
            });
          });
      },
      async updateLogin() {
        await this.$apollo
          .mutate({
            mutation: UPDATE_LOGIN,
            variables: {
              input: {
                firstName: this.firstName,
                lastName: this.lastName,
              },
            },
          })
          .then(() => {})
          .catch(() => {
            this.nextIsLoading = false;
            this.$notify({
              type: 'error',
              title: this.$t('notifications.saveError'),
            });
          });
      },
      async saveData() {
        this.$v.$touch();
        if (this.$v.$invalid) {
          return;
        }

        const eventTrack = this.hasShopify
          ? 'ShopifyEn'
          : this.isHungarianAccount
          ? 'NonShopifyHu'
          : 'NonShopifyEn';
        track(`accountSetup-welcome${eventTrack}`, null);
        this.$v.$reset();
        try {
          await this.updateAccountData();
          this.generateAutoTheme();
          this.extractSiteData();
          await this.redirectToNextPage();
        } catch {}
      },

      generateAutoTheme() {
        this.$apollo.query({
          query: GENERATE_AUTO_THEME,
        });
      },

      extractSiteData() {
        this.$apollo.query({
          query: GENERATE_SITE_EXTRACTIONS,
        });
      },

      async saveDomain() {
        try {
          let domain = getPureDomain(this.domain); // this is unnecessary, because we override in the next line
          domain = removeProtocolFromDomain(this.domain); // cleanDomain is doing the same
          domain = removeWWWFromDomain(domain);
          await apolloClient.query({
            query: ADD_DOMAIN,
            variables: { domain, scrape: true },
          });
          return true;
        } catch (_) {
          return false;
        }
      },

      async saveReferralSource() {
        return this.$apollo.mutate({
          mutation: SAVE_REFERRAL_SOURCE,
          variables: {
            source: this.isOtherSource ? this.otherReferralSource : this.selectedReferralSource.key,
          },
        });
      },

      async determinePreferredLangByDomain() {
        const domain = cleanDomain(this.domain);
        try {
          await this.$apollo
            .mutate({
              mutation: SAVE_PREFERRED_BY_DOMAIN,
              variables: {
                domain,
              },
            })
            .catch(() => {
              this.nextIsLoading = false;
              this.$notify({
                type: 'error',
                title: this.$t('notifications.saveError'),
              });
            });

          return true;
        } catch (_) {
          return false;
        }
      },

      async updateAccountData() {
        this.nextIsLoading = true;
        const hasDomain = this.domain && this.showDomainInput;
        const saveDomainIfNecessary = hasDomain ? [this.saveDomain()] : [];
        const determinePreferredLang = hasDomain ? [this.determinePreferredLangByDomain()] : [];
        const saveNamesIfNecessary =
          this.hasShopify || !this.hasValidName ? [this.updateLogin()] : [];
        const saveReferralSourceIfNecessary = this.selectedReferralSource
          ? [this.saveReferralSource()]
          : [];

        try {
          if (this.hasShopify) {
            await this.saveEmail();
          }
          await Promise.all([
            this.updateBusinessNameAndPhoneNumber(),
            ...determinePreferredLang,
            ...saveNamesIfNecessary,
            ...saveDomainIfNecessary,
            ...saveReferralSourceIfNecessary,
          ]);

          this.nextIsLoading = false;
        } catch (e) {
          this.nextIsLoading = false;
          if (e.message === 'EMAIL_SAVE_ERROR') {
            throw e;
          }
        }
      },
    },
  };
</script>
<style lang="sass">
  @import '@/sass/variables/_colors.sass'

  .welcome-input-container
    width: min(100%, 34.75rem)
    .input-label, .select-label
      font-size: 14px
      font-weight: 700
      color: #505763

  .brand-onboarding-logout
      display: none

  .welcome-form-group
    .is-invalid
      color: #E4252D
      font-size: 12px
    .form-label
      font-size: 0.75rem

    .input-wrapper-phone
      .is-invalid
        display: block

  .input-wrapper-phone
    .input-element-wrapper
      border: none !important
  .email-info
    color: $om-gray-600
</style>
