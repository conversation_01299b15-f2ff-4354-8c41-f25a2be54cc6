<template lang="pug">
.code-insert-platform.mt-3.w-100
  om-body-text(bt400lg) {{ content || $t('codeInsertV2.gomag.description') }}
  om-accordion.mt-6
    om-accordion-item(
      :step="3"
      :trackPrefix="getSetupGuideTrackPrefix('step3')"
      :done="apiKeyInserted"
      defaultActive="true"
    )
      template(#name) {{ $t('codeInsertV2.gomag.steps.2.title') }}
      .code-insert-step-indent
        .api-label {{ $t('codeInsertV2.gomag.apiKeyLabel') }}
        .d-flex.align-items-center.mb-5
          om-input#api-key.w-50(
            type="text"
            v-model="apiKey"
            :error="apiKeyError"
            :errorText="apiKeyErrorText"
          )
          om-button.ml-2.bg-primary.text-white(@click="saveApiKey" :loading="loading") {{ $t('codeInsertV2.gomag.save') }}
        om-body-text(bt400md v-html="$t('codeInsertV2.gomag.steps.2.texts.0')")
    template(v-if="apiKeyInserted")
      .d-flex.align-items-end.mb-5.mt-5
        om-input#api-key.w-50(
          type="text"
          v-model="apiKey"
          :error="apiKeyError"
          :errorText="apiKeyErrorText"
          :label="$t('codeInsertV2.gomag.apiKeyLabel')"
          disabled
        )
        om-button.ml-2.bg-primary.text-white(@click="openRevokeModal" :loading="loading") {{ $t('codeInsertV2.gomag.delete') }}
    om-modal(
      name="revoke-api-key"
      :title="$t('codeInsertV2.gomag.revokeDialog.title')"
      :subtitle="$t('codeInsertV2.gomag.revokeDialog.text')"
      :width="420"
      color="light"
    )
      template(slot="modal-body")
      template(slot="modal-footer")
        .d-flex.justify-content-end
          om-button.mr-3(ghost @click="$modal.hide('revoke-api-key')") {{ $t('codeInsertV2.gomag.revokeDialog.cancel') }}
          om-button.bg-primary.text-white(@click="revokeApiKey" :loading="loading") {{ $t('codeInsertV2.gomag.revokeDialog.confirm') }}
</template>

<script>
  import childRouteMixin from '@/mixins/codeinsert/childRoute';
  import domainStatusMixin from '@/mixins/codeinsert/domainStatus';
  import platformMixin from '@/mixins/codeinsert/platform';
  import ADD_GOMAG_API_KEY from '@/graphql/AddGomagApiKey.gql';
  import REMOVE_GOMAG_API_KEY from '@/graphql/RemoveGomagApiKey.gql';
  import { mapGetters } from 'vuex';

  export default {
    name: 'Unas',
    components: {
      InsertCode: () => import('@/components/CodeInsert/platforms/InsertCode.vue'),
    },
    mixins: [childRouteMixin, domainStatusMixin, platformMixin],
    data() {
      return {
        apiKey: '',
        apiKeyError: false,
        apiKeyErrorText: '',
        apiKeyInserted: false,
        loading: false,
      };
    },
    computed: {
      ...mapGetters(['databaseId', 'shopSettings']),
    },
    mounted() {
      this.apiKey =
        this.shopSettings?.find((setting) => setting.live_domain === this.domain)?.apiKey || '';
      this.apiKeyInserted = !!this.apiKey;
    },
    methods: {
      async saveApiKey() {
        const apiKey = this.apiKey;
        const liveDomain = this.domain;
        this.loading = true;

        if (!apiKey) {
          this.apiKeyError = true;
          this.apiKeyErrorText = 'API Key is required';
          return;
        }

        try {
          const { data } = await this.$apollo.mutate({
            mutation: ADD_GOMAG_API_KEY,
            variables: {
              apiKey,
              liveDomain,
            },
          });

          if (data.addGomagApiKey.success) {
            this.apiKeyInserted = true;
            this.apiKeyError = false;
          } else {
            this.apiKeyError = true;
            this.apiKeyErrorText = this.$t('codeInsertV2.unas.invalidApiKey');
          }
        } catch (error) {
          this.apiKeyError = true;
          console.error('Error saving API key:', error);
        }
        this.loading = false;
      },
      openRevokeModal() {
        this.$modal.show('revoke-api-key');
      },
      async revokeApiKey() {
        try {
          const { data } = await this.$apollo.mutate({
            mutation: REMOVE_GOMAG_API_KEY,
            variables: {
              liveDomain: this.domain,
            },
          });

          if (data.removeGomagApiKey.success) {
            this.apiKeyInserted = false;
            this.apiKey = '';
          }
        } catch (error) {
          console.error('Error removing API key:', error);
        }
        this.$modal.hide('revoke-api-key');
      },
    },
  };
</script>

<style lang="sass" scooped>
  .api-label
    margin-left: 27px
    margin-bottom: 5px
    font-size: 12px
    font-weight: 400
    color: #505763

  .text-danger
    height: 0px !important
    margin-left: 27px !important

  .form-text
    margin-left: 27px !important
    height: 0px !important

  .brand-modal-title
    font-size: 18px !important
    font-weight: 700
    color: #505763
    margin-bottom: 5px
    margin-right: 10px

  .brand-modal-sub-title
    font-size: 15px !important
    margin-right: 50px !important

  .brand-modal-header
    padding-bottom: 0px !important
</style>
