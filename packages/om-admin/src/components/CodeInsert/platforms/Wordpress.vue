<template lang="pug">
.code-insert-platform.mt-3.w-100
  om-body-text(bt400lg) {{ content || $t('codeInsertV2.wordpress.description') }}
  om-accordion.mt-6
    om-accordion-item(
      :step="1"
      :trackPrefix="getSetupGuideTrackPrefix('step1')"
      :done="isConnected"
    )
      template(#name) {{ $t('codeInsertV2.wordpress.steps.0.title') }}
      .code-insert-step-indent
        om-body-text(bt400md v-html="$t('codeInsertV2.wordpress.steps.0.texts.0')")
        img.mt-3(:src="require('@/assets/admin/img/insert-code/platforms/wordpress/step_1_1.png')")
        om-body-text.mt-5(bt400md) {{ $t('codeInsertV2.wordpress.steps.0.texts.1') }}
        img.my-3(:src="require('@/assets/admin/img/insert-code/platforms/wordpress/step_1_2.png')")
        om-body-text.mt-5(bt400md) {{ $t('codeInsertV2.wordpress.steps.0.texts.2') }}

    om-accordion-item(
      :step="2"
      :trackPrefix="getSetupGuideTrackPrefix('step2')"
      :done="isConnected"
    )
      template(#name) {{ $t('codeInsertV2.wordpress.steps.1.title', { userId }) }}
      .code-insert-step-indent
        om-body-text(bt400md v-html="$t('codeInsertV2.wordpress.steps.1.texts.0', { userId })")
        om-body-text.mt-5(bt400md) {{ $t('codeInsertV2.wordpress.steps.1.texts.1') }}
        img.mt-5(:src="require('@/assets/admin/img/insert-code/platforms/wordpress/step_2_1.png')")
        om-body-text.mt-5.mb-3(bt400md) {{ $t('codeInsertV2.wordpress.steps.1.texts.2') }}

    om-accordion-item(
      :step="3"
      :trackPrefix="getSetupGuideTrackPrefix('step3')"
      :done="isConnected"
    )
      template(#name) {{ $t('codeInsertV2.wordpress.steps.2.title') }}
      om-body-text(bt400md) {{ $t('codeInsertV2.wordpress.steps.2.texts.0') }}
      om-body-text.mt-5(bt400md) {{ $t('codeInsertV2.wordpress.steps.2.texts.1') }}
      ul.mt-5
        li {{ $t('codeInsertV2.wordpress.steps.2.texts.2') }}
        li {{ $t('codeInsertV2.wordpress.steps.2.texts.3') }}
      om-body-text.mt-5(bt400md) {{ $t('codeInsertV2.wordpress.steps.2.texts.4') }}
      om-body-text.mt-5.mb-3(bt400md v-html="$t('codeInsertV2.wordpress.steps.2.texts.5')")
    template(v-if="isWoocommerceCentralEnabled")
      om-accordion-item(
        :step="4"
        :trackPrefix="getSetupGuideTrackPrefix('step4')"
        :done="isInstalled"
      )
        template(#name) {{ $t('codeInsertV2.wordpress.steps.3.title') }}
        .code-insert-step-indent
          om-body-text.mt-5(bt400md) {{ $t('codeInsertV2.wordpress.steps.3.texts.0') }}
          img.my-3(
            :src="require('@/assets/admin/img/insert-code/platforms/wordpress/woo-auth.png')"
          )
          om-button(primary @click="startWordpressAuthentication") {{ $t('codeInsertV2.wordpress.steps.3.button') }}
</template>

<script>
  import { mapGetters } from 'vuex';
  import { getAccountIdFromCookie } from '@/util';
  import childRouteMixin from '@/mixins/codeinsert/childRoute';
  import domainStatusMixin from '@/mixins/codeinsert/domainStatus';
  import platformMixin from '@/mixins/codeinsert/platform';
  import { baseUrl } from '../../../config/url';

  export default {
    name: 'Wordpress',
    mixins: [childRouteMixin, domainStatusMixin, platformMixin],
    props: {
      domain: {
        type: String,
        required: true,
      },
    },
    computed: {
      ...mapGetters(['shopSettings', 'isWoocommerceCentralEnabled']),
      isInstalled() {
        return !!this.shopSettings.find(
          (setting) =>
            setting.live_domain === this.domain && setting.consumer_key && setting.consumer_secret,
        );
      },
      userId() {
        return getAccountIdFromCookie();
      },
    },
    methods: {
      async startWordpressAuthentication() {
        window.location.href = `https://${this.domain}/wc-auth/v1/authorize?app_name=OptiMonk&scope=read_write&user_id=${this.userId}&return_url=${window.location.href}&callback_url=${baseUrl}app/wordpress/authentication?domain=${this.domain}`;
      },
    },
  };
</script>
