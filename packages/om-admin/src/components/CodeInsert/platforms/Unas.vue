<template lang="pug">
.code-insert-platform.mt-3.w-100
  om-body-text(bt400lg) {{ content || $t('codeInsertV2.unas.description') }}
  om-accordion.mt-6
    om-accordion-item(
      :step="1"
      :trackPrefix="getSetupGuideTrackPrefix('step1')"
      :done="isConnected"
    )
      template(#name) {{ $t('codeInsertV2.unas.steps.0.title') }}
      .code-insert-step-indent
        om-body-text(bt400md v-html="$t('codeInsertV2.unas.steps.0.texts.0')")
        insert-code(onlyCode type="unas")
    om-accordion-item(
      :step="2"
      :trackPrefix="getSetupGuideTrackPrefix('step2')"
      :done="isConnected"
    )
      template(#name) {{ $t('codeInsertV2.unas.steps.1.title') }}
      .code-insert-step-indent
        om-body-text(bt400md v-html="$t('codeInsertV2.unas.steps.1.texts.0')")
        img.mt-3(:src="require('@/assets/admin/img/insert-code/platforms/unas/step_2_1.jpg')")
        om-body-text.mt-5(bt400md) {{ $t('codeInsertV2.unas.steps.1.texts.1') }}
        img.mt-3(:src="require('@/assets/admin/img/insert-code/platforms/unas/step_2_2.jpg')")
        om-body-text.mt-5(bt400md) {{ $t('codeInsertV2.unas.steps.1.texts.2') }}
      ul.mt-5
        li {{ $t('codeInsertV2.unas.steps.1.texts.3') }}
        li {{ $t('codeInsertV2.unas.steps.1.texts.4') }}
        li {{ $t('codeInsertV2.unas.steps.1.texts.5') }}
        li {{ $t('codeInsertV2.unas.steps.1.texts.6') }}
      img.mt-3(:src="require('@/assets/admin/img/insert-code/platforms/unas/step_2_3.jpg')")
      om-body-text.mt-5(bt400md) {{ $t('codeInsertV2.unas.steps.1.texts.7') }}
      img.mt-3.mb-3(:src="require('@/assets/admin/img/insert-code/platforms/unas/step_2_4.jpg')")

    template(v-if="isUnasCentralEnabled")
      om-accordion-item(
        :step="3"
        :trackPrefix="getSetupGuideTrackPrefix('step3')"
        :done="apiKeyInserted"
        defaultActive="true"
      )
        template(#name) {{ $t('codeInsertV2.unas.steps.2.title') }}
        .code-insert-step-indent
          .api-label {{ $t('codeInsertV2.unas.apiKeyLabel') }}
          .d-flex.align-items-center.mb-5
            om-input#api-key.w-50(
              type="text"
              v-model="apiKey"
              :error="apiKeyError"
              :errorText="apiKeyErrorText"
            )
            om-button.ml-2.bg-primary.text-white(@click="saveApiKey" :loading="loading") {{ $t('codeInsertV2.unas.save') }}
          om-body-text(bt400md v-html="$t('codeInsertV2.unas.steps.2.texts.0')")
          img.mt-3.mb-3(
            :src="require('@/assets/admin/img/insert-code/platforms/unas/step_3_1.png')"
          )
          om-body-text(bt400md v-html="$t('codeInsertV2.unas.steps.2.texts.1')")
          img.mt-3.mb-3(
            :src="require('@/assets/admin/img/insert-code/platforms/unas/step_3_2.png')"
          )
          om-body-text(bt400md v-html="$t('codeInsertV2.unas.steps.2.texts.2')")
          img.mt-3.mb-3(
            :src="require('@/assets/admin/img/insert-code/platforms/unas/step_3_3.png')"
          )
      template(v-if="apiKeyInserted")
        .d-flex.align-items-end.mb-5.mt-5
          om-input#api-key.w-50(
            type="text"
            v-model="apiKey"
            :error="apiKeyError"
            :errorText="apiKeyErrorText"
            :label="$t('codeInsertV2.unas.apiKeyLabel')"
            disabled
          )
          om-button.ml-2.bg-primary.text-white(@click="openRevokeModal" :loading="loading") {{ $t('codeInsertV2.unas.delete') }}
      om-modal(
        name="revoke-api-key"
        :title="$t('codeInsertV2.unas.revokeDialog.title')"
        :subtitle="$t('codeInsertV2.unas.revokeDialog.text')"
        :width="420"
        color="light"
      )
        template(slot="modal-body")
        template(slot="modal-footer")
          .d-flex.justify-content-end
            om-button.mr-3(ghost @click="$modal.hide('revoke-api-key')") {{ $t('codeInsertV2.unas.revokeDialog.cancel') }}
            om-button.bg-primary.text-white(@click="revokeApiKey" :loading="loading") {{ $t('codeInsertV2.unas.revokeDialog.confirm') }}
</template>

<script>
  import childRouteMixin from '@/mixins/codeinsert/childRoute';
  import domainStatusMixin from '@/mixins/codeinsert/domainStatus';
  import platformMixin from '@/mixins/codeinsert/platform';
  import ADD_UNAS_API_KEY from '@/graphql/AddUnasApiKey.gql';
  import REMOVE_UNAS_API_KEY from '@/graphql/RemoveUnasApiKey.gql';
  import { mapGetters } from 'vuex';

  export default {
    name: 'Unas',
    components: {
      InsertCode: () => import('@/components/CodeInsert/platforms/InsertCode.vue'),
    },
    mixins: [childRouteMixin, domainStatusMixin, platformMixin],
    data() {
      return {
        apiKey: '',
        apiKeyError: false,
        apiKeyErrorText: '',
        apiKeyInserted: false,
        loading: false,
      };
    },
    computed: {
      ...mapGetters(['databaseId', 'shopSettings', 'isUnasCentralEnabled']),
    },
    mounted() {
      this.apiKey =
        this.shopSettings?.find((setting) => setting.live_domain === this.domain)?.apiKey || '';
      this.apiKeyInserted = !!this.apiKey;
    },
    methods: {
      async saveApiKey() {
        const apiKey = this.apiKey;
        const liveDomain = this.domain;
        this.loading = true;

        if (!apiKey) {
          this.apiKeyError = true;
          this.apiKeyErrorText = 'API Key is required';
          return;
        }

        try {
          const { data } = await this.$apollo.mutate({
            mutation: ADD_UNAS_API_KEY,
            variables: {
              apiKey,
              liveDomain,
            },
          });

          if (data.addUnasApiKey.success) {
            this.apiKeyInserted = true;
            this.apiKeyError = false;
          } else {
            this.apiKeyError = true;
            this.apiKeyErrorText = this.$t('codeInsertV2.unas.invalidApiKey');
          }
        } catch (error) {
          this.apiKeyError = true;
          console.error('Error saving API key:', error);
        }
        this.loading = false;
      },
      openRevokeModal() {
        this.$modal.show('revoke-api-key');
      },
      async revokeApiKey() {
        try {
          const { data } = await this.$apollo.mutate({
            mutation: REMOVE_UNAS_API_KEY,
            variables: {
              liveDomain: this.domain,
            },
          });

          if (data.removeUnasApiKey.success) {
            this.apiKeyInserted = false;
            this.apiKey = '';
          }
        } catch (error) {
          console.error('Error removing API key:', error);
        }
        this.$modal.hide('revoke-api-key');
      },
    },
  };
</script>

<style lang="sass" scooped>
  .api-label
    margin-left: 27px
    margin-bottom: 5px
    font-size: 12px
    font-weight: 400
    color: #505763

  .text-danger
    height: 0px !important
    margin-left: 27px !important

  .form-text
    margin-left: 27px !important
    height: 0px !important

  .brand-modal-title
    font-size: 18px !important
    font-weight: 700
    color: #505763
    margin-bottom: 5px
    margin-right: 10px

  .brand-modal-sub-title
    font-size: 15px !important
    margin-right: 50px !important

  .brand-modal-header
    padding-bottom: 0px !important
</style>
