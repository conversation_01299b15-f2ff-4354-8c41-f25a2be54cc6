<template lang="pug">
.content-step2
  loading-logo(v-if="loading")
  template(v-else)
    .description(:style="{ fontSize: '14px', color: '#505763' }") {{ $t('integrationFlow.convertKit.formAndSequenceHelper') }}
    om-select#formName.mt-5(
      v-model="formId"
      :options="forms"
      :label="$t('integrationFlow.convertKit.formHelper')"
      :placeholder="$t('integrationFlow.convertKit.selectAForm')"
      deSelectable
    )
    om-select#courseName(
      v-model="courseId"
      :options="courses"
      :label="$t('integrationFlow.convertKit.courseHelper')"
      :placeholder="$t('integrationFlow.convertKit.selectACourse')"
      deSelectable
    )
</template>
<script>
  import { IntegrationService } from '@/services/integrations/integrationService';
  import { getIntegrationsFixedFields } from '@om/integrations';
  import trackStep2Mixin from '@/mixins/integration/trackStep2';

  export default {
    mixins: [trackStep2Mixin],

    props: {
      settings: {
        type: Object,
        required: true,
      },

      validations: {
        type: Object,
        required: true,
      },
    },

    data() {
      return {
        step2Fields: ['formId', 'courseId'],
        loading: false,
        courseId: null,
        courses: [],
        formId: null,
        forms: [],
        hasError: false,
      };
    },

    watch: {
      courseId(option) {
        this.updateCourseSetting(option.key);
      },
      formId(option) {
        this.updateFormSetting(option.key);
      },
    },

    async mounted() {
      this.loading = true;
      // IMPORTANT to load data then set model (behavior of om-select)
      const { courses, forms, fields, tags } = await this.loadData();

      const settings = { ...this.settings };
      settings.fields = fields;
      settings.tags = tags;
      this.$emit('update:settings', settings);

      this.courses = courses;
      this.forms = forms;

      const { courseId, formId } = this.settings.convertedSettings;

      // om-select selected option only needs "key" from object
      this.courseId = courseId ? { key: courseId } : null;
      this.formId = formId ? { key: formId } : null;
      this.loading = false;
    },

    methods: {
      async loadData() {
        const integrationService = new IntegrationService(this.$apollo);
        let result = {};

        try {
          result = await integrationService.fetchIntegrationData(
            this.settings.type,
            this.settings.id,
          );

          const integrationFixedFields = getIntegrationsFixedFields('convertKit');
          const fixedFields = Object.keys(integrationFixedFields).map((key) => {
            return {
              id: integrationFixedFields[key],
              name: this.$t(`integrations.convertKit.fields.${key}`, { key }),
            };
          });

          result.fields = [...fixedFields, ...result.fields];
        } catch (e) {
          this.errorNotification(e.message);
        }

        return result;
      },
      errorNotification(message) {
        this.hasError = true;
        this.$notify({
          type: 'error',
          text: message,
        });
        setTimeout(() => {
          this.$bus.$emit('integration-show-first-step');
        }, 2000);
      },
      updateCourseSetting(value) {
        const settings = { ...this.settings };
        settings.convertedSettings.courseId = value;
        this.$emit('update:settings', settings);
        this.emitIsModified();
      },
      updateFormSetting(value) {
        const settings = { ...this.settings };
        settings.convertedSettings.formId = value;
        this.$emit('update:settings', settings);
        this.emitIsModified();
      },
    },
  };
</script>
