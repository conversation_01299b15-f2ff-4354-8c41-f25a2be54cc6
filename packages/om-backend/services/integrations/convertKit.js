// convertKit

// api key: **********************
// api secret: OgSSj78Ql5mPI5AxH51li8kRhjvd9seZ_AnGmKZ_xlg
// url: https://api.convertkit.com/v3/forms?api_key=<your_public_api_key>
const axios = require('axios');
const log = require('../../logger').child({ integration: 'convertKit' });

class ConvertKitAdapter {
  constructor({ apiKey }) {
    this.api = axios.create({
      baseURL: 'https://api.convertkit.com',
      params: {
        api_key: apiKey,
        // api_secret: apiKey
      },
    });
  }

  // GET /v3/forms
  async ping() {
    try {
      const response = await this.api.get('/v3/forms');
      if (response.status === 200) {
        return true;
      }
    } catch (error) {
      const errorMessage =
        error.response && error.response.data ? error.response.data : error.message;
      log.error(error, 'error while pinging convertKit:', errorMessage);
      return false;
    }
  }

  async getForms() {
    const { data } = await this.api.get('/v3/forms');

    return data.forms
      .sort((a, b) => {
        return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
      })
      .map((form) => ({
        key: form.id,
        value: form.name,
      }));
  }

  async getCourses() {
    const { data } = await this.api.get('/v3/courses');
    return data.courses
      .sort((a, b) => {
        return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
      })
      .map((course) => ({
        key: course.id,
        value: course.name,
      }));
  }

  async getTags() {
    const { data } = await this.api.get('/v3/tags');
    return data.tags
      .sort((a, b) => {
        return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
      })
      .map((tag) => ({ key: tag.id, value: tag.name }));
  }

  async getFields() {
    const { data } = await this.api.get('/v3/custom_fields');
    return data.custom_fields
      .sort((a, b) => {
        return a.label.toLowerCase().localeCompare(b.label.toLowerCase());
      })
      .map((t) => ({
        id: t.key,
        name: t.label,
      }));
  }

  async getData() {
    const ret = { forms: [], courses: [], tags: [], fields: [] };

    ret.forms = await this.getForms();
    ret.courses = await this.getCourses();
    ret.tags = await this.getTags();
    ret.fields = await this.getFields();

    return ret;
  }
}

// ;(async () => {
//   let convertKitAdapter = new ConvertKitAdapter({apiKey: '**********************'})
//   let canConnect = await convertKitAdapter.ping()
//   if (canConnect) {
//     // const forms = await convertKitAdapter.getForms()
//     // console.log('forms:', forms)
//     // const courses = await convertKitAdapter.getCourses()
//     // console.log('courses:', courses)
//     const tags = await convertKitAdapter.getTags()
//     console.log('tags:', tags)
//   }
// })()

module.exports = ConvertKitAdapter;
