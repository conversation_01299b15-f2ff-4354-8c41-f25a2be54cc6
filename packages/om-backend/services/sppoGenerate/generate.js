const { v4: uuidv4 } = require('uuid');
const moment = require('moment');

const { GENERATION_MODE } = require('@om/workflow-sppo');
const { model: PPOPromptModel } = require('../../resources/ppoPrompt/ppoPrompt.model');
const { domainAndShopByDomainId, determineDomainProviderServiceId } = require('../../util/domain');
const { model: AccountModel } = require('../../resources/account/account.model');
const { runFlow } = require('../../helpers/workflow');
const { isUnifiedProductBased } = require('../../helpers/unifiedProductBased');
const { getDefaultShopLocale } = require('../../helpers/defaultShopLocale');

// [product-sync][isUnified]
const GENERATORS_BY_SHOP_TYPE = {
  shopify: 'shopifyPPOPromptGeneration',
  shoprenter: 'shoprenterPPOPromptGeneration',
  unified: {
    unas: 'unasPPOPromptGeneration',
    woocommerce: 'wooPPOPromptGeneration',
  },
};

const getPPOGenerationFlowName = ({ databaseId, shop, domain }) => {
  const generalGenerator = 'generalPPOPromptGeneration';

  if (isUnifiedProductBased({ databaseId, shop, domain })) {
    return GENERATORS_BY_SHOP_TYPE.unified[shop?.type];
  }

  if (Object.keys(GENERATORS_BY_SHOP_TYPE).includes(shop?.type)) {
    return GENERATORS_BY_SHOP_TYPE[shop?.type];
  }

  return generalGenerator;
};

const handleOngoingGenerationError = (ongoingGeneration, log, promptIds) => {
  const message = 'Ongoing generation prevents the new one.';

  log.warn({ message, promptIds, ongoingGeneration });
  const generationError = new Error();

  generationError.extensions = {
    code: 400,
    message,
  };

  throw generationError;
};

const triggerSPPOGeneration = async (
  { domainId, promptIds, settings },
  { userId: databaseId, log, autoGenerate = false },
) => {
  const ongoingGeneration = await PPOPromptModel.findOne({
    _id: { $in: promptIds },
    domainId,
    databaseId,
    deletedAt: { $exists: false },
    $and: [
      { 'lastGeneration.uuid': { $exists: true } },
      { 'lastGeneration.startedAt': { $exists: true } },
      {
        $or: [
          { 'lastGeneration.finishedAt': { $exists: false } },
          { 'lastGeneration.finishedAt': { $eq: null } },
        ],
      },
    ],
  });

  if (ongoingGeneration) handleOngoingGenerationError(ongoingGeneration, log, promptIds);

  const account = await AccountModel.findOne(
    { databaseId },
    { 'settings.shops': 1, 'settings.domains': 1 },
  );

  const {
    settings: { shops, domains },
  } = account;

  const { domain, shop } = domainAndShopByDomainId(domainId, { shops, domains });
  const providerServiceId = determineDomainProviderServiceId({ domain, shop });
  const locale = await getDefaultShopLocale({ databaseId, providerServiceId });

  const promptsToGenerate = await PPOPromptModel.find({
    _id: { $in: promptIds },
    domainId: domain?._id,
    databaseId,
    deletedAt: { $exists: false },
  });

  if (!promptsToGenerate.length) {
    return { message: 'No available prompt to generate', success: false };
  }

  const startedAt = moment().toDate();
  const operations = [];

  await Promise.all(
    promptsToGenerate.map(async (item) => {
      const {
        variableNames,
        prompt,
        modelVersion,
        settings: promptSettings,
        useExternalData = false,
      } = item;
      const uuid =
        settings.generationMode === GENERATION_MODE.GENERATE_ALL
          ? uuidv4()
          : item.lastGeneration.uuid ?? uuidv4();

      await runFlow([
        {
          name: getPPOGenerationFlowName({ databaseId, shop, domain }),
          config: {
            context: {
              uuid,
              variableNames,
              prompt,
              dataValidation: promptSettings.dataValidation,
              requestedProductAmount: settings.requestedProductAmount,
              generationMode: settings.generationMode,
              databaseId,
              providerServiceId,
              modelVersion,
              useExternalData,
              autoGenerate,
              priority: settings.priority,
              locale,
            },
          },
        },
      ]);

      log.debug({
        message: 'PPO prompt generation flow started',
        uuid,
        variableNames,
        prompt,
        databaseId,
        providerServiceId,
        modelVersion,
        useExternalData,
      });

      operations.push({
        updateOne: {
          filter: { _id: item._id },
          update: {
            $set: {
              'lastGeneration.uuid': uuid,
              'lastGeneration.startedAt': startedAt,
              'lastGeneration.initializedAt': null,
              'lastGeneration.finishedAt': null,
              'lastGeneration.requestedProductAmount': settings.requestedProductAmount,
              'lastGeneration.processedProductCount': 0,
              'lastGeneration.erroredProductCount': 0,
              'lastGeneration.loadedProductCount': 0,
              'lastGeneration.originalProcessedProductCount':
                (item.lastGeneration?.processedProductCount ?? 0) +
                (item.lastGeneration?.originalProcessedProductCount ?? 0),
              'lastGeneration.generationMode': settings.generationMode,
              'lastGeneration.error': null,
            },
          },
        },
      });
    }),
  );

  await PPOPromptModel.bulkWrite(operations, { ordered: false });
};

module.exports = {
  triggerSPPOGeneration,
};
