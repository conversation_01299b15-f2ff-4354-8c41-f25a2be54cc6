const axios = require('axios');
const FormData = require('form-data');

const { om_optimage_url: optimageUrl } = process.env;

const decodeData = (res) => {
  const contentType = res.headers['content-type'];
  if (contentType.includes('application/json')) {
    const str = Buffer.from(res.data).toString('utf-8');

    return JSON.parse(str);
  }
  return res.data;
};

module.exports = {
  async process(src, input, log, query) {
    const data = FormData();

    for (const key in input) {
      const value = input[key];

      if (typeof value !== 'undefined') {
        data.append(key, value);
      }
    }

    if (typeof src === 'string') {
      data.append('url', src);
    } else if (typeof src === 'object') {
      data.append('src', src.buffer, { contentType: src.mimetype, filename: src.originalname });
    } else {
      throw new Error('invalid source image');
    }

    try {
      const resp = await axios.post(optimageUrl, data, {
        headers: data.getHeaders(),
        responseType: 'arraybuffer',
        params: { ...query },
      });

      if (resp.status === 200) {
        return {
          mime: resp.headers['content-type'],
          size: resp.headers['content-size'],
          data: resp.data,
          width: resp.headers['x-image-width'],
          height: resp.headers['x-image-height'],
        };
      }

      throw new Error(`invalid response from optimage service (${resp.status})`);
    } catch (err) {
      if (err.response && err.response.data) {
        const data = decodeData(err.response);

        log.error(err, 'error response from optimage service:', data);
        const thrown = new Error(data.error);
        thrown.name = 'OptimageError';

        throw thrown;
      }
      log.error(err);
      throw err;
    }
  },
};
