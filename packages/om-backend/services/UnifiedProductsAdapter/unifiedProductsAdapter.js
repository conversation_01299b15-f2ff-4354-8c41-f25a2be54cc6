const axios = require('axios');
const jwt = require('jsonwebtoken');

class UnifiedProductsAdapter {
  /**
   * Constructor for UnifyProductAdapter.
   * @param {string} productSyncUrl - The base URL of the product sync service.
   * @param {string} sharedKey - The shared secret key for JWT generation.
   * @param {Object} log - Logger instance.
   */
  constructor(productSyncUrl, sharedKey, log) {
    if (!productSyncUrl) {
      throw new Error('Missing productSyncUrl');
    }
    if (!sharedKey) {
      throw new Error('Missing sharedKey');
    }
    this.productSyncUrl = productSyncUrl;
    this.sharedKey = sharedKey;
    this.log = log;
  }

  /**
   * Generate a JWT token for authentication.
   * @param {number} userId - The user ID.
   * @returns {string} The generated JWT token.
   */
  generateOmToken(userId) {
    // TODO: create token with valid payload
    const payload = {
      loginId: userId,
      userId,
      accountType: 'normal',
      role: 'owner',
      iat: Math.floor(Date.now() / 1000),
    };

    return jwt.sign(payload, this.sharedKey, { expiresIn: '1h' });
  }

  /**
   * Generate request headers with authentication token
   * @private
   * @param {number} userId - The user ID for token generation
   * @returns {Object} Headers object with authentication token
   */
  _generateHeaders(userId) {
    const token = this.generateOmToken(userId);
    return {
      'Content-Type': 'application/json',
      'X-OM-TOKEN': token,
    };
  }

  /**
   * Fetch unified products from the product sync service.
   * @param {Object} params - The parameters for the request.
   * @param {Object} params.criteria - The search criteria.
   * @param {Object} params.criteria.userId - The user ID for token generation (synonym for databaseId).
   * @param {Object} params.criteria.domain - The domain (used as providerServiceId).
   * @param {Object} params.pagination - The pagination details.
   * @returns {Object} The unified products response.
   */
  async fetchUnifiedProducts({ criteria, pagination }) {
    if (!criteria.userId) {
      throw new Error('Missing required userId parameter');
    }

    if (!criteria.shopId) {
      throw new Error('Missing required shopId (providerServiceId) parameter');
    }

    const headers = this._generateHeaders(criteria.userId);
    try {
      const response = await axios.get(`${this.productSyncUrl}/products`, {
        params: {
          databaseId: criteria.userId,
          providerServiceId: criteria.shopId,
          page: pagination?.page || 1,
          limit: pagination?.limit || 30,
          search: criteria?.query || '',
        },
        headers,
      });

      const products = response.data.products.map((product) => ({
        id: product.productId,
        variant: product.parentProductId !== '0' ? product.productId : null,
        title: product.name || null,
        originalPrice: product.originalPrice || null,
        price: product.price || product.originalPrice || null,
        currency: product.currency || null,
        sku: product.sku || null,
        url: product.url || product.productUrl || null,
        imgUrl: product.imageUrl || null,
      }));

      const { page, totalPages } = response.data.meta;
      const hasNextPage = totalPages > page;

      const moneyFormat = `${
        products[0]?.currency.toUpperCase() === 'HUF' ? '{{amount_no_decimals}}' : '{{amount}}'
      } ${products[0]?.currency.toUpperCase()}`;

      return {
        products,
        hasNextPage,
        moneyFormat,
      };
    } catch (err) {
      this.log.error('Request failed:', {
        url: `${this.productSyncUrl}/products`,
        params: {
          databaseId: criteria.userId,
          providerServiceId: criteria.shopId,
          page: pagination?.page || 1,
          limit: pagination?.limit || 5,
          search: criteria?.query || '',
        },
        headers,
        response: err.response?.data || err.message,
      });
      throw new Error(`Failed to fetch unified products: ${err.message}`);
    }
  }

  /**
   * Start a product synchronization job for a specific platform.
   * @param {Object} params - The parameters for the sync job.
   * @param {string} params.platform - The platform type (e.g., 'unas', 'woo').
   * @param {string} params.providerServiceId - The domain of the shop.
   * @param {string} params.databaseId - The user ID (databaseId).
   * @param {Object} params.additionalParameters - Additional parameters to include in the request body.
   * @returns {Object} The response from the sync service.
   */
  async startSync({ platform, providerServiceId, locale, databaseId, additionalParameters = {} }) {
    if (!platform) {
      throw new Error('Missing required platform parameter');
    }

    if (!providerServiceId) {
      throw new Error('Missing required providerServiceId parameter');
    }

    if (!databaseId) {
      throw new Error('Missing required databaseId parameter');
    }

    const headers = this._generateHeaders(databaseId);

    const requestBody = {
      platform,
      providerServiceId,
      databaseId: databaseId.toString(),
      ...additionalParameters,
    };

    // Log when syncing starts
    if (this.log) {
      this.log.info('Starting product sync:', {
        platform,
        providerServiceId,
        databaseId,
        timestamp: new Date().toISOString(),
      });
    }

    try {
      const response = await axios.post(`${this.productSyncUrl}/flow`, requestBody, { headers });

      // Log successful sync start
      if (this.log) {
        this.log.info('Product sync started successfully:', {
          platform,
          providerServiceId,
          databaseId,
          jobId: response.data?.jobId || 'unknown',
        });
      }

      return response.data;
    } catch (err) {
      if (this.log) {
        this.log.error('Sync request failed:', {
          url: `${this.productSyncUrl}/flow`,
          requestBody,
          headers,
          response: err.response?.data || err.message,
        });
      }
      throw new Error(`Failed to start product sync: ${err.message}`);
    }
  }

  async runProdSyncQuery({ userId, queryName }) {
    if (!userId) {
      throw new Error('Missing required userId parameter');
    }

    if (!queryName) {
      throw new Error('Missing required queryName parameter');
    }

    const headers = this._generateHeaders(userId);

    const validateStatus = (status) => {
      return (status >= 200 && status < 300) || status === 400;
    };
    const response = await axios.get(`${this.productSyncUrl}/runQuery`, {
      params: { queryName },
      headers,
      validateStatus,
    });

    if (!validateStatus(response.status)) {
      throw new Error(`${response.message}`);
    }

    return response;
  }

  async runProdSyncResync({ userId, data }) {
    if (!userId) {
      throw new Error('Missing required userId parameter');
    }

    if (!data) {
      throw new Error('Missing required data');
    }

    const headers = this._generateHeaders(userId);
    console.log('@data', data);
    await axios.post(
      `${this.productSyncUrl}/resync`,
      { data },
      {
        headers,
      },
    );

    return `Resynchronization started. Shop count: ${data.length}`;
  }

  async getShopSettings({ providerServiceId, databaseId }) {
    const headers = this._generateHeaders(databaseId);

    try {
      const query = `?databaseId=${databaseId}&providerServiceId=${providerServiceId}`;
      const url = `${this.productSyncUrl}/getShopSettings${query}`;
      const response = await axios.get(url, { headers });
      return response.data.shopSettings;
    } catch (err) {
      if (this.log) {
        this.log.error('Sync debug request failed:', {
          headers,
          response: err.response?.data || err.message,
        });
      }
      throw new Error(`Sync debug failed: ${err.message}`);
    }
  }
}

module.exports = UnifiedProductsAdapter;
