const { model } = require('../../resources/smartRecommendations/smartRecommendations.model');
const { runFlow } = require('../../helpers/workflow');
const { getDomainBasedData } = require('./helper');
const { getDefaultShopLocale } = require('../../helpers/defaultShopLocale');

const getSPRList = async (databaseId = null) => {
  const filter = {
    'textEmbeddingStatus.finishedAt': { $ne: null },
    deletedAt: null,
  };
  if (databaseId) {
    filter.accountId = databaseId;
  }
  const SPRlist = await model.find(filter).lean();

  return SPRlist.map((SPR) => ({
    _id: SPR._id,
    domainId: SPR.domainId,
    accountId: SPR.accountId,
    requestedItems: SPR.generationStatus?.requestedAmount ?? null,
  }));
};

const triggerEmbedding = async (SPR, logger) => {
  try {
    const databaseId = SPR.accountId;
    const domainId = SPR.domainId;
    const { providerServiceId, platform } = await getDomainBasedData(databaseId, domainId);
    const locale = await getDefaultShopLocale({ databaseId, providerServiceId });

    const flowData = {
      name: 'SPRGenerateEmbeddings',
      config: {
        context: {
          databaseId,
          providerServiceId,
          domainId,
          platform,
          locale,
        },
      },
    };
    await runFlow([flowData]);

    await model.findOneAndUpdate(
      { _id: SPR._id },
      {
        $set: {
          textEmbeddingStatus: {
            startedAt: new Date(),
            finishedAt: null,
            resultRowCount: 0,
            retryCount: 0,
            error: { code: null, message: null },
          },
        },
      },
    );

    logger.info({ type: 'SPR-embedding', action: 'trigger', flowData });
    return true;
  } catch (error) {
    logger.error({ type: 'SPR-embedding', action: 'trigger', SPR, error });
  }
};

module.exports = {
  getSPRList,
  triggerEmbedding,
};
