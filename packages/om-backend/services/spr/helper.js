const moment = require('moment');
const { getDescriptionEmbeddingsTableId, updateTableExpiration } = require('@om/queries');
const { domainAndShopByDomainId, determineDomainProviderServiceId } = require('../../util/domain');
const {
  model: SPRConfigModel,
} = require('../../resources/smartRecommendations/smartRecommendations.model');
const { model: AccountModel } = require('../../resources/account/account.model');
const { isUnifiedProductBased } = require('../../helpers/unifiedProductBased');

const DATASET_IDS = {
  SPR: 'smart_recommendation',
};

const getDomainBasedData = async (databaseId, domainId) => {
  const account = await AccountModel.findOne(
    { databaseId },
    { 'settings.shops': 1, 'settings.domains': 1 },
  );
  const {
    settings: { shops, domains },
  } = account;
  const { domain, shop } = domainAndShopByDomainId(domainId, { shops, domains });
  const providerServiceId = determineDomainProviderServiceId({ domain, shop });

  // [product-sync][isUnified]
  const isUnified = isUnifiedProductBased({ shop });
  const platform =
    ['shopify', 'shoprenter'].includes(shop?.type) || isUnified ? shop.type : 'custom';

  return { providerServiceId, platform };
};

const setTimedHardDeleteToSPR = async ({ client, accountId, domainId, providerServiceId }) => {
  const expirationInDay = 7;

  await SPRConfigModel.updateOne(
    { accountId, domainId },
    { $set: { deletedAt: new Date(), expireAt: moment.utc().add(expirationInDay, 'days') } },
  );

  const datasetId = DATASET_IDS.SPR;
  const tableId = getDescriptionEmbeddingsTableId(accountId, providerServiceId);

  return updateTableExpiration(client, datasetId, tableId, expirationInDay);
};

const unsetTimedHardDeleteToSPR = async ({ client, accountId, domainId, providerServiceId }) => {
  await SPRConfigModel.updateOne(
    { accountId, domainId },
    { $set: { deletedAt: null }, $unset: { expireAt: null } },
  );

  const datasetId = DATASET_IDS.SPR;
  const tableId = getDescriptionEmbeddingsTableId(accountId, providerServiceId);

  return updateTableExpiration(client, datasetId, tableId, null);
};

module.exports = {
  determineDomainProviderServiceId,
  domainAndShopByDomainId,
  getDomainBasedData,
  setTimedHardDeleteToSPR,
  unsetTimedHardDeleteToSPR,
};
