const express = require('express');
const moment = require('moment');
const axios = require('axios');
const { purifyDomain } = require('../../util/domainHelper');

const router = express.Router();

const { model: AccountModel } = require('../../resources/account/account.model');
const { WPPluginModel } = require('../../resources/activity/activity.model');
const UnifiedProductsAdapter = require('../../services/UnifiedProductsAdapter/unifiedProductsAdapter');
const { determineDomainProviderServiceId } = require('../../util/domain');

const SERVICE = 'wordpress-controller';
const TRACK_TYPES = ['install', 'uninstall'];
const TRACK_TOKEN = 'yNGfPlqxPQD97FbZ6URy01d0n';

const getShopId = async ({ log, databaseId, domain, consumer_key, consumer_secret }) => {
  const url = `https://${domain}/wp-json/wc/v3/settings/general/omplugin_shop_id?consumer_key=${consumer_key}&consumer_secret=${consumer_secret}`;
  try {
    const {
      data: { value: shopId },
    } = await axios.get(url);
    return shopId;
  } catch (error) {
    const message = 'Error during getShopId request';
    log.error({
      service: SERVICE,
      message,
      databaseId,
      domain,
      url,
      error: { message: error?.message, stack: error?.stack },
    });
    return null;
  }
};

router.post('/connect', async (req, res) => {
  // eslint-disable-next-line
  let { user, domain } = req.body;
  user = parseInt(user, 10);
  user = isNaN(user) ? 0 : user;

  if (user < 1) {
    return res.send();
  }

  const account = await AccountModel.findOne({ databaseId: user });

  if (!account) {
    return res.send();
  }

  const hasSetting = account.settings.shops.forEach((setting) => {
    return setting.wordpress_domain === domain;
  });

  const shopSettings = account.settings.shops.filter((setting) => {
    return setting.wordpress_domain !== domain;
  });

  if (hasSetting) {
    return res.send();
  }

  const wordpressSetting = {
    type: 'wordpress',
    wordpress_domain: domain,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  shopSettings.push(wordpressSetting);
  account.settings.shops = shopSettings;
  await account.save();

  res.send();
});

router.post('/track', trackTokenMW, async (req, res) => {
  const { type, domain, timestamp } = req.body;
  const hasValues = TRACK_TYPES.includes(type) && domain && timestamp;

  if (!hasValues) return res.status(400).send();

  try {
    await WPPluginModel.create({
      type,
      domain: purifyDomain(domain),
      timestamp: moment(timestamp).utc().toDate(),
    });
  } catch (e) {
    return res.status(500).send();
  }

  return res.send('OK');
});

router.get('/track-data', trackTokenMW, async (req, res) => {
  const { from, to } = req.query;
  const fromDate = from
    ? moment(from).toDate()
    : moment().utc().subtract(30, 'days').startOf('day').toDate();
  const toDate = to ? moment(to).toDate() : moment().utc().endOf('day').toDate();
  const docs = WPPluginModel.find({ timestamp: { $gte: fromDate, $lte: toDate } });

  res.set('Content-Disposition', `attachment; filename="${Date.now()}-wp-installs.csv"`);
  res.write('domain,type,timestamp\n');

  for await (const { domain, type, timestamp } of docs) {
    res.write(`${domain},${type},${moment(timestamp).utc().format('YYYY-MM-DD HH:mm:ss')}\n`);
  }

  res.end('\n');
});

router.post('/authentication', async (req, res) => {
  try {
    const { key_id, user_id, consumer_key, consumer_secret, key_permissions } = req.body;
    const databaseId = parseInt(user_id, 10) || undefined;
    let liveDomain = purifyDomain(req.query.domain ?? '');
    if (!liveDomain) {
      req.log.error({
        service: SERVICE,
        message: 'Missing domain in request',
        databaseId,
      });
      return res.status(400).send({ error: 'Missing domain' });
    }

    if (!user_id || !key_id || !consumer_key || !consumer_secret || !key_permissions) {
      req.log.error({
        service: SERVICE,
        message: 'Missing required fields in request',
        databaseId,
        domain: liveDomain,
      });
      return res.status(400).send({ error: 'Missing required fields' });
    }

    const shopId = await getShopId({
      log: req.log,
      databaseId: parseInt(user_id, 10),
      domain: liveDomain,
      consumer_key,
      consumer_secret,
    });
    if (!shopId) {
      req.log.error({
        service: SERVICE,
        message: 'Missing shopId',
        databaseId,
        domain: liveDomain,
      });
      return res.status(500).send({ error: 'Failed to request store ID' });
    }

    const account = await AccountModel.findOne({ databaseId });
    if (!account) {
      req.log.error({
        service: SERVICE,
        message: 'Account not found',
        databaseId,
        domain: liveDomain,
      });
      return res.status(404).send({ error: 'Account not found' });
    }

    const existingShopIndex = account.settings.shops.findIndex(
      (shop) => shop.live_domain === liveDomain,
    );

    if (existingShopIndex !== -1) {
      account.settings.shops[existingShopIndex] = {
        type: 'woocommerce',
        shopId,
        consumer_key,
        consumer_secret,
        live_domain: liveDomain,
        active: 1,
        createdAt: account.settings.shops[existingShopIndex].createdAt,
        updatedAt: new Date(),
      };
    } else {
      account.settings.shops.push({
        type: 'woocommerce',
        shopId,
        consumer_key,
        consumer_secret,
        live_domain: liveDomain,
        active: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    const existingDomainIndex = account.settings.domains.findIndex(
      (domain) => domain.domain === liveDomain,
    );

    if (existingDomainIndex !== -1) {
      account.settings.domains[existingDomainIndex].shopId = shopId;
    }

    await account.save();

    const unifiedProductsAdapter = new UnifiedProductsAdapter(
      process.env.PRODUCT_SYNC_SERVICE_URL,
      process.env.om_shared_key,
      req.log,
    );

    const providerServiceId = determineDomainProviderServiceId({
      domain: account.settings.domains[existingDomainIndex],
    });

    unifiedProductsAdapter.startSync({
      platform: 'woo',
      providerServiceId,
      databaseId: user_id,
      additionalParameters: {
        consumerKey: consumer_key,
        consumerSecret: consumer_secret,
        shopUrl: `https://${liveDomain}/`, // TODO: handle if the domain and live_domain are different
      },
    });

    req.log.info({
      service: SERVICE,
      message: 'Succesful authentication',
      databaseId,
      shopId,
      domain: liveDomain,
    });

    return res.send();
  } catch (error) {
    req.log.error({
      service: SERVICE,
      message: 'Error in authentication',
      error: { message: error?.message, stack: error?.stack },
    });
    return res.status(500).send({ error: 'Internal server error' });
  }
});

async function trackTokenMW(req, res, next) {
  if (req.query.token === TRACK_TOKEN) {
    next();
  } else {
    res.sendStatus(401);
  }
}

module.exports = router;
