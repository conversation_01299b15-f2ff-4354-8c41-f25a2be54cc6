const express = require('express');
const { AccountData } = require('@om/queues');
const redis = require('../services/ioRedisAdapter');

const router = express.Router();

const ONE_MINUTE = 60 * 1000;

router.use(async (req, res, next) => {
  if (req.query.cron_token === process.env.cron_token) {
    req.setTimeout(10 * ONE_MINUTE);
    next();
  } else {
    res.sendStatus(401);
  }
});

router.post('/update-user-cache', async (req, res) => {
  const { databaseIds } = req.body;

  if (!Array.isArray(databaseIds) || !databaseIds.length) {
    req.log.error('An array of databaseIds are required to update user caches!');
    return res.status(400).json({ success: false });
  }

  for (const databaseId of databaseIds) {
    try {
      const cacheKey = `frontend:${databaseId}:settings`;
      await redis.del(cacheKey);

      await AccountData.addJobToBackgroundQueue({ databaseId });
      req.log.info({ message: `Added job to background queue, userId: ${databaseId}` });
    } catch (err) {
      req.log.error(err, `Failed to add job to background queue ${databaseId}`);
      return res.status(500).json({ success: false });
    }
  }
  return res.send({ success: true });
});

module.exports = router;
