const express = require('express');

const router = express.Router();
const { getFormattedTableName, getLanguagesTableId, getLanguages } = require('@om/queries');
const { daysFromNow } = require('@om/common');
const { tokenHeader } = require('../util/jwt');
const {
  preparePPOExport,
  prepareExternalPPOExport,
  getPostProcessTransformStream,
  getExternalPostProcessTransformStream,
} = require('../helpers/ppo');
const { model: PPOPrompt } = require('../resources/ppoPrompt/ppoPrompt.model');
const { model: AccountModel } = require('../resources/account/account.model');
const { model: LoginModel } = require('../resources/login/login.model');
const { BigQueryClient } = require('../services/bigQueryAdapter');
const { domainAndShopByDomainId, determineDomainProviderServiceId } = require('../util/domain');
const { queueLeadsExportUpdate } = require('../services/queue/leadsExportQueue');
const { getDefaultShopLocale } = require('../helpers/defaultShopLocale');

const bigQueryClient = new BigQueryClient();
const BQ_PROJECT_ID = process.env.gcp_bigquery_project_id;

router.use(async (req, res, next) => {
  const { userId, loginId, superadmin } = await tokenHeader(req);
  if (userId) {
    req.userId = userId;
    req.loginId = loginId; // Login id is used to send export emails to logged in user
    req.isSuperAdmin = superadmin; // SuperAdmin can configure the email
    next();
  } else {
    res.sendStatus(401);
  }
});

router.get('/subscribers', async (req, res) => {
  try {
    const { userId, loginId, isSuperAdmin } = req;
    const filter = JSON.parse(req.query.filter);
    let sorting = req.query.sorting
      ? JSON.parse(req.query.sorting)
      : { field: 'createdAt', direction: '-1' };
    if (sorting.field === 'date' || sorting.field === 'updatedAt') {
      sorting.field = 'createdAt';
    }
    const type = req.query.type;
    const email = req.query.email;

    const { email: loginEmail, locale } = await LoginModel.findOne(
      { _id: loginId },
      { email: 1, locale: 1 },
    );
    const isLoggedInEmail = loginEmail === email;
    if (isSuperAdmin === false && !isLoggedInEmail) {
      return res.status(401).send('only superAdmin can use other email than logged in user email');
    }

    queueLeadsExportUpdate({
      userId,
      loginId,
      filter,
      sorting,
      type,
      email,
      locale,
    });
    req.log.info({ data: { userId, filter, type } }, 'Queued leads export');

    res.sendStatus(200);
  } catch (err) {
    req.log.error(err, 'Failed to export subscribers.');
    return res.status(500).send('Something went wrong');
  }
});

router.get('/ppo-results/:domainId', async (req, res) => {
  const domainId = req.params.domainId;
  const limit = req.query.limit ? parseInt(req.query.limit, 10) : null;
  const useExternalData = req.query.useExternalData || false;
  const databaseId = req.userId;

  try {
    const [prompts, account] = await Promise.all([
      PPOPrompt.find({
        databaseId,
        domainId,
        'lastGeneration.finishedAt': { $ne: null },
        deletedAt: { $exists: false },
        useExternalData: !useExternalData ? { $in: [null, false] } : useExternalData,
      }),
      AccountModel.findOne({ databaseId }, { 'settings.shops': 1, 'settings.domains': 1 }),
    ]);
    const { domain, shop } = domainAndShopByDomainId(domainId, account.settings);

    if (!domain) {
      throw new Error('Unable to resolve domain.');
    }

    let languages = [];

    if (prompts.length === 0) {
      return res
        .status(404)
        .send('No prompts ready for export, generation must be finished first.');
    }

    const providerServiceId = determineDomainProviderServiceId({ domain, shop });
    const isShoprenter = domain.platform === 'shoprenter';

    if (!useExternalData && isShoprenter) {
      const languagesTableId = getFormattedTableName(
        `${BQ_PROJECT_ID}.${getLanguagesTableId(databaseId, providerServiceId)}`,
      );
      languages = await getLanguages({
        bigQueryClient: bigQueryClient.client,
        languagesTableId,
        location: 'US',
      });
    }

    const locale = await getDefaultShopLocale({ databaseId, providerServiceId });

    const { query, csvStream } = useExternalData
      ? await prepareExternalPPOExport({
          databaseId,
          providerServiceId,
          prompts,
          domain,
        })
      : await preparePPOExport({
          databaseId,
          providerServiceId,
          prompts,
          domain,
          languages,
          platform: domain.platform,
          locale,
        });

    const resultStream = bigQueryClient
      .runQueryStream(query, {
        params: {
          numberOfItems: limit || 500000,
          fromDate: daysFromNow(30),
        },
      })
      .on('error', (err) => {
        req.log.error(err, { databaseId, domainId, message: 'Failed to export ppo results.' });
        resultStream.end();

        if (!res.headersSent) {
          res.status(500).end('Something went wrong');
        }
      });

    res
      .contentType('text/csv')
      .attachment(
        `ppo-results-${
          useExternalData ? 'external-' : ''
        }${providerServiceId}.${new Date().toISOString()}.csv`,
      );
    const postProcessStream = useExternalData
      ? getExternalPostProcessTransformStream({ prompts })
      : getPostProcessTransformStream({ domain, shop, languages, prompts });

    resultStream.pipe(postProcessStream).pipe(csvStream).pipe(res);
  } catch (err) {
    req.log.error({ err, data: { databaseId, domainId, limit } }, 'Failed to export ppo results.');
    res.status(500).end('Something went wrong');
  }
});

module.exports = router;
