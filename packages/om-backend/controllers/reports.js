const _get = require('lodash.get');
const { getAnalyticsTopLevelQuery, getEventTableId } = require('@om/queries');
const moment = require('moment');
const { JFAdapter } = require('../helpers/JFAdapter');
const { redis } = require('../services/ioRedisAdapter');
const log = require('../logger');
const { tokenHeader } = require('../util/jwt');
const {
  createCampaignReport,
  getShopSettings,
  getProviderServiceIds,
} = require('../helpers/reportsHelper');
const { BigQueryClient } = require('../services/bigQueryAdapter');
const { domainAndShopByDomainId } = require('../util/domain');
const { model: AccountModel } = require('../resources/account/account.model');
const { bayesianABTest } = require('../helpers/bayesianAbTest');
const { model: ConfigModel } = require('../resources/config/config.model');
const { getEndDateTime } = require('../resources/goal/helpers/util');
const { purifyDomain } = require('../util/domainHelper');

const cookieAuth = async (req, res, next) => {
  try {
    const { userId } = await tokenHeader(req);
    if (userId) {
      res.locals.userId = userId;
      next();
      return;
    }
  } catch (e) {
    log.error({ err: e }, 'Invalid authentication token');
    res.sendStatus(401);
  }
  res.sendStatus(401);
};

const validateParams = (params) => {
  const missingParams = [];
  for (const key in params) {
    if (typeof params[key] === 'undefined') {
      missingParams.push(key);
    }
  }
  return missingParams;
};

const REPORT = {
  campaign: {
    getReportFn: (jfAdapter) => jfAdapter.getCampaignReport,
    createReportFn: createCampaignReport,
  },
};

const getReport = async ({ reportName, res, req }) => {
  const { date_start, date_end } = req.query;

  const jfAdapter = JFAdapter();

  const { userId } = res.locals;

  const missingParams = validateParams({ date_start, date_end });
  if (missingParams.length > 0) {
    const message = `Missing required query parameters: ${missingParams.join(', ')}`;
    log.error(`${reportName} report: ${message}`);
    return res.status(400).end(message);
  }

  let rawReport;

  const { shops, domains } = await getShopSettings(userId);
  const providerServiceIds = getProviderServiceIds({ shops, domains });

  try {
    const getReport = REPORT[reportName].getReportFn(jfAdapter);
    rawReport = (await getReport({ date_start, date_end, userId, providerServiceIds })).data;
    const createReportFn = REPORT[reportName].createReportFn;
    const report = await createReportFn({
      shops,
      domains,
      reportName,
      rawReport,
      parameters: { date_start, date_end, userId },
    });

    res.send(report);
  } catch (err) {
    if (err?.response?.data?.reason === 'USER_NOT_FOUND') {
      return res.send({});
    }
    const errorMessage = `Cannot get ${reportName} report for ${userId}`;
    log.error({ err: _get(err, 'response.data.errors', err.stack || err.message) }, errorMessage);
    res.status(500).end(`Cannot get ${reportName} report for ${userId}`);
  }
};

const getCampaignReport = async (req, res) => {
  return getReport({ reportName: 'campaign', req, res });
};

const getTopLevelDomainData = async (req, res) => {
  const { userId: accountId } = res.locals;
  const { domain_id: domainId, start_date: startDate, end_date: endDate } = req.query;

  const bigQueryClient = new BigQueryClient();

  if (!domainId || !startDate || !endDate) {
    const message = 'domain_id, start_date and end_date are required parameters!';
    log.error(`getTopLevelData error: ${message}`, { accountId });
    return res.status(400).end(message);
  }

  const isEndToday = moment.utc().startOf('day').unix() === moment.utc(endDate).unix();

  let analyticsLastUpdate;
  if (isEndToday) {
    analyticsLastUpdate = await ConfigModel.findOne({ key: 'campaign_analytics_last_update' });
  }
  const endTime = isEndToday
    ? getEndDateTime({
        endDate,
        analyticsLastUpdate: analyticsLastUpdate?.value,
      })
    : moment.utc(endDate).endOf('day').toISOString();

  const cacheKey = `reportsTopLevelDomainData:v1:${accountId}:${domainId}:${startDate}:${endTime}`;

  const cachedValue = await redis.get(cacheKey);
  if (cachedValue) {
    return res.send(JSON.parse(cachedValue));
  }

  const account = await AccountModel.findOne(
    { databaseId: accountId },
    { 'settings.shops': 1, 'settings.domains': 1 },
  );
  const { settings: { shops = [], domains = [] } = {} } = account ?? {};
  const { domain } = domainAndShopByDomainId(domainId, {
    shops,
    domains,
  });

  if (!domain) {
    log.error('getTopLevelData error: domainId not found', { accountId });
    return res
      .status(400)
      .end(`Cannot get top level domain data for user: ${accountId}, domain: ${domainId}`);
  }

  const providerServiceId = purifyDomain(
    domain.providerServiceIdOverride || domain.shopId || domain.domain,
  );
  const pureDomain = purifyDomain(domain.domain);
  const eventsTable = getEventTableId(accountId, providerServiceId);

  try {
    const query = getAnalyticsTopLevelQuery({
      accountId,
      providerServiceId,
      eventsTable,
      domain: pureDomain,
    });
    const [rawResult] = await bigQueryClient.runQuery(query, {
      params: { startDate, endDate: endTime },
    });

    const mappedRevenue = {};
    const mappedAov = {};

    rawResult.ordersByCurrency.forEach((orderByCurrency) => {
      const { revenue, orderCount, currency } = orderByCurrency;

      for (const device of ['mobile', 'desktop', 'allType']) {
        mappedRevenue[device] = mappedRevenue[device] || {};
        mappedAov[device] = mappedAov[device] || {};
        mappedRevenue[device][currency] = revenue[device];
        mappedAov[device][currency] = parseInt(orderCount[device], 10)
          ? Math.round((parseInt(revenue[device], 10) / parseInt(orderCount[device], 10)) * 100) /
            100
          : null;
      }
    });

    const {
      visitors,
      visitorsWithOrders,
      nonstandardVisitorsWithOrders,
      nonstandardTotalOrders,
      conversionRate,
      nonstandardConversionRate,
      totalOrders,
    } = rawResult;

    const topLevelDomainData = {
      visitors,
      visitorsWithOrders,
      nonstandardVisitorsWithOrders,
      conversionRate,
      nonstandardConversionRate,
      totalOrders,
      nonstandardTotalOrders,
      revenue: mappedRevenue,
      aov: mappedAov,
    };

    await redis.setex(cacheKey, 3600, JSON.stringify(topLevelDomainData));

    res.send(topLevelDomainData);
  } catch (err) {
    const isNotFoundError = err?.response?.status?.errorResult?.reason === 'notFound';
    let errorMessage = `Cannot get top level domain data for user: ${accountId}, domain: ${domainId}`;
    if (isNotFoundError) {
      errorMessage = `USER_NOT_FOUND: ${errorMessage}`;
      log.warn(err, errorMessage, { accountId, domainId });
      return res.status(404).end(errorMessage);
    }

    log.error(err, errorMessage, { accountId, domainId });
    res.status(500).end(errorMessage);
  }
};

const hasAtLeastOneConversion = (variants) => {
  return variants.some((v) => v.conversions);
};

const chanceToWin = async (req, res) => {
  const {
    body: { campaigns },
  } = req;
  const { userId } = res.locals;

  if (!campaigns) {
    const message = 'Missing required data campaigns';
    log.error(`chanceToWin error: ${message}`, { userId });
    return res.status(400).end(message);
  }

  const deviceTypes = ['allType', 'desktop', 'mobile'];

  const results = {};
  try {
    for (let i = 0; i < campaigns.length; i++) {
      const { id, variants } = campaigns[i];

      if (!id || !variants) {
        const message = 'chanceToWin error: Required properties in campaign: id, variants';
        log.error(message, { userId });
        return res.status(400).end(message);
      }

      results[id] = {};

      if (variants.length) {
        if (typeof campaigns[0]?.variants[0]?.impressions === 'object') {
          deviceTypes.forEach((deviceType) => {
            const variantsForDeviceType = variants.map((variant) => {
              return {
                id: variant.id,
                impressions: variant.impressions[deviceType],
                conversions: variant.conversions[deviceType],
              };
            });
            if (hasAtLeastOneConversion(variantsForDeviceType)) {
              results[id][deviceType] = bayesianABTest(variantsForDeviceType);
            } else {
              results[id][deviceType] = variants.reduce((deviceData, v) => {
                deviceData[v.id] = null;
                return deviceData;
              }, {});
            }
          });
        }
      } else {
        results[id] = bayesianABTest(variants);
      }
    }
    res.send(results);
  } catch (err) {
    const errorMessage = `Cannot calculate chance to win for ${userId}`;
    log.error(err, errorMessage);
    return res.status(500).end(errorMessage);
  }
};

module.exports = {
  getCampaignReport,
  cookieAuth,
  chanceToWin,
  getTopLevelDomainData,
};
