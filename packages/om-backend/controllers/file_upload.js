const express = require('express');

const router = express.Router();
const path = require('path');
const AdmZip = require('adm-zip');
const shortid = require('shortid');
const { tokenHeader } = require('../util/jwt');
const { masterModel, userModel } = require('../resources/image/image.model');
const { model: campaignModel } = require('../resources/campaign/campaign.model');
const { model: VariantTemplateModel } = require('../resources/campaign/variantTemplate.model');
const { model: AccountModel } = require('../resources/account/account.model');
const { uploadToS3, deleteObject } = require('../services/s3');
const { uploadImage } = require('../services/imageService');
const optimage = require('../services/optimageAdapter');
const { slugify } = require('../util/string');
const { isWhitelabelRequest } = require('../helpers/whiteLabel');
const { convertToMultipleFormats } = require('../services/fonts/converter');
const { uploadFonts } = require('../helpers/font');

const { FEATURES, isFeatureEnabled } = require(`../util/feature`);

const CDN_URL = process.env.cdn_url;
const WHITELABEL_CDN_URL = process.env.whitelabel_cdn_url;

router.use(async (req, res, next) => {
  const { userId } = await tokenHeader(req);
  if (userId) {
    req.userId = userId;
    next();
  } else {
    next();
  }
});

const processImage = async (req, res, next) => {
  if (req.file) {
    const { mimetype } = req.file;

    // If svg or gif UPLOADED (NOT EDITED) we do not touch it. => JPEGs, WEBP and PNGs will be optimized automatically.
    if (mimetype.includes('svg') || mimetype.includes('gif')) {
      return next();
    }
  }
  try {
    const input = {
      width: req.body.resizeWidth,
      height: req.body.resizeHeight,
      cropTop: req.body.cropTop,
      cropLeft: req.body.cropLeft,
      cropWidth: req.body.cropWidth,
      cropHeight: req.body.cropHeight,
      rotate: req.body.rotate,
      scaleX: req.body.scaleX,
      scaleY: req.body.scaleY,
    };
    let src;

    if (req.file) {
      src = req.file;
    } else if (req.body.srcUrl) {
      src = req.body.srcUrl;
    } else {
      throw new Error('Input image is missing');
    }

    const result = await optimage.process(src, input, req.log, req.query);
    let originalname = req.file ? req.file.originalname : req.body.filename;
    // eslint-disable-next-line
    let { name, ext } = path.parse(originalname);

    if (result.mime === 'image/jpeg') {
      ext = '.jpg';
    } else if (result.mime === 'image/png') {
      ext = '.png';
    } else {
      req.log.warn(`unhandled image MIME type received from optimage: ${result.mime}`);
    }

    originalname = name + ext;

    req.output = {
      width: result.width,
      height: result.height,
    };

    req.file = {
      ...req.file,
      originalname,
      mimetype: result.mime,
      size: result.size,
      buffer: result.data,
    };
  } catch (err) {
    req.log.error(err, 'error while processing image');
    if (err.name === 'OptimageError') {
      return res.status(400).json({ error: err.message }).end();
    }

    return res.status(500).json({ error: 'Error while processing image' });
  }
  next();
};

router.post('/editor', processImage, async (req, res) => {
  try {
    const saveType = req.body.type;
    const saveValue = req.body.value;
    const userId = req.userId;
    const name = req.body.method === 'delete' ? req.body.name : slugify(req.file.originalname);
    const width =
      req.query.trim === '1' ? Number(req.output.width) : req.body.width || req.body.resizeWidth;
    const height =
      req.query.trim === '1' ? Number(req.output.height) : req.body.height || req.body.resizeHeight;
    const overwrite = req.body.overwrite === 'true';
    const file = req.file;

    const imageInstance = await uploadImage({
      file,
      width,
      height,
      name,
      userId,
      saveType,
      saveValue,
      overwrite,
    });

    res.send({ image: imageInstance });
  } catch (e) {
    req.log.error(e);
    res.status(500).send({ e });
  }
});

router.delete('/editor/:id', async (req, res) => {
  const userId = req.userId;
  const campModel = campaignModel(userId);
  const imageId = req.params.id;
  let useCount = 0;
  const account = await AccountModel.findOne(
    { databaseId: userId },
    {
      features: 1,
    },
  );
  if (isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
    useCount = (
      await VariantTemplateModel.distinct('campaignId', {
        databaseId: userId,
        'template.images._id': imageId,
      })
    )?.length;
  } else {
    useCount = await campModel.count({ 'variants.template.images._id': imageId });
  }

  let success = false;
  if (useCount === 0) {
    success = true;
    const { name, saveType, saveValue } = req.query;
    const ImageModel = saveType === 'base' ? masterModel : userModel;
    await deleteObject({ name, saveType, saveValue, userId });
    await ImageModel.deleteOne({ _id: imageId });
  }
  res.send({ success, useCount });
});

router.post('/custom', async (req, res) => {
  if (!req.file) {
    return res.status(422).send({ error: 'Image file is required' });
  }
  try {
    const { dir, name, ext } = path.parse(req.file.originalname);
    req.file.originalname = path.join(dir, `${name}-${Date.now()}${ext}`);
    const url = await uploadToS3(req.file, {
      saveType: 'template',
      saveValue: 'custom',
      userId: req.userId,
    });

    res.send({ url });
  } catch (e) {
    req.log.error(e);
    res.status(500).send('Something went wrong');
  }
});

const customUploadHandler = (saveType, saveValue) => async (req, res) => {
  if (!req.file) {
    return res.status(422).send({ error: 'Image file is required' });
  }

  try {
    const { dir, ext } = path.parse(req.file.originalname);
    req.file.originalname = path.join(dir, `${shortid.generate()}${ext}`);
    const url = await uploadToS3(req.file, {
      saveType,
      saveValue,
      userId: req.userId,
    });

    res.send({ url });
  } catch (e) {
    req.log.error(e);
    res.status(500).send('Something went wrong');
  }
};

router.post('/template-thumbnails', customUploadHandler('thumbnails', 'template'));
router.post('/segment-icons', customUploadHandler('icons', 'segment'));

const validateFile = async (file, userId, name) => {
  const filename = file.originalname;
  const extension = filename.substring(filename.lastIndexOf('.') + 1);
  const extensions = ['ttf', 'otf', 'woff', 'woff2'];
  if (!file || !file.size) {
    return { errorKey: 'missingFile' };
  }

  if (!name) {
    return { errorKey: 'nameIsRequired' };
  }

  if (extensions.includes(extension) === false) {
    return { errorKey: 'extensionMismatchV2' };
  }

  const nOfFontsWithThisName = await AccountModel.countDocuments({
    databaseId: userId,
    'settings.customFonts.name': name,
  });
  if (nOfFontsWithThisName) {
    return { errorKey: 'namIsUsedV2' };
  }

  return true;
};

router.post('/custom-font-v2', async (req, res) => {
  const {
    userId,
    file,
    body: { name },
  } = req;

  const buffer = file.buffer;

  const validationResult = await validateFile(file, userId, name);

  if (validationResult !== true) {
    return res.send(422).send(validationResult);
  }

  // Generate woff and woff2 font formats
  let converted = {};
  try {
    converted = await convertToMultipleFormats(buffer, ['woff', 'woff2']);
  } catch (err) {
    return res.status(422).send({ errorKey: 'unseccesfulConvert' });
  }

  await uploadFonts({
    converted,
    name,
    userId,
    filename: file.originalname,
    isWhiteLabel: isWhitelabelRequest(req),
  });

  await AccountModel.updateOne(
    { databaseId: userId },
    {
      $push: {
        'settings.customFonts': {
          name,
          key: slugify(name),
        },
      },
    },
  );

  return res.status(200).send({ name, value: slugify(name) });
});

router.post('/custom-font', async (req, res) => {
  const {
    userId,
    file,
    body: { name },
  } = req;

  if (!file || !file.size) {
    return res.status(422).send({ errorKey: 'missingFile' });
  }

  if (!name) {
    return res.status(422).send({ errorKey: 'nameIsRequired' });
  }

  const doublicateName = await AccountModel.countDocuments({
    databaseId: userId,
    'settings.customFonts.name': name,
  });

  if (doublicateName) {
    return res.status(422).send({ errorKey: 'nameIsUsed' });
  }

  const uploadPromises = [];
  let originalFontName = '';

  const zip = new AdmZip(file.buffer);
  const zipEntries = zip.getEntries();

  zipEntries.forEach((entry) => {
    const filename = entry.name;
    const extension = filename.substring(filename.lastIndexOf('.') + 1);
    const extensions = ['eot', 'svg', 'ttf', 'woff', 'woff2'];
    if (extensions.includes(extension)) {
      const uploadFile = {
        originalname: `${name}.${extension}`,
        buffer: entry.getData(),
      };
      uploadPromises.push(
        uploadToS3(uploadFile, { saveType: 'customFont', userId, saveValue: name }),
      );

      if (!originalFontName.length) {
        originalFontName = entry.name.replace(`.${extension}`, '');
      }
    }
  });

  const cdnPath = `${
    isWhitelabelRequest(req) ? WHITELABEL_CDN_URL : CDN_URL
  }/customFonts/${userId}/${name}/${name}`;

  const fontFaceCss = `
  @font-face {
    font-family: "${name}";
    font-display: swap;
    src: url('${cdnPath}.eot');
    src: url('${cdnPath}.eot') format('embedded-opentype'),
         url('${cdnPath}.woff2') format('woff2'),
         url('${cdnPath}.woff') format('woff'),
         url('${cdnPath}.ttf') format('truetype'),
         url('${cdnPath}.svg#${originalFontName}') format('svg');
  }`;

  const css = {
    originalname: `${name}.css`,
    buffer: Buffer.from(fontFaceCss),
  };

  uploadPromises.push(uploadToS3(css, { saveType: 'customFont', userId, saveValue: name }));

  try {
    await Promise.all(uploadPromises);
  } catch (e) {
    console.log(e);
  }

  await AccountModel.updateOne(
    { databaseId: userId },
    {
      $push: {
        'settings.customFonts': {
          name,
          key: slugify(name),
        },
      },
    },
  );

  res.sendStatus(200);
});

module.exports = router;
