const {
  prepareIsProductBlackListedQuery,
  prepareGetExternalProductPropsQuery,
  getFormattedTableName,
  getProductBlackListTableId,
  getScraperExternalDataTableId,
} = require('@om/queries');
const {
  parseChatGPTCompletionResult,
  postProcessVariables,
  cleanseVariableName,
  getChatGPTPromptsWithResult,
  initTables,
} = require('@om/workflow-sppo');

const { model: AccountModel } = require('../../resources/account/account.model');
const { model: PPOPromptModel } = require('../../resources/ppoPrompt/ppoPrompt.model');

const { domainAndShopByDomainId, determineDomainProviderServiceId } = require('../../util/domain');
const { getJSONFile, uploadJSONFile } = require('../../helpers/ppo');
const { batchCreateCompletion } = require('../../helpers/openai');
const { getProduct } = require('../../helpers/product');
const { purgePPOPullZone } = require('../../services/omFrontendAdapter');
const { BigQueryClient, PROJECT_ID } = require('../../services/bigQueryAdapter');

const PPO_RESULT_BUCKET_NAME = process.env.ppo_results_bucket_name;
const BQ_PROJECT_ID = process.env.gcp_bigquery_project_id;
const bigQueryClient = new BigQueryClient();

const isProductBlackListed = async ({ providerServiceId, databaseId, productId }) => {
  const blackListedProductsTableId = getFormattedTableName(
    `${PROJECT_ID}.${getProductBlackListTableId(databaseId, providerServiceId)}`,
  );
  const { query, params } = prepareIsProductBlackListedQuery({
    blackListedProductsTableId,
    productId,
  });
  return bigQueryClient.runQuery(query, { params });
};

const getExternalProductProperties = async ({ providerServiceId, databaseId, productId }) => {
  const scrapedExternalProductTableId = getScraperExternalDataTableId(
    databaseId,
    providerServiceId,
  );

  const { query, params } = prepareGetExternalProductPropsQuery({
    scrapedExternalProductTableId,
    productId,
  });
  return bigQueryClient.runQuery(query, { params });
};

const errorToResponse = (err) => ({
  errors: err.errors?.map(
    ({ error, productId, variableName, message = error?.message, code = error?.code }) => ({
      code,
      message,
      productId,
      variableName,
    }),
  ) || [
    {
      productId: null,
      variableName: null,
      message: err.message,
      code: err.code || 'GENERAL_ERROR',
    },
  ],
});

const getExtendedProduct = async ({ productId, domain, shop, providerServiceId, databaseId }) => {
  const [shopProduct, [externalProperties = {}]] = await Promise.all([
    getProduct({
      databaseId,
      productId,
      domain,
      shop,
      params: { fields: 'id,title,body_html' },
    }),
    getExternalProductProperties({
      providerServiceId,
      databaseId,
      productId,
    }),
  ]);

  return {
    id: shopProduct.id,
    accountId: databaseId,
    providerServiceId,
    ...shopProduct,
    ...externalProperties,
  };
};

const generateVariables = async (req, res) => {
  const { userId: databaseId } = req.tokenInfo;
  const { productId, promptIds } = req.body;

  if (isNaN(parseInt(productId, 10))) {
    return res.status(400).json(errorToResponse({ message: 'Requested productId is invalid!' }));
  }

  const [promptsToGenerate, account] = await Promise.all([
    PPOPromptModel.find({
      _id: { $in: promptIds },
      databaseId,
      deletedAt: { $exists: false },
    }).lean(),
    AccountModel.findOne({ databaseId }, { 'settings.shops': 1, 'settings.domains': 1 }).lean(),
  ]);

  if (!promptsToGenerate.length) {
    return res.status(400).json(errorToResponse({ message: 'No available prompt to generate' }));
  }

  const {
    settings: { shops, domains },
  } = account;
  const { domain, shop } = domainAndShopByDomainId(promptsToGenerate[0].domainId, {
    shops,
    domains,
  });

  const providerServiceId = determineDomainProviderServiceId({ domain, shop });

  const datasetId = 'smart_ppo';

  await initTables({
    projectId: BQ_PROJECT_ID,
    datasetId,
    databaseId,
    providerServiceId,
    platform: shop?.type,
    bigQueryClient: bigQueryClient.client,
  });

  const [productBlackListResult] = await isProductBlackListed({
    providerServiceId,
    databaseId,
    productId,
  });
  if (productBlackListResult?.isBlackListed) {
    return res.status(400).json(errorToResponse({ message: 'Requested product is blacklisted!' }));
  }

  const prompts = promptsToGenerate.map((prompt) => ({
    ...prompt,
    ...prompt.settings,
    promptText: prompt.prompt,
  }));

  const product = await getExtendedProduct({
    productId,
    domain,
    shop,
    providerServiceId,
    databaseId,
  });

  const chatGPTPrompts = [];
  try {
    const chatGPTPromptsWithResults = getChatGPTPromptsWithResult({
      source: [product],
      prompts,
      databaseId,
      providerServiceId,
      platform: shop?.type,
      projectId: BQ_PROJECT_ID,
      bigQueryClient: bigQueryClient.client,
      location: 'US',
    });
    for await (const { error, promptText } of chatGPTPromptsWithResults) {
      if (error) {
        throw error;
      }

      chatGPTPrompts.push(promptText);
    }
  } catch (err) {
    req.log.warn(
      { err, data: { product, databaseId, promptsToGenerate } },
      'Failed to get chatgpt prompt',
    );

    return res.status(400).json(errorToResponse(err));
  }

  const chatGPTMessages = chatGPTPrompts.map((promptText, index) => {
    return {
      role: 'user',
      content: promptText,
      version: promptsToGenerate[index].modelVersion,
    };
  });

  const filePath = `${databaseId}/${providerServiceId}/${productId}.json`;
  const [ppoProductVariables, chatGptResponse] = await Promise.all([
    getJSONFile(PPO_RESULT_BUCKET_NAME, filePath),
    batchCreateCompletion(chatGPTMessages),
  ]);

  const generatedVariables = promptsToGenerate.reduce((variables, prompt, index) => {
    const [choice] = chatGptResponse[index].choices;
    const rawResult = choice.message.content;

    return { ...variables, ...parseChatGPTCompletionResult(rawResult, prompt.variableNames) };
  }, {});

  const variablesToUpload = {
    ...ppoProductVariables,
    ...postProcessVariables(generatedVariables, promptsToGenerate),
  };

  await uploadJSONFile(PPO_RESULT_BUCKET_NAME, filePath, variablesToUpload);
  purgePPOPullZone(filePath).catch((error) => {
    req.log.error(error, { message: 'Failed to purge PPO Pull Zone' });
  });
  res.json(variablesToUpload);
};

const getUserPrompts = async (req, res) => {
  const { userId } = req.tokenInfo;
  const { domain } = req.query;

  const databaseId = parseInt(userId, 10);

  const account = await AccountModel.findOne({ databaseId }, { 'settings.domains': 1 });
  const foundDomain = account.settings.domains.find(
    ({ domain: domainString }) => domainString === domain,
  );

  if (!foundDomain) {
    return res.status(404).send('Domain not found');
  }

  const ppoPrompts = await PPOPromptModel.aggregate([
    {
      $match: {
        databaseId,
        domainId: foundDomain._id,
        deletedAt: { $exists: false },
      },
    },
    { $unwind: '$variableNames' },
    {
      $project: { _id: 1, variableName: '$variableNames' },
    },
  ]);

  const cleansed = ppoPrompts.map((row) => {
    const variableName = cleanseVariableName(row.variableName);
    return {
      ...row,
      variableName,
    };
  });

  res.send(cleansed);
};

module.exports = {
  generateVariables,
  getUserPrompts,
};
