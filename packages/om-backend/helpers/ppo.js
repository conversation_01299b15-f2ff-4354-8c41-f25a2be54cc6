const { Storage } = require('@google-cloud/storage');
const { streamToString } = require('@om/common');
const { Transform } = require('stream');
const {
  getFormattedTableName,
  getEventTableId,
  getProductTableId,
  getPPOResultTableId,
  getProductBlackListTableId,
  getScraperExternalDataTableId,
  resolveLanguageCodes,
} = require('@om/queries');
const {
  promptResultsExportQuery,
  promptResultsExportExternalQuery,
  cleanseVariableName,
  detectPromptVariables,
  getPostProcessSetting,
  postProcess,
  mapMostPopularProduct,
  cleanProduct,
} = require('@om/workflow-sppo');
const { stringify } = require('csv-stringify');
const logger = require('../logger').child({ service: 'ppo-helper' });

const BQ_PROJECT_ID = process.env.gcp_bigquery_project_id;
const omBackendCredentials = process.env.gcp_api_project_creds;
const storage = new Storage({
  credentials: omBackendCredentials ? JSON.parse(omBackendCredentials) : undefined,
  projectId: BQ_PROJECT_ID,
});

async function getJSONFile(bucketName, sourceFileName) {
  try {
    const file = storage.bucket(bucketName).file(sourceFileName);
    const readStream = file.createReadStream();

    const fileContent = await streamToString(readStream);

    return JSON.parse(fileContent);
  } catch (error) {
    if (error.code === 404) {
      logger.warn(error, {
        message: 'JSON file not found in bucket',
        bucketName,
        sourceFileName,
      });
    } else {
      logger.error(
        error,
        {
          bucketName,
          sourceFileName,
        },
        'Error getting JSON file from bucket',
      );
    }

    return null;
  }
}

async function uploadJSONFile(bucketName, destinationFileName, data) {
  try {
    const file = storage.bucket(bucketName).file(destinationFileName);
    const fileContent = JSON.stringify(data);

    await file.save(fileContent, {
      contentType: 'application/json',
      resumable: false,
      metadata: {
        cacheControl: 'no-store',
      },
    });

    logger.info(`File uploaded to gs://${bucketName}/${destinationFileName}`);
  } catch (error) {
    logger.error(error, 'Error uploading file to bucket');
  }
}

const getBlackListTableName = (databaseId, providerServiceId) => {
  return getFormattedTableName(
    `${BQ_PROJECT_ID}.${getProductBlackListTableId(databaseId, providerServiceId)}`,
  );
};

const preparePPOExport = async ({
  databaseId,
  providerServiceId,
  prompts,
  domain,
  languages,
  platform,
  locale,
}) => {
  const eventTableId = getFormattedTableName(
    `${BQ_PROJECT_ID}.${getEventTableId(databaseId, providerServiceId)}`,
  );
  const productTableId = getFormattedTableName(
    `${BQ_PROJECT_ID}.${getProductTableId(databaseId, providerServiceId, platform)}`,
  );
  const resultsWildcardTableId = getFormattedTableName(
    `${BQ_PROJECT_ID}.${getPPOResultTableId(databaseId, providerServiceId, '*')}`,
  );
  const blackListedProductsTableId = getBlackListTableName(databaseId, providerServiceId);
  const scraperExternalDataTableId = getFormattedTableName(
    `${BQ_PROJECT_ID}.${getScraperExternalDataTableId(databaseId, providerServiceId)}`,
  );

  const promptTexts = prompts.map((prompt) => prompt.prompt).join(',');

  const excludeFromExportRegex = /^(shortDescription|description|name)/;
  const promptVariableTemplates = Array.from(new Set(detectPromptVariables(promptTexts))).filter(
    (variableTemplate) => !excludeFromExportRegex.test(variableTemplate),
  );

  const promptVariableNames = prompts
    .flatMap((prompt) => prompt.variableNames)
    .map((variableName) => cleanseVariableName(variableName));

  const isShoprenter = domain.platform === 'shoprenter';

  const languageCodes = languages.map((lang) => lang.properties.code);

  const nameColumns = [{ key: 'name' }];
  const descriptionColumns = [{ key: 'description' }];
  const shortDescriptionColumns = [{ key: 'shortDescription' }];
  languageCodes.forEach((code) => {
    nameColumns.push({ key: `name.${code}` });
    descriptionColumns.push({ key: `description.${code}` });
    shortDescriptionColumns.push({ key: `shortDescription.${code}` });
  });
  const csvStream = stringify({
    header: true,
    columns: [
      { key: 'id', header: 'productId' },
      { key: 'url' },
      ...nameColumns,
      ...descriptionColumns,
      ...(isShoprenter ? shortDescriptionColumns : []),
      { key: 'pageViews', header: 'page views' },
      { key: 'excluded' },
      ...promptVariableNames.map((variableName, index) => ({
        key: `${variableName}_${index}`,
        header: `prompt_${variableName}`,
      })),
      ...promptVariableNames.map((variableName, index) => ({
        key: `error_${variableName}_${index}`,
        header: `error_${variableName}`,
      })),
      ...promptVariableTemplates.map((template) => ({
        key: template,
        header: template,
      })),
    ],
  });

  const query = promptResultsExportQuery({
    databaseId,
    providerServiceId,
    eventTableId,
    productTableId,
    resultsWildcardTableId,
    blackListedProductsTableId,
    scraperExternalDataTableId,
    generationUuids: prompts.map((prompt) => prompt.lastGeneration.uuid),
    variableNames: prompts
      .flatMap((prompt) => prompt.variableNames)
      .map((variableName) => cleanseVariableName(variableName)),
    platform,
    locale,
  });

  return { query, csvStream };
};

const prepareExternalPPOExport = async ({ databaseId, providerServiceId, prompts, domain }) => {
  const eventTableId = getFormattedTableName(
    `${BQ_PROJECT_ID}.${getEventTableId(databaseId, providerServiceId)}`,
  );
  const resultsWildcardTableId = getFormattedTableName(
    `${BQ_PROJECT_ID}.${getPPOResultTableId(databaseId, providerServiceId, '*')}`,
  );
  const blackListedProductsTableId = getBlackListTableName(databaseId, providerServiceId);
  const scraperExternalDataTableId = getFormattedTableName(
    `${BQ_PROJECT_ID}.${getScraperExternalDataTableId(databaseId, providerServiceId, '*')}`,
  );

  const promptTexts = prompts.map((prompt) => prompt.prompt).join(',');

  const promptVariableTemplates = Array.from(new Set(detectPromptVariables(promptTexts)));

  const promptVariableNames = prompts
    .flatMap((prompt) => prompt.variableNames)
    .map((variableName) => cleanseVariableName(variableName));

  const csvStream = stringify({
    header: true,
    columns: [
      { key: 'url', header: 'canonicalURL' },
      { key: 'pageViews', header: 'page views' },
      { key: 'excluded' },
      ...promptVariableNames.map((variableName) => ({
        key: variableName,
        header: `prompt_${variableName}`,
      })),
      ...promptVariableNames.map((variableName) => ({
        key: `error_${variableName}`,
        header: `error_${variableName}`,
      })),
      ...promptVariableTemplates.map((template) => ({
        key: template,
        header: template,
      })),
    ],
  });

  const query = promptResultsExportExternalQuery({
    eventTableId,
    resultsWildcardTableId,
    blackListedProductsTableId,
    scraperExternalDataTableId,
    generationUuids: prompts.map((prompt) => prompt.lastGeneration.uuid),
    variableNames: prompts
      .flatMap((prompt) => prompt.variableNames)
      .map((variableName) => cleanseVariableName(variableName)),
  });

  return { query, csvStream };
};

const generateBlackListFromCSV = (blackList) => {
  const templateParts = [];
  const params = {};
  for (const [index, product] of Object.entries(blackList)) {
    const productIdParam = `product_id_${index}`;
    const productExcludedParam = `product_excluded_${index}`;

    templateParts.push(`(@${productIdParam}, @${productExcludedParam})`);
    params[productIdParam] = product.productId;
    params[productExcludedParam] = product.excluded;
  }

  return {
    template: templateParts.join(',\n'),
    params,
  };
};

const prepareMergeBlackListQuery = ({ blackList, databaseId, providerServiceId }) => {
  const { template, params } = generateBlackListFromCSV(blackList);
  const blackListTableName = getBlackListTableName(databaseId, providerServiceId);
  const query = `
CREATE TABLE IF NOT EXISTS ${blackListTableName} (productId STRING);
MERGE INTO ${blackListTableName} black_list_table
USING (SELECT *
FROM
(
  SELECT *  FROM UNNEST(ARRAY<STRUCT<productId STRING, excluded BOOL>>
[
  ${template}
]
))) csv_back_list
ON black_list_table.productId = csv_back_list.productId
WHEN MATCHED AND csv_back_list.excluded = false THEN
  DELETE
WHEN NOT MATCHED AND csv_back_list.excluded = true THEN
  INSERT (productId) VALUES (csv_back_list.productId)`;

  return { query, params };
};

/**
 * Deletes specified files from a bucket
 * @param {*} bucketName Name of the bucket
 * @param {*} fileNames Array of file names to delete
 * @param {import('@google-cloud/storage').DeleteFileOptions} deleteOptions
 */
async function deleteFiles(bucketName, fileNames, deleteOptions = {}) {
  const bucket = storage.bucket(bucketName);

  await Promise.all(
    fileNames.map(
      (fileName) => bucket.file(fileName).delete({ ...deleteOptions }), // need to shallow copy because storage client modifies the object
    ),
  );
}

const postProcessResultRows = ({ row, prompts }) => {
  for (const prompt of prompts) {
    for (const [index, dirtyVariableName] of prompt.variableNames.entries()) {
      const variableName = cleanseVariableName(dirtyVariableName);
      const postProcessSetting = getPostProcessSetting(variableName, prompt.settings?.postProcess);

      const variableKey = `${variableName}_${index}`;

      if (row[variableKey] && postProcessSetting) {
        row[variableKey] = postProcess(row[variableKey], postProcessSetting);
      }
    }
  }

  return row;
};

const getPostProcessTransformStream = ({ domain, shop, languages, prompts }) => {
  return new Transform({
    objectMode: true,
    transform(row, _, callback) {
      let product = mapMostPopularProduct(row, shop);
      if (domain.platform === 'shoprenter') {
        product = resolveLanguageCodes(product, languages);
      }
      let resultRow = { ...row, ...cleanProduct(product, { platform: domain.platform }) };

      resultRow = postProcessResultRows({ row: resultRow, prompts });
      callback(null, resultRow);
    },
  });
};

const getExternalPostProcessTransformStream = ({ prompts }) => {
  return new Transform({
    objectMode: true,
    transform(row, _, callback) {
      const { properties, ...restOfProperties } = row;

      let resultRow = {
        ...restOfProperties,
        ...properties.reduce((props, prop) => {
          props[prop.name] = prop.value;
          return props;
        }, {}),
      };

      resultRow = postProcessResultRows({ row: resultRow, prompts });
      callback(null, resultRow);
    },
  });
};

module.exports = {
  getJSONFile,
  deleteFiles,
  uploadJSONFile,
  preparePPOExport,
  prepareExternalPPOExport,
  getBlackListTableName,
  prepareMergeBlackListQuery,
  postProcessResultRows,
  getPostProcessTransformStream,
  getExternalPostProcessTransformStream,
};
