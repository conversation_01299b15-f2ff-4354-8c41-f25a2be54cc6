const UnifiedProductsAdapter = require('../services/UnifiedProductsAdapter/unifiedProductsAdapter');

const logger = require('../logger').child({ service: 'shop-local-helper' });

const getDefaultShopLocale = async ({ databaseId, providerServiceId }) => {
  // [product-sync]
  let locale = null;
  try {
    const unifiedProductsAdapter = new UnifiedProductsAdapter(
      process.env.PRODUCT_SYNC_SERVICE_URL,
      process.env.om_shared_key,
      logger,
    );
    const shopSettings = await unifiedProductsAdapter.getShopSettings({
      providerServiceId,
      databaseId,
    });
    locale = shopSettings?.locale ?? null;
  } catch (error) {
    const message = 'Error during product details request (get default locale)';
    logger.error({
      message,
      databaseId,
      providerServiceId,
      ...(error ? { error: { code: error.code, message: error.message, stack: error.stack } } : {}),
    });
  }

  return locale;
};

module.exports = { getDefaultShopLocale };
