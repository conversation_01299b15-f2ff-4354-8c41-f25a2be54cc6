// [product-sync][isUnified]
const COMPATIBLE_PLATFORMS = ['unas', 'woocommerce'];

const isUnifiedUnas = ({ shop }) => {
  return shop?.type === 'unas';
};

const isUnifiedWoo = ({ shop }) => {
  return shop?.type === 'woocommerce';
};

const isUnifiedProductBased = ({ shop }) => {
  return COMPATIBLE_PLATFORMS.includes(shop?.type);
};

module.exports = {
  UNIFIED_BASED_PLATFORMS: COMPATIBLE_PLATFORMS,
  isUnifiedUnas,
  isUnifiedWoo,
  isUnifiedProductBased,
};
