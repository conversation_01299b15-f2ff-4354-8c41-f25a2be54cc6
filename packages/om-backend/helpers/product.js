const axios = require('axios');
const { unifiedProducts } = require('@om/queries');
const ShopifyAdapter = require('../services/shopifyAdapter');
const { isUnifiedProductBased } = require('./unifiedProductBased');
const { getDefaultShopLocale } = require('./defaultShopLocale');
const { BigQueryClient } = require('../services/bigQueryAdapter');
const { determineDomainProviderServiceId } = require('../util/domain');
const logger = require('../logger').child({ service: 'product-helper' });

const bigQueryClient = new BigQueryClient();
const BQ_PROJECT_ID = process.env.gcp_bigquery_project_id;

const getShopifyProduct = async (id, shop, params = {}) => {
  const client = new ShopifyAdapter({
    shopName: shop.myshopify_domain,
    accessToken: shop.oauth_token,
  });

  let product;
  try {
    product = await client.product.get(id, params);
  } catch (error) {
    const message = 'Error during product details request';
    logger.error(error, message);
    throw new Error(message);
  }

  return product;
};

const getShoprenterProduct = async (id, shop) => {
  const url = `http://${shop.shopname}.api.shoprenter.hu/productExtend?innerId=${id}&full=1&limit=1`;
  const axiosOptions = {
    auth: { username: shop.username, password: shop.password },
    headers: { Accept: 'application/json' },
  };
  let product;

  try {
    const { data } = await axios.get(url, axiosOptions);

    product = data.items[0];
  } catch (error) {
    const message = 'Error during product details request';
    logger.error(error, message);
    throw new Error(message);
  }

  return product;
};

const getShoprenterLanguages = async (shop) => {
  const url = `http://${shop.shopname}.api.shoprenter.hu/languages?full=1`;
  const axiosOptions = {
    auth: { username: shop.username, password: shop.password },
    headers: { Accept: 'application/json' },
  };
  let languages = [];

  try {
    const { data } = await axios.get(url, axiosOptions);

    languages = data.items;
  } catch (error) {
    const message = 'Error during languages details request';
    logger.error(error, message);
    throw new Error(message);
  }

  return languages;
};

const getUnifiedProduct = async ({ databaseId, productId, providerServiceId }) => {
  // [product-sync]
  try {
    const locale = await getDefaultShopLocale({ databaseId, providerServiceId });
    const query = unifiedProducts.getProductForPPOGenerate({ projectId: BQ_PROJECT_ID, locale });
    const queryParams = { databaseId, providerServiceId, productId };
    const rawResult = await bigQueryClient.runQuery(query, {
      params: queryParams,
    });
    return rawResult[0];
  } catch (error) {
    const message = 'Error during product details request';
    logger.error(error, message);
    throw new Error(message);
  }
};

const getProduct = async ({ databaseId, domain, shop, productId, params }) => {
  let product;

  if (shop.type === 'shopify') {
    product = await getShopifyProduct(productId, shop, params);

    product.name = product.title;
    product.description = product.body_html;
    return product;
  }

  if (shop.type === 'shoprenter') {
    const [languages, shoprenterProduct] = await Promise.all([
      getShoprenterLanguages(shop),
      getShoprenterProduct(productId, shop, params),
    ]);

    product = shoprenterProduct;

    product.productDescriptions.forEach((productDetails) => {
      const language = languages.find((language) => language.href === productDetails.language.href);
      const languageId = language?.innerId ? `.${language.innerId}` : '';

      product[`name${languageId}`] = productDetails.name;
      product[`description${languageId}`] = productDetails.description;
      product[`shortDescription${languageId}`] = productDetails.shortDescription;
    });

    return product;
  }

  // [product-sync][isUnified]

  if (isUnifiedProductBased({ shop })) {
    const providerServiceId = determineDomainProviderServiceId({ domain, shop });
    return getUnifiedProduct({ databaseId, productId, providerServiceId });
  }

  return null;
};

module.exports = {
  getProduct,
  getShopifyProduct,
  getShoprenterProduct,
};
