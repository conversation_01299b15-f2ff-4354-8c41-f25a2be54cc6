const { model, ObjectId } = require('../resources/campaign/campaign.model');
const { model: AccountModel } = require('../resources/account/account.model');
const { model: ExperienceModel } = require('../resources/experience/experience.model');
const { purifyDomain } = require('../util/domainHelper');
const { model: ConfigModel } = require('../resources/config/config.model');
const { domainAndShopByDomainId, determineDomainProviderServiceId } = require('../util/domain');

const getCampaignData = async (variantIds, userId) => {
  const CampaignModel = model(userId);

  return CampaignModel.collection
    .find(
      {
        'variants._id': { $in: variantIds },
      },
      {
        projection: {
          domain: 1,
          name: 1,
          'variants._id': 1,
          'variants.name': 1,
          status: 1,
          'variants.status': 1,
          'variants.isControlVariant': 1,
          'variants.createdAt': 1,
          id: 1,
          createdAt: 1,
        },
      },
    )
    .toArray();
};

const divideSingleValue = (dividend, divider, roundTo = 2) => {
  if (
    divider === 0 ||
    dividend === null ||
    typeof dividend === 'undefined' ||
    typeof divider === 'undefined'
  ) {
    return 'N/A';
  }
  const round = Math.pow(10, roundTo);
  if (typeof dividend !== 'object') {
    const result = dividend / divider;
    return Math.round(result * round) / round;
  }
  return Object.entries(dividend).reduce((result, [groupKey, groupValue]) => {
    result[groupKey] = divideSingleValue(groupValue, divider[groupKey], roundTo);
    return result;
  }, {});
};

const calculateDerivedForLevel = (report, levels, index) => {
  const nextLevel = levels[index];

  const result = {
    ...report,
    conversionRate: divideSingleValue(report.conversionCount, report.impressionCount, 4),
    aov: divideSingleValue(report.assistedRevenue, report.orders),
    ...(nextLevel && {
      [nextLevel]: report[nextLevel].map((item) => {
        return calculateDerivedForLevel(item, levels, index + 1);
      }),
    }),
  };

  return result;
};

const calculateDerivedValues = (fullReport) => {
  const levels = ['domains', 'campaigns', 'variants'];

  for (let i = 0; i < levels.length; i++) {
    return calculateDerivedForLevel(fullReport, levels, i);
  }
};

const calculateTotals = (fullReport) => {
  const keysToSum = [
    'impressionCount',
    'visitorCount',
    'conversionCount',
    'orders',
    'nonstandardOrders',
    'assistedRevenue',
  ];

  if (fullReport.length === 0) {
    return {
      impressionCount: { allType: 0, desktop: 0, mobile: 0 },
      visitorCount: { allType: 0, desktop: 0, mobile: 0 },
      conversionCount: { allType: 0, desktop: 0, mobile: 0 },
      orders: {
        fiveDaysFilled: { allType: {}, desktop: {}, mobile: {} },
        fiveDaysShown: { allType: {}, desktop: {}, mobile: {} },
        inSessionFilled: { allType: {}, desktop: {}, mobile: {} },
        inSessionShown: { allType: {}, desktop: {}, mobile: {} },
      },
      nonstandardOrders: {
        fiveDaysFilled: { allType: {}, desktop: {}, mobile: {} },
        fiveDaysShown: { allType: {}, desktop: {}, mobile: {} },
        inSessionFilled: { allType: {}, desktop: {}, mobile: {} },
        inSessionShown: { allType: {}, desktop: {}, mobile: {} },
      },
      assistedRevenue: {
        fiveDaysFilled: { allType: {}, desktop: {}, mobile: {} },
        fiveDaysShown: { allType: {}, desktop: {}, mobile: {} },
        inSessionFilled: { allType: {}, desktop: {}, mobile: {} },
        inSessionShown: { allType: {}, desktop: {}, mobile: {} },
      },
    };
  }

  return keysToSum.reduce((totalValues, key) => {
    const sum = fullReport.reduce((totalValue, item, index) => {
      let sum;

      if (index === 0) {
        sum = totalValue;
      } else {
        sum = summarizeSingleValue(totalValue, item[key]);
      }

      return sum;
    }, fullReport[0][key]);
    totalValues[key] = sum;
    return totalValues;
  }, {});
};

const mergeCampaignReportWithDomains = (reportsByCampaigns, reportsByDomains) => {
  return reportsByDomains.map((domainReport) => {
    return {
      ...domainReport,
      campaigns: reportsByCampaigns.filter((campaignReport) => {
        return purifyDomain(campaignReport.domain) === purifyDomain(domainReport.name);
      }),
    };
  });
};

const getShopSettings = async (userId) => {
  const [account] = await AccountModel.find(
    {
      databaseId: userId,
    },
    {
      _id: 0,
      'settings.shops': 1,
      'settings.domains': 1,
    },
  );

  return {
    shops: account?.settings?.shops || [],
    domains: account?.settings?.domains || [],
  };
};

const getProviderServiceIds = ({ shops, domains }) => {
  const domainIds = domains.map((d) => d._id);

  return domainIds.reduce((uniqueIds, domainId) => {
    const { domain, shop } = domainAndShopByDomainId(domainId, { domains, shops });
    const providerServiceId = determineDomainProviderServiceId({ domain, shop });

    if (providerServiceId && !uniqueIds.includes(providerServiceId)) {
      uniqueIds.push(providerServiceId);
    }

    return uniqueIds;
  }, []);
};

const filterUnknownShopDomains = async (reportByDomains, shops, domains) => {
  return reportByDomains.reduce((resolvedDomains, domain) => {
    const mongoDomain = domains.find((d) => purifyDomain(d.domain) === purifyDomain(domain.id));
    if (!mongoDomain) {
      return resolvedDomains;
    }

    const { id, providerServiceId, ...rest } = domain;

    if (['shopify', 'shoprenter'].includes(mongoDomain.platform)) {
      const shop = shops.find(
        (shop) =>
          shop.myshopify_domain === domain.providerServiceId ||
          shop.shopname === domain.providerServiceId,
      );
      if (shop) {
        resolvedDomains.push({
          ...rest,
          shopId: providerServiceId,
          name: id,
        });
      }
      return resolvedDomains;
    }

    resolvedDomains.push({
      ...rest,
      shopId: providerServiceId,
      name: id,
    });

    return resolvedDomains;
  }, []);
};

const summarizeSingleValue = (currentSum, nextItem) => {
  if (typeof currentSum !== 'object' && typeof nextItem !== 'object' && currentSum !== null) {
    if (nextItem !== null && typeof nextItem !== 'undefined') return currentSum + nextItem;
    return currentSum;
  }

  if (currentSum === null) return nextItem;

  const allKeys = Array.from(new Set([...Object.keys(currentSum), ...Object.keys(nextItem)]));

  return allKeys.reduce((sum, key) => {
    const currentsum = currentSum[key] || 0;
    const nextValue = nextItem[key] || 0;
    sum[key] = summarizeSingleValue(currentsum, nextValue);

    return sum;
  }, {});
};

const summarizeReportData = (
  currentGroup,
  item,
  keysToSum = [
    'impressionCount',
    'conversionCount',
    'orders',
    'nonstandardOrders',
    'assistedRevenue',
  ],
) => {
  const summarizedValue = keysToSum.reduce((reportData, key) => {
    const sum = summarizeSingleValue(currentGroup[key], item[key]);
    reportData[key] = sum;
    return reportData;
  }, {});
  return {
    ...currentGroup,
    groupName: currentGroup.groupName ? purifyDomain(currentGroup.groupName) : null,
    ...summarizedValue,
  };
};

const groupReportRows = (rawReport, groupBy) => {
  const rowsWithMappedCurrency = rawReport.map((row) => {
    return {
      ...row,
      orders: mapCurrencies(row.orders),
      ...(row.nonstandardOrders
        ? { nonstandardOrders: mapCurrencies(row.nonstandardOrders) }
        : {
            nonstandardOrders: {
              fiveDaysShown: { mobile: null, desktop: null, allType: null },
              fiveDaysFilled: { mobile: null, desktop: null, allType: null },
              inSessionShown: { mobile: null, desktop: null, allType: null },
              inSessionFilled: { mobile: null, desktop: null, allType: null },
            },
          }),
      assistedRevenue: mapCurrencies(row.assistedRevenue),
    };
  });

  const filteredRows = rowsWithMappedCurrency.filter((row) => row.groupBy === groupBy);
  let visitorCountPerGroup = {};
  const groupedReport = [];

  filteredRows.forEach((dailyReport) => {
    const keysToSum = [
      'impressionCount',
      'conversionCount',
      'orders',
      'nonstandardOrders',
      'assistedRevenue',
    ];
    const hasWWW = dailyReport.groupName.includes('www.');
    const prop = hasWWW ? 'wwwDomain' : 'pureDomain';
    const pureGroupName = purifyDomain(dailyReport.groupName);

    if (!visitorCountPerGroup?.[pureGroupName]?.[prop]) {
      keysToSum.push('visitorCount');
      visitorCountPerGroup = {
        ...visitorCountPerGroup,
        [pureGroupName]: {
          ...visitorCountPerGroup[pureGroupName],
          [prop]: dailyReport.visitorCount,
        },
      };
    }

    const grouppedItem = groupedReport.find(
      (group) => purifyDomain(group.groupName) === purifyDomain(dailyReport.groupName),
    );
    if (grouppedItem) {
      const index = groupedReport.findIndex(
        (group) => purifyDomain(group.groupName) === purifyDomain(dailyReport.groupName),
      );
      const newItem = summarizeReportData(grouppedItem, dailyReport, keysToSum);
      groupedReport[index] = newItem;
    } else {
      groupedReport.push({
        ...dailyReport,
        groupName: purifyDomain(dailyReport.groupName),
      });
    }
  });

  return groupedReport;
};

const pruneUnnecessaryProperties = (report) => {
  return report.map((row) => {
    // eslint-disable-next-line no-unused-vars
    const { day, groupBy, groupName, accountId, ...rowRest } = row;
    return {
      id: groupName,
      ...rowRest,
    };
  });
};

const mergeVariantsWithCampaignData = async (reportByVariants, campaigns) => {
  return campaigns.map((campaign) => {
    const { id, name, domain, variants, createdAt, status } = campaign;
    const variantIds = reportByVariants.map((report) => report.variantId || report.id);
    const seenOrFilledVariants = variants.filter((variant) =>
      variantIds.some(
        (variantId) =>
          variant._id.toString() === variantId || variant._id.toString() === variant.id,
      ),
    );

    const variantReports = seenOrFilledVariants.map((variant) => {
      const reportForVariant = reportByVariants.find((report) => {
        return report.variantId === variant._id.toString() || report.id === variant._id.toString();
      });

      return {
        ...reportForVariant,
        name: variant.name,
        status: variant.status,
        isControlVariant: variant.isControlVariant || false,
        createdAt: variant.createdAt,
      };
    });

    return {
      id,
      name,
      domain: purifyDomain(domain),
      createdAt,
      status,
      variants: variantReports,
    };
  });
};

const mapCurrencies = (group) => {
  if (Array.isArray(group)) {
    if (group.length === 0) {
      return null;
    }

    return group.reduce((valuesByCurrency, valueByCurrency) => {
      valuesByCurrency[valueByCurrency.currency] = valueByCurrency.value;
      return valuesByCurrency;
    }, {});
  }
  return Object.entries(group).reduce((result, [groupKey, groupValue]) => {
    result[groupKey] = mapCurrencies(groupValue);
    return result;
  }, {});
};

const findShopIdIfProviderServiceIdIsDomain = async ({ rawReport, shops }) => {
  const rawReportWithCorrectProviderServiceIds = rawReport.map((report) => {
    const shop = shops.find(
      (accountShop) =>
        (purifyDomain(accountShop.live_domain) === purifyDomain(report.providerServiceId) &&
          accountShop.myshopify_domain) ||
        (accountShop.type === 'shoprenter' &&
          !!accountShop.domains?.find(
            ({ domain }) => purifyDomain(domain) === purifyDomain(report.providerServiceId),
          )),
    );
    return {
      ...report,
      providerServiceId: shop ? shop.myshopify_domain ?? shop.shopname : report.providerServiceId,
    };
  });
  return rawReportWithCorrectProviderServiceIds;
};

async function getExperiencesMap(variantIds, userId) {
  const experiences = await ExperienceModel.find(
    {
      databaseId: userId,
      deletedAt: { $exists: false },
      variants: { $in: variantIds },
    },
    { _id: 1, name: 1, variants: 1, campaign: 1 },
  );

  return experiences.reduce((experiencesMap, experience) => {
    experience.variants.forEach((variantId) => {
      experiencesMap[variantId] = experience;
    });
    return experiencesMap;
  }, {});
}

function mergeCampaignReportWithExperiences(fullReport, experiencesMap) {
  return fullReport.map((report) => {
    return {
      ...report,
      variants: report.variants.map((variant) => ({
        ...variant,
        experience: experiencesMap[variant.variantId || variant.id] ?? null,
      })),
    };
  });
}

const createCampaignReport = async ({ rawReport, shops, domains, parameters: { userId } }) => {
  let reportRows = rawReport;

  reportRows = await findShopIdIfProviderServiceIdIsDomain({
    userId,
    rawReport: reportRows,
    shops,
  });

  const reportByDomains = await filterUnknownShopDomains(
    pruneUnnecessaryProperties(groupReportRows(reportRows, 'domain')),
    shops,
    domains,
  );

  if (reportByDomains.length === 0) {
    return {};
  }

  const reportByVariants = pruneUnnecessaryProperties(groupReportRows(reportRows, 'variant'));
  const variantIds = reportByVariants.map((report) => ObjectId(report.id));

  const [campaigns, experiencesMap, lastUpdated] = await Promise.all([
    getCampaignData(variantIds, userId),
    getExperiencesMap(variantIds, userId),
    ConfigModel.findOne({ key: 'campaign_analytics_last_update' }),
  ]);

  let fullReport;

  fullReport = await mergeVariantsWithCampaignData(reportByVariants, campaigns);
  fullReport = mergeCampaignReportWithExperiences(fullReport, experiencesMap);
  fullReport = mergeCampaignReportWithDomains(fullReport, reportByDomains);

  return calculateDerivedValues({
    lastUpdated: lastUpdated?.value,
    ...calculateTotals(fullReport),
    domains: fullReport.map((domain) => {
      return {
        ...domain,
        campaigns: domain.campaigns.map((campaign) => {
          const variantsWithoutControlVariantImpression = campaign.variants.map((variant) => {
            if (variant.isControlVariant) {
              return { ...variant, impressionCount: { mobile: 0, desktop: 0, allType: 0 } };
            }
            return variant;
          });
          const totals = calculateTotals(variantsWithoutControlVariantImpression);
          return {
            ...campaign,
            variants: variantsWithoutControlVariantImpression,
            ...totals,
          };
        }),
      };
    }),
  });
};

module.exports = {
  createCampaignReport,
  findShopIdIfProviderServiceIdIsDomain,
  mapCurrencies,
  groupReportRows,
  calculateTotals,
  summarizeReportData,
  getShopSettings,
  getProviderServiceIds,
};
