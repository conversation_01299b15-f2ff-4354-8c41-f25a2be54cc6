const { hasProductData } = require('@om/queries');
const { model } = require('../smartRecommendations.model');
const { runFlow } = require('../../../helpers/workflow');
const { handleError } = require('../errorHandling');
const { getDomainBasedData, unsetTimedHardDeleteToSPR } = require('../../../services/spr/helper');
const { BigQueryClient } = require('../../../services/bigQueryAdapter');
const { getDefaultShopLocale } = require('../../../helpers/defaultShopLocale');
const { UNIFIED_BASED_PLATFORMS } = require('../../../helpers/unifiedProductBased');

const _getBQClient = () => {
  const BQ = new BigQueryClient();
  return BQ.client;
};

const _hasProductData = async (accountId, providerServiceId, platform) => {
  if (UNIFIED_BASED_PLATFORMS.includes(platform)) {
    return true;
  }
  return hasProductData({
    client: _getBQClient(),
    accountId,
    providerServiceId,
    platform,
  });
};

const addSPRConfig = async (_, { sprConfig: { domainId } }, { userId: accountId, log }) => {
  let SPRConfig;
  let domainBasedData;
  try {
    SPRConfig = await model.findOne({ accountId, domainId });
    domainBasedData = await getDomainBasedData(accountId, domainId);
  } catch (error) {
    handleError(log, 'Failed to add SPR config', error);
  }
  const { providerServiceId, platform } = domainBasedData;

  if (!providerServiceId) {
    handleError(log, 'Missing providerServiceId ');
  }

  if (SPRConfig && (SPRConfig.deletedAt === null || SPRConfig.deletedAt === undefined)) {
    return { success: false, message: `This SPR config already exists!` };
  }

  let locale;
  try {
    locale = await getDefaultShopLocale({ databaseId: accountId, providerServiceId });
  } catch (error) {
    handleError(log, 'Failed to add SPR config', error);
  }

  if (!(await _hasProductData(accountId, providerServiceId, platform))) {
    // TODO[Dzsi] This is inaccurate. We should not only look at the existence of the table.
    return { success: false, message: `There is no product data for this domain!` };
  }

  try {
    if (SPRConfig) {
      await unsetTimedHardDeleteToSPR({
        client: _getBQClient(),
        accountId,
        domainId,
        providerServiceId,
      });

      return { success: true };
    }

    const newSPRConfig = {
      accountId,
      domainId,
      textEmbeddingStatus: {
        startedAt: new Date(),
        finishedAt: null,
        resultRowCount: null,
        retryCount: 0,
        error: null,
      },
      generationStatus: {
        requestedAmount: null,
        startedAt: null,
        finishedAt: null,
        resultRowCount: null,
        error: null,
      },
      deletedAt: null,
    };

    await model.create(newSPRConfig);

    const flowData = {
      name: 'SPRGenerateEmbeddings',
      config: {
        context: {
          databaseId: accountId,
          providerServiceId,
          domainId,
          platform,
          locale,
        },
      },
    };
    await runFlow([flowData]);

    return { success: true };
  } catch (error) {
    handleError(log, 'Failed to add SPR config', error);
  }
};

module.exports = {
  addSPRConfig,
};
