const { model, ObjectId } = require('../smartRecommendations.model');
const { runFlow } = require('../../../helpers/workflow');
const { handleError } = require('../errorHandling');
const { getDomainBasedData } = require('../../../services/spr/helper');
const { getDefaultShopLocale } = require('../../../helpers/defaultShopLocale');

const generateSPR = async (_, { id, requestedItems }, { userId: databaseId, log }) => {
  let SPRConfig;
  try {
    SPRConfig = await model.findOne({ _id: ObjectId(id) }).orFail();
  } catch (error) {
    handleError(log, 'SPR config not found', error);
  }

  const domainId = SPRConfig.domainId;
  let domainBasedData;
  try {
    domainBasedData = await getDomainBasedData(databaseId, domainId);
  } catch (error) {
    handleError(log, 'Failed to trigger flow', error);
  }
  const { providerServiceId, platform } = domainBasedData;

  if (!providerServiceId) {
    handleError(log, 'Missing providerServiceId ');
  }

  let locale;
  try {
    locale = await getDefaultShopLocale({ databaseId, providerServiceId });
  } catch (error) {
    handleError(log, 'Failed to trigger flow', error);
  }

  try {
    const flowData = {
      name: 'SPRGenerateRecommendations',
      config: {
        context: {
          databaseId,
          providerServiceId,
          domainId: SPRConfig.domainId,
          platform,
          requestedItems,
          locale,
        },
      },
    };
    await runFlow([flowData]);

    await model.findOneAndUpdate(
      { _id: SPRConfig._id },
      {
        $set: {
          generationStatus: {
            requestedAmount: requestedItems,
            startedAt: new Date(),
            finishedAt: null,
            resultRowCount: null,
            error: null,
          },
        },
      },
    );

    return { success: true };
  } catch (error) {
    handleError(log, 'Failed to trigger flow', error);
  }
};

module.exports = {
  generateSPR,
};
