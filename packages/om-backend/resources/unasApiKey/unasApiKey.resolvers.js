const axios = require('axios');
const xml2js = require('xml2js');
const { model: AccountModel } = require('../account/account.model');
const UnifiedProductsAdapter = require('../../services/UnifiedProductsAdapter/unifiedProductsAdapter');
const { determineDomainProviderServiceId } = require('../../util/domain');

const addUnasApiKey = async (_, { apiKey, liveDomain }, { log, userId }) => {
  if (!apiKey || !liveDomain) {
    return {
      success: false,
      message: 'API Key or Provider Service ID is missing',
    };
  }

  try {
    const shopId = await getUnasShopId(apiKey);

    if (!shopId) {
      return {
        success: false,
        message: 'Invalid API Key',
      };
    }

    const accountModel = await AccountModel.findOne({ databaseId: userId });

    if (!accountModel) {
      return {
        success: false,
        message: 'Account not found',
      };
    }

    const isApiKeyInUse = accountModel.settings.shops.some((shop) => shop.apiKey === apiKey);

    if (isApiKeyInUse) {
      return {
        success: false,
        message: 'This API Key is already in use',
      };
    }

    const providedShop = accountModel.settings.shops.find(
      (shop) => shop.live_domain === liveDomain && shop.type === 'unas',
    );

    if (providedShop) {
      await AccountModel.updateOne(
        { databaseId: userId, 'settings.shops.live_domain': liveDomain },
        {
          $set: {
            'settings.shops.$.apiKey': apiKey,
            'settings.shops.$.shopId': shopId,
            'settings.shops.$.updatedAt': new Date().toISOString(),
            'settings.shops.$.active': 1,
            'settings.type': 'unas',
          },
        },
      );
    }

    if (!providedShop) {
      accountModel.settings.shops.push({
        type: 'unas',
        apiKey,
        live_domain: liveDomain,
        active: 1,
        shopId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
    }

    const domain = accountModel.settings.domains.find((d) => d.domain === liveDomain);
    domain.shopId = shopId;

    await accountModel.save();

    const unifiedProductsAdapter = new UnifiedProductsAdapter(
      process.env.PRODUCT_SYNC_SERVICE_URL,
      process.env.om_shared_key,
      log,
    );

    const providerServiceId = determineDomainProviderServiceId({ domain });

    unifiedProductsAdapter.startSync({
      platform: 'unas',
      providerServiceId,
      databaseId: userId,
      additionalParameters: { apiKey },
    });

    return {
      success: true,
      message: 'API Key added successfully',
    };
  } catch (error) {
    log.error('Error adding API Key:', error);
    return {
      success: false,
      message: error.message || 'An error occurred while validating the API Key',
    };
  }
};

const removeUnasApiKey = async (_, { liveDomain }, { log, userId }) => {
  if (!liveDomain) {
    return {
      success: false,
      message: 'Missing parameter (liveDomain)',
    };
  }
  try {
    await AccountModel.updateOne(
      { databaseId: userId, 'settings.shops.live_domain': liveDomain },
      {
        $set: {
          'settings.shops.$.apiKey': null,
          'settings.shops.$.updatedAt': new Date().toISOString(),
        },
      },
    );

    return {
      success: true,
      message: 'API Key removed successfully',
    };
  } catch (error) {
    log.error('Error removing API Key:', error);
    return {
      success: false,
      message: error.message || 'An error occurred while removing the API Key',
    };
  }
};

const getUnasShopId = async (apiKey) => {
  const body = `<?xml version="1.0" encoding="UTF-8" ?>
                  <Params>
                    <ApiKey>${apiKey}</ApiKey>
                    <WebshopInfo>true</WebshopInfo>
                  </Params>`;

  const response = await axios.post('https://api.unas.eu/shop/login', body, {
    headers: {
      'Content-Type': 'application/xml',
    },
  });

  const parser = new xml2js.Parser();
  const parsedResponse = await parser.parseStringPromise(response.data);

  const shopId = parsedResponse?.Login?.ShopId?.[0];

  return shopId ?? null;
};

module.exports = {
  Mutation: {
    addUnasApiKey,
    removeUnasApiKey,
  },
};
