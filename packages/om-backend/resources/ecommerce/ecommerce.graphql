type EcommerceShopInfo {
  moneyFormat: String!
}

type ShoprenterProduct {
  id: ID!
  variant: ID!
  title: String!
  originalPrice: Float
  price: Float!
  currency: String!
  sku: String!
  url: String
  imgUrl: String!
}

# rename this to generic product
type UnifiedProduct {
  id: ID
  variant: ID
  title: String
  originalPrice: Float
  price: Float
  currency: String
  sku: String
  url: String
  imgUrl: String
}

input ShoprenterProductQuery {
  criteria: JSON
  pagination: JSON
}

# TODO: Rename this to generic product query
input UnifiedProductQuery {
  criteria: JSON
  pagination: JSON
}

type ShoprenterGetProductsResponse {
  products: [ShoprenterProduct]!
  hasNextPage: Boolean
  moneyFormat: String
}

# TODO: Rename this to generic
type UnifiedGetProductsResponse {
  products: [UnifiedProduct]!
  hasNextPage: Boolean
  moneyFormat: String
}

type UnifiedImage {
  id: ID
  url: String
}

type ShopifyImage {
  id: ID!
  src: String!
}

type ShopifyVariant {
  id: ID!
  price: Float!
  title: String!
  sku: String
  compareAtPrice: Float
  image: ShopifyImage
}

type ShopifyProduct {
  id: ID!
  title: String!
  handle: String!
  images: [ShopifyImage]
  variants: [ShopifyVariant]
}

input ShopifyProductQuery {
  criteria: JSON
  pagination: JSON
}

input ShopifyVariantQuery {
  criteria: JSON
}

type ShopifyGetProductsResponse {
  products: [ShopifyProduct]!
  moneyFormat: String
  cursor: String
  hasNextPage: Boolean
}

type ShopifyGetVariantsResponse {
  variants: [ShopifyVariant]!
}

extend type Query {
  getShopInfo(type: String!, domain: String!): EcommerceShopInfo
  getShoprenterProducts(input: ShoprenterProductQuery): ShoprenterGetProductsResponse
  getUnifiedProducts(input:UnifiedProductQuery): UnifiedGetProductsResponse
  getShopifyProducts(input: ShopifyProductQuery): ShopifyGetProductsResponse
  getShopifyVariants(input: ShopifyVariantQuery): ShopifyGetVariantsResponse
}
