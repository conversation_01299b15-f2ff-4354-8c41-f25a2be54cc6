/* eslint-disable camelcase */
const Shopify = require('shopify-api-node');
const axios = require('axios');
const ShopRenterAdapter = require('../../services/shoprenterAdapter');
const { redis } = require('../../services/ioRedisAdapter');
const { getAuthData } = require('../../helpers/getShopAuthData');
const UnifiedProductsAdapter = require('../../services/UnifiedProductsAdapter/unifiedProductsAdapter');
const { SHOPIFY_API_VERSION } = require('../../util/constant');
const { model: AccountModel } = require('../account/account.model');
const { determineDomainProviderServiceId, domainAndShopByDomainId } = require('../../util/domain');

const _c = (v) => JSON.parse(JSON.stringify(v));

const decodeEntities = (encodedString) => {
  const translate_re = /&(nbsp|amp|quot|lt|gt);/g;
  const translate = {
    nbsp: ' ',
    amp: '&',
    quot: '"',
    lt: '<',
    gt: '>',
  };
  return encodedString
    .replace(translate_re, (match, entity) => translate[entity])
    .replace(/&#(\d+);/gi, (match, numStr) => String.fromCharCode(parseInt(numStr, 10)));
};

const getShopifyShopInfo = async (adapterConfig, log) => {
  try {
    const client = new Shopify(adapterConfig);
    const shopData = await client.shop.get();

    return {
      moneyFormat: shopData.money_with_currency_format,
    };
  } catch (err) {
    log.error(err, 'Failed to fetch shopify shop info');
    throw new Error('Failed to fetch shopify shop info');
  }
};

const getShoprenterShopInfo = async (adapterConfig, log) => {
  try {
    const shopData = await ShopRenterAdapter.getShopInfo(
      adapterConfig.shopName,
      adapterConfig.auth,
      log,
    );
    if (shopData) {
      let moneyFormat = `{{amount}}`;

      if (shopData.currency === 'HUF') {
        moneyFormat = `{{amount}} Ft`;
      } else if (shopData.currency) {
        moneyFormat = `{{amount}} ${shopData.currency}`;
      }

      return {
        moneyFormat,
      };
    }

    throw new Error('Shop data is not returned from adapter.');
  } catch (err) {
    log.error(
      { responseData: err.response && err.response.data ? err.response.data : null },
      `Failed to fetch ${adapterConfig.shopName} with message: ${err.message}`,
    );
    log.error(err, 'Failed to fetch "%s" shoprenter shop info', adapterConfig.shopName);
    throw new Error('Failed to fetch shoprenter shop info.');
  }
};

const getShopInfo = async (_, { type, domain }, { userId: databaseId, log }) => {
  if (type !== 'shoprenter' && type !== 'shopify') {
    throw new Error(
      `Unsupported shop type "${type}" provided when trying to fetch ecommerce shop info.`,
    );
  }

  const authData = await getAuthData({ databaseId, type, domain: `${domain}`.replace('www.', '') });

  if (!authData) {
    throw new Error(
      `Failed to get "${type}" shop adapter config for domain: ${domain}, databaseId: ${databaseId}`,
    );
  }

  try {
    if (type === 'shopify') {
      return await getShopifyShopInfo(authData, log);
    }
    if (type === 'shoprenter') {
      return await getShoprenterShopInfo(authData, log);
    }
  } catch (e) {
    log.error(e, 'Ecommerce getShopInfo error');
  }

  return null;
};

const getUnifiedProducts = async (_, { input: { criteria, pagination } }, { userId, log }) => {
  const productSyncUrl = process.env.PRODUCT_SYNC_SERVICE_URL;
  const sharedKey = process.env.om_shared_key;

  if (!productSyncUrl || !sharedKey) {
    throw new Error('Missing required environment variables');
  }

  if (!userId) {
    throw new Error('Missing required userId (databaseId)');
  }

  if (!criteria.domain) {
    throw new Error('Missing required domain');
  }

  const account = await AccountModel.findOne(
    { databaseId: userId },
    { 'settings.shops': 1, 'settings.domains': 1 },
  );
  const domainId = account.settings.domains.find((d) => d.domain === criteria.domain)?._id;
  const { domain, shop } = domainAndShopByDomainId(domainId, account.settings);
  const providerServiceId = determineDomainProviderServiceId({ domain, shop });

  if (!providerServiceId) {
    throw new Error(
      `Missing required providerServiceId (shopId), for domain: ${domain?.domain},  ${domain?.shopId}`,
    );
  }

  const adapter = new UnifiedProductsAdapter(productSyncUrl, sharedKey, log);
  return adapter.fetchUnifiedProducts({
    criteria: {
      ...criteria,
      userId, // userId is a synonym for databaseId
      shopId: providerServiceId,
    },
    pagination,
  });
};

const getShoprenterProducts = async (_, { input: { criteria, pagination } }, { userId, log }) => {
  const authData = await getAuthData({
    databaseId: userId,
    type: 'shoprenter',
    domain: criteria.domain,
  });
  if (authData) {
    const { auth, shopName } = authData;
    const { moneyFormat } = await getShoprenterShopInfo(authData, log);
    const { username, password } = auth;
    const shopSettings = await getShoprenterShopSettings({ shopName, username, password });
    const result = [];
    let hasNextPage = true;

    const srRequestOptions = {
      auth: { username, password },
      headers: { Accept: 'application/json' },
    };
    try {
      let query = `http://${shopName}.api.shoprenter.hu/productExtend?full=1&limit=50&published=1`;
      if (criteria.productIds) {
        const cacheKey = `shoprenter-products:${userId}:${
          criteria.domain
        }:${criteria.productIds.join(',')}`;
        const cachedValue = await redis.get(cacheKey);
        if (cachedValue) {
          return {
            products: JSON.parse(cachedValue),
            moneyFormat: '',
            hasNextPage: false,
          };
        }

        const { data } = await axios.post(
          `http://${shopName}.api.shoprenter.hu/batch`,
          {
            data: {
              requests: criteria.productIds.map((id) => ({
                method: 'GET',
                uri: `http://${shopName}.api.shoprenter.hu/productExtend?full=1&limit=50&published=1&innerId=${id}`,
              })),
            },
          },
          srRequestOptions,
        );

        if (data.requests && Array.isArray(data.requests.request)) {
          data.requests.request.forEach((req) => {
            const { response } = req;
            if (response.header.statusCode === 200) {
              const shoprenterProducts = response.body.items;
              if (shoprenterProducts && Array.isArray(shoprenterProducts)) {
                shoprenterProducts.forEach((product) => {
                  result.push(parseShopRenterProduct(product, shopSettings, log));
                });
              }
            }
          });
        } else {
          throw new Error(`Invalid batch response body received: ${JSON.stringify(data)}`);
        }

        const products = result.filter(Boolean);
        await redis.setex(cacheKey, 600, JSON.stringify(products));

        return {
          products,
          hasNextPage,
          moneyFormat,
        };
      }

      if (criteria.query) {
        const rawSku = criteria.query;
        let sku;
        try {
          sku = encodeURIComponent(rawSku);
        } catch (err) {
          sku = rawSku;
        }

        query += `&sku=${sku}`;
      }
      if (pagination.page) query += `&page=${pagination.page}`;
      const { data } = await axios.get(query, srRequestOptions);
      const { items: products, pageCount } = data;
      hasNextPage = pageCount - 1 !== pagination.page;
      if (products && Array.isArray(products)) {
        products.forEach((product) => {
          result.push(parseShopRenterProduct(product, shopSettings, log));
        });
      } else {
        throw new Error(`Invalid response body received: ${JSON.stringify(data)}`);
      }
    } catch (err) {
      if (err?.response?.status === 429) {
        log.warn({
          message: 'ShopRenter product details batch failed, too many requests',
          errorMessage: err.message,
          stack: err.stack,
          shopName,
          data: err.response.data,
          status: err.response.status,
        });
        throw new Error('ShopRenter product details batch failed, too many requests.');
      }

      if (err.response) {
        log.error(
          'ShopRenter products request failed, error: %s, stack: %s, shopname: %s, resp: %o, status: %d',
          err.message,
          err.stack,
          shopName,
          err.response.data,
          err.response.status,
        );
      } else {
        log.error(
          'ShopRenter products request failed, error: %s, stack: %s, shopname: %s',
          err.message,
          err.stack,
          shopName,
        );
      }
      throw new Error(`ShopRenter products request failed: ${err.message}`);
    }

    return {
      products: result.filter(Boolean),
      hasNextPage,
      moneyFormat,
    };
  }

  return null;
};

const getShopifyProducts = async (_, { input: { criteria, pagination } }, { userId, log }) => {
  const authData = await getAuthData({
    databaseId: userId,
    type: 'shopify',
    domain: `${criteria.domain}`.replace('www.', ''),
  });
  log.info('[Shopify] Fetch products for %d started', userId);
  if (authData) {
    let productsResponse;
    const shopGQLEndpoint = `https://${authData.shopName}.myshopify.com/admin/api/${SHOPIFY_API_VERSION}/graphql.json`;

    try {
      if (criteria.productIds) {
        const cacheKey = `shopify-products:${userId}:${criteria.domain}:${criteria.productIds.join(
          ',',
        )}`;
        const cachedValue = await redis.get(cacheKey);
        if (cachedValue) {
          return {
            products: JSON.parse(cachedValue),
            moneyFormat: '',
            cursor: '',
            hasNextPage: false,
          };
        }

        productsResponse = await axios.post(
          shopGQLEndpoint,
          {
            query: `query($ids: [ID!]!) {
              nodes(ids: $ids) {
                ...on Product {
                  title,
                  id,
                  handle,
                  images(first: 1) {
                    edges {
                      node {
                        id
                        src: originalSrc
                      }
                    }
                  }
                }
              }
            }`,
            variables: {
              ids: criteria.productIds,
            },
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'X-Shopify-Access-Token': authData.accessToken,
            },
          },
        );

        const mappedProducts = productsResponse.data.data.nodes.map((node) => {
          const {
            title,
            id,
            handle,
            images: { edges },
          } = node;
          return {
            title,
            id,
            handle,
            images: edges.length ? [{ src: edges[0].node.src, id: edges[0].node.id }] : [],
          };
        });
        await redis.setex(cacheKey, 600, JSON.stringify(mappedProducts));
        return {
          products: mappedProducts,
          moneyFormat: '',
          cursor: '',
          hasNextPage: false,
        };
      }

      productsResponse = await axios.post(
        shopGQLEndpoint,
        {
          query: `query($cursor: String, $query: String) {
            products(first: 15, after: $cursor, query: $query) {
              pageInfo {
                hasNextPage
              }
              edges {
                cursor
                node {
                  id
                  title
                  handle
                  variants(first: 10) {
                    edges {
                      node {
                        id
                        price
                        title
                        sku
                        compareAtPrice
                        image {
                          id
                          src
                        }
                      }
                    }
                  }
                  images(first: 10) {
                    edges {
                      node {
                        id
                        src
                      }
                    }
                  }
                }
              }
            }
          }`,
          variables: {
            cursor: pagination.cursor,
            query: [
              criteria.query ? `(title:${criteria.query}* OR sku:${criteria.query}*)` : '',
              'published_status:published AND status:active',
            ]
              .filter((v) => !!v)
              .join(' AND '),
          },
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': authData.accessToken,
          },
        },
      );

      if (productsResponse.data.errors) {
        log.error('[Shopify] getProducts error: %o', productsResponse.data.errors);
        throw new Error('Failed to fetch products');
      }

      const {
        data: {
          products: {
            edges,
            pageInfo: { hasNextPage },
          },
        },
      } = productsResponse.data;
      const productsResult = [];
      let cursor = null;
      for (const edge of edges) {
        const product = _c(edge.node);
        product.variants = product.variants.edges.map((edge) => edge.node);
        product.images = product.images.edges.map((edge) => edge.node);
        productsResult.push(product);
        cursor = edge.cursor;
      }
      log.info('[Shopify] Fetch products for %d ended', userId);

      const moneyFormatResponse = await axios.post(
        `https://${authData.shopName}.myshopify.com/admin/api/${SHOPIFY_API_VERSION}/graphql.json`,
        {
          query: `query {
              shop {
                currencyFormats {
                  moneyWithCurrencyFormat
                }
              }
            }`,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': authData.accessToken,
          },
        },
      );

      return {
        products: productsResult,
        moneyFormat: decodeEntities(
          moneyFormatResponse.data.data.shop.currencyFormats.moneyWithCurrencyFormat,
        ),
        cursor,
        hasNextPage,
      };
    } catch (error) {
      const errorMessage = error.response?.data ?? error.message;
      log.error(
        {
          err: errorMessage,
        },
        `[Shopify] api error: ${JSON.stringify(errorMessage)}, databaseId: ${userId}`,
      );
    }
  }
  return {
    products: [],
    moneyFormat: '{{amount}}',
  };
};

const getShopifyVariants = async (_, { input: { criteria } }, { userId, log }) => {
  const authData = await getAuthData({
    databaseId: userId,
    type: 'shopify',
    domain: `${criteria.domain}`.replace('www.', ''),
  });
  log.info('[Shopify] Fetch variants for %d started', userId);
  if (authData) {
    try {
      const response = await axios.post(
        `https://${authData.shopName}.myshopify.com/admin/api/${SHOPIFY_API_VERSION}/graphql.json`,
        {
          query: `query {
            product(id: "${criteria.productId}") {
              tracksInventory
              variants(first: 250) {
                edges {
                  node {
                    id
                    price
                    title
                    sku
                    inventoryPolicy
                    inventoryQuantity
                    compareAtPrice
                    image {
                      id
                      src
                    }
                  }
                }
              }
            }
          }`,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': authData.accessToken,
          },
        },
      );

      if (response.data.errors) {
        log.error(response.data.errors, '[Shopify] getVariants error');
      }

      log.info('[Shopify] Fetch variants for %d ended', userId);

      const {
        data: {
          product: {
            tracksInventory,
            variants: { edges },
          },
        },
      } = response.data;
      let variants = edges.map((edge) => edge.node);
      if (tracksInventory) {
        variants = variants.filter(
          ({ inventoryPolicy, inventoryQuantity }) =>
            inventoryPolicy === 'CONTINUE' || inventoryQuantity > 0,
        );
      }

      return { variants };
    } catch (error) {
      const errorMessage =
        error.response && error.response.data ? error.response.data : error.message;
      log.error(`[Shopify] api error: ${errorMessage}`);
    }
  }

  return {
    variants: [],
  };
};

const parseShopRenterProduct = (item, { noStockAddToCart }, log) => {
  const price = item.productPrices.find((e) => e.customerGroup.default === true);
  let parentId = item.innerId;
  if (item.parentProduct && item.parentProduct.href) {
    const rawId = item.parentProduct.href.split('/').pop();
    if (rawId) {
      if (rawId.startsWith('product_')) {
        parentId = rawId.split('_').pop();
      } else if (+item.innerId) {
        parentId = item.innerId;
      } else {
        parentId = Buffer.from(rawId, 'base64').toString('utf-8').split('=').pop();
      }
    }
  }
  const status = parseInt(item.status, 10);
  const onStock = noStockAddToCart || isShoprenterProductOnStock(item);

  if (status !== 1 || !onStock) {
    return null;
  }

  if (!item.urlAliases?.[0]?.urlAlias) {
    log.warn(
      {
        shoprenter: { product: item },
      },
      'Unable to map SR product since unsupported structure passed',
    );
    return null;
  }

  return {
    id: parentId,
    variant: item.innerId,
    title: item.productDescriptions[0].name,
    originalPrice:
      price.grossOriginal && price.grossOriginal !== price.gross
        ? parseFloat(price.grossOriginal)
        : null,
    price: parseFloat(price.gross),
    currency: price.currencyCode,
    sku: item.sku,
    url: `/${item.urlAliases[0].urlAlias}`,
    imgUrl: encodeURI(item.allImages.mainImage),
  };
};

const isShoprenterProductOnStock = (product) => {
  const quantityInStock = Math.max(
    parseInt(product.stock1, 10),
    parseInt(product.stock2, 10),
    parseInt(product.stock3, 10),
    parseInt(product.stock4, 10),
  );

  return quantityInStock > 0;
};

const getShoprenterShopSettings = async ({ shopName, username, password }) => {
  const cacheKey = `shopSettings:shopRenter:${shopName}`;
  const cached = await redis.get(cacheKey);

  if (cached) {
    return JSON.parse(cached);
  }

  const { data } = await axios.get(
    `https://${shopName}.api.shoprenter.hu/settings?full=1&key=config_disable_addtocart_no_quantity`,
    {
      auth: { username, password },
      headers: { Accept: 'application/json' },
    },
  );

  const settings = {
    noStockAddToCart: false,
  };

  if (!data || !data.items) {
    throw new Error('Failed to get shoprenter shop settings.');
  }

  data.items.forEach(({ key, value }) => {
    if (key === 'config_disable_addtocart_no_quantity') {
      // 0 -> allow customers to add products out of stock to cart
      // 1 -> forbid customers to add products out of stock to cart
      settings.noStockAddToCart = !parseInt(value, 10);
    }
  });

  await redis.setex(cacheKey, 60, JSON.stringify(settings));

  return settings;
};

module.exports = {
  Query: {
    getShopInfo,
    getShoprenterProducts,
    getShopifyProducts,
    getUnifiedProducts,
    getShopifyVariants,
  },
};
