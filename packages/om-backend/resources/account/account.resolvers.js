const merge = require('lodash.merge');
const crypto = require('crypto');
const uuidAPIKey = require('uuid-apikey');
const moment = require('moment');
const striptags = require('striptags');
const fs = require('fs');
const _get = require('lodash.get');
const { getConfigForIntegration } = require('@om/integrations');
const { PartnerTypeService } = require('@om/payment/src/partnerType');
const { AccountLimit } = require('@om/queues');

const { model: AccountModel, ObjectId } = require('./account.model');
const { model: LoginModel } = require('../login/login.model');
const { model: campaignModel } = require('../campaign/campaign.model');
const { model: TemplateModel } = require('../template/template.model');
const ShopifyHelper = require('../../helpers/shopify');
const ShopRenterHelper = require('../../helpers/shoprenter');
const Mailer = require('../../util/mailer');
const AccountCreator = require('../../services/accountCreator');
const HeapAnalytics = require('../../services/heapAnalytics');
const Translator = require('../../util/translator');
const Templating = require('../../util/templating');
const { queueAccountInfoUpload } = require('../../services/queue/accountInfoQueue');

const omAdminUrl = process.env.om_new_admin_url;
const Intercom = require('../../services/intercom');
const EventDispatcher = require('../../services/EventDispatcher');
const ActOnAdapter = require('../../services/integrations/actOn');
const intercom = require('../../services/intercom');
const hubSpot = require('../../services/hubSpot');
const omMailChimpAdapter = require('../../services/mailChimp');
const { updateDomainInfo, detectSiteInfo } = require('../../services/domainChecker');
const AccountFonts = require('../../services/fonts/accountFonts');
const { deleteObjects } = require('../../services/s3');
const {
  getLostPageViews,
  getLostDomains,
  getLostFeatures,
  getPlanDetails,
  setDowngradeSettings,
} = require('../../helpers/downgrade');
const BackofficeApiClient = require('../../services/backofficeApiClient');

const { asyncSign } = require('../../util/jwt');
const redisClient = require('../../services/ioRedisAdapter');
const salesMysql = require('../../util/salesMysql');
const { getCustomerIdFromPartnerId, isAffiliate } = require('../../util/affiliate');
const {
  sendNewAgencyRegistration,
  sendAiRequestsOfTheUser,
  sendVIPOnboarding,
} = require('../../services/slack');
const { addCacheLoadJob } = require('../../services/frontendCacheLoadQueue');
const { getCacheKey } = require('../../util/wizard');
const { getPartnerInfo } = require('../../util/partnerInfo');
const { toggleShopifyAppExtensionStatus } = require('./mutations/toggleShopifyAppExtensionStatus');
const { checkShopifyAppEmbedStatuses } = require('./mutations/checkShopifyAppEmbedStatuses');
const { upsertGeneralSetting } = require('./mutations/upsertGeneralSetting');
const { purifyDomain, removeProtocolFromDomain } = require('../../util/domainHelper');
const { setUserProfileKey, getAccountSettings } = require('./helpers');
const logger = require('../../logger');
const hubSpotV3 = require('../../services/hubSpotV3');
const { queueSimilarwebJob, queueHubspotJob } = require('../../services/queue/thirdParty');
const {
  createAccountForShop: createAccountForShopifyShop,
} = require('../../services/shopifyInstaller');
const { determineLangByDomain } = require('../../services/preferredLanguage');

const invitationLimit = async (uniqueKey) => {
  const invitationKey = `invitationRequest:${uniqueKey}`;
  const invitationDaily = `invitationRequestDaily:${uniqueKey}`;
  const [recentSent, dailyLimit] = await Promise.all([
    redisClient.get(invitationKey),
    redisClient.get(invitationDaily),
  ]);

  if (recentSent) {
    return { success: false, message: 'recentlySent' };
  }

  if (dailyLimit >= 30) {
    return { success: false, message: 'dailyLimit' };
  }

  await redisClient.setex(invitationKey, 1, 10);
  if (!dailyLimit) {
    const now = new Date();
    const tomorrow = new Date().setDate(now.getDate() + 1);
    const diff = tomorrow - now;
    await redisClient.setex(invitationDaily, 0, Math.round(diff / 1000));
  }
  await redisClient.incr(invitationDaily);

  return { success: true };
};

const _defaultWhiteLabelling = {
  customLogo: null,
  customSquared: null,
  brandName: 'OptiMonk',
  domainBase: 'optimonk.com',
};

const getAccount = async (_, __, { userId, loginId, superadmin, role }) => {
  const [account, login] = await Promise.all([
    AccountModel.findOne({ databaseId: userId }),
    LoginModel.findOne({ _id: loginId }),
  ]);
  let mergeFields = {};

  if (!account) {
    return {};
  }

  if (account.type === 'agency') {
    const subAccountSum = await AccountModel.aggregate([
      { $match: { _id: { $in: account.subAccounts } } },
      {
        $group: {
          _id: null,
          totalAssigned: { $sum: '$limits.maxVisitor' },
        },
      },
      { $project: { _id: 0 } },
    ]);

    const agencyLimits =
      subAccountSum.length > 0
        ? { totalAssigned: subAccountSum[0].totalAssigned, usedVisitor: account.limits.usedVisitor }
        : { totalAssigned: 0, usedVisitor: 0 };

    const result = merge(account, { login }, agencyLimits, {
      userHash: Intercom.createUserHash(login.email),
      userRole: role,
      isSuperAdmin: superadmin,
    });

    account.settings.whiteLabel = merge(
      {},
      _defaultWhiteLabelling,
      account.settings.whiteLabel || {},
    );

    return result;
  }

  if (account.type === 'sub') {
    const agency = await AccountModel.findOne(
      { subAccounts: account._id },
      { _id: 1, features: 1, users: 1 },
    );

    if (agency) {
      const agencyUser = agency.users.find((user) => user.loginId.toString() === loginId);

      mergeFields = merge({}, mergeFields, {
        features: agency.features,
        agencyUserRole: agencyUser?.role ?? null,
      });
    }
  }

  return merge(
    account,
    {
      login,
      isSuperAdmin: superadmin,
      userHash: Intercom.createUserHash(login.email),
      userRole: role,
    },
    mergeFields,
  );
};

const getSubAccounts = async (_, { filter, pagination, sorting }, { userId }) => {
  const account = await AccountModel.findOne({ databaseId: userId, type: 'agency' });

  if (account) {
    const count = account.subAccounts.length;
    pagination = pagination || { page: 1, limit: 30 };
    sorting = sorting || { field: 'name', direction: 1 };

    const page = pagination.page - 1 < 0 ? 0 : pagination.page - 1;

    const subAccounts = await AccountModel.aggregate([
      {
        $match: {
          _id: { $in: account.subAccounts },
          type: 'sub',
          ...(filter?.search ? { name: new RegExp(filter.search.replace(/\+/, '\\$&'), 'i') } : {}),
        },
      },

      {
        $project: {
          _id: 1,
          campaignInfos: 1,
          databaseId: 1,
          limits: 1,
          settings: 1,
          name: 1,
          users: { $add: [{ $size: '$users' }] },
        },
      },
      { $sort: { [sorting.field]: parseInt(sorting.direction, 10) } },
      { $skip: page * pagination.limit },
      { $limit: pagination.limit },
    ]);
    return { subAccounts, count };
  }
  return {};
};

const getAllSubAccounts = async (_, __, { userId }) => {
  const account = await AccountModel.findOne({ databaseId: userId, type: 'agency' });
  let subAccounts = [];

  if (account) {
    subAccounts = await AccountModel.aggregate([
      { $match: { _id: { $in: account.subAccounts }, type: 'sub' } },
      {
        $project: {
          _id: 1,
          campaignInfos: 1,
          databaseId: 1,
          limits: 1,
          settings: 1,
          name: 1,
          users: { $add: [{ $size: '$users' }, { $size: '$invitations' }] },
        },
      },
    ]);
  }

  return subAccounts;
};

const addSubAccount = async (_, { input }, { req, userId, loginId, log }) => {
  const limit = await invitationLimit(userId);
  if (!limit.success) {
    return limit;
  }

  const { name, limits, users, invitations } = input;
  const agencyAccount = await AccountModel.findOne({ databaseId: userId });
  const agencyLogin = await LoginModel.findOne({ _id: agencyAccount.users[0].loginId });
  limits.domains = 99999;

  log.info('adding sub account to %o', { userId, name });
  const login = await LoginModel.findOne({ _id: loginId });
  const invitationObjects = [];
  invitations.forEach((invitation) => {
    // const updated = await AccountModel.updateOne({_id: accountId}, {$push: {invitations: invitation}}, {new: true})
    const invObj = createInvitation(invitation);
    invitationObjects.push(invObj);
    // log.info('@@@', invitation, email)
    sendInvitationEmail(name, login, invObj, {
      req,
      whiteLabelSettings: agencyAccount.settings.whiteLabel,
    });
  });
  const ownerUser = agencyAccount.users.find((account) => account.role === 'owner');
  users.unshift({ loginId: ownerUser.loginId, role: 'owner' }); // add owner first
  if (ownerUser.loginId.toString() !== loginId.toString()) {
    users.push({ loginId, role: 'write' }); // add creator
  }
  const subAccount = await AccountCreator.createSubAccount({
    ownerId: ownerUser.loginId,
    limits,
    name,
    users,
    invitations: invitationObjects,
    ownerAccount: agencyAccount,
    ownerLogin: agencyLogin,
  });

  await AccountModel.update(
    { databaseId: userId, type: 'agency' },
    { $push: { subAccounts: subAccount._id } },
  );

  return true;
};

const updateSubAccount = async (_, { input }, { userId }) => {
  const agencyAccount = await AccountModel.findOne({ databaseId: userId });

  if (agencyAccount && arrayContainsObjectId(agencyAccount.subAccounts, input._id)) {
    const result = await AccountModel.findOneAndUpdate({ _id: input._id }, input, { new: true });
    await AccountLimit.addJob({ databaseId: userId });

    return result;
  }

  return false;
};

const removeInvitation = async (_, { accountId, email }) => {
  await AccountModel.updateOne(
    { _id: accountId, type: 'sub' },
    { $pull: { invitations: { email } } },
  );
  return true;
};

const removeSubAccount = async (_, { accountId }, { userId }) => {
  await AccountModel.findOneAndUpdate(
    { databaseId: userId, type: 'agency' },
    { $pull: { subAccounts: ObjectId(accountId) } },
  );
  return AccountModel.findOneAndUpdate({ _id: accountId }, { deleted: true });
};

const getUsers = async (_, { accountId }, { userId, log }) => {
  let account;
  if (accountId) {
    const agencyAccount = await AccountModel.findOne(
      { databaseId: userId, type: 'agency' },
      { subAccounts: 1 },
    );
    const subAccounts = agencyAccount?.subAccounts || [];
    const foundSub = subAccounts.find((sub) => sub.toString() === accountId);
    if (foundSub) {
      account = await AccountModel.findOne({ _id: accountId }, { users: 1 });
    } else {
      log.info(`provided accountId ${accountId} is not sub account of ${userId}`);
      throw new Error('cannot query users for account');
    }
  } else {
    account = await AccountModel.findOne({ databaseId: userId }, { users: 1 });
  }

  const users = account && account.users ? account.users : null;
  if (users == null) {
    return [];
  }
  const userIds = users.map((user) => user.loginId);
  const logins = await LoginModel.find({ _id: { $in: userIds } });

  const usersCombined = [];
  for (let i = 0; i < users.length; i++) {
    const { loginId, role } = users[i];
    const login = logins.find(({ _id }) => _id.equals(loginId));

    if (login) {
      usersCombined.push({ login, role });
    }
  }
  // usersCombined.sort((u1, u2) => (u1.login.fullName > u2.login.fullName) - (u1.login.fullName < u2.login.fullName))

  return usersCombined;
};

const getUsersWithSuggestions = async (_, { accountId }, { userId }) => {
  const subAccount = await AccountModel.findOne({ _id: accountId });

  if (!subAccount) {
    return [];
  }
  const userIds = subAccount.users.map((user) => user.loginId);
  const logins = await LoginModel.find({ _id: { $in: userIds } });

  const users = [];
  subAccount.users.forEach((user) => {
    users.push({
      role: user.role,
      login: logins.find((login) => login._id.equals(user.loginId)),
    });
  });

  const agencyAccount = await AccountModel.findOne({ databaseId: userId });

  const otherUserIds = await AccountModel.aggregate([
    { $match: { _id: { $in: agencyAccount.subAccounts } } },
    { $unwind: '$users' },
    { $replaceRoot: { newRoot: '$users' } },
    { $group: { _id: '$loginId' } },
    { $match: { _id: { $nin: userIds } } },
  ]);

  const otherUsers = await LoginModel.find({ _id: { $in: otherUserIds } });

  otherUsers.forEach((ou) => {
    users.push({
      login: ou,
      role: null,
    });
  });

  // users.sort((u1, u2) => (u1.login.fullName > u2.login.fullName) - (u1.login.fullName < u2.login.fullName))

  return users;
};

const getInvitations = async (_, { accountId }, { userId }) => {
  const agencyAccount = await AccountModel.findOne({ databaseId: userId });
  const subAccount = await AccountModel.findOne({ _id: accountId });

  if (agencyAccount && arrayContainsObjectId(agencyAccount.subAccounts, accountId)) {
    return subAccount.invitations;
  }

  return [];
};

const isAddedOrInvitedAlready = async (invitations, users, email) => {
  const invited = invitations.map((invitation) => invitation.email).indexOf(email) !== -1;
  const logins = await LoginModel.find({ _id: { $in: users.map((u) => u.loginId) } }, { email: 1 });
  const added = logins.map((l) => l.email).includes(email);
  return invited || added;
};

const addInvitation = async (_, { accountId, email }, { req, userId }) => {
  const limit = await invitationLimit(userId);
  if (!limit.success) {
    return limit;
  }

  let account;
  let users;
  let inviterLoginId;
  let agency;

  if (!accountId) {
    // normal account
    account = await AccountModel.findOne({ databaseId: userId });
    accountId = account._id;

    users = account.users;
    inviterLoginId = account.users[0].loginId;
    const inviterRole = account.users[0].role;

    if (inviterRole !== 'owner') {
      return { success: false, message: 'onlyOwnerCanInvite' };
    }
  } else {
    // subaccount
    const [subAccount, agencyAccount] = await Promise.all([
      AccountModel.findOne({ _id: accountId }),
      AccountModel.findOne({ databaseId: userId }),
    ]);

    // check if subAccount found by paramter if sub account of authenticated user
    const subAccounts = agencyAccount?.subAccounts || [];
    const foundSub = subAccounts.find((sub) => sub.toString() === accountId);
    if (!foundSub) {
      return { success: false, message: 'insufficientInvitePermission' };
    }

    account = subAccount;
    agency = agencyAccount;

    users = account.users.concat(agencyAccount.users[0]);
    inviterLoginId = agencyAccount.users[0].loginId;
  }

  const addedOrInvitedAlready = await isAddedOrInvitedAlready(account.invitations, users, email);
  if (addedOrInvitedAlready) {
    return { success: false, message: 'alreadyInvitedOrAdded' };
  }

  const senderLogin = await LoginModel.findOne({ _id: inviterLoginId });
  const invitation = createInvitation(email);

  sendInvitationEmail(account.name, senderLogin, invitation, {
    req,
    whiteLabelSettings: agency && agency.settings.whiteLabel,
  });

  await AccountModel.updateOne({ _id: accountId }, { $push: { invitations: invitation } });
  return { success: true };
};

const getInvitation = async (_, { hash }, { log }) => {
  try {
    const account = await AccountModel.findOne({ 'invitations.hash': hash });

    let inviter;

    if (!account) {
      return {
        success: false,
        message: 'Invalid token!',
      };
    }

    if (account.type === 'normal' || account.type === 'agency') {
      inviter = await LoginModel.findOne({ _id: account.users[0].loginId });
    } else {
      const agencyAccount = await AccountModel.findOne({ subAccounts: account._id });
      inviter = await LoginModel.findOne({ _id: agencyAccount.users[0].loginId });
    }

    const index = account.invitations.map((invitation) => invitation.hash).indexOf(hash);
    const expiration = new Date(account.invitations[index].expiration);
    const now = new Date();

    if (expiration < now) {
      return {
        success: false,
        message: 'Token expired!',
      };
    }

    return {
      success: true,
      inviter,
    };
  } catch (e) {
    log.error(e);
  }

  return {
    success: false,
    message: 'Invalid token!',
  };
};

const getUserSuggestions = async (_, __, { userId }) => {
  const agencyAccount = await AccountModel.findOne({ databaseId: userId, type: 'agency' });
  if (agencyAccount) {
    const userIds = await AccountModel.aggregate([
      { $match: { _id: { $in: agencyAccount.subAccounts } } },
      { $unwind: '$users' },
      { $replaceRoot: { newRoot: '$users' } },
      { $group: { _id: '$loginId' } },
    ]);

    const users = await LoginModel.find({ _id: { $in: userIds } });

    // users.sort((u1, u2) => (u1.fullName > u2.fullName) - (u1.fullName < u2.fullName))

    const ownerUser = agencyAccount.users.find((account) => account.role === 'owner');
    return { users, agencyOwner: ownerUser.loginId };
  }
  return {};
};

const getAccountMembers = async (_, __, { userId, log }) => {
  try {
    const account = await AccountModel.findOne(
      { databaseId: userId },
      { users: 1, 'invitations.email': 1, 'invitations.expiration': 1, 'invitations._id': 1 },
    ).lean();
    const userIds = account.users.map((user) => user.loginId);
    const logins = await LoginModel.find({ _id: { $in: userIds } }).lean();

    let members = [];
    for (let i = 0; i < account.users.length; i++) {
      if (account.users[i].role !== 'owner') {
        const login = logins.find((l) => account.users[i].loginId.equals(l._id));
        if (login) {
          members.push({
            firstName: login.firstName,
            lastName: login.lastName,
            email: login.email,
          });
        }
      }
    }

    members = members.concat(account.invitations);
    members.sort((m1, m2) => (m1.email > m2.email) - (m1.email < m2.email));

    return members;
  } catch (e) {
    log.error('error while getting members for account with databaseId = %d error: %o', userId, e);
  }
};

const removeMember = async (_, { email }, { userId }) => {
  const account = await AccountModel.findOne({ databaseId: userId });

  if (account.invitations.map((i) => i.email).includes(email)) {
    const updated = await AccountModel.update(
      { databaseId: userId },
      { $pull: { invitations: { email } } },
    );
    return !!updated;
  }

  const login = await LoginModel.findOne({ email });
  if (!login) return false;

  const accountCount = await AccountModel.count({ 'users.loginId': login._id });
  if (accountCount === 1) {
    await LoginModel.deleteOne({ _id: login._id });
  }

  const updated = await AccountModel.update(
    { databaseId: userId },
    { $pull: { users: { loginId: login._id } } },
  );
  return !!updated;
};

const updateUser = async (_, { accountId, userInput }) => {
  return AccountModel.findOneAndUpdate(
    { _id: accountId, 'users.loginId': userInput.loginId },
    { $set: { 'users.$.role': userInput.role } },
    { new: true },
  );
};

const removeUser = async (_, { accountId, loginId }, { userId }) => {
  const agencyAccount = await AccountModel.findOne({ databaseId: userId });

  const accountCount = await AccountModel.count({ 'users.loginId': loginId });
  if (accountCount === 1) {
    await LoginModel.deleteOne({ _id: loginId });
  }

  if (agencyAccount && arrayContainsObjectId(agencyAccount.subAccounts, accountId)) {
    const subAccount = await AccountModel.findOne({ _id: accountId }, { users: 1 });
    const role = subAccount?.users?.find((user) => user.loginId.equals(loginId))?.role;
    let updated;
    if (role !== 'owner') {
      updated = await AccountModel.findOneAndUpdate(
        { _id: accountId },
        { $pull: { users: { loginId } } },
        { new: true },
      );
    }
    return !!updated;
  }

  return false;
};

const addUser = async (_, { accountId, loginId }, { userId }) => {
  const agencyAccount = await AccountModel.findOne({ databaseId: userId });

  if (agencyAccount && arrayContainsObjectId(agencyAccount.subAccounts, accountId)) {
    const login = await LoginModel.findOne({ _id: loginId });
    if (login) {
      const updated = await AccountModel.findOneAndUpdate(
        { _id: accountId },
        { $push: { users: { loginId, role: 'write' } } },
        { new: true },
      );
      return updated ? { login, role: 'write' } : {};
    }
  }

  return {};
};

const updateSettings = async (_, { input }, { userId, log }) => {
  const { codeInserted, spamProtection } = input;
  const updateSettings = {};
  if (codeInserted !== undefined) {
    updateSettings['settings.codeInserted'] = codeInserted;
  }

  if (spamProtection !== undefined) {
    updateSettings['settings.spamProtection'] = spamProtection;
  }

  try {
    await AccountModel.updateOne({ databaseId: userId }, { $set: updateSettings });

    if (codeInserted) {
      EventDispatcher.emit(EventDispatcher.events.JS_CODE_INSERT, { databaseId: userId });
    }

    return true;
  } catch (e) {
    log.error('Error during account setting update', input);
    return false;
  }
};

const updateWhiteLabelSettings = async (_, { input }, { userId, log }) => {
  const { customLogo, customSquared, brandName } = input;
  const result = await AccountModel.updateOne(
    { databaseId: userId },
    {
      $set: {
        'settings.whiteLabel.customLogo': customLogo,
        'settings.whiteLabel.customSquared': customSquared,
        'settings.whiteLabel.brandName': brandName,
      },
    },
  );

  const agencyAccount = await AccountModel.findOne({ databaseId: userId, type: 'agency' });
  if (agencyAccount) {
    try {
      const subAccounts = await AccountModel.aggregate([
        { $match: { _id: { $in: agencyAccount.subAccounts }, type: 'sub' } },
        {
          $project: {
            databaseId: 1,
          },
        },
      ]);
      await Promise.all(subAccounts.map(({ databaseId }) => queueAccountInfoUpload(databaseId)));
    } catch (error) {
      log.error({ message: 'Error during upload account info', error, userId });
      return false;
    }
  }
  return result.ok === 1;
};

const getAgencyWhiteLabelSettings = async (_, __, { userId }) => {
  const account = await AccountModel.findOne(
    { databaseId: userId },
    { _id: 1, type: 1, 'settings.whiteLabel': 1 },
  );
  let accountWhiteLabelSettings = {};
  if (account.type === 'sub') {
    const agencyAccount = await AccountModel.findOne(
      { subAccounts: account._id },
      { 'settings.whiteLabel': 1 },
    );

    if (agencyAccount) {
      accountWhiteLabelSettings = agencyAccount.settings.whiteLabel || {};
    }
  } else if (account.type === 'agency') {
    accountWhiteLabelSettings = account.settings.whiteLabel || {};
  }

  return merge({}, _defaultWhiteLabelling, accountWhiteLabelSettings);
};

const getAgencyWhiteLabelSettingsForDomain = async (_, __, { req, log }) => {
  const host = req.get('x-forwarded-host') || req.get('host');
  log.info('@@@@@White-label host', host);
  const agency = await AccountModel.findOne(
    {
      type: 'agency',
      'settings.whiteLabel.domainBase': host || '',
    },
    { _id: 1, 'settings.whiteLabel': 1 },
  );
  if (agency) {
    return agency.settings.whiteLabel || null;
  }

  return null;
};

const sendInsertCode = async (_, { input }, { userId, loginId }) => {
  const recentlySentKey = `insertCodeSentRecent:${userId}`;
  const dailyLimitKey = `insertCodeSentDaily:${userId}`;
  const [recentSent, dailyLimit] = await Promise.all([
    redisClient.get(recentlySentKey),
    redisClient.get(dailyLimitKey),
  ]);

  if (recentSent) {
    return { success: false, message: 'recentlySent' };
  }

  if (dailyLimit >= 10) {
    return { success: false, message: 'dailyLimit' };
  }

  const { email, name, note } = input;
  const login = await LoginModel.findOne({ _id: loginId });
  Translator.setLocale(login.locale);

  const message = Templating.render('mail/send-insert-code', { login, name, note, userId });
  const text = Templating.render('mail/send-insert-code.text', { login, name, note, userId });

  const mailConf = {
    from: Mailer.getFromByLogin(login),
    to: email,
    subject: Translator.translate('send-insert-code.mail.subject'),
    text,
    html: message,
  };

  try {
    await Mailer.sendMail(mailConf);

    redisClient.setex(recentlySentKey, 1, 10);
    if (!dailyLimit) {
      const now = new Date();
      const tomorrow = new Date().setDate(now.getDate() + 1);
      const diff = tomorrow - now;
      redisClient.setex(dailyLimitKey, 0, Math.round(diff / 1000));
    }
    redisClient.incr(dailyLimitKey);

    return { success: true };
  } catch (e) {
    return { success: false };
  }
};

const sendCredentials = async (_, { input }, { userId, loginId }) => {
  const recentlySentKey = `insertCodeHelpUsRecent:${userId}`;
  const dailyLimitKey = `insertCodeHelpUsDaily:${userId}`;
  const [recentSent, dailyLimit] = await Promise.all([
    redisClient.get(recentlySentKey),
    redisClient.get(dailyLimitKey),
  ]);

  if (recentSent) {
    return { success: false, message: 'recentlySent' };
  }

  if (dailyLimit >= 10) {
    return { success: false, message: 'dailyLimit' };
  }

  const { url, username, password } = input;
  const login = await LoginModel.findOne({ _id: loginId });
  Translator.setLocale(login.locale);

  const message = Templating.render('mail/send-credentials', {
    login,
    userId,
    url,
    username,
    password,
  });
  const text = Templating.render('mail/send-credentials.text', {
    login,
    userId,
    url,
    username,
    password,
  });

  const mailConf = {
    from: 'OptiMonk <<EMAIL>>',
    to: Mailer.getFromByLogin(login),
    subject: Translator.translate('send-credentials.mail.subject'),
    text,
    html: message,
  };

  try {
    await Mailer.sendMail(mailConf);

    redisClient.setex(recentlySentKey, 1, 10);
    if (!dailyLimit) {
      const now = new Date();
      const tomorrow = new Date().setDate(now.getDate() + 1);
      const diff = tomorrow - now;
      redisClient.setex(dailyLimitKey, 0, Math.round(diff / 1000));
    }
    redisClient.incr(dailyLimitKey);

    return { success: true };
  } catch (e) {
    return { success: false };
  }
};

const addIntegration = async (_, { input }, { userId, log }) => {
  const { type, data } = input;

  if (type === 'actOn') {
    const { expiresIn, lastTokenRequest, accessToken, refreshToken } =
      await ActOnAdapter.getTokenByCredentials({
        username: data.username,
        password: data.password,
      });

    data.username = undefined;
    data.password = undefined;
    data.expiresIn = expiresIn;
    data.lastTokenRequest = lastTokenRequest;
    data.accessToken = accessToken;
    data.refreshToken = refreshToken;
  }

  const canConnect = await tryToConnectToIntegration(type, data, log);
  if (!canConnect) return { success: false };

  const integrationId = ObjectId();
  const account = await AccountModel.findOneAndUpdate(
    { databaseId: userId },
    { $push: { 'settings.integrations': { _id: integrationId, type, data } } },
    { new: true },
  );

  if (!account) {
    return { success: false };
  }

  return { success: true, id: integrationId };
};

const addOAuthIntegration = async (_, { type, name, params }, { userId, log }) => {
  const oAuthParams = getConfigForIntegration(type)?.oAuthParams;
  log.info('oAuthParams: %o', oAuthParams);
  const oAuthParamsWithValues = {};

  for (const key in params) {
    if (oAuthParams.includes(key)) {
      oAuthParamsWithValues[key] = params[key];
    }
  }

  log.info('oAuthParamsWithValues: %o', oAuthParamsWithValues);

  if (integrationAdapterExists(type)) {
    const IntegrationAdapter = require(`../../services/integrations/${type}`);

    try {
      const data = await IntegrationAdapter.getAuthData(oAuthParamsWithValues);

      const integrationId = ObjectId();
      await AccountModel.findOneAndUpdate(
        { databaseId: userId },
        {
          $push: { 'settings.integrations': { _id: integrationId, type, data: { name, ...data } } },
        },
        { new: true },
      );

      return { success: true, id: integrationId };
    } catch (error) {
      log.error(error, 'error while adding oauth integration', {
        status: error.respone.status,
        body: error.response.data,
      });
      return { success: false };
    }
  }
};

const editIntegration = async (_, { integrationId, input }, { userId, log }) => {
  const { type, data } = input;

  const canConnect = await tryToConnectToIntegration(type, data, log);

  if (!canConnect) return { success: false };

  const account = await AccountModel.findOneAndUpdate(
    { databaseId: userId, 'settings.integrations._id': ObjectId(integrationId) },
    { $set: { [`settings.integrations.$`]: { _id: integrationId, type, data } } },
    { new: true },
  );

  if (!account) return { success: false };

  return { success: true };
};

const getDomainUsageCount = async (_, { domainId }, { userId }) => {
  const CampaignModel = campaignModel(userId);
  return CampaignModel.count({ domainId: ObjectId(domainId) });
};

const removeDomain = async (_, { domainId }, { userId }) => {
  const CampaignModel = campaignModel(userId);

  const [accountUpdate, campaignUpdate] = await Promise.all([
    AccountModel.findOneAndUpdate(
      { databaseId: userId },
      { $pull: { 'settings.domains': { _id: domainId } } },
    ),
    CampaignModel.updateMany(
      { domainId: ObjectId(domainId), status: { $nin: ['deleted', 'archived'] } },
      { domainId: null, domain: '', status: 'inactive' },
    ),
  ]);

  addCacheLoadJob({ accountId: userId, type: 'all-domains-for-account' });

  return !!accountUpdate && !!campaignUpdate;
};

const isDomainExists = async (userId, domain) => {
  const exactCount = await AccountModel.count({
    databaseId: userId,
    'settings.domains.domain': domain,
  });

  if (domain.includes('www.')) {
    const replacedCount = await AccountModel.count({
      databaseId: userId,
      'settings.domains.domain': domain.replace('www.', ''),
    });
    return exactCount || replacedCount;
  }

  return exactCount;
};

const addDomain = async (_, { domain, scrape = true }, { userId, loginId, log }) => {
  const {
    limits: { domains: domainLimit },
    settings: { domains },
    type: accountType,
  } = await AccountModel.findOne(
    { databaseId: userId },
    {
      'limits.domains': 1,
      'settings.domains': 1,
      type: 1,
    },
  );

  const cleanDomain = purifyDomain(removeProtocolFromDomain(domain));

  const domainExists = await isDomainExists(userId, cleanDomain);

  if (domainExists) {
    return { _id: null, message: 'domainAlreadyExists' };
  }

  const domainId = ObjectId();
  const activeDomains = domains.filter((domain) => !domain.inactive);
  let active = true;
  if (accountType !== 'sub' && activeDomains.length >= domainLimit) {
    active = false;
  }

  await AccountModel.updateOne(
    { databaseId: userId },
    {
      $push: {
        'settings.domains': {
          _id: domainId,
          domain: cleanDomain,
          lastRequestDate: null,
          v3LastRequestDate: null,
          inactive: !active,
        },
      },
    },
  );

  const login = await LoginModel.findOne({ _id: ObjectId(loginId) });

  updateDomainInfo(userId, cleanDomain, null, login);

  addCacheLoadJob({ accountId: userId, type: 'all-domains-for-account' });

  if (scrape) {
    queueSimilarwebJob({ email: login.email, userId });
  }

  return { _id: domainId, domain: cleanDomain };

  // return {_id: null, domain: '', message: 'domainLimitReached'}
};

const codeInsertedInAllDomains = async (_, __, { userId }) => {
  const {
    settings: { domains },
  } = await AccountModel.findOne({ databaseId: userId }, { _id: 0, 'settings.domains': 1 });

  return (
    domains.length &&
    domains.every(
      (d) =>
        d.v3LastRequestDate &&
        moment.duration(moment().diff(moment(d.v3LastRequestDate))).asHours() < 24,
    )
  );
};

const removeIntegration = async (_, { integrationId }, { userId }) => {
  const CampaignModel = campaignModel(userId);
  const campaignsWhichUsesIntegration = await CampaignModel.count({
    'settings.integrations.id': ObjectId(integrationId),
  });

  let campaignRemove = true;
  if (campaignsWhichUsesIntegration) {
    campaignRemove = await CampaignModel.updateMany(
      { 'settings.integrations.id': ObjectId(integrationId) },
      { $pull: { 'settings.integrations': { id: ObjectId(integrationId) } } },
    );
  }

  const accountRemove = await AccountModel.findOneAndUpdate(
    { databaseId: userId },
    { $pull: { 'settings.integrations': { _id: ObjectId(integrationId) } } },
  );

  return !!campaignRemove && !!accountRemove;
};

const changeIntegrationName = async (_, { integrationId, name }, { userId }) => {
  const res = await AccountModel.findOneAndUpdate(
    { databaseId: userId, 'settings.integrations._id': integrationId },
    { 'settings.integrations.$.data.name': name },
  );

  return !!res;
};

const resendInvitation = async (_, { id }) => {
  const limit = await invitationLimit(id);

  if (!limit.success) {
    return limit;
  }

  const account = await AccountModel.findOne(
    { 'invitations._id': ObjectId(id) },
    { invitations: 1, 'users.loginId': 1, name: 1 },
  );

  if (!account) {
    return false;
  }

  const { email } = account.invitations.find((el) => String(el._id) === id);

  const senderLogin = await LoginModel.findOne({ _id: account.users[0].loginId });
  const invitation = createInvitation(email);

  sendInvitationEmail(account.name, senderLogin, invitation);

  const updated = await AccountModel.update(
    { 'invitations._id': ObjectId(id) },
    { 'invitations.$': invitation },
  );

  return !!updated;
};

const createInvitation = (email) => {
  const expiration = new Date();
  const expirationTime = 2; // 2 days
  expiration.setDate(expiration.getDate() + expirationTime);

  // Not unique: multiple pending invitation for same agency sub accounts?
  // Or multiple invite accept with 1 invitation link?
  const invitation = {
    email,
    expiration,
    hash: crypto
      .createHash('sha512')
      .update(email + expiration)
      .digest('hex'),
  };

  return invitation;
};

const sendInvitationEmail = async (
  subAccountName,
  inviterLogin,
  invitation,
  { req, whiteLabelSettings } = {},
) => {
  const invitationLogger = logger.child({ service: 'invitation-email' });
  Translator.setLocale(inviterLogin.locale);

  const hasUserAlreadyHaveAccount = await LoginModel.count({ email: invitation.email });

  const adminBaseUrl =
    !!whiteLabelSettings && whiteLabelSettings.domainBase
      ? `${req.get('x-forwarded-proto') || 'http'}://${whiteLabelSettings.domainBase}`
      : omAdminUrl;
  const url = `${adminBaseUrl}/invitation/${inviterLogin.locale}/${
    invitation.hash + (hasUserAlreadyHaveAccount ? '?confirm=1' : '')
  }`;
  const localeTag = inviterLogin.locale === 'hu' ? 'hu' : 'en';
  const message = Templating.render(`mail/invitation-${localeTag}`, {
    login: inviterLogin,
    subAccountName,
    url,
    isWhitelabel: !!whiteLabelSettings,
    logoUrl: whiteLabelSettings && whiteLabelSettings.customLogo,
    brandName: whiteLabelSettings && whiteLabelSettings.brandName,
  });

  const from = whiteLabelSettings
    ? Mailer.getWhitelabelFrom(whiteLabelSettings.brandName)
    : Mailer.getFromByLogin(inviterLogin);
  const mailConf = {
    from,
    to: invitation.email,
    subject: Translator.translate('invitation.mail.subject', {
      firstName: inviterLogin.firstName,
      brandName: whiteLabelSettings ? whiteLabelSettings.brandName : 'OptiMonk',
    }),
    text: striptags(striptags(message, '</b>', '\n')),
    html: message,
  };

  // eslint-disable-next-line

  try {
    await Mailer.sendMail(mailConf);
    return { message: 'Invitation email sent!' };
  } catch (e) {
    invitationLogger.error({
      message: 'Error during invitation email sending!',
      errorMessage: e.message,
      subAccountName,
      invited: invitation.email,
      inviter: inviterLogin.email,
    });
    return { message: 'Error during invitation email sending!' };
  }
};

const arrayContainsObjectId = (array, id) => {
  const index = array.map((s) => s.toString()).indexOf(id.toString());

  if (index !== -1) return true;
  return false;
};

const integrationAdapterExists = (type) => {
  let integrations = fs.readdirSync('./services/integrations');
  integrations = integrations.map((i) => i.split('.')[0]);

  return integrations.includes(type);
};

const tryToConnectToIntegration = async (type, data, log) => {
  if (integrationAdapterExists(type)) {
    const authData = {};
    for (const f of getConfigForIntegration(type)?.authFields) {
      authData[f] = data[f];
    }

    const IntegrationAdapter = require(`../../services/integrations/${type}`);
    const integrationAdapter = new IntegrationAdapter(authData);

    try {
      const canConnect = await integrationAdapter.ping();
      if (canConnect) return true;
    } catch (error) {
      log.error({
        message: 'Error while trying to connect integration',
        integrationType: type,
        errorMessage: error.message,
        data,
      });
      return false;
    }
  } else {
    log.warn('No adapter found', type);
  }

  return false;
};

const getGlobalIntegrations = async (_, __, { userId }) => {
  const {
    settings: { integrations },
  } = await AccountModel.findOne({ databaseId: userId }, { 'settings.integrations': 1 }).lean();

  return integrations;
};

const getDomains = async (_, __, { userId }) => {
  const {
    settings: { domains },
  } = await AccountModel.findOne({ databaseId: userId }, { 'settings.domains': 1 });

  return domains.sort((d1, d2) => (d1.domain > d2.domain) - (d1.domain < d2.domain));
};

const getDomainsWithSiteData = async (_, __, { userId }) => {
  const result = await AccountModel.aggregate([
    { $match: { databaseId: userId } },
    {
      $lookup: {
        from: 'user_site_extractions',
        foreignField: 'domain',
        localField: 'settings.domains.domain',
        as: 'siteExtractions',
      },
    },
    {
      $lookup: {
        from: 'user_auto_theme',
        foreignField: 'domain',
        localField: 'settings.domains.domain',
        as: 'autoThemes',
      },
    },
    {
      $project: {
        'settings.domains': 1,
        siteExtractions: 1,
        autoThemes: 1,
      },
    },
  ]);

  const domains = result[0].settings.domains.sort(
    (d1, d2) => (d1.domain > d2.domain) - (d1.domain < d2.domain),
  );

  const { siteExtractions, autoThemes } = result[0];

  const siteData = {};

  for (const { domain, _id } of domains) {
    const siteExtraction = siteExtractions.find((item) => item.domain === domain);
    const autoTheme = autoThemes.find((item) => item.domain === domain);

    if (!siteExtraction && !autoTheme) continue;

    siteData[_id] = {
      language: siteExtraction?.language,
      logo: siteExtraction?.logo,
      siteTheme: autoTheme?.siteTheme,
    };
  }

  return {
    domains,
    siteData,
  };
};

const getDetailedDomainsWithPagination = async (_, { pagination, filter }, { userId }) => {
  const {
    settings: { domains: accountDomains, shops },
  } = await AccountModel.findOne(
    { databaseId: userId },
    { 'settings.domains': 1, 'settings.shops': 1 },
  );
  const allDomains = filter
    ? accountDomains.filter((d) => d.domain.match(`.*${filter}.*`))
    : accountDomains;
  const count = allDomains.length;
  let domains = allDomains.sort((d1, d2) => (d1.domain > d2.domain) - (d1.domain < d2.domain));

  if (pagination) {
    domains = domains.slice(
      (pagination.page - 1) * pagination.limit,
      pagination.page * pagination.limit,
    );
  }

  const domainIds = domains.map((domain) => domain._id);
  const campaigns = await campaignModel(userId).find({
    domainId: { $in: domainIds },
    status: 'active',
  });

  return {
    domains: domains.map((domain) => {
      const activeCampaigns = campaigns
        .filter((campaign) => campaign.domainId.equals(domain._id))
        .map((campaign) => ({
          ...campaign.toObject(),
          conversionRate:
            campaign.impressions > 0 ? (campaign.conversions / campaign.impressions).toFixed(2) : 0,
        }));

      return {
        ...domain.toObject(),
        isShopifyAppExtensionActive: !!shops.find(
          (shop) =>
            shop.myshopify_domain === domain.shopId ||
            shop.myshopify_domain === domain.domain ||
            (shop.live_domain && purifyDomain(shop.live_domain) === purifyDomain(domain.domain)),
        )?.isShopifyAppExtensionActive,
        activeCampaigns,
      };
    }),
    count,
  };
};

const getDomainsCount = async (_, __, { userId }) => {
  const {
    settings: { domains },
  } = await AccountModel.findOne({ databaseId: userId }, { 'settings.domains': 1 });
  return domains.length;
};

const renameDomain = async (_, { domainId, domain }, { userId, loginId }) => {
  const CampaignModel = campaignModel(userId);

  const cleanDomain = purifyDomain(removeProtocolFromDomain(domain));

  const login = await LoginModel.findOne({ _id: ObjectId(loginId) });
  updateDomainInfo(userId, cleanDomain, null, login);

  const [accountUpdate, campaignUpdate] = await Promise.all([
    AccountModel.updateOne(
      { databaseId: userId, 'settings.domains._id': ObjectId(domainId) },
      {
        $set: {
          'settings.domains.$.domain': cleanDomain,
          'settings.domains.$.lastRequestDate': null,
          'settings.domains.$.v3LastRequestDate': null,
        },
      },
    ),
    CampaignModel.updateMany({ domainId: ObjectId(domainId) }, { domain: cleanDomain }),
  ]);

  await redisClient.deleteDomainsCache(userId);

  return !!accountUpdate && !!campaignUpdate;
};

const hideWelcomeMessage = async (_, __, { userId }) => {
  const { modifiedCount } = await AccountModel.updateOne(
    { databaseId: userId },
    { 'settings.onboarding.showWelcomeMessage': false },
  );
  return modifiedCount !== 0;
};

const _upsertUserInHubSpotV3 = async (email, properties, { log }) => {
  try {
    hubSpotV3.createOrUpdateUser(email, properties, { log });
  } catch (e) {
    log?.error?.('[HUBSPOT V3] Error while sending qualifications to HubSpot.', e);
  }
};

const sendUserQualifications = async (
  _,
  { business, otherBusiness, stage, whyUs, onboardingVersion },
  { userId, loginId, log },
) => {
  const login = await LoginModel.findOne({ _id: loginId });

  await _upsertUserInHubSpotV3(
    login.email,
    {
      business_desc: business,
      other_business_desc: otherBusiness,
      business_stage: stage,
      whyus: whyUs,
      onboarding_version: onboardingVersion,
      phone: login.phoneNumber,
    },
    { log },
  );

  await AccountModel.updateOne(
    { databaseId: userId },
    {
      'settings.userQualificationFilled': true,
      'settings.qualifications': {
        business,
        otherBusiness,
        stage,
        whyUs,
      },
    },
  );
  HeapAnalytics.trackQualification(userId, {
    business,
    otherBusiness,
    stage,
    whyUs,
    onboarding_version: onboardingVersion,
  });
  try {
    intercom.createOrUpdateUser({
      email: login.email,
      custom_attributes: {
        business_desc: business,
        other_business_desc: otherBusiness,
        business_stage: stage,
        whyUs,
        onboarding_version: onboardingVersion,
      },
    });
  } catch (e) {
    log.error('Error while sending qualifications to Intercom.', e);
  }

  try {
    hubSpot.createOrUpdateUser(login.email, {
      business_desc: business,
      other_business_desc: otherBusiness,
      business_stage: stage,
      whyus: whyUs,
      onboarding_version: onboardingVersion,
    });
  } catch (e) {
    log.error('Error while sending qualifications to HubSpot.', e);
  }

  if (business === 'agency') {
    sendNewAgencyRegistration({
      businessType: otherBusiness,
      email: login.email,
      firstName: login.firstName,
      lastName: login.lastName,
    });
  }
  return true;
};

const sendAdditionalOnboardingInfo = async (
  _,
  { phoneNumber, agencyIndustry, clientsNumber },
  { userId, loginId, log },
) => {
  const [
    login,
    {
      settings: { domains },
    },
  ] = await Promise.all([
    LoginModel.findOne({ _id: loginId }),
    AccountModel.findOne({ databaseId: userId }, { 'settings.domains': 1 }),
  ]);

  const attributes = {
    phone: phoneNumber,
    agency_industry: agencyIndustry,
    clients_number: clientsNumber,
  };

  const toInsert = {};

  Object.keys(attributes).forEach((key) => {
    if (!attributes[key]) {
      delete attributes[key];
    } else {
      toInsert[`settings.onboarding.additional.${key}`] = attributes[key];
    }
  });

  await AccountModel.updateOne(
    { databaseId: userId },
    {
      'settings.userQualificationFilled': true,
      ...toInsert,
    },
  );

  const intercomData = {
    email: login.email,
    custom_attributes: {},
  };

  if (phoneNumber) {
    intercomData.phone = phoneNumber;
  }

  // we get the domain in the registration
  if (domains && domains.length) {
    intercomData.custom_attributes.domains = domains[0].domain;
  }

  if (agencyIndustry) {
    intercomData.custom_attributes.agency_industry = agencyIndustry;
  }

  if (clientsNumber) {
    intercomData.custom_attributes.clients_number = clientsNumber;
  }

  try {
    intercom.createOrUpdateUser(intercomData);
  } catch (e) {
    log.error('Error while sending additional onboarding info to Intercom.', e);
  }

  try {
    hubSpot.createOrUpdateUser(login.email, attributes);
  } catch (e) {
    log.error('Error while sending addition onboarding info to HubSpot.', e);
  }
};

const sendMasterContactUs = async (_, { input }, { userId }) => {
  const { firstName, lastName, email, phoneNumber, websiteUrl, monthlyPageViews } = input;

  const mailConf = {
    from: 'OptiMonk <<EMAIL>>',
    to: '<EMAIL>',
    subject: 'Master plan request',
    text: `Account ID: ${userId}\nFirstname: ${firstName}\nLastname: ${lastName}\nEmail: ${email}\nPhone: ${phoneNumber}\nWebsite: ${websiteUrl}\nMonthly Pageviews: ${monthlyPageViews}`,
    html: `Account ID: ${userId}<br><br>Firstname: ${firstName}<br><br>Lastname: ${lastName}<br><br>Email: ${email}<br><br>Phone: ${phoneNumber}<br><br>Website: ${websiteUrl}<br><br>Monthly Pageviews: ${monthlyPageViews}`,
  };

  try {
    await Mailer.sendMail(mailConf);
  } catch (e) {
    logger.error({
      message: 'Error in Master Contact Us!',
      errorMessage: e.message,
      databaseId: userId,
    });
    return false;
  }

  return true;
};

const getFonts = async (_, __, { userId: databaseId }) => {
  return AccountFonts.getFonts(databaseId);
};

const updateFonts = async (_, { key, subsets, weights }, { userId: databaseId }) => {
  const account = await AccountModel.findOne({ databaseId }, { 'settings.fonts': 1 });
  const installed = _get(account, 'settings.fonts', []);
  const existingIndex = installed.findIndex((font) => font.key === key);

  if (existingIndex > -1) {
    installed[existingIndex].subsets = subsets;
    installed[existingIndex].weights = weights;
  } else {
    installed.push({ key, subsets, weights });
  }

  const result = await AccountModel.findOneAndUpdate(
    { databaseId },
    {
      $set: {
        'settings.fonts': installed,
      },
    },
  );

  return !!result;
};

const deleteFont = async (_, { key, type }, { userId: databaseId }) => {
  const settingKey = type === 'custom' ? 'settings.customFonts' : 'settings.fonts';

  const account = await AccountModel.findOne({ databaseId }, { [settingKey]: 1 });
  const installed = _get(account, settingKey, []);
  const existingIndex = installed.findIndex((font) => font.key === key);
  let fontName = '';

  if (existingIndex > -1) {
    fontName = installed[existingIndex].name;
    installed.splice(existingIndex, 1);
  }

  const result = await AccountModel.findOneAndUpdate(
    { databaseId },
    {
      $set: {
        [settingKey]: installed,
      },
    },
  );

  if (type === 'custom') {
    const extensions = ['eot', 'svg', 'ttf', 'woff', 'woff2', 'css'];
    const fileNames = extensions.map((e) => `${fontName}.${e}`);
    await deleteObjects({
      names: fileNames,
      saveType: 'customFont',
      saveValue: fontName,
      userId: databaseId,
    });
  }

  return !!result;
};

const getDomain = async (_, { domainId }, { userId }) => {
  const {
    settings: { domains },
  } = (await AccountModel.findOne(
    { databaseId: userId, 'settings.domains._id': ObjectId(domainId) },
    { 'settings.domains.$': 1 },
  )) || { settings: {} };
  return domains?.find((d) => d._id.toString() === domainId.toString());
};

const toggleAnalyticsStatus = async (_, { domainId, enabled }, { userId, log }) => {
  const { modifiedCount } = await AccountModel.updateOne(
    { databaseId: userId, 'settings.domains._id': domainId },
    { 'settings.domains.$.analytics.enabled': enabled },
  );
  try {
    await redisClient.deleteDomainsCache(userId);
    queueAccountInfoUpload(userId);
  } catch (error) {
    log.error({ message: 'cannot upload account info at analytics change', databaseId: userId });
  }
  return modifiedCount !== 0;
};

const toggleDomainActiveStatus = async (_, { domainId, active }, { userId: databaseId }) => {
  const {
    limits: { domains: domainLimit },
    settings: { domains },
  } = await AccountModel.findOne({ databaseId }, { 'limits.domains': 1, 'settings.domains': 1 });

  const activeDomains = domains.filter((domain) => !domain.inactive);
  if (active && activeDomains.length >= domainLimit) {
    return { success: false, message: 'activeDomainLimitReached' };
  }

  await AccountModel.updateOne(
    { databaseId, 'settings.domains._id': domainId },
    { 'settings.domains.$.inactive': !active },
  );
  if (!active) {
    await campaignModel(databaseId).updateMany(
      { domainId, status: { $ne: 'deleted' } },
      { $set: { status: 'inactive' } },
    );
  }

  return { success: true };
};

const sendCampaignRebuildTicket = async (
  _,
  { campaignId, variantId },
  { userId, loginId, log },
) => {
  const login = await LoginModel.findOne({ _id: loginId });

  const mailConf = {
    from: 'OptiMonk <<EMAIL>>',
    to: '<EMAIL>',
    subject: 'Old Campaign Rebuild',
    text: `Account ID: ${userId}\nEmail: ${login.email}\nCampaign Id: ${campaignId}\nVariant Id: ${variantId}`,
    html: `Account ID: ${userId}<br><br>Email: ${login.email}<br><br>Campaign Id: ${campaignId}<br><br>Variant Id: ${variantId}`,
  };

  try {
    await Mailer.sendMail(mailConf);
    return true;
  } catch (e) {
    log.error('Error while sending old campaign redo ticket', e);
    return false;
  }
};

const finishOnboarding = async (_, __, { userId }) => {
  const { modifiedCount } = await AccountModel.updateOne(
    { databaseId: userId },
    {
      'settings.onboarding.finished': true,
      'settings.onboarding.stage': null,
      'settings.userQualificationFilled': true,
    },
  );
  return modifiedCount !== 0;
};

const saveOnboardingStage = async (_, { stage }, { userId }) => {
  const { modifiedCount } = await AccountModel.updateOne(
    { databaseId: userId },
    { 'settings.onboarding.stage': stage },
  );
  return modifiedCount !== 0;
};

const saveOnboardingRole = async (_, { role }, { userId }) => {
  const account = await AccountModel.findOneAndUpdate(
    { databaseId: userId },
    { 'settings.onboarding.role': role },
    { projection: { name: 1 } },
  );

  Intercom.createOrUpdateUser({
    email: account.name,
    custom_attributes: {
      role,
    },
  });
};

const saveOnboardingWhoBuildPopup = async (_, { whoBuildPopup }, { userId, log }) => {
  const account = await AccountModel.findOneAndUpdate(
    { databaseId: userId },
    { 'settings.onboarding.whoBuildPopup': whoBuildPopup },
    { projection: { name: 1 } },
  );

  Intercom.createOrUpdateUser({
    email: account.name,
    custom_attributes: {
      who_build_popup: whoBuildPopup,
    },
  });
};

const setOnboardingProperty = async (_, { key, value }, { userId: databaseId, log }) => {
  const savePath = `settings.onboarding.${key}`;
  const update = { $set: { [savePath]: value } };
  try {
    await AccountModel.updateOne({ databaseId }, update);
  } catch (e) {
    log.error(e);
    return false;
  }
  return true;
};

const updateBusinessNameAndPhoneNumber = async (
  _,
  { businessName, phoneNumber },
  { userId, log },
) => {
  const account = await AccountModel.findOne({ databaseId: userId });
  const login = await LoginModel.findOne({ _id: account.users[0].loginId });

  if (!phoneNumber) {
    phoneNumber = login.phoneNumber;
  }

  await account.updateOne({ businessName, 'settings.onboarding.additional.phone': phoneNumber });
  await login.updateOne({ phoneNumber });

  if (login?.email && businessName) {
    await queueHubspotJob({ email: login?.email, businessName });
  }

  Intercom.createOrUpdateUser({
    email: account.name,
    custom_attributes: {
      business_name: businessName,
    },
    phone: phoneNumber,
  });

  if (phoneNumber) {
    try {
      await BackofficeApiClient.post(`/payment/customer/${userId}`, {
        telephone: phoneNumber,
      });
    } catch (e) {
      log.error('Error during update customer', e);
    }
  }
};

const trackPageview = async (_, { url }, { req, userId }) => {
  try {
    const parsedUrl = new URL(url || '');
    HeapAnalytics.trackPageview(userId, { path: `${parsedUrl.pathname}${parsedUrl.search}` });
    return true;
  } catch (e) {
    req.log.error(e);
    return false;
  }
};

const getShopUserHash = async (_, { type }, { req }) => {
  req.log.warn('SHOP USER HASH', type);

  if (type === 'shopify') {
    return ShopifyHelper.getShopUserHash(req.cookies.myshid);
  }
  if (type === 'shoprenter') {
    return ShopRenterHelper.getShopUserHash(req.cookies.mysrid);
  }

  return null;
};

const createAccountForShop = async (_, { type }, { req, res }) => {
  if (type === 'shopify') {
    return createAccountForShopifyShop(req, res);
  }
  if (type === 'shoprenter') {
    return {
      success: ShopRenterHelper.createAccountForShop(req, res),
    };
  }

  throw new Error(`unsupported shop type: ${type}`);
};

const downgradeSubscription = (
  _,
  {
    plan,
    period,
    activeDomainIds,
    hasFunctionalDowngrade,
    needShopifyApprove,
    skipDisableLostFeatures,
  },
  { userId },
) => {
  return setDowngradeSettings({
    databaseId: userId,
    plan,
    period,
    activeDomainIds,
    hasFunctionalDowngrade,
    needShopifyApprove,
    skipDisableLostFeatures,
  });
};

const getDowngradeLostFeatures = async (_, { toPlan, fromPlan = null }, { userId }) => {
  const account = await AccountModel.findOne({ databaseId: userId }, { billing: 1, profile: 1 });
  if (account) {
    let currentPlanName = fromPlan !== null ? fromPlan : account.billing.package;
    if (currentPlanName && currentPlanName.toLowerCase().includes('master')) {
      currentPlanName = 'MASTER';
    }
    const lostFeatures = getLostFeatures(currentPlanName, toPlan);
    const lostPageViews = getLostPageViews(currentPlanName, toPlan);
    const lostDomainsCount = getLostDomains(currentPlanName, toPlan);
    const currentPlan = getPlanDetails(currentPlanName);
    const newPlan = getPlanDetails(toPlan);

    if (!currentPlan || !newPlan) {
      throw new Error(`Could not find plan "${currentPlanName}" or "${toPlan}".`);
    }

    return {
      oldColor: currentPlan.color,
      newColor: newPlan.color,
      lostPageViews,
      lostDomainsCount,
      newPageViews: newPlan.details.pageViews || newPlan.details.visitors,
      newDomainsCount: newPlan.details.domains,
      lostFeatures,
    };
  }

  return null;
};

const sendMagicLinkEmail = async (_, { email, template }, { userId, loginId }) => {
  const [login, templateObj] = await Promise.all([
    LoginModel.findOne({ _id: loginId }).lean(),
    TemplateModel.findOne({ _id: ObjectId(template) }, { previewUrls: 1 }),
  ]);

  const token = await asyncSign(
    {
      loginId,
      userId,
    },
    process.env.magic_link_key,
    {
      expiresIn: '30d',
    },
  );

  let magicLink = `${process.env.om_backend_url}/login/magic?token=${token}&version=1`;
  let message;
  let text;
  let mailConf;

  Translator.setLocale(login.locale);

  const localeTag = login.locale === 'hu' ? 'hu' : 'en';

  if (template) {
    magicLink += `&template=${template}`;
    const trackingImage = `${process.env.om_backend_url}/images/monk-type.png?user=${userId}`;

    message = Templating.render('mail/send-magic-link/1', {
      magicLink,
      firstName: login.firstName,
      trackingImage,
      templatePreview: templateObj.previewUrls[0],
    });
    text = Templating.render('mail/send-magic-link/1.text', {
      magicLink,
      firstName: login.firstName,
    });

    mailConf = {
      from: `${Translator.translate('send-magic-link.1.from')} <<EMAIL>>`,
      to: email,
      subject: Translator.translate('send-magic-link.1.subject'),
      text,
      html: message,
    };
  } else {
    message = Templating.render(`mail/send-magic-link/5-${localeTag}`, {
      magicLink,
      firstName: login.firstName,
    });

    mailConf = {
      from: `${Translator.translate('send-magic-link.1.from')} <<EMAIL>>`,
      to: email,
      subject: Translator.translate('send-magic-link.5.subject'),
      html: message,
    };
  }

  try {
    await Mailer.sendMail(mailConf);
    await LoginModel.updateOne(
      {
        _id: loginId,
      },
      {
        $push: {
          magicLinks: {
            type: 'onboarding',
            email,
            requestedAt: new Date(),
            token,
          },
        },
      },
    );

    return true;
  } catch (e) {
    return false;
  }
};

const migrateAffiliateSlug = async (userId) => {
  const path = 'settings.affiliate.partnerInfo.slug';
  try {
    const projectedAccount = await AccountModel.findOne({ databaseId: userId }, { [path]: 1 });
    const currentSlug = _get(projectedAccount, path, null);

    const { slug } = await getPartnerInfo({ firstName: currentSlug, lastName: '' }, AccountModel);
    await AccountModel.updateOne({ databaseId: userId }, { $set: { [path]: slug } });

    await redisClient.del(`partner_collection:${currentSlug}`);
  } catch (e) {
    return false;
  }
  return true;
};

const updateAffiliatePartnerInfo = async (
  _,
  {
    data: { displayName, slug, headline, buttonText, buttonUrl, about, image, migrateSlug = false },
  },
  { userId, log },
) => {
  try {
    if (migrateSlug) {
      const result = await migrateAffiliateSlug(userId);
      return { success: result, message: '' };
    }

    const slugAlreadyExists = await AccountModel.countDocuments({
      databaseId: { $ne: userId },
      'settings.affiliate.partnerInfo.slug': slug,
    });

    if (slugAlreadyExists) {
      return {
        success: false,
        message: 'slugAlreadyExists',
      };
    }

    await AccountModel.updateOne(
      { databaseId: userId },
      {
        'settings.affiliate.partnerInfo': {
          displayName,
          slug,
          headline,
          buttonText,
          buttonUrl,
          about,
          image,
        },
      },
    );

    await redisClient.del(`partner_collection:${slug}`);

    return {
      success: true,
      message: '',
    };
  } catch (e) {
    log.error(`updateAffiliatePartnerInfo - error, ${e}`);
  }
};

const getAgencyManagedAccounts = async (_, { filter }, { userId, log }) => {
  try {
    const account = await AccountModel.findOne({ databaseId: userId });

    const partnerTypeService = new PartnerTypeService({
      accountType: account?.type,
      partnerType: account?.partnerType,
    });

    const isAgency = partnerTypeService.isAgencyPartnerType() || partnerTypeService.isCustom();

    if (!isAgency) {
      log.error('getAgencyManagedAccounts ERROR not agency account');
      return [];
    }

    const managedAccountIds = await salesMysql.getAffiliateCustomers(userId);

    const accounts = await AccountModel.find({
      databaseId: { $in: managedAccountIds },
      'settings.affiliate.canBeManagedByAgency': true,
      ...(filter?.search ? { name: new RegExp(filter.search.replace(/\+/, '\\$&'), 'i') } : {}),
    });
    const loginIds = accounts.map((a) => a.users.find((u) => u.role === 'owner').loginId);
    const logins = await LoginModel.find({ _id: { $in: loginIds } });

    const managedAccounts = [];

    for (const account of accounts) {
      const ownerLogin = account.users.find((uu) => uu.role === 'owner');
      const login = logins.find((l) => l._id.toString() === ownerLogin.loginId.toString());

      managedAccounts.push({
        account,
        login,
      });
    }

    return managedAccounts;
  } catch (e) {
    log.error(`getAgencyManagedAccounts ERROR ${e}`);
  }
};

const isAgencyAffiliate = async (_, { partnerId }) => {
  const customerId = getCustomerIdFromPartnerId(partnerId);
  const databaseId = await salesMysql.getDatabaseIdByCustomerId(customerId);

  if (databaseId) {
    const account = await AccountModel.findOne({ databaseId });

    const partnerTypeService = new PartnerTypeService({
      accountType: account?.type,
      partnerType: account?.partnerType,
    });

    const isAgency = partnerTypeService.isAgencyPartnerType() || partnerTypeService.isCustom();

    if (isAgency && isAffiliate(account)) {
      return {
        isAgencyAffiliate: true,
        name: _get(account, 'settings.whiteLabel.brandName', account.name),
      };
    }
  }

  return {
    isAgencyAffiliate: false,
  };
};

const getAffiliateSubscribers = async (_, __, { userId, log }) => {
  let subscribers = [];
  try {
    const response = await BackofficeApiClient.get(`/accounts/${userId}/affiliate-subscribers`);

    if (response?.data?.success) {
      subscribers = response.data.subscribers;
    }
  } catch (e) {
    log.warn({
      message: 'Affiliate subscription request failed',
      error: e.message,
      databaseId: userId,
    });
  }

  return subscribers;
};

const getAffiliateData = async (_, __, { userId, log }) => {
  let data = [];
  try {
    const response = await BackofficeApiClient.get(`/accounts/${userId}/affiliate-data`);

    log.info(response.data);
    if (response?.data?.success) {
      data = response.data.data;
    }
  } catch (e) {
    log.warn({
      message: 'Affiliate all data request failed',
      error: e.message,
      databaseId: userId,
    });
  }

  return data;
};

const getAffiliatePayoutItems = async (_, __, { userId, log }) => {
  let data = [];
  try {
    const response = await BackofficeApiClient.get(`/accounts/${userId}/affiliate-payout-items`);

    if (response?.data?.success) {
      data = response.data.data;
    }
  } catch (e) {
    log.warn({
      message: 'Affiliate payout items request failed',
      error: e.message,
      databaseId: userId,
    });
  }

  return data;
};

const affiliatePayoutRequest = async (_, __, { userId, log }) => {
  if (!userId) {
    return { success: false };
  }

  try {
    const { data } = await BackofficeApiClient.post(`/accounts/${userId}/affiliate-payout-request`);

    if (!data.success) {
      log.warn('Unsuccessful affiliatePayoutRequest', data);
      return { success: false };
    }

    return { success: true };
  } catch (e) {
    log.error('Error in affiliatePayoutRequest', { error: e.message, userId });
  }
};

const generateApiKey = async (_, __, { userId }) => {
  const { apiKey } = uuidAPIKey.create({ noDashes: true });
  await AccountModel.updateOne({ databaseId: userId }, { $set: { 'settings.apiKeys': [apiKey] } });
  return apiKey;
};

const deleteApiKey = async (_, __, { userId }) => {
  await AccountModel.updateOne({ databaseId: userId }, { $set: { 'settings.apiKeys': [] } });
  return '';
};

const getApiKey = async (_, __, { userId }) => {
  const {
    settings: { apiKeys },
  } = await AccountModel.findOne({ databaseId: userId }, { _id: 0, 'settings.apiKeys': 1 });
  return apiKeys[0] || '';
};

const storeProductTourStatus = async (_, { tour, status }, { userId }) => {
  await AccountModel.updateOne(
    { databaseId: userId },
    { $set: { [`profile.productTours.${tour}`]: status } },
  );
  return { success: true };
};

const saveAiFeatureRequest = async (_, { feature }, { userId: databaseId, log }) => {
  const key = `aiRequest_${feature}`;
  const account = await AccountModel.findOne({ databaseId }, { [`profile.${key}`]: 1 }).lean();
  if (!_get(account.profile, key)) {
    await setUserProfileKey({ databaseId, key, value: new Date(), log });
    await sendAiRequestsOfTheUser(databaseId);
  }
  return { success: true };
};

const saveVIPOnboardingRequest = async (_, __, { userId: databaseId, log }) => {
  await setUserProfileKey({ databaseId, key: 'vipOnboarding', value: true, log });
  await sendVIPOnboarding(databaseId);
  return true;
};

const storeInfoBoxVisibility = async (_, { box, hide }, { userId }) => {
  await AccountModel.updateOne(
    { databaseId: userId },
    { $set: { [`profile.infoBox.${box}`]: hide } },
  );
  return { success: true };
};

const USER_PROFILE_WHITELIST_KEYS = [
  'personalizedExperiences',
  'firstExperience',
  'onboardingSectionShown',
  'onboardingSectionDone',
  'onboardingSectionActivated',
  'aiOnboardingSectionDone',
  'interestedFeature',
  'betterEmailCheck',
  'wizardLastPage202404',
  'wizardAnimationShown',
];

const setProfileKey = async (_, { key, value }, { userId: databaseId, log }) => {
  if (!key || !USER_PROFILE_WHITELIST_KEYS.includes(key)) return false;

  if (key === 'firstExperience') value = new Date(value);

  return setUserProfileKey({ databaseId, key, value: value || true, log });
};

const EXP_CAMPAIGN_AB_TEST_SETTING_KEY = 'CampaignsAbTest';
const EXP_CAMPAIGN_AB_TEST_KEY = `settings.experiments.${EXP_CAMPAIGN_AB_TEST_SETTING_KEY}.testCases`;
const EXP_GLOBAL_FREQUENCY_CAP_SETTING_KEY = 'GlobalFrequencyCap';
const EXP_GLOBAL_FREQUENCY_CAP_KEY = `settings.experiments.${EXP_GLOBAL_FREQUENCY_CAP_SETTING_KEY}`;
const EXP_BROWSER_TAB_NOTIFICATION_SETTING_KEY = 'BrowserTabNotification';
const EXP_BROWSER_TAB_NOTIFICATION_KEY = `settings.experiments.${EXP_BROWSER_TAB_NOTIFICATION_SETTING_KEY}`;

const addCampaignsAbTest = async (databaseId, data) => {
  const result = await AccountModel.updateOne(
    { databaseId },
    { $push: { [EXP_CAMPAIGN_AB_TEST_KEY]: data } },
  );

  queueAccountInfoUpload(databaseId);
  return result;
};

const updateCampaignsAbTest = async (databaseId, id, data) => {
  const result = await AccountModel.updateOne(
    { databaseId, [`${EXP_CAMPAIGN_AB_TEST_KEY}.id`]: id },
    { $set: { [`${EXP_CAMPAIGN_AB_TEST_KEY}.$`]: data } },
  );

  redisClient.delAccountSettings(databaseId);
  queueAccountInfoUpload(databaseId);
  return result;
};

const globalFrequencyCap = async (databaseId, data) => {
  let update = { $set: { [EXP_GLOBAL_FREQUENCY_CAP_KEY]: data } };

  if (data.reset) {
    update = { $unset: { [EXP_GLOBAL_FREQUENCY_CAP_KEY]: '' } };
  }

  await AccountModel.updateOne({ databaseId }, update);
  redisClient.delAccountSettings(databaseId);
  queueAccountInfoUpload(databaseId);

  return data;
};

const browserTabNotification = async (databaseId, data) => {
  const update = { $set: { [EXP_BROWSER_TAB_NOTIFICATION_KEY]: data } };

  await AccountModel.updateOne({ databaseId }, update);
  redisClient.delAccountSettings(databaseId);

  queueAccountInfoUpload(databaseId);
  return data;
};

const experimentalHandlers = {
  add: {
    [EXP_CAMPAIGN_AB_TEST_SETTING_KEY]: addCampaignsAbTest,
    [EXP_GLOBAL_FREQUENCY_CAP_SETTING_KEY]: globalFrequencyCap,
    [EXP_BROWSER_TAB_NOTIFICATION_SETTING_KEY]: browserTabNotification,
  },
  update: {
    [EXP_CAMPAIGN_AB_TEST_SETTING_KEY]: updateCampaignsAbTest,
  },
};

const addExperimentalSetting = async (_, { input }, { userId }) => {
  if (!input || !input.type)
    return { success: false, request: { input }, reason: 'missing required input' };
  const { type, data } = input;
  if (!experimentalHandlers.add[type]) return { success: false, request: { type, data } };
  return {
    success: true,
    result: await experimentalHandlers.add[type](userId, data),
  };
};

const updateExperimentalSetting = async (_, { input }, { userId }) => {
  if (!input || !input.type || !input.id)
    return { success: false, request: { input }, reason: 'missing required input' };
  const { type, data } = input;
  if (!experimentalHandlers.update[type]) return { success: false, request: { type, data } };
  return {
    success: true,
    result: await experimentalHandlers.update[type](userId, input.id, data),
  };
};

const getExperimentalSettings = async (_, __, { userId }) => {
  const settings = await getAccountSettings(userId, false);
  return _get(settings, 'experiments', {}) || {};
};

// Wizard

const updateWizardPreferences = async (_, { input }, { userId }) => {
  let { wizardPreferences } = await getAccountSettings(userId, false);
  wizardPreferences = { ...wizardPreferences, ...input };
  await AccountModel.updateOne(
    { databaseId: userId },
    { $set: { 'settings.wizardPreferences': wizardPreferences } },
  );
  return true;
};

const requestToAccessFeature = async (_, { email, locale, feature }, { userId }) => {
  Translator.setLocale(locale);
  const { message } = Translator.translate(feature);

  let user = await Intercom.getUserByEmail(email);
  if (!user) {
    user = await Intercom.createUser(email);
  }

  const success = await Intercom.createConversation(user?.id, message);

  if (!success) return false;

  return true;
};

const setWizardPreference = async (_, { key, value }, { userId }) => {
  if (key !== 'recommendationDismissed') {
    return false;
  }
  try {
    await AccountModel.updateOne(
      { databaseId: userId },
      { $set: { [`settings.wizardPreferences.${key}`]: value } },
    );
    return true;
  } catch (err) {
    return false;
  }
};

const deleteCookie = async (_, { customId }, { userId }) => {
  await AccountModel.updateOne(
    { databaseId: userId },
    {
      $pull: { 'settings.experiments.CampaignsAbTest.testCases': { id: customId } },
    },
  );

  return { success: true };
};

const setWizardPreferencesByToken = async (_, { token }, { userId, log }) => {
  if (!token) return false;
  try {
    const key = getCacheKey(token);
    const raw = await redisClient.get(key);
    const preferences = JSON.parse(raw);

    if (preferences) {
      await AccountModel.updateOne(
        { databaseId: userId, 'settings.wizardPreferences.useCase': { $exists: false } }, // Don't override existing preferences
        { $set: { 'settings.wizardPreferences': preferences } },
      );
    }

    return true;
  } catch (e) {
    log.error(e);
    log.warn("Can't set wizard preferences for account", { token, userId });
  }
  return false;
};

const getLoginEmails = async (_, __, { userId: databaseId }) => {
  const accounts = await AccountModel.aggregate([
    { $match: { databaseId } },
    {
      $lookup: {
        from: 'master_logins',
        foreignField: '_id',
        localField: 'users.loginId',
        as: 'logins',
      },
    },
  ]);

  return accounts?.[0]?.logins.map((login) => login.email);
};

const getSiteInfo = async (_, { url }, { userId: databaseId }) => {
  const domainLowerCase = url.toLowerCase();

  const getSiteInfoGenerationStart = performance.now();
  const siteInfo = (await detectSiteInfo(domainLowerCase, true)) || {};
  const getSiteInfoGenerationEnd = performance.now();

  logger.info({
    domain: domainLowerCase,
    took: (getSiteInfoGenerationEnd - getSiteInfoGenerationStart).toFixed(2),
    message: `get site info for domain: ${domainLowerCase}`,
  });

  return siteInfo;
};

const saveAgencyPotential = async (
  _,
  { firstName, lastName, email, agencyName },
  { userId: databaseId, loginId, log },
) => {
  await setUserProfileKey({ databaseId, key: 'agencyPotential', value: true, log });
  try {
    const login = await LoginModel.findOne({ _id: loginId });

    if (agencyName) {
      // user clicked "this is my client's contact info", so the contact is NOT an agency but managed by agency
      await _upsertUserInHubSpotV3(
        login.email,
        { managed_by_agency: true, invited_owner: 'owner' },
        { log },
      );

      // create contact with provided data which is an agency
      await _upsertUserInHubSpotV3(
        email,
        {
          agency_potential: true,
          managed_account: databaseId,
          business_name: agencyName,
          firstname: firstName,
          lastname: lastName,
          region: login.region,
          invited_owner: 'owner',
        },
        { log },
      );

      intercom.createOrUpdateUser({
        email,
        custom_attributes: {
          agency_potential: true,
        },
      });

      omMailChimpAdapter.addTagToMember(email, [{ name: 'agency_potential', status: 'active' }]);
    } else {
      // contact is an agency
      await _upsertUserInHubSpotV3(
        login.email,
        {
          agency_potential: true,
          phone: login.phoneNumber,
        },
        { log },
      );

      intercom.createOrUpdateUser({
        email: login.email,
        custom_attributes: {
          agency_potential: true,
        },
      });

      omMailChimpAdapter.addTagToMember(login.email, [
        { name: 'agency_potential', status: 'active' },
      ]);
    }
  } catch (e) {
    log.error('Error while saving agency potential', e);
    return false;
  }

  return true;
};

const updateAgencyContactNeeded = async (_, { contactNeeded }, { userId: databaseId }) => {
  await AccountModel.updateOne(
    { databaseId },
    { $set: { [`profile.agencyContactNeeded`]: contactNeeded } },
  );

  return true;
};

const flagAccountAsAgency = async (_, __, { userId: databaseId }) => {
  await AccountModel.updateOne({ databaseId }, { $set: { [`profile.agencyPotential`]: true } });

  return true;
};

const preferredTemplateLanguage = async (_, __, { userId: databaseId }) => {
  const account = await AccountModel.findOne({ databaseId }, { profile: 1 });

  return account.profile?.preferredTemplateLanguage || 'en';
};

const updatePreferredTemplateLanguage = async (_, { languageCode }, { userId: databaseId }) => {
  await AccountModel.updateOne(
    { databaseId },
    { $set: { [`profile.preferredTemplateLanguage`]: languageCode } },
  );

  return true;
};

const savePreferredLangByDomain = async (_, { domain }, { userId: databaseId }) => {
  const preferredLang = determineLangByDomain(domain);
  await AccountModel.updateOne(
    { databaseId },
    { $set: { [`profile.preferredTemplateLanguage`]: preferredLang } },
  );

  return true;
};
/**
 *
 * @param {parent} param1
 * @param {arguments} param2
 * @param {context} param3
 * @param {info} param4
 *
 * @returns {
 *   om: number databaseId,
 *   engine: string shopify,magento,wordpress,etc. packages/om-frontend/src/client/preload/Engine/Engine.js,
 *   shopId: number packages/om-frontend/src/client/preload/Engine/Engine.js,
 *   isKlaviyoDetected: boolean,
 *   inserted: boolean,
 * }
 */
const fetchSiteInfo = async (_, { domain, improved }, { userId: databaseId }) => {
  const domainLowerCase = domain.toLowerCase();
  const siteInfo = (await detectSiteInfo(domainLowerCase, improved || false)) || {};
  await updateDomainInfo(databaseId, domainLowerCase, siteInfo);
  siteInfo.inserted = !!((siteInfo?.collected?.om || siteInfo?.om) === databaseId);

  return siteInfo;
};

const saveReferralSource = async (_, { source }, { userId: databaseId, log }) => {
  await setUserProfileKey({ databaseId, key: 'referralSource', value: source, log });
  HeapAnalytics.trackReferralSource(databaseId, { source });

  return true;
};

module.exports = {
  Query: {
    getAccount,
    getSubAccounts,
    getUsers,
    getUsersWithSuggestions,
    getInvitations,
    getInvitation,
    getUserSuggestions,
    getAccountMembers,
    getGlobalIntegrations,
    getDomains,
    codeInsertedInAllDomains,
    getDomainUsageCount,
    getDomainsCount,
    getFonts,
    getDomain,
    getShopUserHash,
    getAgencyWhiteLabelSettings,
    getAgencyWhiteLabelSettingsForDomain,
    getAllSubAccounts,
    getDetailedDomainsWithPagination,
    getDowngradeLostFeatures,
    getAgencyManagedAccounts,
    isAgencyAffiliate,
    getApiKey,
    getExperimentalSettings,
    requestToAccessFeature,
    getAffiliateSubscribers,
    getLoginEmails,
    getAffiliateData,
    getAffiliatePayoutItems,
    preferredTemplateLanguage,
    getSiteInfo,
    getDomainsWithSiteData,
  },
  Mutation: {
    addInvitation,
    addSubAccount,
    removeInvitation,
    removeSubAccount,
    removeUser,
    addUser,
    updateSubAccount,
    updateUser,
    updateSettings,
    updateWhiteLabelSettings,
    sendInsertCode,
    sendCredentials,
    addIntegration,
    addOAuthIntegration,
    editIntegration,
    removeDomain,
    addDomain,
    removeIntegration,
    changeIntegrationName,
    removeMember,
    resendInvitation,
    renameDomain,
    sendUserQualifications,
    sendAdditionalOnboardingInfo,
    hideWelcomeMessage,
    updateFonts,
    deleteFont,
    toggleAnalyticsStatus,
    sendCampaignRebuildTicket,
    createAccountForShop,
    finishOnboarding,
    saveOnboardingStage,
    toggleDomainActiveStatus,
    toggleShopifyAppExtensionStatus,
    downgradeSubscription,
    sendMagicLinkEmail,
    sendMasterContactUs,
    saveAiFeatureRequest,
    trackPageview,
    generateApiKey,
    deleteApiKey,
    updateAffiliatePartnerInfo,
    updateBusinessNameAndPhoneNumber,
    storeProductTourStatus,
    storeInfoBoxVisibility,
    addExperimentalSetting,
    updateWizardPreferences,
    updateExperimentalSetting,
    deleteCookie,
    setWizardPreference,
    setWizardPreferencesByToken,
    saveOnboardingRole,
    saveOnboardingWhoBuildPopup,
    checkShopifyAppEmbedStatuses,
    setProfileKey,
    setOnboardingProperty,
    affiliatePayoutRequest,
    updatePreferredTemplateLanguage,
    savePreferredLangByDomain,
    fetchSiteInfo,
    saveVIPOnboardingRequest,
    saveAgencyPotential,
    upsertGeneralSetting,
    updateAgencyContactNeeded,
    flagAccountAsAgency,
    saveReferralSource,
  },
};
