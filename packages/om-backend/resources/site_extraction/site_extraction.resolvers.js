const axios = require('axios');
const probe = require('probe-image-size');
const path = require('path');
const { gzip } = require('node-gzip');
const { languages } = require('@om/template-properties/src/translate/constants');
const { model: AccountModel } = require('../account/account.model');
const { status: SiteExtractionStatus } = require('./site_extraction.model');
const { userModel: ImageModel } = require('../image/image.model');
const SiteExtractions = require('../../services/siteExtractions');
const { model: SiteExtractionModel } = require('./site_extraction.model');
const { uploadToS3 } = require('../../services/s3');
const { process } = require('../../services/optimageAdapter');

const getLastAddedDomain = async (databaseId) => {
  const account = await AccountModel.findOne({ databaseId }, { settings: 1 });
  return account?.settings?.domains?.slice(-1)[0] || false;
};

const getDomainById = async (databaseId, domainId) => {
  const account = await AccountModel.findOne({ databaseId }, { settings: 1 });

  return account?.settings?.domains?.find((domain) => domain._id.toString() === domainId);
};

const processImage = async (imageUrl, domain, log) => {
  imageUrl = encodeURI(imageUrl);
  let extension;
  let imageMeta;

  try {
    imageMeta = await probe(imageUrl);
    extension = imageMeta.type;
  } catch (e) {
    const urlObj = new URL(imageUrl);
    extension = urlObj.pathname.split('.').pop();
  }

  const imageName = `logo-${domain.domain}_${Date.now()}.${extension}`;
  const imageResponse = await axios.get(imageUrl, { responseType: 'arraybuffer' });

  let file = {
    originalname: imageName,
    buffer: imageResponse.data,
    mimeType: imageResponse.headers['content-type'],
  };

  try {
    const processedImage = await process(file, {}, log, { trim: '1' });

    let { name, ext } = path.parse(file.originalname);

    if (processedImage.mime === 'image/jpeg') {
      ext = '.jpg';
    } else if (processedImage.mime === 'image/png') {
      ext = '.png';
    } else {
      log.warn(`unhandled image MIME type received from optimage: ${processedImage.mime}`);
    }

    file = {
      ...file,
      originalname: `${name}${ext}`,
      mimetype: processedImage.mime,
      size: processedImage.size,
      buffer: processedImage.data,
      width: Math.round(Number(processedImage.width)),
      height: Math.round(Number(processedImage.height)),
    };
  } catch (e) {
    file.width = Math.round(imageMeta?.width);
    file.height = Math.round(imageMeta?.height);
  }

  return file?.width && file?.height ? file : null;
};

const saveAndUploadImage = async (imageUrl, domain, databaseId, log) => {
  const imageRegexp = `logo-${domain.domain.replace(/\./g, '\\.')}_\\d{13}\\.\\w+`;

  const hasImage = await ImageModel.findOne({
    databaseId,
    name: new RegExp(imageRegexp),
  });

  if (hasImage) return;

  const file = await processImage(imageUrl, domain, log);

  if (!file) return;

  const { width, height, ...s3CompatibleFile } = file;

  const url = await uploadToS3(s3CompatibleFile, {
    saveType: 'user',
    saveValue: domain.domain,
    userId: databaseId,
  });

  await ImageModel.create({
    databaseId,
    name: s3CompatibleFile.originalname,
    url,
    width,
    height,
  });
};

const saveLanguageToProfile = (language, databaseId) => {
  if (language in languages) {
    return AccountModel.updateOne(
      { databaseId },
      { $set: { [`profile.preferredTemplateLanguage`]: language } },
    );
  }
};

const getSiteExtractionsAndStore = async (domain, databaseId) => {
  const insertedRow = await SiteExtractionModel.create({
    domain: domain.domain,
    status: SiteExtractionStatus.inProgress,
  });
  const result = await SiteExtractions.getData(domain, databaseId);

  if (!result) {
    await insertedRow.remove();
    return false;
  }

  if (result.imageUrl?.startsWith('//')) {
    result.imageUrl = `https:${result.imageUrl}`;
  }

  insertedRow.status = SiteExtractionStatus.completed;
  insertedRow.pageContent = await gzip(result.pageContent);
  insertedRow.logo = result.imageUrl;
  insertedRow.language = result.language;

  await insertedRow.save();
  return insertedRow;
};

const generateSiteExtractions = async (_, { domainId }, { userId, log }) => {
  try {
    const domain = domainId
      ? await getDomainById(userId, domainId)
      : await getLastAddedDomain(userId, log);

    if (!domain) {
      log.error({ message: 'No domain found for account', databaseId: userId });
      return '';
    }

    let siteExtractions = await SiteExtractionModel.findOne({
      domain: domain.domain,
    });

    if (!siteExtractions) {
      siteExtractions = await getSiteExtractionsAndStore(domain, userId);
    }

    if (siteExtractions?.logo) {
      await saveAndUploadImage(siteExtractions.logo, domain, userId, log);
    }
    await saveLanguageToProfile(siteExtractions.language, userId);

    return siteExtractions;
  } catch (e) {
    log.error({
      message: 'Error during generating site extractions',
      errorMessage: e.message,
      stack: e.stack,
    });
    return false;
  }
};

const getAutoLogo = async (_, { domainId }, { userId, log }) => {
  try {
    const domain = domainId
      ? await getDomainById(userId, domainId)
      : await getLastAddedDomain(userId, log);

    const hasRow = await SiteExtractionModel.findOne({
      domain: domain.domain,
      status: SiteExtractionStatus.completed,
    });

    if (hasRow?.logo) {
      await saveAndUploadImage(hasRow.logo, domain, userId, log);
    }

    return hasRow?.logo;
  } catch (e) {
    log.error({ message: 'Error during get logo', errorMessage: e.message, stack: e.stack });
    return false;
  }
};

module.exports = {
  Query: {
    getAutoLogo,
    generateSiteExtractions,
  },
};
