const axios = require('axios');
const { model: AccountModel } = require('../account/account.model');
const UnifiedProductsAdapter = require('../../services/UnifiedProductsAdapter/unifiedProductsAdapter');
const { determineDomainProviderServiceId } = require('../../util/domain');

const addGomagApiKey = async (_, { apiKey, liveDomain }, { log, userId }) => {
  if (!apiKey || !liveDomain) {
    return {
      success: false,
      message: 'API Key or Provider Service ID is missing',
    };
  }

  try {
    const isValidApiKey = await validateGomagApiKey(apiKey, liveDomain);

    if (!isValidApiKey) {
      return {
        success: false,
        message: 'Invalid API Key',
      };
    }

    const accountModel = await AccountModel.findOne({ databaseId: userId });

    if (!accountModel) {
      return {
        success: false,
        message: 'Account not found',
      };
    }

    const providedShop = accountModel.settings.shops.find(
      (shop) => shop.live_domain === liveDomain && shop.type === 'gomag',
    );

    if (providedShop) {
      await AccountModel.updateOne(
        { databaseId: userId, 'settings.shops.live_domain': liveDomain },
        {
          $set: {
            'settings.shops.$.apiKey': apiKey,
            'settings.shops.$.updatedAt': new Date().toISOString(),
            'settings.shops.$.gomag_domain': liveDomain, // Update gomag if needed
            'settings.shops.$.shopId': liveDomain, // TODO: get shopid from gomag API if it exists
          },
        },
      );
    }

    if (!providedShop) {
      accountModel.settings.shops.push({
        type: 'gomag',
        apiKey,
        live_domain: liveDomain,
        gomag_domain: liveDomain,
        shopId: liveDomain, // TODO: get shopid from gomag API if it exists
        active: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
    }

    const domain = accountModel.settings.domains.find((d) => d.domain === liveDomain);
    if (domain) {
      domain.shopId = liveDomain;
      domain.platform = 'gomag'; // Hanldle platform in domain insert
    }

    await accountModel.save();

    // Initialize the UnifiedProductsAdapter and start sync
    const unifiedProductsAdapter = new UnifiedProductsAdapter(
      process.env.PRODUCT_SYNC_SERVICE_URL,
      process.env.om_shared_key,
      log,
    );

    const providerServiceId = determineDomainProviderServiceId({
      domain,
    });

    unifiedProductsAdapter.startSync({
      platform: 'gomag',
      providerServiceId,
      locale: 'ro',
      databaseId: userId,
      additionalParameters: { apiKey },
    });

    return {
      success: true,
      message: 'API Key added successfully',
    };
  } catch (error) {
    log.error('Error adding API Key:', error);
    return {
      success: false,
      message: error.message || 'An error occurred while validating the API Key',
    };
  }
};

const removeGomagApiKey = async (_, { liveDomain }, { log, userId }) => {
  if (!liveDomain) {
    return {
      success: false,
      message: 'Missing parameter (liveDomain)',
    };
  }
  try {
    await AccountModel.updateOne(
      { databaseId: userId, 'settings.shops.live_domain': liveDomain },
      {
        $set: {
          'settings.shops.$.apiKey': null,
          'settings.shops.$.updatedAt': new Date().toISOString(),
        },
      },
    );

    return {
      success: true,
      message: 'API Key removed successfully',
    };
  } catch (error) {
    log.error('Error removing API Key:', error);
    return {
      success: false,
      message: error.message || 'An error occurred while removing the API Key',
    };
  }
};

const validateGomagApiKey = async (apiKey, domain) => {
  const response = await axios.get('https://api.gomag.ro/api/v1/category/read/json', {
    headers: {
      'Content-Type': 'application/xml',
      ApiKey: apiKey,
      ApiShop: domain,
    },
  });

  if (response.data.total) {
    return true; // If the response contains total, the API key is valid
  }
  return false; // Placeholder for actual API key validation logic
};

module.exports = {
  Mutation: {
    addGomagApiKey,
    removeGomagApiKey,
  },
};
