const { getExperimentStat, getEventTableId } = require('@om/queries');
const { model: ExperimentModel } = require('../experiment.model');
const { model } = require('../../campaign/campaign.model');
const { model: AccountModel } = require('../../account/account.model');
const { model: VariantTemplateModel } = require('../../campaign/variantTemplate.model');
const { model: StatisticsModel } = require('../../statistics/statistics.model');
const { roundToNDecimals } = require('../../../helpers/math');
const { findActiveVariant } = require('../../../util/templateUtil');
const { BigQueryClient } = require('../../../services/bigQueryAdapter');
const {
  domainAndShopByDomainId,
  determineDomainProviderServiceId,
} = require('../../../util/domain');

const { FEATURES, isFeatureEnabled } = require(`../../../util/feature`);

const getExperimentStatistics = async (campaignIds) => {
  return StatisticsModel.aggregate([
    {
      $match: {
        campaign: {
          $in: campaignIds,
        },
      },
    },
    {
      $group: {
        _id: { campaign: '$campaign', experimentGroupId: '$experimentGroupId' },
        impressions: { $sum: '$impressions' },
        conversions: { $sum: '$conversions' },
      },
    },
    {
      $project: {
        _id: 0,
        campaign: '$_id.campaign',
        experimentGroupId: '$_id.experimentGroupId',
        impressions: 1,
        conversions: 1,
      },
    },
    { $match: { experimentGroupId: { $ne: null, $exists: true } } },
  ]);
};

const getCampaignStat = (stats, groupId, campaignId) => {
  return stats.find(
    (stat) =>
      stat.experimentGroupId === groupId.toString() &&
      campaignId.toString() === stat.campaign.toString(),
  );
};

const getConversionRate = (impressions, conversions) => {
  if (impressions === 0) {
    return 0;
  }
  return roundToNDecimals({ number: conversions / impressions, decimalPlaces: 4 });
};

const getGroupStats = (stats, groupId) => {
  const summedStats = stats
    .filter((stat) => stat.experimentGroupId === groupId.toString())
    .reduce(
      (groupStat, filteredStat) => {
        groupStat.impressions += filteredStat.impressions;
        groupStat.conversions += filteredStat.conversions;
        return groupStat;
      },
      { impressions: 0, conversions: 0 },
    );

  summedStats.conversionRate = getConversionRate(summedStats.impressions, summedStats.conversions);

  return summedStats;
};

const getExperiment = async (_, { experimentId }, { userId }) => {
  let experiment = await ExperimentModel.findOne(
    { _id: experimentId },
    { createdAt: 0, updatedAt: 0 },
  ).lean();

  if (experiment) {
    const account = await AccountModel.findOne(
      { databaseId: userId },
      { 'settings.shops': 1, 'settings.domains': 1, features: 1 },
    );
    const { settings: { shops = [], domains = [] } = {} } = account ?? {};
    const { domain, shop } = domainAndShopByDomainId(experiment.domainId, {
      shops,
      domains,
    });

    const providerServiceId = determineDomainProviderServiceId({ domain, shop });

    const campaignIds = [];
    experiment.groups.forEach((group) => {
      campaignIds.push(...group.campaigns);
    });

    if (campaignIds.length) {
      const CampaignModel = model(userId);

      const campaigns = await CampaignModel.find(
        {
          id: {
            $in: campaignIds,
          },
          status: {
            $in: ['active', 'inactive', 'archived', 'deleted'],
          },
          'variants.status': { $in: ['active', 'inactive'] },
        },
        {
          _id: 1,
          id: 1,
          device: 1,
          status: 1,
          name: 1,
          'variants._id': 1,
          'variants.template.style.mode': 1,
          'variants.updatedAt': 1,
          'variants.previewGeneratedAt': 1,
          'variants.status': 1,
          type: 1,
        },
      ).lean();

      let experimentStat = [];
      if (experiment.startedAt) {
        const eventsTable = getEventTableId(userId, providerServiceId);
        const query = getExperimentStat({ eventsTable });
        const bigQueryClient = new BigQueryClient();
        experimentStat = await bigQueryClient.runQuery(query, {
          params: {
            experimentId,
            experimentStartedAt: experiment.startedAt,
            experimentFinishedAt: experiment.finishedAt || '2099-12-31 00:00:00',
          },
        });
      }

      const detailedGroups = [];

      for (const group of experiment.groups) {
        const { campaigns: campaignStats, visitors = 0 } =
          experimentStat?.find((stat) => `${stat.groupId}` === `${group._id}`) || {};

        let groupImpressions = 0;
        let groupConversions = 0;

        const groupCampaigns = [];
        for (const campaignId of group.campaigns) {
          const campaign = campaigns.find(
            (campaign) => campaign.id.toString() === campaignId.toString(),
          );
          if (campaign) {
            const campaignStat = campaignStats?.find((stat) => {
              return stat.campaign_id === campaign?.id.toString();
            });

            let templateType = '';
            const activeVariant = findActiveVariant(campaign.variants);
            let { template, updatedAt, previewGeneratedAt } = activeVariant;
            if (isFeatureEnabled(account, FEATURES.VARIANT_TEMPLATE_MIGRATED)) {
              template = (
                await VariantTemplateModel.findOne(
                  { databaseId: userId, campaignId: campaign.id, variantId: activeVariant._id },
                  { 'template.style.mode': 1 },
                )
              )?.template;
            }

            if (template) {
              templateType = template.style.mode;
            } else if (campaign.type === 'dynamic_content') {
              templateType = 'dynamic_content';
            }

            const impressions = campaignStat?.impressions || 0;
            const conversions = campaignStat?.conversions || 0;

            groupImpressions += impressions;
            groupConversions += conversions;

            groupCampaigns.push({
              ...campaign,
              updatedAt: updatedAt || previewGeneratedAt || new Date('2021-11-11'),
              visitors: campaignStat?.visitors || 0,
              impressions,
              conversions,
              conversionRate: campaignStat?.conversion_rate,
              templateType,
            });
          }
        }

        detailedGroups.push({
          ...group,
          campaigns: groupCampaigns,
          stats: {
            visitors,
            impressions: groupImpressions,
            conversions: groupConversions,
            conversionRate: groupImpressions
              ? Math.round((groupConversions / groupImpressions) * 10000) / 10000
              : null,
          },
        });
      }

      experiment.groups = detailedGroups;
    }
  }
  return experiment;
};

module.exports = {
  getExperiment,
};
