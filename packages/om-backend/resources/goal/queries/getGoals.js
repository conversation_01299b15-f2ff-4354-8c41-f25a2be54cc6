const { model: GoalModel } = require('../goal.model');
const { defaultRules } = require('../helpers/defaultGoals');
const { model: AccountModel } = require('../../account/account.model');
const { model: PrimaryGoalModal } = require('../../primaryGoal/primaryGoal.model');
const {
  isUnifiedProductBased,
  UNIFIED_BASED_PLATFORMS,
} = require('../../../helpers/unifiedProductBased');
const {
  determineDomainProviderServiceId,
  domainAndShopByDomainId,
} = require('../../../util/domain');

const shouldAddDefaultGoals = async ({ domainId, databaseId }) => {
  const account = await AccountModel.findOne(
    { databaseId },
    { 'settings.shops': 1, 'settings.domains': 1 },
  );

  if (!account) return false;

  if (!domainId) {
    return account.settings.shops.some(
      (shop) =>
        [1, '1', true].includes(shop.active) &&
        ['shoprenter', 'shopify', ...UNIFIED_BASED_PLATFORMS].includes(shop.type),
    );
  }

  const { domain, shop } = domainAndShopByDomainId(domainId, account.settings);
  if (!domain) {
    throw new Error('Unable to resolve shop/domain.');
  }
  const providerServiceId = determineDomainProviderServiceId({ domain, shop });
  if (!providerServiceId) return false;

  if (!shop) return false;
  if (![1, '1', true].includes(shop.active)) return false;

  // [product-sync][isUnified]
  if (isUnifiedProductBased({ shop })) return true;

  if (['shopify', 'shoprenter'].includes(shop.type)) return true;

  return false;
};

const mapDefaultRule = (defaultRules, id, domainId, primaryGoal) => {
  return {
    _id: id,
    name: id.split('_')[1],
    domainId: domainId || null,
    rules: {
      expressions: [defaultRules],
    },
    default: true,
    isPrimary: primaryGoal?.goalId?.toString() === id.toString(),
  };
};

const getGoals = async (_, { pagination, domainId, campaignId }, { log, userId }) => {
  try {
    const query = {
      databaseId: userId,
      ...(domainId && { domainId }),
      deletedAt: { $exists: false },
    };

    const options = {
      sort: { _id: -1 },
      lean: true,
    };

    if (pagination) {
      const page = Math.max(pagination.page - 1, 0);
      const limit = pagination.limit || 30;
      options.skip = page * limit;
      options.limit = limit;
    }
    const [goals, primaryGoal, addDefaults, count] = await Promise.all([
      GoalModel.find(query, null, options),
      campaignId ? PrimaryGoalModal.findOne({ databaseId: userId, campaignId }) : null,
      shouldAddDefaultGoals({ domainId, databaseId: userId }),
      GoalModel.countDocuments(query),
    ]);

    const goalResult = goals.map(({ domainId: goalDomainId, name, rules, _id, createdAt }) => ({
      isPrimary: primaryGoal?.goalId?.toString() === _id.toString(),
      domainId: goalDomainId,
      name,
      rules,
      _id,
      createdAt,
      default: false,
    }));

    if (addDefaults) {
      goalResult.push(
        mapDefaultRule(defaultRules.default_addToCart, 'default_addToCart', domainId, primaryGoal),
      );
    }
    goalResult.push(
      mapDefaultRule(defaultRules.default_purchase, 'default_purchase', domainId, primaryGoal),
    );

    return { goals: goalResult, count };
  } catch (error) {
    log.error({
      message: 'Error getting goals',
      error: { code: error?.code, message: error?.message, stack: error?.stack },
    });
    throw new Error('Error getting goals');
  }
};

module.exports = {
  getGoals,
};
