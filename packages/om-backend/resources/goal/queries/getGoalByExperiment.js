const { performance } = require('perf_hooks');
const { model: GoalModel } = require('../goal.model');
const { model: ExperimentModel } = require('../../experiment/experiment.model');
const { isDefaultGoal, getDefaultGoal } = require('../helpers/defaultGoals');
const { handleNotFoundGoal } = require('../helpers/error');
const { getMaterializedViewName } = require('../helpers/util');
const { BigQueryClient } = require('../../../services/bigQueryAdapter');
const { getGoalQueryByExperiment } = require('../helpers/getGoalQuery');
const { model: AccountModel } = require('../../account/account.model');
const {
  domainAndShopByDomainId,
  determineDomainProviderServiceId,
} = require('../../../util/domain');

const getGoalByExperiment = async (_, { experimentId, goalId }, { userId, log, superadmin }) => {
  const isDefault = isDefaultGoal(goalId);

  try {
    let goalDefinition;
    let experiment;
    let domainId;
    let account;
    if (!isDefault) {
      const [exp, goal, acc] = await Promise.all([
        ExperimentModel.findOne(
          {
            _id: experimentId,
            databaseId: userId,
            status: { $ne: 'DRAFT' },
          },
          { 'groups.campaigns': 1, domainId: 1, startedAt: 1 },
        ),
        GoalModel.findOne({
          _id: goalId,
          databaseId: userId,
          deletedAt: { $exists: false },
        }),
        AccountModel.findOne(
          { databaseId: userId },
          { 'settings.shops': 1, 'settings.domains': 1 },
        ),
      ]);

      experiment = exp;
      goalDefinition = goal;
      account = acc;
      domainId = goal.domainId;
      handleNotFoundGoal(goalDefinition);
    } else {
      const [exp, acc] = await Promise.all([
        ExperimentModel.findOne(
          {
            _id: experimentId,
            databaseId: userId,
            status: { $ne: 'DRAFT' },
          },
          { 'groups.campaigns': 1, domainId: 1, startedAt: 1 },
        ),
        AccountModel.findOne(
          { databaseId: userId },
          { 'settings.shops': 1, 'settings.domains': 1 },
        ),
      ]);
      experiment = exp;
      domainId = experiment.domainId;
      account = acc;
      const useNonstandardOrders = account?.settings?.domains?.find(
        (domain) => domain?._id?.toString() === domainId?.toString(),
      )?.useNonstandardOrders;
      goalDefinition = getDefaultGoal({ goalId, domainId, userId, useNonstandardOrders });
    }
    const { domain, shop } = domainAndShopByDomainId(domainId, account.settings);
    if (!domain) {
      throw new Error('Unable to resolve shop/domain.');
    }
    const providerServiceId = determineDomainProviderServiceId({ domain, shop });

    const materializedViewName = await getMaterializedViewName({
      providerServiceId,
      accountId: userId,
    });

    const { query, params } = getGoalQueryByExperiment({
      accountId: userId,
      providerServiceId,
      materializedViewName,
      rules: goalDefinition.rules,
      experimentId,
      experimentStartedAt: experiment.startedAt,
      uniqueConversions: true,
    });

    const bigQueryClient = new BigQueryClient();
    const getGoalStart = performance.now();
    const queryResult = await bigQueryClient.runQuery(query, { params });
    const getGoalEnd = performance.now();
    log.info({
      userId,
      goalId,
      took: getGoalEnd - getGoalStart,
      message: `getGoalByExperiment live query ${userId} ${goalId}`,
    });

    return { groups: queryResult };
  } catch (e) {
    log.error(
      { userId, goalId, experimentId, err: e },
      `Failed to get goal by experiment userId: ${userId}, goalId: ${goalId}, experimentId: ${experimentId}`,
    );
    const goalError = new Error();
    goalError.extensions = {
      code: 500,
      message: 'Failed to get goal by experiment',
    };
    throw goalError;
  }
};

module.exports = {
  getGoalByExperiment,
};
