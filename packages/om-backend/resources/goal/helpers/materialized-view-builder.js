const { daysFromNow, arrayify } = require('@om/common');
const { getUniqueTableIdentifier } = require('./util');
const { addDefaultRules } = require('./defaultGoals');

const CUSTOM_PROPERTY_EXISTS = 'customPropertyExists';

class MaterializedViewBuilder {
  constructor(projectId, databaseId, providerServiceId, domainId) {
    if (!projectId || !databaseId || !providerServiceId || !domainId)
      throw new Error('Params missing from Materialized View Builder');
    this.projectId = projectId;
    this.providerServiceId = providerServiceId;
    this.databaseId = databaseId;
    this.domainId = domainId;
    this.uniqueIdentifier = getUniqueTableIdentifier(this.databaseId, this.providerServiceId);
  }

  _collectFields(eventFieldMap, expressions) {
    expressions.forEach((subExpressions) => this._collectField(eventFieldMap, subExpressions));
  }

  _collectField(eventFieldMap, expression) {
    if (expression.expressions) {
      return this._collectFields(eventFieldMap, expression.expressions);
    }

    eventFieldMap[expression.type] = arrayify(eventFieldMap[expression.type]);
    const field = expression.field || CUSTOM_PROPERTY_EXISTS;
    if (!eventFieldMap[expression.type].includes(field)) {
      eventFieldMap[expression.type].push(field);
    }
  }

  async createEventFieldMap({ rules }) {
    const eventFieldMap = rules.reduce((mapFromRules, goalRule) => {
      this._collectFields(mapFromRules, [goalRule]);
      return mapFromRules;
    }, {});

    return addDefaultRules({
      eventFieldMap,
      databaseId: this.databaseId,
      domainId: this.domainId,
    });
  }

  async _buildWhereClause({ eventFieldMap }) {
    return Object.keys(eventFieldMap)
      .map((event) => {
        return eventFieldMap[event]
          .map((field) => {
            if (field === CUSTOM_PROPERTY_EXISTS) {
              return `(e.type = '${event}' AND p.name = '${field}')`;
            }
            return `CONCAT(e.type, '_', p.name) = '${event}_${field}'`;
          })
          .join(' OR ');
      })
      .join(' OR ');
  }

  _getQuery({ whereClause }) {
    const fromDate = daysFromNow(180);

    return `
      CREATE MATERIALIZED VIEW
  \`${this.projectId}.behavioural_events_optimonk._goal_fields_${this.uniqueIdentifier}\`
      PARTITION BY DATE(timestamp)
      OPTIONS (enable_refresh = FALSE) AS (
        SELECT
          timestamp,
          a.customerAddress as deviceId,
          IF(p.name = '${CUSTOM_PROPERTY_EXISTS}', e.type, CONCAT(e.type, '_', p.name)) AS field,
          IF(p.value = '${CUSTOM_PROPERTY_EXISTS}', null, p.value) AS value,
          ANY_VALUE(IF(c.name = 'visitorInExperiment', c.value, null)) as visitorInExperiment,
          ANY_VALUE(IF(c.name = 'deviceType', c.value, null)) as deviceType
        FROM
        \`${this.projectId}.behavioural_events_optimonk.events_${this.uniqueIdentifier}\` as be,
          UNNEST(be.events) as e,
          UNNEST(be.context) as c,
          UNNEST(be.addresses) as a,
          UNNEST (
            CASE WHEN ARRAY_LENGTH(e.props) >= 1
                THEN ARRAY_CONCAT(e.props,[('${CUSTOM_PROPERTY_EXISTS}', '${CUSTOM_PROPERTY_EXISTS}')])
                ELSE [('${CUSTOM_PROPERTY_EXISTS}', '${CUSTOM_PROPERTY_EXISTS}')] END
            )  as p

        WHERE
          (${whereClause} OR CONCAT(e.type, '_', p.name) = 'eoi_variantId' OR CONCAT(e.type, '_', p.name) = 'eoi_campaignId' OR CONCAT(e.type, '_', p.name) = 'eoi_smartAbTest' OR CONCAT(e.type, '_', p.name) = 'splitURLTest_variantId')
          AND c.name IN('visitorInExperiment', 'deviceType')
          AND a.provider = 'optimonk' AND addressType = 'deviceId' AND a.customerAddress IS NOT NULL
          AND DATE(timestamp) >= DATE('${fromDate}')
        GROUP BY timestamp, deviceId, field, value
      )
    `;
  }

  getRefreshViewQuery() {
    return `
          CALL BQ.REFRESH_MATERIALIZED_VIEW('${this.projectId}.behavioural_events_optimonk._goal_fields_${this.uniqueIdentifier}');
    `;
  }

  _handleEmptyEventFieldMap(eventFieldMap) {
    if (
      !eventFieldMap ||
      Object.keys(eventFieldMap).length === 0 ||
      Object.values(eventFieldMap).some((fields) => !fields?.length)
    ) {
      throw Error('Empty expressions');
    }
  }

  async getCreateQuery(eventFieldMap) {
    this._handleEmptyEventFieldMap(eventFieldMap);
    const whereClause = await this._buildWhereClause({ eventFieldMap });

    return this._getQuery({ whereClause });
  }

  getDropQuery() {
    return `
      DROP MATERIALIZED VIEW IF EXISTS \`${this.projectId}.behavioural_events_optimonk._goal_fields_${this.uniqueIdentifier}\`
    `;
  }
}

module.exports = {
  MaterializedViewBuilder,
};
