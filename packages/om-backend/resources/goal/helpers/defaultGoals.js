const { isUnifiedProductBased } = require('../../../helpers/unifiedProductBased');
const { domainAndShopByDomainId } = require('../../../util/domain');
const { model: AccountModel } = require('../../account/account.model');
const { handleNotFoundGoal } = require('./error');

const defaultRules = {
  // the visitor has added any product to the cart
  default_addToCart: {
    type: 'addToCart',
    field: 'quantity',
    operator: 'greaterThanEquals',
    value: 1,
  },
  // the visitor has placed an order, with any value or product
  default_purchase: {
    type: 'eoo',
    field: 'total',
    operator: 'greaterThanEquals',
    value: 0,
  },
};

const addDefaultToEventFieldMap = (eventFieldMap, defaultRule) => {
  let eventFieldMapWithDefaults = JSON.parse(JSON.stringify(eventFieldMap));

  const { type, field } = defaultRule;

  const hasType = eventFieldMapWithDefaults.hasOwnProperty(type);
  const hasField = hasType && eventFieldMapWithDefaults[type].includes(field);

  if (!hasType) {
    eventFieldMapWithDefaults[type] = [field];
  } else if (!hasField) {
    eventFieldMapWithDefaults[type].push(field);
  }

  return eventFieldMapWithDefaults;
};

const shouldAddDefaultGoals = async ({ databaseId, domainId }) => {
  const account = await AccountModel.findOne(
    { databaseId },
    { 'settings.domains': 1, 'settings.shops': 1 },
  );

  if (!account) return false;

  const {
    settings: { domains, shops },
  } = account;

  const { domain, shop } = domainAndShopByDomainId(domainId, { shops, domains });
  if (!shop || !domain) return false;

  // [product-sync][isUnified]
  if (isUnifiedProductBased({ shop })) return true;

  if (['shopify', 'shoprenter'].includes(shop.type)) return true;

  return false;
};

const addDefaultRules = async ({ eventFieldMap, databaseId, domainId }) => {
  const _shouldAddDefaultGoals = await shouldAddDefaultGoals({ databaseId, domainId });

  let _eventFieldMap = eventFieldMap;
  if (_shouldAddDefaultGoals) {
    _eventFieldMap = addDefaultToEventFieldMap(_eventFieldMap, defaultRules.default_addToCart);
  }
  _eventFieldMap = addDefaultToEventFieldMap(_eventFieldMap, defaultRules.default_purchase);

  return _eventFieldMap;
};

const isDefaultGoal = (goalId) => {
  return Object.keys(defaultRules).includes(goalId);
};

const getDefaultGoal = ({ goalId, domainId, userId, useNonstandardOrders }) => {
  let rule = defaultRules[goalId];

  if (goalId === 'default_purchase' && useNonstandardOrders) {
    rule = {
      type: 'nonstandardOrder',
    };
  }

  handleNotFoundGoal(rule);

  const name = goalId.split('_')[1];

  return {
    domainId,
    name,
    databaseId: userId,
    rules: {
      expressions: [rule],
    },
  };
};

module.exports = {
  addDefaultRules,
  defaultRules,
  isDefaultGoal,
  getDefaultGoal,
};
