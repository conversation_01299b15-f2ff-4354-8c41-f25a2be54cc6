const { performance } = require('perf_hooks');
const isEqualWith = require('lodash.isequalwith');
const { BigQueryClient } = require('../../../services/bigQueryAdapter');
const { MaterializedViewBuilder } = require('./materialized-view-builder');
const { model: GoalModel } = require('../goal.model');
const { model: GoalViewsModel, ObjectId } = require('../goalViews.model');
const { getAccount } = require('../../../services/repositories/integrationRepository');
const {
  domainAndShopByDomainId,
  determineDomainProviderServiceId,
} = require('../../../util/domain');

// this schema is duplicated in jetfabric/js/packages/schemas/lib/bigquery/behaviouralEventsTable.json
// when modifying, needs to be done in both places
const eventsTableSchema = require('./eventsTableSchema.json');
const log = require('../../../logger');
const { getUniqueTableIdentifier, DATASET_ID } = require('./util');

const CURRENT_VIEW_VERSION = 4;

const handleArrays = (value, other) => {
  if (Array.isArray(value) && Array.isArray(other)) {
    return (
      value.length === other.length &&
      value.every((v) => other.includes(v)) &&
      other.every((o) => value.includes(o))
    );
  }
};

const isUptodateByVersion = (existingView) => {
  return existingView && parseInt(existingView.version, 10) >= CURRENT_VIEW_VERSION;
};

const shouldRecreateMatView = ({ eventFieldMap, existingView, force }) => {
  if (!existingView) return true;
  if (force) return true;
  if (!isUptodateByVersion(existingView)) return true;
  if (!isEqualWith(eventFieldMap, existingView.eventFieldMap, handleArrays)) return true;

  return false;
};

const createEmptyEventsTable = async (bigQueryClient, uniqueIdentifier) => {
  const tableId = `events_${uniqueIdentifier}`;
  return bigQueryClient.createTable({
    datasetId: DATASET_ID,
    tableId,
    tableOptions: eventsTableSchema,
  });
};

const dropView = async ({ builder, bigQueryClient }) => {
  const dropQuery = builder.getDropQuery();
  await bigQueryClient.runQuery(dropQuery, { location: 'US' });
};

const handleNotFoundEventsTable = async ({
  projectId,
  databaseId,
  providerServiceId,
  domainId,
  bigQueryClient,
  createQuery,
}) => {
  const uniqueIdentifier = getUniqueTableIdentifier(databaseId, providerServiceId);
  const tableName = `${projectId}.${DATASET_ID}.events_${uniqueIdentifier}`;
  log.info(
    { databaseId, domainId, tableName },
    'Missing events table while creating goals materialized view. Creating new empty table.',
  );
  await createEmptyEventsTable(bigQueryClient, uniqueIdentifier);
  log.info({ databaseId, domainId, tableName }, 'Events table successfully created.');
  await bigQueryClient.runQuery(createQuery, { location: 'US' });
};

const handleMaterializedViewRecreation = async ({
  projectId,
  providerServiceId,
  bigQueryClient,
  existingView,
  builder,
  eventFieldMap,
  databaseId,
  domainId,
}) => {
  const _id = existingView?._id || new ObjectId();

  await dropView({ builder, bigQueryClient });

  const isEmptyEventMap = Object.keys(eventFieldMap).length === 0;
  if (!isEmptyEventMap) {
    const getMatViewGenerationStart = performance.now();
    const createQuery = await builder.getCreateQuery(eventFieldMap);
    const getMatViewGenerationEnd = performance.now();
    log.info({
      databaseId,
      domainId,
      took: getMatViewGenerationEnd - getMatViewGenerationStart,
      message: `create materialized view query account: ${databaseId} domainId: ${domainId}`,
    });
    try {
      await bigQueryClient.runQuery(createQuery, { location: 'US' });
    } catch (e) {
      if (e?.errors?.[0]?.reason === 'notFound') {
        await handleNotFoundEventsTable({
          projectId,
          databaseId,
          providerServiceId,
          domainId,
          bigQueryClient,
          createQuery,
        });
      } else {
        throw e;
      }
    }
  }

  await GoalViewsModel.findOneAndUpdate(
    { _id },
    {
      accountId: databaseId,
      domainId,
      eventFieldMap,
      ...(isUptodateByVersion(existingView) ? {} : { version: CURRENT_VIEW_VERSION.toString() }),
    },
    { upsert: true },
  );
};

const recreateOrDropMaterializedView = async ({
  projectId,
  databaseId,
  domainId,
  force = false,
}) => {
  const bigQueryClient = new BigQueryClient();
  const account = await getAccount(databaseId);

  const { domain, shop } = domainAndShopByDomainId(domainId, account.settings);

  const [existingView, goals] = await Promise.all([
    GoalViewsModel.findOne({ accountId: databaseId, domainId }).lean(),
    GoalModel.find({
      domainId,
      databaseId,
      deletedAt: { $exists: false },
    }),
  ]);
  const rules = goals.map((goal) => goal.rules);

  const providerServiceId = determineDomainProviderServiceId({ domain, shop });

  const builder = new MaterializedViewBuilder(projectId, databaseId, providerServiceId, domainId);
  const eventFieldMap = await builder.createEventFieldMap({ rules });

  const shouldRecreate = shouldRecreateMatView({
    domainId,
    eventFieldMap,
    existingView,
    force,
    databaseId,
  });

  if (shouldRecreate) {
    await handleMaterializedViewRecreation({
      projectId,
      providerServiceId,
      bigQueryClient,
      existingView,
      builder,
      eventFieldMap,
      databaseId,
      domainId,
    });
  }

  return shouldRecreate;
};

module.exports = {
  CURRENT_VIEW_VERSION,
  recreateOrDropMaterializedView,
  shouldRecreateMatView,
};
