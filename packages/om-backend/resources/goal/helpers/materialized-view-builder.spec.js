const mockAccountFind = jest.fn();

const { hash } = require('@om/schema/src/utils');
const { MaterializedViewBuilder } = require('./materialized-view-builder');

jest.mock('../../account/account.model', () => {
  return {
    model: { findOne: mockAccountFind },
  };
});

describe('MaterializedViewBuilder', () => {
  beforeAll(async () => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2023-01-05T14:26:27.709Z'));
  });

  beforeEach(() => {
    mockAccountFind.mockResolvedValue({
      settings: {
        domains: [{ _id: 'domainId' }],
        shops: [
          {
            type: 'wordpress',
            shopname: 'some wordpress store',
          },
        ],
      },
    });
  });

  afterAll(async () => {
    jest.useRealTimers();
  });

  describe('constructor', () => {
    test('throws an error if any parameter is missing', () => {
      const paramsMissingError = new Error('Params missing from Materialized View Builder');

      expect(() => new MaterializedViewBuilder()).toThrowError(paramsMissingError);
      expect(() => new MaterializedViewBuilder('projectId')).toThrowError(paramsMissingError);
      expect(() => new MaterializedViewBuilder('projectId', 'databaseId')).toThrowError(
        paramsMissingError,
      );
    });

    test('sets the properties correctly', () => {
      const builder = new MaterializedViewBuilder(
        'projectId',
        'databaseId',
        'providerServiceId',
        'domainId',
      );
      expect(builder.projectId).toBe('projectId');
      expect(builder.databaseId).toBe('databaseId');
      expect(builder.providerServiceId).toBe('providerServiceId');
      expect(builder.domainId).toBe('domainId');
      expect(builder.uniqueIdentifier).toBe(`databaseId_${hash('providerServiceId')}`);
    });
  });

  describe('createEventFieldMap', () => {
    test('simple expression', async () => {
      const rules = [
        {
          operator: 'or',
          expressions: [
            {
              operator: 'and',
              expressions: [
                {
                  type: 'eoo',
                  field: 'total',
                  operator: 'gte',
                  value: 49,
                },
              ],
            },
          ],
        },
      ];
      const builder = new MaterializedViewBuilder(
        'projectId',
        'databaseId',
        'providerServiceId',
        'domainId',
      );

      const result = await builder.createEventFieldMap({ rules });
      const expected = { eoo: ['total'] };

      expect(result).toEqual(expected);
    });

    test('duplicated fields in expressions', async () => {
      const rules = [
        {
          operator: 'or',
          expressions: [
            {
              operator: 'and',
              expressions: [
                {
                  type: 'eoo',
                  field: 'total',
                  operator: 'gte',
                  value: 49,
                },
              ],
            },
            {
              operator: 'and',
              expressions: [
                {
                  type: 'eoo',
                  field: 'total',
                  operator: 'gte',
                  value: 50,
                },
              ],
            },
          ],
        },
        {
          operator: 'or',
          expressions: [
            {
              operator: 'and',
              expressions: [
                {
                  type: 'eoo',
                  field: 'total',
                  operator: 'gte',
                  value: 49,
                },
              ],
            },
          ],
        },
      ];

      const builder = new MaterializedViewBuilder(
        'projectId',
        'databaseId',
        'providerServiceId',
        'domainId',
      );

      const result = await builder.createEventFieldMap({ rules });
      const expected = { eoo: ['total'] };

      expect(result).toEqual(expected);
    });

    test('complex expressions', async () => {
      const builder = new MaterializedViewBuilder(
        'projectId',
        'databaseId',
        'providerServiceId',
        'domainId',
      );

      const rules = [
        {
          operator: 'or',
          expressions: [
            {
              operator: 'and',
              expressions: [
                {
                  type: 'order',
                  field: 'total',
                  operator: 'gte',
                  value: 49,
                },
              ],
            },
            {
              operator: 'and',
              expressions: [
                {
                  type: 'order',
                  field: 'total',
                  operator: 'gte',
                  value: 50,
                },
                {
                  type: 'pageView',
                  field: 'url',
                  operator: 'contains',
                  value: 'cart',
                },
              ],
            },
          ],
        },
        {
          operator: 'and',
          expressions: [
            {
              operator: 'or',
              expressions: [
                {
                  type: 'cartContent',
                  field: 'totalPrice',
                  operator: 'gte',
                  value: 99,
                },
                {
                  type: 'cartContent',
                  field: 'itemCount',
                  operator: 'gte',
                  value: 2,
                },
              ],
            },
          ],
        },
      ];

      const result = await builder.createEventFieldMap({ rules });

      const expected = {
        cartContent: ['totalPrice', 'itemCount'],
        order: ['total'],
        pageView: ['url'],
        eoo: ['total'],
      };
      expect(result).toEqual(expected);
    });

    test('adding default rules', async () => {
      mockAccountFind.mockResolvedValue({
        settings: {
          domains: [{ _id: 'domainId', shopId: 'example.myshopify.com' }],
          shops: [
            {
              type: 'shopify',
              myshopify_domain: 'example.myshopify.com',
            },
          ],
        },
      });

      const builder = new MaterializedViewBuilder(
        'projectId',
        'databaseId',
        'example.myshopify.com',
        'domainId',
      );

      const rules = [
        {
          operator: 'or',
          expressions: [
            {
              operator: 'and',
              expressions: [
                {
                  type: 'pageView',
                  field: 'url',
                  operator: 'contains',
                  value: 'cart',
                },
              ],
            },
          ],
        },
      ];

      const result = await builder.createEventFieldMap({ rules });

      const expected = { addToCart: ['quantity'], eoo: ['total'], pageView: ['url'] };

      expect(result).toEqual(expected);
    });

    test('add existence property for custom', async () => {
      mockAccountFind.mockResolvedValue({
        settings: {
          domains: [],
          shops: [],
        },
      });

      const builder = new MaterializedViewBuilder(
        'projectId',
        'databaseId',
        'example.com',
        'domainId',
      );

      const rules = [
        {
          operator: 'or',
          expressions: [
            {
              operator: 'and',
              expressions: [
                {
                  type: 'pageView',
                  field: 'url',
                  operator: 'contains',
                  value: 'cart',
                },
                {
                  type: 'custom:order',
                },
                {
                  type: 'custom:order',
                  field: 'total',
                  operator: 'greaterThan',
                  value: 10,
                },
              ],
            },
          ],
        },
      ];

      const result = await builder.createEventFieldMap({ rules });

      const expected = {
        pageView: ['url'],
        eoo: ['total'],
        'custom:order': ['customPropertyExists', 'total'],
      };

      expect(result).toEqual(expected);
    });
  });

  describe('getCreateQuery', () => {
    test('simple expression', async () => {
      const builder = new MaterializedViewBuilder(
        'projectId',
        'databaseId',
        'providerServiceId',
        'domainId',
      );

      const result = await builder.getCreateQuery({
        'custom:order': ['customPropertyExists', 'total'],
        order: ['total'],
      });
      const expected = `
        CREATE MATERIALIZED VIEW
        \`${builder.projectId}.behavioural_events_optimonk._goal_fields_${builder.uniqueIdentifier}\`
            PARTITION BY DATE(timestamp)
            OPTIONS (enable_refresh = FALSE) AS (
            SELECT
            timestamp,
            a.customerAddress as deviceId,
            IF(p.name = 'customPropertyExists', e.type, CONCAT(e.type, '_', p.name)) AS field,
            IF(p.value = 'customPropertyExists', null, p.value) AS value,
            ANY_VALUE(IF(c.name = 'visitorInExperiment', c.value, null)) as visitorInExperiment,
            ANY_VALUE(IF(c.name = 'deviceType', c.value, null)) as deviceType
          FROM
          \`${builder.projectId}.behavioural_events_optimonk.events_${builder.uniqueIdentifier}\` as be,
            UNNEST(be.events) as e,
            UNNEST(be.context) as c,
            UNNEST(be.addresses) as a,
            UNNEST ( CASE WHEN ARRAY_LENGTH(e.props) >= 1
            THEN ARRAY_CONCAT(e.props,[('customPropertyExists', 'customPropertyExists')])
             ELSE [('customPropertyExists', 'customPropertyExists')] END
           ) as p
          WHERE
            ((e.type = 'custom:order' AND p.name = 'customPropertyExists') OR CONCAT(e.type, '_', p.name) = 'custom:order_total' OR CONCAT(e.type, '_', p.name) = 'order_total' OR CONCAT(e.type, '_', p.name) = 'eoi_variantId' OR CONCAT(e.type, '_', p.name) = 'eoi_campaignId' OR CONCAT(e.type, '_', p.name) = 'eoi_smartAbTest' OR CONCAT(e.type, '_', p.name) = 'splitURLTest_variantId')
            AND c.name IN('visitorInExperiment', 'deviceType')
            AND a.provider = 'optimonk' AND addressType = 'deviceId' AND a.customerAddress IS NOT NULL
            AND DATE(timestamp) >= DATE('2022-07-09T14:26:27.709Z')
          GROUP BY timestamp, deviceId, field, value
        )
      `;

      expect(result).normalizedEquals(expected);
    });

    test('duplicated fields in expressions', async () => {
      const builder = new MaterializedViewBuilder(
        'projectId',
        'databaseId',
        'providerServiceId',
        'domainId',
      );

      const result = await builder.getCreateQuery({ order: ['total'] });

      const expected = `
        CREATE MATERIALIZED VIEW
        \`${builder.projectId}.behavioural_events_optimonk._goal_fields_${builder.uniqueIdentifier}\`
           PARTITION BY DATE(timestamp)
           OPTIONS (enable_refresh = FALSE) AS (
            SELECT
            timestamp,
            a.customerAddress as deviceId,
            IF(p.name = 'customPropertyExists', e.type, CONCAT(e.type, '_', p.name)) AS field,
            IF(p.value = 'customPropertyExists', null, p.value) AS value,
            ANY_VALUE(IF(c.name = 'visitorInExperiment', c.value, null)) as visitorInExperiment,
            ANY_VALUE(IF(c.name = 'deviceType', c.value, null)) as deviceType
          FROM
          \`${builder.projectId}.behavioural_events_optimonk.events_${builder.uniqueIdentifier}\` as be,
            UNNEST(be.events) as e,
            UNNEST(be.context) as c,
            UNNEST(be.addresses) as a,
            UNNEST ( CASE WHEN ARRAY_LENGTH(e.props) >= 1
            THEN ARRAY_CONCAT(e.props,[('customPropertyExists', 'customPropertyExists')])
            ELSE [('customPropertyExists', 'customPropertyExists')] END
          ) as p
          WHERE
            (CONCAT(e.type, '_', p.name) = 'order_total' OR CONCAT(e.type, '_', p.name) = 'eoi_variantId' OR CONCAT(e.type, '_', p.name) = 'eoi_campaignId' OR CONCAT(e.type, '_', p.name) = 'eoi_smartAbTest' OR CONCAT(e.type, '_', p.name) = 'splitURLTest_variantId')
            AND c.name IN('visitorInExperiment', 'deviceType')
            AND a.provider = 'optimonk' AND addressType = 'deviceId' AND a.customerAddress IS NOT NULL
            AND DATE(timestamp) >= DATE('2022-07-09T14:26:27.709Z')
          GROUP BY timestamp, deviceId, field, value
        )
      `;

      expect(result).normalizedEquals(expected);
    });

    test('complex expressions', async () => {
      const builder = new MaterializedViewBuilder(
        'projectId',
        'databaseId',
        'providerServiceId',
        'domainId',
      );

      const result = await builder.getCreateQuery({
        order: ['total'],
        pageView: ['url'],
        cartContent: ['totalPrice', 'itemCount'],
      });
      const expected = `
        CREATE MATERIALIZED VIEW
        \`${builder.projectId}.behavioural_events_optimonk._goal_fields_${builder.uniqueIdentifier}\`
          PARTITION BY DATE(timestamp)
          OPTIONS (enable_refresh = FALSE) AS (
            SELECT
            timestamp,
            a.customerAddress as deviceId,
            IF(p.name = 'customPropertyExists', e.type, CONCAT(e.type, '_', p.name)) AS field,
            IF(p.value = 'customPropertyExists', null, p.value) AS value,
            ANY_VALUE(IF(c.name = 'visitorInExperiment', c.value, null)) as visitorInExperiment,
            ANY_VALUE(IF(c.name = 'deviceType', c.value, null)) as deviceType
          FROM
          \`${builder.projectId}.behavioural_events_optimonk.events_${builder.uniqueIdentifier}\` as be,
            UNNEST(be.events) as e,
            UNNEST(be.context) as c,
            UNNEST(be.addresses) as a,
            UNNEST ( CASE WHEN ARRAY_LENGTH(e.props) >= 1
            THEN ARRAY_CONCAT(e.props,[('customPropertyExists', 'customPropertyExists')])
            ELSE [('customPropertyExists', 'customPropertyExists')] END
          ) as p
          WHERE
             (CONCAT(e.type, '_', p.name) = 'order_total' OR
              CONCAT(e.type, '_', p.name) = 'pageView_url' OR
               CONCAT(e.type, '_', p.name) = 'cartContent_totalPrice' OR
                CONCAT(e.type, '_', p.name) = 'cartContent_itemCount' OR
                 CONCAT(e.type, '_', p.name) = 'eoi_variantId' OR CONCAT(e.type, '_', p.name) = 'eoi_campaignId' OR CONCAT(e.type, '_', p.name) = 'eoi_smartAbTest' OR CONCAT(e.type, '_', p.name) = 'splitURLTest_variantId')
                 AND c.name IN('visitorInExperiment', 'deviceType')
                 AND a.provider = 'optimonk' AND addressType = 'deviceId' AND a.customerAddress IS NOT NULL
                 AND DATE(timestamp) >= DATE('2022-07-09T14:26:27.709Z')
          GROUP BY timestamp, deviceId, field, value
        )
      `;

      expect(result).normalizedEquals(expected);
    });

    test('empty eventFieldMap', async () => {
      const builder = new MaterializedViewBuilder(
        'projectId',
        'databaseId',
        'providerServiceId',
        'domainId',
      );

      const eventFieldMap = {};

      await expect(builder.getCreateQuery(eventFieldMap)).rejects.toThrowError('Empty expressions');
    });

    test('empty field list', async () => {
      const builder = new MaterializedViewBuilder(
        'projectId',
        'databaseId',
        'providerServiceId',
        'domainId',
      );

      const eventFieldMap = { addToCart: [] };

      await expect(builder.getCreateQuery(eventFieldMap)).rejects.toThrowError('Empty expressions');
    });

    test('refresh view', () => {
      const builder = new MaterializedViewBuilder(
        'projectId',
        'databaseId',
        'providerServiceId',
        'domainId',
      );
      const result = builder.getRefreshViewQuery();
      const expected = `
          CALL BQ.REFRESH_MATERIALIZED_VIEW('${builder.projectId}.behavioural_events_optimonk._goal_fields_${builder.uniqueIdentifier}');
    `;

      expect(result).normalizedEquals(expected);
    });
  });
});
