const {
  Query: { getDomain },
} = require('../../account/account.resolvers');
const { MaterializedViewBuilder } = require('./materialized-view-builder');
const { BigQueryClient } = require('../../../services/bigQueryAdapter');
const log = require('../../../logger');

const BQ_PROJECT_ID = process.env.gcp_bigquery_project_id;
const { model: GoalViewsModel } = require('../goalViews.model');
const { determineDomainProviderServiceId } = require('../../../util/domain');

const refreshMatView = async ({ domainId, userId: databaseId }) => {
  const domain = await getDomain(null, { domainId }, { userId: databaseId });
  const providerServiceId = determineDomainProviderServiceId({ domain });
  const bigQueryClient = new BigQueryClient();
  const builder = new MaterializedViewBuilder(
    BQ_PROJECT_ID,
    databaseId,
    providerServiceId,
    domainId,
  );

  const view = await GoalViewsModel.findOne({ accountId: databaseId, domainId });
  const matViewExists = view?.eventFieldMap && Object.keys(view.eventFieldMap).length > 0;
  if (matViewExists) {
    const refreshViewQuery = builder.getRefreshViewQuery();
    await bigQueryClient.runQuery(refreshViewQuery, { location: 'US' });
  } else {
    log.warn(
      { domainId, databaseId },
      'Materialized view does not exist yet for domain, cannot refresh',
    );
  }
};

module.exports = { refreshMatView };
