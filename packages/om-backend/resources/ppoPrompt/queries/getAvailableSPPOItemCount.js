const {
  getFormattedTableName,
  getEventTableId,
  getProductTableId,
  blackListedProductsTableSchema,
  createTablesIfNotExist,
  scraperExternalDataSchema,
} = require('@om/queries');
const { mostPopularProductsCountQuery, mostPopularPagesCountQuery } = require('@om/workflow-sppo');
const { hash } = require('@om/schema/src/utils');
const { daysFromNow } = require('@om/common');
const { BigQueryClient } = require('../../../services/bigQueryAdapter');
const { model: AccountModel } = require('../../account/account.model');
const {
  domainAndShopByDomainId,
  determineDomainProviderServiceId,
} = require('../../../util/domain');
const { handleNotFoundDomain } = require('../helpers/error');
const { getDefaultShopLocale } = require('../../../helpers/defaultShopLocale');

const bigQueryClient = new BigQueryClient();
const MAX_NUMBER_OF_ITEMS = 500000;
const BQ_PROJECT_ID = process.env.gcp_bigquery_project_id;

const getAvailableSPPOItemCount = async (
  _,
  { domainId, useExternalData },
  { userId: databaseId, log },
) => {
  const account = await AccountModel.findOne(
    { databaseId },
    { 'settings.shops': 1, 'settings.domains': 1 },
  );

  const {
    settings: { shops, domains },
  } = account;

  const { domain, shop } = domainAndShopByDomainId(domainId, { shops, domains });
  handleNotFoundDomain(domain);

  const providerServiceId = determineDomainProviderServiceId({ domain, shop });

  const locale = await getDefaultShopLocale({ databaseId, providerServiceId });

  try {
    const eventTableId = getFormattedTableName(
      `${BQ_PROJECT_ID}.${getEventTableId(databaseId, providerServiceId)}`,
    );

    const productTableId = getFormattedTableName(
      `${BQ_PROJECT_ID}.${getProductTableId(databaseId, providerServiceId, shop?.type)}`,
    );
    const datasetId = 'smart_ppo';
    const blackListTableId = `blacklisted_products_${databaseId}_${hash(providerServiceId)}`;
    const blackListedProductsTableId = getFormattedTableName(
      `${BQ_PROJECT_ID}.${datasetId}.${blackListTableId}`,
    );
    const scraperExternalDataTableId = `scraper_external_data_${databaseId}_${hash(
      providerServiceId,
    )}`;
    const scraperExternalDataTable = getFormattedTableName(
      `${BQ_PROJECT_ID}.${datasetId}.${scraperExternalDataTableId}`,
    );

    await createTablesIfNotExist({
      bigQueryClient: bigQueryClient.client,
      createConfigs: [
        {
          datasetId,
          tableId: blackListTableId,
          schema: blackListedProductsTableSchema,
        },
        {
          datasetId,
          tableId: scraperExternalDataTableId,
          schema: scraperExternalDataSchema,
          partitionOptions: {
            field: 'timestamp',
            type: 'DAY',
            requirePartitionFilter: true,
          },
        },
      ],
    });

    if (useExternalData) {
      const [pagesCount] = await bigQueryClient.runQuery(
        mostPopularPagesCountQuery(
          eventTableId,
          blackListedProductsTableId,
          scraperExternalDataTable,
        ),
        {
          location: 'US',
          params: {
            fromDate: daysFromNow(30),
            numberOfItems: MAX_NUMBER_OF_ITEMS,
          },
        },
      );

      return pagesCount.count;
    }

    const [productsCount] = await bigQueryClient.runQuery(
      mostPopularProductsCountQuery(
        eventTableId,
        productTableId,
        blackListedProductsTableId,
        scraperExternalDataTable,
        shop?.type,
        databaseId,
        providerServiceId,
        locale,
      ),
      {
        location: 'US',
        params: {
          fromDate: daysFromNow(30),
          numberOfProducts: MAX_NUMBER_OF_ITEMS,
        },
      },
    );

    return productsCount.count;
  } catch (error) {
    log.error(error, {
      databaseId,
      domainId,
      message: 'Failed to fetch available products count.',
    });

    throw error;
  }
};

module.exports = {
  getAvailableSPPOItemCount,
};
