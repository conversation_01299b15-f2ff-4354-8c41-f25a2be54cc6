const { stringify } = require('csv-stringify/sync');

const {
  getProductTableId,
  getEventTableId,
  getFormattedTableName,
  createTablesIfNotExist,
  blackListedProductsTableSchema,
  scraperExternalDataSchema,
} = require('@om/queries');

const { hash } = require('@om/schema/src/utils');
const { model: AccountModel } = require('../../account/account.model');
const { handleNotFoundDomain } = require('../helpers/error');
const {
  domainAndShopByDomainId,
  determineDomainProviderServiceId,
} = require('../../../util/domain');
const { BigQueryClient, PROJECT_ID } = require('../../../services/bigQueryAdapter');
const { exportPageSample, exportProductSample } = require('../helpers/exportTopHandler');
const { getDefaultShopLocale } = require('../../../helpers/defaultShopLocale');

const exportTopSample = async (
  _,
  { domainId, numberOfItems = 100, useExternalData = false },
  { userId: databaseId },
) => {
  const account = await AccountModel.findOne(
    { databaseId },
    { 'settings.shops': 1, 'settings.domains': 1 },
  );

  const {
    settings: { shops, domains },
  } = account;

  const { domain, shop } = domainAndShopByDomainId(domainId, { shops, domains });

  handleNotFoundDomain(domain);

  const providerServiceId = determineDomainProviderServiceId({ domain, shop });

  const locale = await getDefaultShopLocale({ databaseId, providerServiceId });

  const bigQueryClient = new BigQueryClient();

  const eventTableId = getFormattedTableName(
    `${PROJECT_ID}.${getEventTableId(databaseId, providerServiceId)}`,
  );

  const productTableId = getFormattedTableName(
    `${PROJECT_ID}.${getProductTableId(databaseId, providerServiceId, shop?.type)}`,
  );

  const datasetId = 'smart_ppo';
  const blackListTableId = `blacklisted_products_${databaseId}_${hash(providerServiceId)}`;
  const blackListedProductsTableId = getFormattedTableName(
    `${PROJECT_ID}.${datasetId}.${blackListTableId}`,
  );
  const scraperExternalDataTableId = `scraper_external_data_${databaseId}_${hash(
    providerServiceId,
  )}`;
  const scraperExternalDataTable = getFormattedTableName(
    `${PROJECT_ID}.${datasetId}.${scraperExternalDataTableId}`,
  );

  await createTablesIfNotExist({
    bigQueryClient: bigQueryClient.client,
    createConfigs: [
      {
        datasetId,
        tableId: blackListTableId,
        schema: blackListedProductsTableSchema,
      },
      {
        datasetId,
        tableId: scraperExternalDataTableId,
        schema: scraperExternalDataSchema,
        partitionOptions: {
          field: 'timestamp',
          type: 'DAY',
          requirePartitionFilter: true,
        },
      },
    ],
  });

  const sample = useExternalData
    ? await exportPageSample({
        bigQueryClient,
        eventTableId,
        blackListedProductsTableId,
        scraperExternalDataTableId: scraperExternalDataTable,
        numberOfItems,
      })
    : await exportProductSample({
        bigQueryClient,
        databaseId,
        providerServiceId,
        eventTableId,
        productTableId,
        blackListedProductsTableId,
        scraperExternalDataTableId: scraperExternalDataTable,
        numberOfItems,
        domain,
        shop,
        locale,
      });

  return stringify(sample);
};

module.exports = {
  exportTopSample,
  exportPageSample,
};
