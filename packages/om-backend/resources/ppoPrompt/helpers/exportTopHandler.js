const {
  mostPopularProductsQuery,
  mapMostPopularProduct,
  mostPopularPagesQuery,
  cleanProduct,
} = require('@om/workflow-sppo');
const {
  getFormattedTableName,
  getLanguagesTableId,
  getLanguages,
  resolveLanguageCodes,
} = require('@om/queries');
const { daysFromNow } = require('@om/common');
const { PROJECT_ID } = require('../../../services/bigQueryAdapter');

const exportProductSample = async ({
  bigQueryClient,
  eventTableId,
  productTableId,
  blackListedProductsTableId,
  scraperExternalDataTableId,
  numberOfItems,
  databaseId,
  providerServiceId,
  domain,
  shop,
  locale,
}) => {
  const mostPopularProducts = await bigQueryClient.runQuery(
    mostPopularProductsQuery({
      databaseId,
      providerServiceId,
      eventTableId,
      productTableId,
      blackListedProductsTableId,
      scraperExternalDataTableId,
      platform: shop?.type,
      locale,
    }),
    {
      location: 'US',
      params: {
        fromDate: daysFromNow(30),
        numberOfProducts: numberOfItems,
      },
    },
  );
  const languagesTableId = getFormattedTableName(
    `${PROJECT_ID}.${getLanguagesTableId(databaseId, providerServiceId)}`,
  );
  let languages = [];
  const isShoprenter = domain.platform === 'shoprenter';

  if (isShoprenter) {
    languages = await getLanguages({
      bigQueryClient: bigQueryClient.client,
      languagesTableId,
      location: 'US',
    });
  }

  const headers = ['ID'];
  const langCodes = languages.map((lang) => lang.properties.code);
  const nameHeaders = ['Name'];
  const descriptionHeaders = ['Description'];
  const shortDescriptionHeaders = ['ShortDescription'];

  const data = [headers];
  langCodes.forEach((code) => {
    nameHeaders.push(`Name.${code}`);
    descriptionHeaders.push(`Description.${code}`);
    shortDescriptionHeaders.push(`ShortDescription.${code}`);
  });

  const cleanedProducts = [];

  headers.push(
    ...nameHeaders,
    ...descriptionHeaders,
    ...(isShoprenter ? shortDescriptionHeaders : []),
    'URL',
  );
  mostPopularProducts.forEach((item) => {
    let product = mapMostPopularProduct(item, shop);
    if (domain.platform === 'shoprenter') {
      product = resolveLanguageCodes(product, languages);
    }
    const cleanedProduct = cleanProduct(product);
    cleanedProducts.push(cleanedProduct);
    const externalPropertiesInProduct = Object.keys(cleanedProduct).filter((key) =>
      key.startsWith('ext_'),
    );
    const missingExternalPropKeys = externalPropertiesInProduct.filter(
      (key) => !headers.some((header) => header === key),
    );
    headers.push(...missingExternalPropKeys);
  });

  const externalPropertiesKeys = headers.filter((header) => header.startsWith('ext_'));

  cleanedProducts.forEach((cleanedProduct) => {
    let row = [
      cleanedProduct.id,
      cleanedProduct.name,
      ...langCodes.map((code) => cleanedProduct[`name.${code}`]),
      cleanedProduct.description,
      ...langCodes.map((code) => cleanedProduct[`description.${code}`]),
      ...(isShoprenter
        ? [
            cleanedProduct.shortDescription,
            ...langCodes.map((code) => cleanedProduct[`shortDescription.${code}`]),
          ]
        : []),
      cleanedProduct.url,
      ...externalPropertiesKeys.map((key) => cleanedProduct[key] || ''),
    ];

    data.push(row);
  });

  return data;
};

const exportPageSample = async ({
  bigQueryClient,
  eventTableId,
  blackListedProductsTableId,
  scraperExternalDataTableId,
  numberOfItems,
}) => {
  const mostPopularPages = await bigQueryClient.runQuery(
    mostPopularPagesQuery({ eventTableId, blackListedProductsTableId, scraperExternalDataTableId }),
    {
      location: 'US',
      params: {
        fromDate: daysFromNow(30),
        numberOfItems,
      },
    },
  );

  const headers = ['canonicalURL'];
  const mappedMostPopularPages = [];

  mostPopularPages.forEach((page) => {
    const mappedProps = page.properties.reduce((props, prop) => {
      props[prop.name] = prop.value;
      return props;
    }, {});
    const propsInPage = Object.keys(mappedProps).filter((key) => key !== 'url');
    const missingKeys = propsInPage.filter((key) => !headers.some((header) => header === key));
    headers.push(...missingKeys);

    mappedMostPopularPages.push({ canonicalURL: page.url, ...mappedProps });
  });

  const rows = mappedMostPopularPages.map((page) => {
    return [...headers.map((key) => page[key] || '')];
  });
  return [headers, ...rows];
};

module.exports = {
  exportProductSample,
  exportPageSample,
};
