const { isUnifiedProductBased } = require('../helpers/unifiedProductBased');
const { purifyDomain } = require('./domainHelper');

class NoProviderServiceIdError extends Error {
  constructor(message) {
    super(message);
    this.name = 'NoProviderServiceIdError';
  }
}

const determineDomainProviderServiceId = ({ domain, shop }) => {
  const possiblyProviderServiceId =
    domain?.providerServiceIdOverride ||
    domain?.shopId ||
    shop?.shopname ||
    shop?.myshopify_domain ||
    domain?.domain;
  if (possiblyProviderServiceId) {
    return purifyDomain(possiblyProviderServiceId);
  }
  throw new NoProviderServiceIdError(
    `Could not determine domain provider service id, ${JSON.stringify({ domain, shop })}`,
  );
};

const domainAndShopByDomainId = (domainId, { shops, domains }) => {
  const domain = domains.find((domain) => `${domain._id}` === `${domainId}`);

  if (!domain) return {};

  const pureDomain = purifyDomain(domain.domain);
  const shop = shops.find((shop) => {
    if (shop.type === 'shoprenter') {
      if (domain.shopId && shop.shopname === domain.shopId) {
        return true;
      }

      if (shop.domains && shop.domains.length) {
        return shop.domains.some((srDomain) => purifyDomain(srDomain.domain) === pureDomain);
      }
    }
    if (shop.type === 'shopify') {
      if (domain.shopId && shop.myshopify_domain === domain.shopId) {
        return true;
      }

      return purifyDomain(shop.live_domain) === pureDomain || shop.myshopify_domain === pureDomain;
    }
    // [product-sync][isUnified]
    if (isUnifiedProductBased({ shop })) {
      return shop.shopId && domain.shopId === shop.shopId;
    }

    return false;
  });

  return { shop, domain };
};

const domainAndShopByProviderServiceId = (providerServiceId, { shops, domains }) => {
  for (const currentDomain of domains) {
    const { shop, domain } = domainAndShopByDomainId(currentDomain._id, { shops, domains });
    const id = determineDomainProviderServiceId({ domain, shop });
    if (id === providerServiceId) {
      return { shop, domain };
    }
  }
  return {};
};

module.exports = {
  determineDomainProviderServiceId,
  domainAndShopByDomainId,
  domainAndShopByProviderServiceId,
  NoProviderServiceIdError,
};
