const { domainAndShopByDomainId, domainAndShopByProviderServiceId } = require('./domain');

describe('domainAndShopByDomainId', () => {
  const mockDomain = {
    _id: 'mockDomainId',
    domain: 'myshop.com',
  };

  const mockShop = {
    type: 'shoprenter',
    domains: [{ domain: 'myshop.com' }],
    shopname: 'myshop',
  };

  const mockData = {
    shops: [mockShop],
    domains: [mockDomain],
  };

  it('finds shop and domain by domainId for shoprenter type', () => {
    const result = domainAndShopByDomainId('mockDomainId', mockData);
    expect(result.shop).toEqual(mockShop);
    expect(result.domain).toEqual(mockDomain);
  });

  it('returns empty objects when domain is not found', () => {
    const result = domainAndShopByDomainId('nonExistentDomainId', mockData);
    expect(result.shop).toEqual(undefined);
    expect(result.domain).toEqual(undefined);
  });

  it('returns empty objects when shop is not found for shoprenter type', () => {
    const result = domainAndShopByDomainId('mockDomainId', {
      ...mockData,
      shops: [],
    });
    expect(result.shop).toEqual(undefined);
    expect(result.domain).toEqual(mockDomain);
  });

  it('returns empty objects when shop type is not shopify or shoprenter', () => {
    const invalidShop = {
      type: 'unknown',
      live_domain: 'random-domain.com',
    };
    const result = domainAndShopByDomainId('mockDomainId', {
      shops: [invalidShop],
      domains: [mockDomain],
    });
    expect(result.shop).toEqual(undefined);
    expect(result.domain).toEqual(mockDomain);
  });

  it('finds shop and domain by domainId for shopify type when shop has different live_domain', () => {
    const shopifyShop = {
      type: 'shopify',
      live_domain: 'myshop.com',
      myshopify_domain: 'myshop.myshopify.com',
    };
    const result = domainAndShopByDomainId('mockDomainId', {
      shops: [shopifyShop],
      domains: [mockDomain],
    });
    expect(result.shop).toEqual(shopifyShop);
    expect(result.domain).toEqual(mockDomain);
  });

  it('finds shop and domain by domainId for shopify type when shop has myshopify domain', () => {
    const shopifyShop = {
      type: 'shopify',
      live_domain: 'myshop.myshopify.com',
      myshopify_domain: 'myshop.myshopify.com',
    };
    const shopifyDomain = {
      _id: 'mockShopifyDomainId',
      domain: 'myshop.myshopify.com',
    };
    const result = domainAndShopByDomainId('mockShopifyDomainId', {
      shops: [shopifyShop],
      domains: [shopifyDomain],
    });
    expect(result.shop).toEqual(shopifyShop);
    expect(result.domain).toEqual({
      _id: 'mockShopifyDomainId',
      domain: 'myshop.myshopify.com',
    });
  });

  it('finds shop and domain by domainId for unified type', () => {
    const mockDomain = {
      _id: 'mockDomainId',
      domain: 'myshop.com',
      shopId: 'myshop',
    };

    const mockShop = {
      type: 'unas',
      domains: [{ domain: 'myshop.com' }],
      shopId: 'myshop',
    };

    const mockData = {
      shops: [mockShop],
      domains: [mockDomain],
    };

    const result = domainAndShopByDomainId('mockDomainId', mockData);
    expect(result.shop).toEqual(mockShop);
    expect(result.domain).toEqual(mockDomain);
  });
});

describe('domainAndShopByProviderServiceId', () => {
  it('finds shop and domain by providerServiceId for unified type - shopId', () => {
    const mockDomain = {
      _id: 'mockDomainId1',
      domain: 'myshop.com',
      shopId: 'myshop',
    };

    const mockShop = {
      type: 'unas',
      domains: [{ domain: 'myshop.com' }],
      shopId: 'myshop',
    };

    const mockData = {
      shops: [
        {
          type: 'unas',
          domains: [{ domain: 'myshop.com' }],
          shopId: 'oldshop',
        },
        mockShop,
      ],
      domains: [
        {
          _id: 'mockDomainId2',
          domain: 'myshop.com',
          shopId: 'oldshop',
        },
        mockDomain,
      ],
    };

    const result = domainAndShopByProviderServiceId('myshop', mockData);
    expect(result.shop).toEqual(mockShop);
    expect(result.domain).toEqual(mockDomain);
  });

  it('finds shop and domain by providerServiceId for unified type - providerServiceIdOverride', () => {
    const mockDomain = {
      _id: 'mockDomainId',
      domain: 'myshop.com',
      shopId: 'myshop',
      providerServiceIdOverride: 'myshop-override',
    };

    const mockShop = {
      type: 'unas',
      domains: [{ domain: 'myshop.com' }],
      shopId: 'myshop',
    };

    const mockData = {
      shops: [
        {
          type: 'unas',
          domains: [{ domain: 'myshop.com' }],
          shopId: 'oldshop',
        },
        mockShop,
      ],
      domains: [
        {
          _id: 'mockDomainId2',
          domain: 'myshop.com',
          shopId: 'oldshop',
        },
        mockDomain,
      ],
    };

    const result = domainAndShopByProviderServiceId('myshop-override', mockData);
    expect(result.shop).toEqual(mockShop);
    expect(result.domain).toEqual(mockDomain);
  });
});
