const sharp = require('sharp');
const log = require('../logger');

sharp.cache(false);

class ImageProcessorJob {
  constructor(buffer) {
    this.image = sharp(buffer);
    this._meta = null;
  }

  async metadata(force = false) {
    if (!force && this._meta) {
      return this._meta;
    }

    // eslint-disable-next-line
    return (this._meta = await this.image.metadata());
  }

  async fixOrientation() {
    const meta = await this.metadata();

    if (meta.orientation !== 1) {
      // Fix orientation of image based on EXIF data
      this.image.rotate();
      log.debug('orientation of image will be fixed');
    }
  }

  async resize(width, height) {
    log.debug('resizing image to ', { width, height });
    this.image.resize(width, height, {
      fit: 'outside',
    });
  }

  async crop(left, top, width, height) {
    const { width: imgWidth, height: imgHeight } = await this.metadata();
    let right = 0;
    let bottom = 0;

    if (left + width > imgWidth) {
      right = imgWidth - width - left;
    }

    if (top + height > imgHeight) {
      bottom = imgHeight - height - top;
    }

    if (left < 0 || top < 0 || right < 0 || bottom < 0) {
      await this.extend(
        left < 0 ? Math.abs(left) : 0,
        top < 0 ? Math.abs(top) : 0,
        right < 0 ? Math.abs(right) : 0,
        bottom < 0 ? Math.abs(bottom) : 0,
      );

      if (left < 0) {
        width += left;
        left = 0;
      }
      if (top < 0) {
        height += top;
        top = 0;
      }
      if (right < 0) {
        width += right;
        right = 0;
      }
      if (bottom < 0) {
        height += bottom;
        bottom = 0;
      }
    }

    this.image.extract({
      left,
      top,
      width,
      height,
    });
  }

  async rotate(angle = undefined, background = { r: 0, g: 0, b: 0, alpha: 0 }) {
    log.debug('rotating image by', angle, 'degrees');
    this.image = sharp(await this.image.rotate(angle, { background }).toBuffer());
    // need to reset cached meta in order to get real width and height after rotation
    this._meta = null;
  }

  async extend(left, top, right, bottom, background = { r: 0, g: 0, b: 0, alpha: 0 }) {
    log.debug('extending image by', { left, top, right, bottom, background });
    this.image.extend({
      top,
      left,
      right,
      bottom,
      background,
    });
  }

  async optimize(quality, outputFormat) {
    const { format: inputFormat } = await this.metadata();
    let format = inputFormat;

    if (outputFormat) {
      format = outputFormat;
    }

    if (['svg', 'webp', 'gif', 'heif'].includes(format)) {
      log.debug(`Output format will be 'png' instead of requested '${format}'`);
      format = 'png';
    }

    if (quality < 100) {
      log.debug(`applying lossy image optimization`, {
        quality,
        inputFormat,
        outputFormat: format,
      });
    }
    switch (format) {
      case 'jpeg':
        this.image.jpeg({
          quality,
          progressive: true,
          trellisQuantisation: true,
        });
        break;
      case 'png':
        this.image.png({
          quality,
          progressive: true,
          palette: true,
        });
        break;
      default:
        throw new Error(`Output image format is not supported: ${format}`);
    }
  }

  async flip() {
    this.image = sharp(await this.image.flip().toBuffer());
  }

  async flop() {
    this.image = sharp(await this.image.flop().toBuffer());
  }

  async trim() {
    log.debug(`trimming image`);
    this.image = sharp(await this.image.trim().toBuffer());
  }

  async getResult() {
    const { data, info: output } = await this.image.toBuffer({
      resolveWithObject: true,
    });

    return {
      data,
      meta: {
        input: await this.metadata(),
        output,
      },
    };
  }
}

module.exports = ImageProcessorJob;
