const ImageProcessorJob = require('./job');

class ImageProcessor {
  async process(input, trim = false) {
    const job = new ImageProcessorJob(input.getBuffer());

    await job.fixOrientation();

    if (input.quality || input.format) {
      await job.optimize(input.quality, input.format);
    }

    if (input.scale) {
      const { x, y } = input.scale;

      if (x === -1) {
        await job.flop();
      }

      if (y === -1) {
        await job.flip();
      }
    }

    if (input.rotate) {
      await job.rotate(input.rotate);
    }

    if (input.crop) {
      const { left, top, width, height } = input.crop;

      await job.crop(left, top, width, height);
    }

    if (input.resize) {
      const { width, height } = input.resize;

      await job.resize(width, height);
    }

    if (trim) {
      await job.trim();
    }

    return job.getResult();
  }
}

module.exports = ImageProcessor;
