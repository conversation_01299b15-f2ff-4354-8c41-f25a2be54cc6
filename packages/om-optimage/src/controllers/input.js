const router = require('express').Router();

const inputParsers = require('../middlewares/input');
const imageProcessor = require('../services/imageProcessor');
const updateStats = require('../middlewares/stats');

router.use(updateStats);

router.post('/', inputParsers, async (req, res) => {
  if (req.input) {
    try {
      const {
        data,
        meta: { output },
      } = await imageProcessor.process(req.input, req.query.trim === '1');

      req.log.info({}, 'image processed');

      res.output = output;

      res.contentType(output.format);
      res.setHeader('X-Image-Width', output.width);
      res.setHeader('X-Image-Height', output.height);
      res.write(data);
      res.end();
    } catch (err) {
      req.log.error(err, 'error while processing input image');
      return res
        .status(500)
        .json({
          status: 500,
          error: `Something went wrong on our side while processing image`,
        })
        .end();
    }
  }
});

module.exports = router;
