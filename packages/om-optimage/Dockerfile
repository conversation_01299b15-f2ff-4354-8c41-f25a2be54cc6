# syntax=docker/dockerfile:1.4
FROM node:22-alpine as build
WORKDIR /app

# Install libvips and build dependencies
RUN apk add --no-cache \
    vips-dev \
    build-base \
    python3 \
    make \
    g++

COPY package.json yarn.lock .yarnrc.yml ./
COPY .yarn .yarn
# Dependencies
COPY packages/om-optimage/package.json ./packages/om-optimage/
ENV CI=true
RUN --mount=type=cache,target=/.yarn YARN_CACHE_FOLDER=/.yarn yarn workspaces focus om-optimage --production
COPY packages/om-optimage/ ./packages/om-optimage/

FROM node:22-alpine
WORKDIR /app

# Install libvips runtime dependencies
RUN apk add --no-cache vips

COPY --from=build /app /app
WORKDIR /app/packages/om-optimage
CMD [ "yarn", "run", "start" ]
