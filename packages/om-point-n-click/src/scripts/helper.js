import { getSelectorPath } from '@dom-utils/selector-path';
import { ChangeBuilder } from '@om/change-builder';
import {
  LS_SETTING_KEY,
  AUTH_HEADER_NAME,
  RESTORE_AUTH_HEADER_NAME,
  EDITABLE_TAGS,
} from './constants';
import { DOM } from './services/actions/helper/DOM';
import storage from './storage';

export const findParentDiv = (event, tagNames) => {
  let current = event;
  while (current && !tagNames.includes(current.tagName)) {
    current = current.parentNode;
  }
  return current;
};

export const isEventAboveIgnoreLine = (event) => {
  const { height: ignoreBelowLine } = document
    .querySelector('.om-web-selector-navbar')
    .getBoundingClientRect();
  return event.clientY <= ignoreBelowLine;
};

export const hasSomeParentTheClass = (element, className) => {
  if (element.classList.contains(className)) return true;
  return element.parentElement && hasSomeParentTheClass(element.parentElement, className);
};

export const checkMissingToken = () => {
  const token = storage.get(AUTH_HEADER_NAME) || storage.get(RESTORE_AUTH_HEADER_NAME);

  storage.set(RESTORE_AUTH_HEADER_NAME, token);
};

export const removeAuthToken = () => {
  storage.delete(AUTH_HEADER_NAME);
  storage.delete(RESTORE_AUTH_HEADER_NAME);
};

const getBaseAdminUrl = () => {
  let baseUrl = window.OptiMonkRegistry.beUrl;
  if (window.OptiMonkRegistry.beUrl.indexOf('http') !== 0) {
    baseUrl = `https:${baseUrl}`;
  }
  return new URL(`${baseUrl}/api/web-selector/back-to-admin`);
};

export const backToAdmin = (token, params) => {
  let returnUrl = '';
  let backUrl = '';
  let campaignId = '';
  let type = '';
  let variantId = '';
  let settings = storage.get(LS_SETTING_KEY);

  if (settings) {
    settings = JSON.parse(settings);
    returnUrl = settings.returnUrl;
    backUrl = settings.backUrl;
    campaignId = settings.campaignId;
    type = settings.type;
    variantId = settings.variantId;
  }
  storage.set(RESTORE_AUTH_HEADER_NAME, token);
  removeAuthToken();
  const url = getBaseAdminUrl();
  url.searchParams.append('token', token);

  if (params?.isNew) {
    url.searchParams.append('isNew', params.isNew);
  }

  if (params?.back && backUrl) {
    url.searchParams.append('location', backUrl);
  } else if (returnUrl) {
    if (settings.isNew === '1') {
      const urlParts = returnUrl.split('?');
      const newSettingsUrl = `${urlParts[0]}/new?${urlParts[1]}`;
      url.searchParams.append('location', newSettingsUrl);
    } else {
      url.searchParams.append('location', returnUrl);
    }
  }

  if (campaignId) {
    url.searchParams.append('campaignId', campaignId);
  }

  if (type) {
    url.searchParams.append('type', type);
  }

  if (variantId) {
    url.searchParams.append('variantId', variantId);
  }

  if (params?.beacon) {
    navigator.sendBeacon(url.origin + url.pathname, url.searchParams);
  }

  window.location = url.href;
};

export const getSettings = () => {
  const settingsStr = storage.get(LS_SETTING_KEY);
  if (settingsStr) {
    return JSON.parse(settingsStr);
  }
};

export const saveSelectedMode = (mode) => {
  const settingsStr = storage.get(LS_SETTING_KEY);
  let settings;

  if (settingsStr) {
    settings = JSON.parse(settingsStr);
    settings.selectedMode = mode;
  } else {
    settings = { selectedMode: mode };
  }
  storage.set(LS_SETTING_KEY, JSON.stringify(settings));
};

export const isElementMissing = (selector) => {
  return !DOM.getElement(selector);
};

export const isElementMissingById = (id) => {
  return !document.getElementById(id);
};

export const hasNotExistingElement = (selectors) => {
  return selectors.map((selector) => DOM.getElement(selector))?.some((elem) => !elem);
};

export const getFullPath = (element) => {
  const path = [];
  let currentElement = element;
  while (currentElement.tagName.toLowerCase() !== 'body') {
    path.unshift({
      tagname: currentElement.tagName.toLowerCase(),
      selector: getSelectorPath(currentElement),
      id: currentElement?.id,
      className: currentElement?.className,
    });
    currentElement = currentElement.parentElement;
  }
  path.unshift({ tagname: 'body', selector: 'body' });
  return path;
};

export const generateRandomString = (length = 10) => {
  return ChangeBuilder.generateId(length);
};

export const _clone = (param) => JSON.parse(JSON.stringify(param));

export const scrollToElement = (element) => {
  if (!element) return;

  const yOffset = -window.innerHeight / 2 + element.offsetHeight / 2;
  const y = element.getBoundingClientRect().top + window.pageYOffset + yOffset;
  window.scrollTo({ top: y, behavior: 'smooth' });
};

export const isProductPage = () => {
  return !!(
    window?.ShopRenter?.product?.id ||
    window?.ShopifyAnalytics?.meta?.product?.id ||
    window.UNAS?.shop?.product_id ||
    window.WooDataForOM?.product?.id
  );
};

export const getProductId = () => {
  return (
    window?.ShopRenter?.product?.id ||
    window?.ShopifyAnalytics?.meta?.product?.id ||
    window.UNAS?.shop?.product_id ||
    window.WooDataForOM?.product?.id?.toString()
  );
};

export const isEditableElement = (tagName) => {
  if (!tagName) return false;

  return EDITABLE_TAGS.includes(tagName.toUpperCase());
};

export const isOnlyTextDiv = (element) => {
  if (element.tagName !== 'DIV' || !element.innerText.trim().length) {
    return false;
  }

  return element.innerText === element.innerHTML;
};

export const getEditableElement = (e, target) => {
  const isEditable = isEditableElement(target.tagName);
  const targetTextContent = target.textContent.trim();
  const elementFromClickContent = document
    .elementFromPoint(e.clientX, e.clientY)
    .textContent.trim();

  const hasAfterAttribute = window.getComputedStyle(target, '::after').content !== 'none';

  if (isEditable && targetTextContent === elementFromClickContent && !hasAfterAttribute) {
    return target;
  }

  const elementsWithOutTarget = document
    .elementsFromPoint(e.clientX, e.clientY)
    .filter((element) => element !== target);

  const editableElement = elementsWithOutTarget.find((element) =>
    isEditableElement(element.tagName),
  );

  return editableElement || null;
};

const slugify = (str) => {
  return str
    .toLowerCase() // Convert to lowercase
    .trim() // Remove leading and trailing whitespace
    .replace(/(^\w+:|^)\/\//, '') // Remove protocol
    .replace(/[^a-z0-9 -]/g, '-') // Remove non-alphanumeric characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with a single hyphen
    .replace(/(?<=.)-$/, ''); // Replace traling slash, if there are other charecters before it (root filename will be -.json)
};

export const getPageIdentifier = async () => {
  const fullIdentifier =
    document.querySelector('link[rel="canonical"]')?.href || window.location.href;
  const pathIdentifier = new URL(fullIdentifier).pathname;
  return Promise.resolve(slugify(pathIdentifier));
};

export const purifyDomain = (domain) => {
  if (!domain) return domain;
  let lowerCase = domain.toLowerCase();
  if (lowerCase.startsWith('m.')) lowerCase = lowerCase.substring(2);
  else if (lowerCase.startsWith('www.')) lowerCase = lowerCase.substring(4);
  return lowerCase;
};
