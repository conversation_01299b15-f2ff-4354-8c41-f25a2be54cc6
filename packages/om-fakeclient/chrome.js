const puppeteer = require('puppeteer');
const { URL, URLSearchParams } = require('url');

const CLIENT_AUTH_USER = process.env.CLIENT_AUTH_USER;
const CLIENT_AUTH_PASS = process.env.CLIENT_AUTH_PASS;
const FRONT_URL = process.env.FRONT_URL;
const OLD_PAGE_IN_MS = 30000;
const log = require('./logger').child({
  chrome: { CLIENT_AUTH_USER, CLIENT_AUTH_PASS },
});

const SITEINFO_URL = `${FRONT_URL}/site-info.min.js`;

const blockPredicates = ['analytics.js', 'front.optimonk', 'googletagmanager', 'gtag/js'];

class Chrome {
  constructor(browser) {
    this.browser = browser;
  }

  async getPage(url) {
    const page = await this.browser.newPage();
    await page.goto(url);

    return page;
  }

  async getBodyTextContent(domain) {
    if (!domain.match('^http(s)?://')) {
      domain = `https://${domain}`;
    }

    const page = await this.browser.newPage();
    await page.goto(domain);

    const bodyTextContent = await page.evaluate(() => {
      return document.body.textContent;
    });

    return bodyTextContent;
  }

  async getPageContent(domain) {
    if (!domain.match('^http(s)?://')) {
      domain = `https://${domain}`;
    }

    const time = new Date().getTime();
    domain = `${domain}?time=${time}`;

    const page = await this.createPage(domain, false, undefined, undefined, true);

    const html = await page.content();

    page.close();

    return html;
  }

  async collectDomainData(domain, improved) {
    if (!domain.match('^http(s)?://')) {
      domain = `https://${domain}`;
    }

    const time = new Date().getTime();
    domain = `${domain}?time=${time}`;

    const logger = log.child({ service: 'domainCheck' });
    let hasGTM = false;

    logger.info(`Collect data from domain: ${domain}`);
    let siteResponse;

    const page = await new Promise(async (resolve, reject) => {
      const page = await this.browser.newPage();

      page.on('request', (request) => {
        if (request.url().indexOf('www.googletagmanager.com/gtm.js?id=') > -1) {
          hasGTM = true;
        }
      });

      try {
        let navWait;
        let funcWait;

        if (improved) {
          logger.info(`Improved site detection: ${domain}, setting up wait promises...`);
          navWait = page.waitForRequest(
            (req) => {
              const url = req.url();
              return url.includes(FRONT_URL) && url.includes('site-info');
            },
            { timeout: 15000 },
          );
          funcWait = page.waitForFunction(
            function () {
              return !!window?.OptiMonk?.Engine?.initialize;
            },
            { polling: 500, timeout: 20000 },
          );
        } else {
          navWait = page.waitForNavigation({ waitUntil: 'networkidle2' });
        }
        siteResponse = await page.goto(domain);

        if (improved) {
          logger.info('Adding siteinfo js injector');
          await page.addScriptTag({ url: SITEINFO_URL });
        }

        logger.info(`Go to domain: ${domain}`);
        if (improved) logger.info('Waiting for siteinfo request');
        await navWait;
        if (funcWait) {
          logger.info('Waiting for engine init implementation appear');
          await funcWait;
        }
        resolve(page);
      } catch (e) {
        try {
          if (e.message === 'net::ERR_TOO_MANY_REDIRECTS' && domain.startsWith('https://')) {
            const httpUrl = domain.replace('https://', 'http://');
            const navWait = page.waitForNavigation({ waitUntil: 'networkidle2' });
            siteResponse = page.goto(httpUrl);

            logger.info(`Retrying with domain with http: ${httpUrl}`);
            await navWait;
          } else {
            return reject(e);
          }
        } catch (e) {
          return reject(e);
        }
      }
    });

    return new Promise(async (resolve, reject) => {
      let om = false;
      let engine = '';
      let shopId = null;
      let isKlaviyoDetected = false;

      try {
        if (hasGTM) {
          await page.waitForTimeout(3000);
        }
        om = await page.evaluate('!!window["OptiMonkRegistry"] && OptiMonkRegistry.account');
        logger.info('Evaluate OptiMonk on global scope');
      } catch (e) {
        if (e.message.indexOf('Error: failed to find element matching selector') < 0) {
          logger.error({ message: 'Collect domain has optimonk', errorMessage: e.message });
        } else {
          logger.error({ errorMessage: e.message });
        }
      }

      if (!improved) {
        try {
          await page.addScriptTag({ url: SITEINFO_URL });
          logger.info('Adding front.optimonk.com preload script');
        } catch (e) {
          reject(e);
        }
      }

      try {
        const info = await page.evaluate('OptiMonk.Engine.initialize();OptiMonk.Engine.getInfo()');
        logger.info({
          message: 'Evaluate Engine scripts',
          info,
        });

        if (info) {
          if (info.type) {
            engine = info.type;
          }
          if (info.shopId) {
            shopId = info.shopId;
          }
        }

        const klaviyoInfo = await page.evaluate('OptiMonk.SiteInfo.getKlaviyoInfo()');

        logger.info({ domainCheck: { domain, klaviyoInfo }, message: 'Evaluate getKlaviyInfo' });

        if (klaviyoInfo) isKlaviyoDetected = klaviyoInfo.isKlaviyoDetected;
      } catch (e) {
        logger.error({
          message: 'Evaluate Engine scripts',
          error: e.message,
          stack: e.stack,
          err: e,
        });
      }

      if (engine === 'custom') {
        engine = siteResponse?.headers?.()?.['powered-by']?.toLowerCase?.() ?? engine;
      }

      logger.info({ om, engine, shopId, message: 'Domain check ended successfully' });
      resolve({ page, om, engine, shopId, isKlaviyoDetected });
    });
  }

  async createPage(url, internal = true, height = 1080, width = 1920, blocking = false) {
    return new Promise(async (resolve, reject) => {
      let assetCheckerInterval = null;

      try {
        const page = await this.browser.newPage();

        if (blocking) {
          page.setRequestInterception(true);
          page.on('request', (request) => {
            let blocked = false;
            blockPredicates.forEach((predicate) => {
              if (request.url().indexOf(predicate) > -1) {
                blocked = true;
                log.info('request blocked (matched %s) - %s', predicate, request.url());
              }
            });
            if (blocked) request.abort();
            else request.continue();
          });
        }

        await page.setViewport({
          width,
          height,
          // , deviceScaleFactor: 2
        });

        if (internal) {
          let pageLoaded = false;
          let frontOMCssLoaded = false;
          let fontRequest = 0;
          let fontResponse = 0;
          let imageRequest = 0;
          let imageResponse = 0;
          const fontRegex = /fonts\.gstatic\.com/i;
          const customFontRegex = /\/customFonts\//i;
          const isFontUrl = (url) => fontRegex.test(url) || customFontRegex.test(url);
          const imageRegex = /(\/userImages\/\d+\/)|(\/templates\/)/i;
          const isImageUrl = (url) => imageRegex.test(url);
          const frontOMCssRegex = /\/assets\/css\/om\.base\.css/i;
          const isFrontOMCssUrl = (url) => frontOMCssRegex.test(url);

          page.on('close', () => {
            if (assetCheckerInterval) {
              clearInterval(assetCheckerInterval);
              assetCheckerInterval = null;

              // Will only reject the promise if it wasn't resolved before
              return reject(new Error('Page was closed while waiting for fonts or images...'));
            }

            reject(new Error('Page was closed'));
          });
          page.on('request', (request) => {
            if (isFontUrl(request.url())) {
              ++fontRequest;
              log.info('font request started - %s', request.url());
            } else if (isImageUrl(request.url())) {
              ++imageRequest;
              log.info('image request started - %s', request.url());
            }
          });
          page.on('requestfailed', (request) => {
            const response = request.response();

            log.warn('##requestfailed', request.url(), request.failure().errorText);

            if (isFontUrl(request.url())) {
              ++fontResponse;
              log.info(
                { url: request.url },
                'font request failed (status: %s) - started: %d, finished: %d - %s',
                (response && response.status()) || 'N/A',
                fontRequest,
                fontResponse,
                request.url(),
              );
            } else if (isImageUrl(request.url())) {
              ++imageResponse;
              log.info(
                { url: request.url },
                'image request failed (status: %s) - started: %d, finished: %d - %s',
                (response && response.status()) || 'N/A',
                imageRequest,
                imageResponse,
                request.url(),
              );
            }
          });
          page.on('requestfinished', (request) => {
            const response = request.response();

            if (isFontUrl(request.url())) {
              ++fontResponse;
              log.info(
                'font request finished (status: %s) - started: %d, finished: %d - %s',
                (response && response.status()) || 'N/A',
                fontRequest,
                fontResponse,
                request.url(),
              );
            } else if (isImageUrl(request.url())) {
              ++imageResponse;
              log.info(
                'image request finished (status: %s) - started: %d, finished: %d - %s',
                (response && response.status()) || 'N/A',
                imageRequest,
                imageResponse,
                request.url(),
              );
            } else if (isFrontOMCssUrl(request.url())) {
              frontOMCssLoaded = true;

              log.info('Front om.base.css loaded %s', request.url());
            }
          });
          page.on('console', (message) =>
            log.info(`###log ${message.type().substr(0, 3).toUpperCase()} ${message.text()}`),
          );
          page.on('pageerror', (err) => log.warn('Page error ocurred:', err.message));

          if (CLIENT_AUTH_USER && CLIENT_AUTH_PASS) {
            log.info('basic auth with user', CLIENT_AUTH_USER);
            await page.authenticate({
              username: CLIENT_AUTH_USER,
              password: CLIENT_AUTH_PASS,
            });
          }

          assetCheckerInterval = setInterval(() => {
            if (pageLoaded) {
              if (
                fontRequest <= fontResponse &&
                imageRequest <= imageResponse &&
                frontOMCssLoaded
              ) {
                clearInterval(assetCheckerInterval);
                assetCheckerInterval = null;
                resolve(page);
              }
            }
          }, 100);

          await page.goto(url, { waitUntil: 'networkidle2' });
          pageLoaded = true;
        } else {
          try {
            await page.goto(url, { waitUntil: 'networkidle2', timeout: 15000 });
          } catch (e) {
            if (e.message === 'net::ERR_TOO_MANY_REDIRECTS' && url.startsWith('https://')) {
              const httpUrl = url.replace('https://', 'http://');
              await page.goto(httpUrl, { waitUntil: 'networkidle2' });
            } else {
              if (e.message.includes('Navigation timeout')) {
                try {
                  await page.goto(url, { waitUntil: 'domcontentloaded' });
                  return resolve(page);
                } catch (error) {
                  return reject(error);
                }
              }
              return reject(e);
            }
          }
          resolve(page);
        }
      } catch (error) {
        log.error({
          message: 'Puppeteer error',
          error,
        });
        clearInterval(assetCheckerInterval);
        assetCheckerInterval = null;
        reject(error);
      }
    });
  }

  async findBoundingPaddingOfPopup(page, box) {
    const { x: pageX, y: pageY, width: pageWidth, height: pageHeight } = await box.boundingBox();
    const pageRight = pageX + pageWidth;
    const pageBottom = pageY + pageHeight;
    const elements = await page.$$(`.om-ribbon-inner, .om-element, .canv-col`);
    let minX = Number.MAX_VALUE;
    let minY = Number.MAX_VALUE;
    let maxX = Number.MIN_VALUE;
    let maxY = Number.MIN_VALUE;
    for (const e of elements) {
      const bounding = await e.boundingBox();
      if (bounding) {
        const { x, y, width, height } = bounding;
        const right = x + width;
        const bottom = y + height;

        if (right < 0 || bottom < 0) {
          // off-screen content, no need to check it
          continue;
        }

        if (x < minX) minX = x;
        if (y < minY) minY = y;
        if (right > maxX) maxX = right;
        if (bottom > maxY) maxY = bottom;
      }
    }

    let xPadding = 0;
    if (minX < pageX) xPadding = pageX - minX;
    if (pageRight < maxX && maxX - pageRight > xPadding) xPadding = maxX - pageRight;

    let yPadding = 0;
    if (minY < pageY) yPadding = pageY - minY;
    if (pageBottom < maxY && maxY - pageBottom > yPadding) yPadding = maxY - pageBottom;

    return { xPadding, yPadding };
  }

  async screenshotDOMElement({ page, pageIds, isFullscreen, isPrettyBackground }) {
    const selector = isFullscreen ? '.om-overlay' : `#${pageIds[0]}`;

    // Implemented in om-admin
    // await page.evaluate(() => {
    //   const top = document.querySelector('.om-workspace-top')
    //   if (top) {
    //     top.style.display = 'none'
    //   }
    // })

    const box = await page.$(selector);
    let { x, y, width, height } = await box.boundingBox();
    let clip;
    if (!isFullscreen) {
      if (isPrettyBackground) {
        const widthRatio = 1.4;
        const originalWidth = width;
        const originalHeight = height;
        if (width > height) {
          const padding = 140;
          width += 2 * padding;
          height = width / widthRatio;
        } else {
          const padding = 120;
          height += 2 * padding;
          width = height * widthRatio;
        }
        x -= (width - originalWidth) / 2;
        y -= (height - originalHeight) / 2;
        clip = { x, y, width, height };
      } else {
        const { xPadding, yPadding } = await this.findBoundingPaddingOfPopup(page, box);
        clip = {
          x: x - xPadding,
          y: y - yPadding,
          width: width + xPadding * 2,
          height: height + yPadding * 2,
        };
      }
    } else {
      clip = { x, y, width, height };
    }

    if (clip.x < 21) {
      // Keep the correct width
      // Content will fit in nicely because left boundary has been moved so many pixels
      const correction = clip.x < 0 ? Math.abs(clip.x) + 21 : 21 - clip.x;
      clip.width -= correction;
      clip.x = 21; // left boundary of workspace content
    }

    return page.screenshot({ omitBackground: true, clip });
  }

  async close() {
    await this.browser.close();
  }

  async pages() {
    return this.browser.pages();
  }

  async closeErrorPages() {
    const pages = await this.pages();
    for (const page of pages) {
      const url = page.url();
      const isErrorPage = url.startsWith('chrome-error:');
      const isPageOld = this.pageIsOld(url);
      if (isErrorPage || isPageOld) {
        log.info('closing page:', url);
        page.close();
      }
    }
  }

  pageIsOld(url) {
    let result = false;
    try {
      const currentUrl = new URL(url);
      const queryString = currentUrl.search;
      const searchParams = new URLSearchParams(queryString);
      let time = searchParams.get('time');
      if (time) {
        time = parseInt(time, 10);
        const currentTime = new Date().getTime();
        if (currentTime > time + OLD_PAGE_IN_MS) {
          result = true;
        }
      }

      if (result) {
        log.info(`Page with URL "${url}" is marked as 'old', will be force-closed soon.`);
      }

      return result;
    } catch (e) {
      log.error(e, 'error while checking age of %s', url);
      return result;
    }
  }
}

async function create() {
  const browser = await puppeteer.launch({
    executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || undefined,
    args: [
      '--no-sandbox',
      '--disable-dev-shm-usage',
      '--ignore-certificate-errors',
      '--blink-settings=primaryPointerType=4', // @media (pointer: fine) support
    ],
  });
  return new Chrome(browser);
}

module.exports = create;
