const campaignReportQuery = require('@om/queries/src/campaignReport');
const getUnifiedOrdersQuery = require('@om/queries/src/unifiedOrders');
const extractHeapPropsQuery = require('@om/queries/src/extractHeapProps');
const runQueryFlowConfig = require('../flows/runQuery.json');
const runUnifiedOrdersQueryFlowConfig = require('../flows/unifiedOrders.json');
const heapPropertiesFlowConfig = require('../flows/heapPropertiesFlow.json');
const generalPPOPromptGenerationConfig = require('../flows/generalPPOPromptGeneration.json');
const PPOPromptPublishConfig = require('../flows/PPOPromptPublish.json');
const SyncProductFeedFlowConfig = require('../flows/syncProductFeed.json');
const shopifyEltConfig = require('../flows/shopifyEltConfig.json');
const shoprenterEltConfig = require('../flows/shoprenterEltConfig.json');
const shoprenterProductELTConfig = require('../flows/shoprenterProductELTFlow.json');
const runPeriodicQueriesConfig = require('../flows/runPeriodicQueries.json');
const runSPRGenerateEmbeddingsConfig = require('../flows/SPRTextEmbeddingGeneration.json');
const SPRGenerateRecommendationsConfig = require('../flows/SPRGenerateRecommendations.json');
const cacheLoadConfig = require('../flows/cacheLoad.json');
const { SHOPRENTER_FIELDS, SHOPIFY_FIELDS } = require('./common/defaults');

const { SECURE_PROJECT_ID: secureProjectId } = process.env;

const campaignReport = ({ date } = {}) => {
  let dateStr;
  if (date === 'yesterday') {
    dateStr = new Date();
    dateStr.setDate(dateStr.getDate() - 1);
  } else if (date) {
    dateStr = new Date(date);
  } else {
    dateStr = new Date();
  }
  dateStr = dateStr.toISOString().substring(0, 10);

  const params = [
    {
      query: campaignReportQuery({
        impressionsTableName: 'views.impressions',
        conversionsTableName: 'views.conversions',
        ordersTableName: 'orders_optimonk.unified_orders',
        date: dateStr,
      }),
      location: 'US',
      priority: 'BATCH',
      destination: {
        datasetId: 'reports',
        tableId: `campaigns$${dateStr.replace(/\W/g, '')}`,
      },
      writeDisposition: 'WRITE_TRUNCATE',
    },
  ];
  return [runQueryFlowConfig, [params]];
};

const trimDate = (date) => {
  const dateToTrim = date ? new Date(date) : new Date();
  return dateToTrim.toISOString().substring(0, 10);
};

const getUnifiedOrdersParams = ({ startDate, endDate }) => {
  let dateStr;
  let dateStart;
  let dateEnd;
  if (startDate === 'yesterday') {
    dateStr = new Date();
    dateStr = trimDate(dateStr.setDate(dateStr.getDate() - 1));
    dateStart = dateStr;
    dateEnd = dateStr;
  } else if (startDate && endDate) {
    // calculate for 1 specific day only
    if (startDate === endDate) {
      dateStr = trimDate(startDate);
      dateStart = dateStr;
      dateEnd = dateStr;
    }
    // calculate for a longer period, rewrite the whole table, not just some partitions
    else {
      dateStr = null;
      dateStart = trimDate(startDate);
      dateEnd = trimDate(endDate);
    }
  }
  // calculate for today
  else {
    dateStr = trimDate();
    dateStart = dateStr;
    dateEnd = dateStr;
  }

  return [
    {
      query: getUnifiedOrdersQuery({
        eooOrdersTable: 'views.orders',
        webhookOrdersTableName: 'views.webhook_orders',
        cartTokenTableName: 'behavioural_events_optimonk._cart_token_info',
        ga4OrdersTableName: 'views.ga4_orders',
        ga4NonStandardOrdersTableName: 'views.ga4_nonstandard_orders',
        webpixelOrdersTableName: 'views.webpixel_orders',
        dateStart,
        dateEnd,
      }),
      location: 'US',
      priority: 'BATCH',
      destination: {
        datasetId: 'orders_optimonk',
        tableId: dateStr ? `unified_orders$${dateStr.replace(/\W/g, '')}` : 'unified_orders',
      },
      writeDisposition: 'WRITE_TRUNCATE',
    },
  ];
};

const getUnifiedOrders = ({ startDate, endDate } = {}) => {
  const params = getUnifiedOrdersParams({ startDate, endDate });
  return [runUnifiedOrdersQueryFlowConfig, [params]];
};

const runPeriodicQueries = ({ startDate, endDate } = {}) => {
  const params = getUnifiedOrdersParams({ startDate, endDate });
  const config = {
    ...runPeriodicQueriesConfig,
    ...(startDate === 'yesterday'
      ? {
          context: {
            date: 'yesterday',
          },
        }
      : {}),
  };
  return [config, [params]];
};

const pushUserPropertiesToHeap = () => {
  const params = [
    {
      query: extractHeapPropsQuery({ secureProjectId }),
      location: 'US',
      priority: 'BATCH',
    },
  ];
  return [heapPropertiesFlowConfig, [params]];
};

const generalPPOPromptGeneration = (config) => {
  const { context, ...restOfConfig } = config;
  const params = [
    {
      location: 'US',
      ...restOfConfig,
    },
  ];

  return [
    {
      ...generalPPOPromptGenerationConfig,
      context: {
        platform: 'general',
        ...generalPPOPromptGenerationConfig.context,
        ...(context ?? {}),
      },
    },
    [params],
  ];
};

const shopifyPPOPromptGeneration = (config) =>
  generalPPOPromptGeneration({ ...config, context: { ...config.context, platform: 'shopify' } });

const shoprenterPPOPromptGeneration = (config) =>
  generalPPOPromptGeneration({ ...config, context: { ...config.context, platform: 'shoprenter' } });

// [product-sync][isUnified]
const unasPPOPromptGeneration = (config) =>
  generalPPOPromptGeneration({ ...config, context: { ...config.context, platform: 'unas' } });

const wooPPOPromptGeneration = (config) =>
  generalPPOPromptGeneration({
    ...config,
    context: { ...config.context, platform: 'woocommerce' },
  });

const PPOPromptPublish = (config) => {
  const { context, ...restOfConfig } = config;
  const params = [
    {
      location: 'US',
      ...restOfConfig,
    },
  ];

  return [
    {
      ...PPOPromptPublishConfig,
      context: {
        ...PPOPromptPublishConfig.context,
        ...(context ?? {}),
      },
    },
    [params],
  ];
};

const syncProductFeed = (config) => {
  const { context, ...restOfConfig } = config;
  const params = [
    {
      location: 'US',
      ...restOfConfig,
    },
  ];

  return [
    {
      ...SyncProductFeedFlowConfig,
      context: {
        ...SyncProductFeedFlowConfig.context,
        ...(context ?? {}),
      },
    },
    [params],
  ];
};

const SPRGenerateEmbeddings = (config) => {
  const { context, ...restOfConfig } = config;
  const params = [
    {
      location: 'US',
      ...restOfConfig,
    },
  ];

  return [
    {
      ...runSPRGenerateEmbeddingsConfig,
      context: {
        ...runSPRGenerateEmbeddingsConfig.context,
        ...(context ?? {}),
      },
    },
    [params],
  ];
};

const SPRGenerateRecommendations = (config) => {
  const { context, ...restOfConfig } = config;
  const params = [
    {
      location: 'US',
      ...restOfConfig,
    },
  ];

  return [
    {
      ...SPRGenerateRecommendationsConfig,
      context: {
        ...SPRGenerateRecommendationsConfig.context,
        ...(context ?? {}),
      },
    },
    [params],
  ];
};

const shopifyELT = (config) => {
  const { context, ...restOfConfig } = config;
  const params = {
    location: 'US',
    ...restOfConfig,
  };

  return [
    {
      ...shopifyEltConfig,
      context: {
        ...shopifyEltConfig.context,
        ...(context ?? {}),
      },
    },
    [params],
  ];
};

const shoprenterELT = (config) => {
  const { context, ...restOfConfig } = config;
  const params = {
    location: 'US',
    ...restOfConfig,
  };

  return [
    {
      ...shoprenterEltConfig,
      context: {
        ...shoprenterEltConfig.context,
        ...(context ?? {}),
      },
    },
    [params],
  ];
};

const shoprenterProductELT = (config) => {
  const { context, ...restOfConfig } = config;
  const params = {
    location: 'US',
    ...restOfConfig,
  };

  return [
    {
      ...shoprenterProductELTConfig,
      context: {
        ...shoprenterProductELTConfig.context,
        ...(context ?? {}),
      },
    },
    [params],
  ];
};

const cacheLoad = (config) => {
  const { context, shop, ...restOfConfig } = config;

  const fields = shop.type === 'shoprenter' ? SHOPRENTER_FIELDS : SHOPIFY_FIELDS;

  const params = {
    location: 'US',
    ...restOfConfig,
    fields,
  };

  return [
    {
      ...cacheLoadConfig,
      context: {
        ...cacheLoadConfig.context,
        ...(context ?? {}),
      },
    },
    [params],
  ];
};

module.exports = {
  campaignReport,
  pushUserPropertiesToHeap,
  generalPPOPromptGeneration,
  shopifyPPOPromptGeneration,
  shoprenterPPOPromptGeneration,
  unasPPOPromptGeneration,
  wooPPOPromptGeneration,
  PPOPromptPublish,
  getUnifiedOrders,
  shopifyELT,
  shoprenterELT,
  shoprenterProductELT,
  runPeriodicQueries,
  syncProductFeed,
  SPRGenerateEmbeddings,
  SPRGenerateRecommendations,
  cacheLoad,
};
