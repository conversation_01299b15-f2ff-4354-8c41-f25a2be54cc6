const {
  mostPopularPagesQuery,
  mostPopularProductsQuery,
  GENERATION_MODE,
  missingMostPopularPagesQuery,
  missingMostPopularProductsQuery,
  currentProcessedProductCountQuery,
} = require('@om/workflow-sppo');
const { daysFromNow } = require('@om/common');
const { runQuery } = require('@om/queries');

const { AlreadyInitializedError } = require('./errors');

const PPO_COLLECTION_NAME = 'user_ppo_prompts';
const MAX_NUMBER_OF_ITEMS = 500000;

const getCurrentProcessedItemCount = async ({
  bigQueryClient,
  ppoResultsTableId,
  blackListedProductsTableId,
  location,
}) => {
  const [result] = await runQuery(
    bigQueryClient,
    currentProcessedProductCountQuery(ppoResultsTableId, blackListedProductsTableId),
    {
      location,
    },
  );

  return result.currentResultCount;
};

const createQueries = async ({
  databaseId,
  providerServiceId,
  bigQueryClient,
  generationMode,
  location,
  requestedProductAmount,
  blackListedProductsTableId,
  scraperExternalDataTable,
  ppoResultsTableId,
  eventTableId,
  useExternalData,
  productTableId,
  platform,
  locale,
}) => {
  let currentResultCount = 0;

  const queries = {
    product: mostPopularProductsQuery,
    external: mostPopularPagesQuery,
    productMissing: missingMostPopularProductsQuery,
    externalMissing: missingMostPopularPagesQuery,
  };

  const queryType = useExternalData ? 'external' : 'product';

  const queryStream = bigQueryClient.createQueryStream({
    query:
      generationMode === GENERATION_MODE.GENERATE_ALL
        ? queries[queryType]({
            databaseId,
            providerServiceId,
            eventTableId,
            ...(useExternalData ? {} : { productTableId }),
            blackListedProductsTableId,
            scraperExternalDataTableId: scraperExternalDataTable,
            platform,
            locale,
          })
        : queries[`${queryType}Missing`]({
            databaseId,
            providerServiceId,
            eventTableId,
            ...(useExternalData ? {} : { productTableId }),
            ppoResultsTableId,
            blackListedProductsTableId,
            scraperExternalDataTableId: scraperExternalDataTable,
            platform,
            locale,
          }),
    location,
    params: {
      fromDate: daysFromNow(30),
      ...(useExternalData
        ? { numberOfItems: requestedProductAmount ?? MAX_NUMBER_OF_ITEMS }
        : { numberOfProducts: requestedProductAmount ?? MAX_NUMBER_OF_ITEMS }),
    },
  });

  if (generationMode === GENERATION_MODE.GENERATE_MISSING) {
    currentResultCount = await getCurrentProcessedItemCount({
      bigQueryClient,
      location,
      ppoResultsTableId,
      blackListedProductsTableId,
    });
  }

  return { queryStream, currentResultCount };
};

/**
 *
 * @param {*} step
 * @param {{ mongoClient: import('mongodb').MongoClient }} param1
 */
const ensureInitOnce = async (step, { mongoClient }) => {
  const {
    context: { databaseId, uuid },
  } = step;
  const collection = mongoClient.db().collection(PPO_COLLECTION_NAME);

  try {
    const result = await collection.updateOne(
      {
        databaseId: +databaseId,
        'lastGeneration.uuid': uuid,
        'lastGeneration.initializedAt': null, // this ensures that the update is done only once
      },
      { $set: { 'lastGeneration.initializedAt': new Date() } },
    );

    if (result.matchedCount === 0) {
      throw new AlreadyInitializedError();
    }
  } catch (err) {
    await collection.updateOne(
      {
        databaseId: +databaseId,
        'lastGeneration.uuid': uuid,
      },
      {
        $set: {
          'lastGeneration.finishedAt': new Date(),
          'lastGeneration.error': {
            code: 'INITIALIZATION_FAILED',
            message: err.message,
          },
        },
      },
    );
    throw err;
  }
};

module.exports = {
  createQueries,
  ensureInitOnce,
};
