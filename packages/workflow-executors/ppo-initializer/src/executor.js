const { Executor } = require('@om/workflow/src/executor');
const { logger } = require('@om/logger');
const { BigQuery } = require('@google-cloud/bigquery');
const { shouldNotRetry } = require('@om/queries');
const {
  getTableNames,
  getChatGPTPromptsWithResult,
  initTables,
  InsertableError,
  chatGPTModelVersions,
} = require('@om/workflow-sppo');
const { getMongoClient } = require('@om/workflow-mongo');

const { createQueries, ensureInitOnce } = require('./helpers');
const { AlreadyInitializedError } = require('./errors');

const {
  SECURE_PROJECT_ID: secureProjectId,
  MONGO_URI_SECRET_NAME: mongoUriSecretName = 'mongo-uri',
  MONGO_URI_SECRET_VERSION: mongoUriSecretVersion = 'latest',
} = process.env;

const PPOInitializerExecutor = async ({ projectId }) => {
  let mongoClient;
  try {
    mongoClient = await getMongoClient({
      secureProjectId,
      mongoUriSecretName,
      mongoUriSecretVersion,
    });
    logger.info('Connected to MongoDB');
  } catch (error) {
    logger.error(error, 'Failed to connect MongoDB');
    process.exit(1);
  }

  process.on('SIGTERM', () => {
    logger.info('PPOInitializerExecutor got SIGTERM, closing MongoDB connection');
    mongoClient?.close?.();
  });

  return Executor(
    async (step) => {
      const {
        context: {
          location = 'US',
          providerServiceId,
          databaseId,
          prompt: promptText,
          dataValidation,
          variableNames,
          requestedProductAmount,
          generationMode,
          uuid,
          platform,
          modelVersion = chatGPTModelVersions.getDefaultVersion(),
          useExternalData,
          priority,
          locale,
        },
      } = step;

      logger.info({ trace: step.flowId, data: { step } }, 'Init handler invoked.');

      if (platform === 'general' && !useExternalData) {
        logger.error(
          { trace: step.flowId, data: { step } },
          '`useExternalData` must be true for `general` platform. Aborting.',
        );
        return;
      }

      const startedAt = new Date();

      const bigQueryClient = new BigQuery({ projectId });
      const datasetId = 'smart_ppo';

      const {
        tableId,
        blackListedProductsTableId,
        scraperExternalDataTable,
        ppoResultsTableId,
        eventTableId,
        productTableId,
      } = getTableNames({ platform, projectId, datasetId, databaseId, providerServiceId, uuid });

      let originalProcessedProductCount = 0;
      let loadedItems = 0;

      try {
        await initTables({
          projectId,
          datasetId,
          databaseId,
          providerServiceId,
          bigQueryClient,
          prompts: [{ uuid }],
        });

        await ensureInitOnce(step, { mongoClient });

        const { queryStream, currentResultCount } = await createQueries({
          databaseId,
          providerServiceId,
          bigQueryClient,
          generationMode,
          location,
          requestedProductAmount,
          blackListedProductsTableId,
          scraperExternalDataTable,
          ppoResultsTableId,
          eventTableId,
          useExternalData,
          productTableId,
          platform,
          locale,
        });
        originalProcessedProductCount = currentResultCount;

        const destination = {
          datasetId,
          tableId,
        };
        const chatGPTPromptsWithResults = getChatGPTPromptsWithResult({
          source: queryStream,
          prompts: [
            {
              promptText,
              variableNames,
              dataValidation,
              modelVersion,
              useExternalData,
            },
          ],
          databaseId,
          providerServiceId,
          platform,
          projectId,
          bigQueryClient,
          location,
        });

        for await (const chatGPTPrompt of chatGPTPromptsWithResults) {
          if (chatGPTPrompt.loadedItems) {
            loadedItems = chatGPTPrompt.loadedItems;
          }
          if (chatGPTPrompt.error) {
            if (chatGPTPrompt.error instanceof InsertableError) {
              await bigQueryClient
                .dataset(datasetId)
                .table(tableId)
                .insert(chatGPTPrompt.error.errors);
              continue;
            }
            throw chatGPTPrompt.error;
          }
          const chatgptInput = {
            prompt: chatGPTPrompt.promptText,
            variableNames,
            dataValidation,
            productId: useExternalData ? chatGPTPrompt.resultRow.url : chatGPTPrompt.resultRow.id,
            uuid,
            version: modelVersion,
            destination,
          };
          logger.debug(
            {
              trace: step.flowId,
              data: {
                step,
                chatgptInput,
              },
            },
            'Send message to ChatGPT executor',
          );
          if (priority === 'HIGH') {
            await step.nextStep.priorityChatGpt(chatgptInput);
          } else {
            await step.nextStep.chatgpt(chatgptInput);
          }
        }
      } catch (err) {
        if (shouldNotRetry(err) || err instanceof AlreadyInitializedError) {
          step.setRetry(false);
        }
        logger.error(
          {
            trace: step.flowId,
            err,
            data: {
              step,
              eventTableId,
              productTableId,
            },
          },
          'Error occurred during initialization',
        );
        throw err;
      }

      step.nextStep.checkProgress({
        startedAt: startedAt.toISOString(),
        loadedProductCount: loadedItems,
        originalProcessedProductCount,
      });
    },
    { messageBrokerOptions: { publishMode: 'immediate' } },
  );
};

module.exports = { PPOInitializerExecutor };
