const { BigQuery } = require('@google-cloud/bigquery');
const { logger } = require('@om/logger');
const { shouldNotRetry } = require('@om/queries');
const { MongoClient } = require('mongodb');
const { SecretManagerServiceClient } = require('@google-cloud/secret-manager');
const { Executor } = require('@om/workflow/src/executor');
const {
  init,
  runEmbeddingQuery,
  SPR_COLLECTION_NAME,
  setEmbeddingGenerationError,
} = require('./steps');

const {
  MONGO_URI_SECRET_NAME: mongoUriSecretName = 'mongo-uri',
  MONGO_URI_SECRET_VERSION: mongoUriSecretVersion = 'latest',
} = process.env;

const SPRTextEmbeddingControllerExecutor = async ({ projectId }) => {
  let mongoClient;
  try {
    const client = new SecretManagerServiceClient({ projectId });
    const [mongoVersion] = await client.accessSecretVersion({
      name: `projects/${projectId}/secrets/${mongoUriSecretName}/versions/${mongoUriSecretVersion}`,
    });
    const mongoUri = mongoVersion.payload.data.toString('utf8');
    mongoClient = await MongoClient.connect(mongoUri);

    logger.info('SPRTextEmbeddingControllerExecutor connected to MongoDB');
  } catch (error) {
    logger.error(error, 'SPRTextEmbeddingControllerExecutor failed to connect MongoDB');

    throw error;
  }

  process.on('SIGTERM', () => {
    logger.info('SPRTextEmbeddingControllerExecutor got SIGTERM, closing MongoDB connection');
    mongoClient?.close?.();
  });

  return Executor(async (step) => {
    const {
      context: { location = 'US', providerServiceId, databaseId, platform, domainId, locale },
      flowId,
    } = step;
    logger.info(
      { trace: step.flowId, data: { step } },
      'Text embedding generator executor invoked.',
    );

    const bigQueryClient = new BigQuery({ projectId });
    const collection = mongoClient.db().collection(SPR_COLLECTION_NAME);

    try {
      const query = await init({
        providerServiceId,
        databaseId,
        platform,
        location,
        bigQueryClient,
        projectId,
        locale,
      });

      await runEmbeddingQuery({
        collection,
        query,
        retryCount: 0,
        domainId,
        databaseId,
        bigQueryClient,
        flowId,
        location,
        providerServiceId,
      });
    } catch (err) {
      if (
        shouldNotRetry(err) ||
        [
          'EMBEDDING_INIT_ERROR',
          'RETRY_LIMIT_REACHED',
          'GENERATION_QUERY_ERROR',
          'RESULT_COUNT_ERROR',
        ].includes(err.customCode)
      ) {
        step.setRetry(false);
      }
      logger.error(
        {
          trace: step.flowId,
          err,
          data: {
            step,
            providerServiceId,
            databaseId,
            domainId,
            platform,
          },
        },
        'Error occurred during text embedding generation',
      );
      await setEmbeddingGenerationError({
        collection,
        databaseId,
        domainId,
        error: { code: err.customCode || 'EMBEDDING_GENERATION_ERROR', message: err.message },
      });
      throw err;
    }
  });
};

module.exports = { SPRTextEmbeddingControllerExecutor };
