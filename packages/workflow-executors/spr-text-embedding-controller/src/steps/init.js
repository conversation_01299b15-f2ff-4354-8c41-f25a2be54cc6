const {
  createTablesIfNotExist,
  getDescriptionEmbeddingsTableId,
  getFormattedTableName,
  getLanguagesTableId,
  getLanguages,
  getDefaultLanguageIdAndCodePair,
  generateTextEmbeddingsForProducts,
  generateTextEmbeddingsForExternalData,
  getEventTableId,
  getScraperExternalDataTableId,
  getProductTableId,
  descriptionEmbeddingsSchema,
} = require('@om/queries');
const { SPR_DATASET_NAME } = require('./common');

const DRY_RUN = process.env.DRY_RUN === '1' || false;

async function init({
  providerServiceId,
  databaseId,
  platform,
  location,
  bigQueryClient,
  projectId,
  locale,
}) {
  let query;
  const eventTable = getEventTableId(databaseId, providerServiceId);
  const externalPropertiesTable = getScraperExternalDataTableId(databaseId, providerServiceId);
  const productTable = getProductTableId(databaseId, providerServiceId, platform);

  const descriptionEmbeddingsResultTableId = getDescriptionEmbeddingsTableId(
    databaseId,
    providerServiceId,
  );
  try {
    await createTablesIfNotExist({
      bigQueryClient,
      createConfigs: [
        {
          datasetId: SPR_DATASET_NAME,
          tableId: descriptionEmbeddingsResultTableId,
          schema: descriptionEmbeddingsSchema,
        },
      ],
    });

    let languages = [];
    let generationProperty = 'description';
    let fallbackProperty;

    if (platform === 'custom') {
      query = generateTextEmbeddingsForExternalData({
        accountId: databaseId,
        providerServiceId,
        eventTable,
        externalPropertiesTable,
        resultTable: `${SPR_DATASET_NAME}.${descriptionEmbeddingsResultTableId}`,
        projectId,
        ...(DRY_RUN ? { dryRun: true } : {}),
      });
    } else {
      if (platform === 'shoprenter') {
        const languagesTableId = getFormattedTableName(
          `${projectId}.${getLanguagesTableId(databaseId, providerServiceId)}`,
        );
        languages = await getLanguages({
          bigQueryClient,
          languagesTableId,
          location,
        });
        const [defaultLanguageId] = getDefaultLanguageIdAndCodePair(languages);
        generationProperty = `description.${defaultLanguageId}`;
        fallbackProperty = `shortDescription.${defaultLanguageId}`;
      }
      query = generateTextEmbeddingsForProducts({
        accountId: databaseId,
        providerServiceId,
        eventTable,
        productTable,
        resultTable: `${SPR_DATASET_NAME}.${descriptionEmbeddingsResultTableId}`,
        generationProperty,
        platform,
        projectId,
        fallbackProperty,
        locale,
        ...(DRY_RUN ? { dryRun: true } : {}),
      });
    }

    return query;
  } catch (err) {
    err.customCode = 'EMBEDDING_INIT_ERROR';
    throw err;
  }
}

module.exports = {
  init,
};
