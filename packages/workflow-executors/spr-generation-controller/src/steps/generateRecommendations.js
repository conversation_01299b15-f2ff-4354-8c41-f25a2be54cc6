const { BigQuery } = require('@google-cloud/bigquery');
const {
  getScraperExternalDataTableId,
  getDescriptionEmbeddingsTableId,
  createTemporaryTables,
  recommendationsTempTableSchema,
  getRecommendationsTempTableId,
  getEventTableId,
  generateRecommendationsForProducts,
  generateRecommendationsForExternalData,
  getProductTableId,
  getSPRWhiteListTableId,
  getFormattedTableName,
  getLanguages,
  getLanguagesTableId,
  getDefaultLanguageIdAndCodePair,
} = require('@om/queries');
const { logger } = require('@om/logger');
const { SPR_DATASET_NAME, GenerationError } = require('../common');

async function generateRecommendations({ projectId, step }) {
  const {
    context: { location = 'US', providerServiceId, databaseId, platform, requestedItems, locale },
  } = step;

  logger.info({ trace: step.flowId, data: { step } }, 'generateRecommendations handler invoked');

  let query;
  const eventTable = getEventTableId(databaseId, providerServiceId);
  const descriptionEmbeddingsResultTableId = getDescriptionEmbeddingsTableId(
    databaseId,
    providerServiceId,
  );
  const textEmbeddingTable = `${SPR_DATASET_NAME}.${descriptionEmbeddingsResultTableId}`;
  const recommendationsTempTableId = getRecommendationsTempTableId(databaseId, providerServiceId);
  const whiteListTableId = getSPRWhiteListTableId(databaseId, providerServiceId);

  const bigQueryClient = new BigQuery({ projectId });

  try {
    await createTemporaryTables({
      bigQueryClient,
      location,
      createConfigs: [
        {
          datasetId: SPR_DATASET_NAME,
          tableId: recommendationsTempTableId,
          schema: recommendationsTempTableSchema,
        },
      ],
    });

    const [whiteListTableExists] = await bigQueryClient
      .dataset(SPR_DATASET_NAME)
      .table(whiteListTableId)
      .exists();

    if (platform === 'custom') {
      const externalDataTable = getScraperExternalDataTableId(databaseId, providerServiceId);

      query = generateRecommendationsForExternalData({
        accountId: databaseId,
        providerServiceId,
        eventTable,
        externalDataTable,
        textEmbeddingTable,
        requestedItems,
        ...(whiteListTableExists
          ? { whiteListedProductsTable: `${SPR_DATASET_NAME}.${whiteListTableId}` }
          : {}),
      });
    } else {
      let titleProperty = 'title';
      if (platform === 'shoprenter') {
        const languagesTableId = getFormattedTableName(
          `${projectId}.${getLanguagesTableId(databaseId, providerServiceId)}`,
        );
        const languages = await getLanguages({
          bigQueryClient,
          languagesTableId,
          location,
        });
        const [defaultLanguageId] = getDefaultLanguageIdAndCodePair(languages);
        titleProperty = `name.${defaultLanguageId}`;
      }
      const productTable = getProductTableId(databaseId, providerServiceId, platform);
      query = generateRecommendationsForProducts({
        accountId: databaseId,
        providerServiceId,
        eventTable,
        productTable,
        textEmbeddingTable,
        platform,
        locale,
        titleProperty,
        requestedItems,
        ...(whiteListTableExists
          ? { whiteListedProductsTable: `${SPR_DATASET_NAME}.${whiteListTableId}` }
          : {}),
      });
    }
    const [job] = await bigQueryClient.createQueryJob({
      query,
      priority: 'BATCH',
      location,
      destination: bigQueryClient.dataset(SPR_DATASET_NAME).table(recommendationsTempTableId),
      writeDisposition: 'WRITE_TRUNCATE',
    });

    logger.info(
      { trace: step.flowId, data: { step, jobId: job.id } },
      'Created job to generate recommendations',
    );

    step.nextStep({ jobIds: [job.id] });
  } catch (err) {
    throw new GenerationError(err.message, 'RECOMMENDATION_GENERATION_ERROR');
  }
}

module.exports = {
  generateRecommendations,
};
