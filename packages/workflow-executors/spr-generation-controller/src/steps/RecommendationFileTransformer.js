const { slugify } = require('@om/common');
const { Transform } = require('stream');

class RecommendationFile {
  constructor(path, content, { uploadOptions } = {}) {
    this.path = path;
    this.content = content;
    this.uploadOptions = uploadOptions;
  }
}

class RecommendationFileTransformer extends Transform {
  constructor({ databaseId, providerServiceId, platform, highWaterMark = 16, ...options }) {
    super({ ...options, objectMode: true, highWaterMark });
    this.databaseId = databaseId;
    this.platform = platform;
    this.providerServiceId = providerServiceId;
    this.propertiesToMap = {
      id: 'id',
      name: 'title',
      originalPrice: 'originalPrice',
      price: 'price',
      currency: 'currency',
      imgUrl: 'imgUrl',
      url: 'url',
      availability: 'availability',
    };
  }

  _mapRecommendations(recommendations) {
    return recommendations.map((recommendation) => {
      const { productId, variantId, properties } = recommendation;
      // [product-sync][isUnified]
      const UNIFIED_PLATFORMS = ['unas', 'woocommerce'];

      if (['shopify', 'shoprenter', ...UNIFIED_PLATFORMS].includes(this.platform)) {
        return {
          productId,
          variantId,
        };
      }

      const mappedProps = properties.reduce((mappedProps, prop) => {
        if (Object.keys(this.propertiesToMap).includes(prop.name)) {
          mappedProps[this.propertiesToMap[prop.name]] = prop.value;
        }

        return mappedProps;
      }, {});

      const id = properties.find((p) => p.name === 'id');

      if (id) {
        mappedProps.variant = id.value;
      }

      return mappedProps;
    });
  }

  async _transform(row, _, done) {
    try {
      const { recommendations } = row;

      const mappedRecommendations = this._mapRecommendations(recommendations);

      const content = Buffer.from(JSON.stringify({ recommendations: mappedRecommendations }));
      let fileName = row.id;
      if (isNaN(parseInt(row.id, 10))) {
        // row.id is an URL (external data use case)
        // we need to strip the host part and slugify the rest
        const url = new URL(row.id);
        fileName = slugify(url.pathname);
        fileName = fileName.replace(/(?<=.)-$/, ''); // Replace traling slash, if there are other charecters before it (root filename will be -.json)
      }
      const filePath = `${this.databaseId}/${this.providerServiceId}/recommender/${fileName}.json`;
      done(
        null,
        new RecommendationFile(filePath, content, {
          uploadOptions: {
            metadata: {
              cacheControl: 'no-store',
            },
          },
        }),
      );
    } catch (err) {
      done(err);
    }
  }
}

module.exports = { RecommendationFileTransformer, RecommendationFile };
