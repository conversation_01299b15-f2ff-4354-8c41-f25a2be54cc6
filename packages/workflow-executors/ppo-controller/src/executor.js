const { Executor } = require('@om/workflow/src/executor');
const { logger } = require('@om/logger');
const { getMongoClient } = require('@om/workflow-mongo');

const stepHandlers = require('./steps');

const {
  SECURE_PROJECT_ID: secureProjectId,
  MONGO_URI_SECRET_NAME: mongoUriSecretName = 'mongo-uri',
  MONGO_URI_SECRET_VERSION: mongoUriSecretVersion = 'latest',
} = process.env;

// [product-sync][isUnified]
const steps = {
  general: {
    checkProgress: stepHandlers.checkProgressHandler,
    finished: stepHandlers.finishedHandler,
  },
  shoprenter: {
    checkProgress: stepHandlers.checkProgressHandler,
    finished: stepHandlers.finishedHandler,
  },
  shopify: {
    checkProgress: stepHandlers.checkProgressHandler,
    finished: stepHandlers.finishedHandler,
  },
  unas: {
    checkProgress: stepHandlers.checkProgressHandler,
    finished: stepHandlers.finishedHandler,
  },
  woocommerce: {
    checkProgress: stepHandlers.checkProgressHandler,
    finished: stepHandlers.finishedHandler,
  },
};

const PPOControllerExecutor = async ({ projectId }) => {
  let mongoClient;
  try {
    mongoClient = await getMongoClient({
      secureProjectId,
      mongoUriSecretName,
      mongoUriSecretVersion,
    });
    logger.info('Connected to MongoDB');
  } catch (error) {
    logger.error(error, 'Failed to connect MongoDB');

    process.exit(1);
  }

  process.on('SIGTERM', () => {
    logger.info('PPOControllerExecutor got SIGTERM, closing MongoDB connection');
    mongoClient?.close?.();
  });

  return Executor(
    async (step, data) => {
      const handler = steps[step.id] ?? steps[step.context.platform]?.[step.id];
      if (handler) {
        await handler({ projectId, step, data, mongoClient });
      } else {
        logger.error({ trace: step.flowId, step, data }, 'PPO Controller step not found.');
      }
    },
    { messageBrokerOptions: { publishMode: 'immediate' } },
  );
};

module.exports = { PPOControllerExecutor };
