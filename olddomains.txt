
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f2658c"
        },
        "domain": "www.webaruhaztulelocsomag.hu",
        "lastRequestDate": {
          "$date": "2023-01-09T11:06:30.660Z"
        },
        "isKlaviyoDetected": false,
        "shopId": ""
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f2658d"
        },
        "domain": "webaruhazadatvedelem.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "isKlaviyoDetected": false,
        "shopId": ""
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f2658e"
        },
        "domain": "www.optimonk.hu",
        "lastRequestDate": {
          "$date": "2023-01-11T12:06:07.761Z"
        },
        "v3LastRequestDate": {
          "$date": "2023-01-11T12:05:39.309Z"
        },
        "isKlaviyoDetected": false,
        "shopId": ""
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f2658f"
        },
        "domain": "www.webshopexperts.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "platform": "custom"
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f26590"
        },
        "domain": "www.conversific.com",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "platform": "custom"
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f26591"
        },
        "domain": "www.webshop-seo.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "platform": "custom"
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f26592"
        },
        "domain": "www.shoprenter.hu",
        "lastRequestDate": {
          "$date": "2022-12-12T21:10:18.706Z"
        },
        "platform": "custom"
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f26593"
        },
        "domain": "optimonk.com",
        "lastRequestDate": {
          "$date": "2023-10-25T10:07:24.905Z"
        },
        "v3LastRequestDate": {
          "$date": "2025-01-29T13:28:53.531Z"
        },
        "isKlaviyoDetected": false,
        "shopId": ""
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f26594"
        },
        "domain": "www.optimonk.de",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f26595"
        },
        "domain": "tudascsomag.webshop-seo.hu",
        "lastRequestDate": {
          "$date": "2023-01-10T10:01:29.572Z"
        },
        "platform": "custom",
        "v3LastRequestDate": {
          "$date": "2023-01-10T10:01:29.826Z"
        }
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f26596"
        },
        "domain": "tibishop.shoprenter.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f26597"
        },
        "domain": "www.easycart.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f26598"
        },
        "domain": "support.optimonk.com",
        "lastRequestDate": {
          "$date": "2023-01-11T12:06:34.629Z"
        },
        "platform": "custom"
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f26599"
        },
        "domain": "www.ecomexpo.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "platform": "custom"
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f2659a"
        },
        "domain": "webaruhazjogicsomag.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "platform": "shoprenter"
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f2659b"
        },
        "domain": "shoprenter.webshopexperts.com",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f2659c"
        },
        "domain": "support.shoprenter.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "platform": "custom"
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f2659d"
        },
        "domain": "*",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "analytics": {
          "enabled": true
        },
        "isKlaviyoDetected": false,
        "shopId": ""
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f2659e"
        },
        "domain": "support.optimonk.hu",
        "lastRequestDate": {
          "$date": "2023-01-11T12:09:22.668Z"
        },
        "platform": "custom"
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f2659f"
        },
        "domain": "www.innonic.com",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "platform": "custom"
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f265a0"
        },
        "domain": "my.optimonk.com",
        "lastRequestDate": {
          "$date": "2021-12-03T08:55:57.663Z"
        }
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f265a1"
        },
        "domain": "my.optimonk.hu",
        "lastRequestDate": {
          "$date": "2021-12-03T08:43:22.386Z"
        }
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f265a2"
        },
        "domain": "kgabi.shoprenter.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "platform": "shoprenter"
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f265a3"
        },
        "domain": "reflexshop.wseshop.kgabor",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f265a4"
        },
        "domain": "landing.optimonk.com",
        "lastRequestDate": {
          "$date": "2023-01-11T11:56:58.324Z"
        },
        "platform": "custom"
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f265a5"
        },
        "domain": "my.conversific.com",
        "lastRequestDate": {
          "$date": "2022-11-16T12:28:44.623Z"
        },
        "platform": "custom"
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f265a6"
        },
        "domain": "landing.webshopexperts.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "platform": "custom"
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f265a7"
        },
        "domain": "evwebaruhaza.shoprenter.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f265a8"
        },
        "domain": "profile.codersrank.io",
        "lastRequestDate": {
          "$date": "2021-12-03T08:31:26.241Z"
        },
        "platform": "custom"
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f265a9"
        },
        "domain": "my.test.conversific.com",
        "lastRequestDate": {
          "$date": "2021-12-02T11:40:43.257Z"
        },
        "platform": "custom"
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f265aa"
        },
        "domain": "academy.innonic.com",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "isKlaviyoDetected": false,
        "shopId": ""
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f265ab"
        },
        "domain": "sissora.com",
        "lastRequestDate": {
          "$date": "2024-01-19T09:16:48.064Z"
        },
        "shopId": "innonintshop.myshopify.com",
        "isKlaviyoDetected": false,
        "platform": "shopify"
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f265ac"
        },
        "domain": "welcome.peerbly.com",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f265ad"
        },
        "domain": "discount.peerbly.com",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f265af"
        },
        "domain": "codersrank.io",
        "lastRequestDate": {
          "$date": "2023-01-11T12:09:44.969Z"
        }
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f265b0"
        },
        "domain": "majorosmark.shoprenter.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "platform": "custom"
      },
      {
        "_id": {
          "$oid": "5bd78fe57786f10027f265b1"
        },
        "domain": "www.conversific.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "platform": "custom"
      },
      {
        "_id": {
          "$oid": "5be46d55fa5a22002763f3a4"
        },
        "domain": "app.optimonk.com",
        "lastRequestDate": {
          "$date": "2024-06-07T16:52:07.581Z"
        },
        "isKlaviyoDetected": false,
        "shopId": "",
        "v3LastRequestDate": {
          "$date": "2025-01-24T16:21:41.371Z"
        }
      },
      {
        "_id": {
          "$oid": "5be6de190b45dd0027a67fe9"
        },
        "domain": "yourdomain.com",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "_id": {
          "$oid": "5beaa3f0c66ec400280322f4"
        },
        "domain": "bgergely.shoprenter.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "platform": "custom"
      },
      {
        "_id": {
          "$oid": "5beabe88c66ec400280323e8"
        },
        "domain": "steve01.myshoprenter.hu",
        "lastRequestDate": {
          "$date": "2022-11-17T08:14:12.384Z"
        },
        "platform": "shoprenter",
        "shopId": "steve01"
      },
      {
        "_id": {
          "$oid": "5bf3d909388352002b4fd8fa"
        },
        "domain": "test.cm.gygergo",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "isKlaviyoDetected": false,
        "shopId": ""
      },
      {
        "_id": {
          "$oid": "5bf51a2b388352002b4fde15"
        },
        "domain": "landing.optimonk.hu",
        "lastRequestDate": {
          "$date": "2023-01-11T12:08:06.245Z"
        },
        "platform": "custom"
      },
      {
        "_id": {
          "$oid": "5c73b783d85e2a0010510bb7"
        },
        "domain": "test.cm.fopi",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "isKlaviyoDetected": false,
        "shopId": ""
      },
      {
        "_id": {
          "$oid": "5c9a0a8144f85b0010fe2f3c"
        },
        "domain": "analitika-mesterkurzus.hu",
        "lastRequestDate": {
          "$date": "2022-04-13T15:01:46.774Z"
        }
      },
      {
        "_id": {
          "$oid": "5ccc7aad9fdc830011ea7122"
        },
        "domain": "www.webshop-suli.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "platform": "woocommerce"
      },
      {
        "_id": {
          "$oid": "5ce7a8cc158615001075feb9"
        },
        "domain": "e2e-teszt.myshopify.com",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "isKlaviyoDetected": false,
        "shopId": "e2e-teszt.myshopify.com",
        "platform": "shopify"
      },
      {
        "_id": {
          "$oid": "5ce7fe451586150010760b98"
        },
        "domain": "pergejanos.shoprenter.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "platform": "custom"
      },
      {
        "_id": {
          "$oid": "5cee49ada8ee3e0010888dec"
        },
        "domain": "test.conversific.com",
        "lastRequestDate": {
          "$date": "2022-07-01T14:36:53.660Z"
        }
      },
      {
        "_id": {
          "$oid": "5d0b78d85a67c9001278ca9a"
        },
        "domain": "local.test",
        "lastRequestDate": {
          "$date": "2022-03-04T13:00:03.076Z"
        },
        "platform": "custom"
      },
      {
        "analytics": {
          "enabled": true
        },
        "_id": {
          "$oid": "5d1f6dbbfae0700011d027c8"
        },
        "domain": "ujdomain.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "_id": {
          "$oid": "5d2d910c52cae8001975a767"
        },
        "domain": "seoaudit.shoprenter.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "platform": "custom"
      },
      {
        "analytics": {
          "enabled": true
        },
        "_id": {
          "$oid": "5d3ee49574248700181461f0"
        },
        "domain": "ilcsidebrecen.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "_id": {
          "$oid": "5d4422d19850140012f2c072"
        },
        "domain": "shoprentertest.shoprenter.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "platform": "custom"
      },
      {
        "analytics": {
          "enabled": true
        },
        "_id": {
          "$oid": "5d7755f8b2bbfe0012251ca5"
        },
        "domain": "test-magento2.optimonk.com",
        "lastRequestDate": {
          "$date": "2022-10-25T11:28:52.206Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "_id": {
          "$oid": "5d778089688e7e0011032930"
        },
        "domain": "penztar.ecomexpo.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "platform": "woocommerce"
      },
      {
        "analytics": {
          "enabled": true
        },
        "_id": {
          "$oid": "5d8ca253ca31d80011a825bb"
        },
        "domain": "www.webaruhazmarketingutmutato.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "platform": "custom"
      },
      {
        "analytics": {
          "enabled": true
        },
        "_id": {
          "$oid": "5d9464247178e20011be6d00"
        },
        "domain": "marketing.shoprenter.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "platform": "custom"
      },
      {
        "analytics": {
          "enabled": true
        },
        "_id": {
          "$oid": "5da9ca1fb84f80001133b05d"
        },
        "domain": "blog.codersrank.io",
        "lastRequestDate": {
          "$date": "2023-01-11T12:11:14.104Z"
        },
        "platform": "custom"
      },
      {
        "analytics": {
          "enabled": true
        },
        "_id": {
          "$oid": "5dd3c5425a8c870019ad26da"
        },
        "domain": "omshop6.myshopify.com",
        "lastRequestDate": {
          "$date": "2022-05-24T10:55:35.027Z"
        },
        "isKlaviyoDetected": false,
        "shopId": ""
      },
      {
        "analytics": {
          "enabled": true
        },
        "_id": {
          "$oid": "5de8fd1ef4dda00018aea3f0"
        },
        "domain": "shoprenter.hu",
        "lastRequestDate": {
          "$date": "2022-09-11T13:26:45.334Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "custom",
        "_id": {
          "$oid": "5e7e3932ddc11b00110bbcff"
        },
        "domain": "baritz.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "wordpress",
        "_id": {
          "$oid": "5e832448e2c3890012eb3f41"
        },
        "domain": "rapidsearch.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "custom",
        "_id": {
          "$oid": "5e85a4b60869950012447351"
        },
        "domain": "www.webshopkeszites.org",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "custom",
        "_id": {
          "$oid": "5e8aded7d5e8c70019371369"
        },
        "domain": "www.hazaitahazhoz.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "wordpress",
        "_id": {
          "$oid": "5eb18b8f4a7a2800115f5fad"
        },
        "domain": "optimonk.hu",
        "lastRequestDate": {
          "$date": "2022-10-10T08:23:20.002Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "_id": {
          "$oid": "5eb29621d8e5bb0012443042"
        },
        "domain": "optimonk.com",
        "lastRequestDate": {
          "$date": "2022-10-10T08:22:54.161Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "custom",
        "_id": {
          "$oid": "5eba62603298a100189304e4"
        },
        "domain": "landing.shoprenter.hu",
        "lastRequestDate": {
          "$date": "2023-01-11T07:03:55.056Z"
        },
        "v3LastRequestDate": {
          "$date": "2023-01-11T07:03:54.621Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "wordpress",
        "_id": {
          "$oid": "5ee871e5e5bf830011f21fdf"
        },
        "domain": "ecommercerevolution.hu",
        "lastRequestDate": {
          "$date": "2023-01-11T07:23:46.295Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "custom",
        "_id": {
          "$oid": "5f50b724c9bdd700184d28c9"
        },
        "domain": "www.seico.com",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "custom",
        "_id": {
          "$oid": "5f5a805b70366e0011cf18d5"
        },
        "domain": "balazsom.myshopify.com",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "isKlaviyoDetected": false,
        "shopId": ""
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "custom",
        "_id": {
          "$oid": "5f8710d815e9ea001368a459"
        },
        "domain": "doc.shoprenter.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "custom",
        "shopId": null,
        "_id": {
          "$oid": "5fabdd5764ca460012c67f19"
        },
        "domain": "shoprenter.conversific.com",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "wordpress",
        "shopId": null,
        "_id": {
          "$oid": "5fb7d6a3cfb17f001305dff4"
        },
        "domain": "conversific.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": null,
        "_id": {
          "$oid": "5fc6632cf8c7b50013272f44"
        },
        "domain": "static-test.local",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": null,
        "_id": {
          "$oid": "606dd5295de692001c419889"
        },
        "domain": "jobs.codersrank.io",
        "lastRequestDate": {
          "$date": "2023-01-11T11:31:13.396Z"
        },
        "platform": "wordpress"
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "custom",
        "shopId": null,
        "_id": {
          "$oid": "606f3721b2ffe9001c169ee2"
        },
        "domain": "seo-audit.shoprenter.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "custom",
        "shopId": null,
        "_id": {
          "$oid": "606f3ee0b2ffe9001c169ef9"
        },
        "domain": "www.hazaitolahazhoz.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "",
        "_id": {
          "$oid": "609d0b59b209cb001dc041e3"
        },
        "domain": "eles-teszt11.myshopify.com",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "isKlaviyoDetected": false,
        "platform": "custom"
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "shopify",
        "shopId": "polskie-smakolyki.myshopify.com",
        "_id": {
          "$oid": "60d1d4e11d7762001d96b7f8"
        },
        "domain": "polskie-smakolyki.myshopify.com",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "shopify",
        "shopId": "rs-jani-test.myshopify.com",
        "_id": {
          "$oid": "60f0144412e656001eedc7ed"
        },
        "domain": "rs-jani-test.myshopify.com",
        "lastRequestDate": {
          "$date": "2023-01-11T09:24:54.145Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "",
        "_id": {
          "$oid": "612cc7e262a8d5001d755747"
        },
        "domain": "rec.autocommerce.io",
        "lastRequestDate": {
          "$date": "2023-01-11T12:12:02.615Z"
        },
        "isKlaviyoDetected": false
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "shopify",
        "shopId": "truest-store.myshopify.com",
        "_id": {
          "$oid": "6130cb3ec71d3c001ca16fe8"
        },
        "domain": "www.trueststore.com",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "shopify",
        "shopId": "nikiteszt0904.myshopify.com",
        "_id": {
          "$oid": "61385fad48aeb6001ca7541c"
        },
        "domain": "nikiteszt0904.myshopify.com",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "",
        "_id": {
          "$oid": "614dfb7d0b126f001c135e71"
        },
        "domain": "tiborszantai.myshoprenter.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "isKlaviyoDetected": false
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "",
        "_id": {
          "$oid": "616966b7b7c296001c91b0a9"
        },
        "domain": "storemrn.myshopify.com",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        },
        "isKlaviyoDetected": false
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": null,
        "_id": {
          "$oid": "616d85f24e3a2e001da2da25"
        },
        "domain": "static-test.dev",
        "lastRequestDate": {
          "$date": "2024-09-17T12:45:38.144Z"
        },
        "v3LastRequestDate": {
          "$date": "2025-03-28T14:18:46.417Z"
        },
        "isKlaviyoDetected": false
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "shoprenter",
        "shopId": "bb9606",
        "_id": {
          "$oid": "6180e38b1910ee001efdc81a"
        },
        "domain": "bb9606.myshoprenter.hu",
        "lastRequestDate": {
          "$date": "2021-11-26T06:11:04.070Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "custom",
        "shopId": null,
        "_id": {
          "$oid": "61a0cf0f4979bc001d2984d1"
        },
        "domain": "da-lullu.myshopify.com",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "",
        "_id": {
          "$oid": "61b1f5debac547001dd3e1ab"
        },
        "domain": "steve01.shoprenter.hu",
        "lastRequestDate": null,
        "isKlaviyoDetected": false
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "shopify",
        "shopId": "optimonkdev-948.myshopify.com",
        "_id": {
          "$oid": "61e022ee13a59c001d4b28c5"
        },
        "domain": "optimonkdev-948.myshopify.com",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "shopify",
        "shopId": "lolliesandchillies.myshopify.com",
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "627147603e037a0024d1532f"
        },
        "domain": "lolliesandchillies.myshopify.com",
        "lastRequestDate": {
          "$date": "2022-05-13T19:32:27.889Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "",
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "627a45e4cbe66500243d4a7b"
        },
        "domain": "tibor-szantai-surfacetest2.myshopify.com",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "shoprenter",
        "shopId": "tiborszantaiom",
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "628f321fc058e8002389bba7"
        },
        "domain": "tiborszantaiom.myshoprenter.hu",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "",
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "62b311501016e100242ad016"
        },
        "domain": "fruzsi-test.myshopify.com",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "",
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "64c3b99ed7e74f5fc0f68fed"
        },
        "domain": "https://debhack.tech",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "",
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "64c3bba0d7e74f5fc0f68fef"
        },
        "domain": "debhack.tech",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "",
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "64c3bbb3d7e74f5fc0f68ff1"
        },
        "domain": "10.32.43.54",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "",
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "64c3bbc6d7e74f5fc0f68ff3"
        },
        "domain": "www.optimonk.commo",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "",
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "64c8eb8b987f26c375170f59"
        },
        "domain": "debhack.tec",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "",
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "64c8ee40987f26c375170f5b"
        },
        "domain": "rfusd.com",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": null,
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "64e092f992acba8358f425c3"
        },
        "domain": "google.com",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "",
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "6501bb65e1dd1d2a83cc6b3f"
        },
        "domain": "denestesztelget.myshopify.com",
        "lastRequestDate": null,
        "platform": "custom"
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "",
        "_id": {
          "$oid": "650d49e7671b09715d781705"
        },
        "domain": "ulaandco.com",
        "lastRequestDate": {
          "$date": "2023-10-25T10:09:37.654Z"
        },
        "isKlaviyoDetected": false
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "",
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "650d6921671b09715d781707"
        },
        "domain": "eles-test1.myshopify.com",
        "lastRequestDate": {
          "$date": "2023-09-22T10:44:09.864Z"
        }
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "suomikauppa2.myshopify.com",
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "650d71f5671b09715d781709"
        },
        "domain": "suomikauppa.fi",
        "lastRequestDate": null,
        "platform": "shopify"
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": null,
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "651d6011780d0633df885183"
        },
        "domain": "a",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": null,
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "651d6033780d0633df885185"
        },
        "domain": "sfdg",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": null,
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "651d61c9780d0633df885187"
        },
        "domain": "asdf.com",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": null,
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "651d61f1780d0633df885189"
        },
        "domain": "healthnaturalprod.wixsite.com",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "",
        "_id": {
          "$oid": "6524088ffc47362d5180aaee"
        },
        "domain": "om-oneclick3.myshopify.com",
        "lastRequestDate": {
          "$date": "2023-10-09T14:11:11.687Z"
        },
        "isKlaviyoDetected": false
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "thevrshop-official.myshopify.com",
        "isKlaviyoDetected": true,
        "_id": {
          "$oid": "6538e7eb45045bc55200b817"
        },
        "domain": "thevrshop.hu",
        "lastRequestDate": null,
        "platform": "shopify"
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": null,
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "659bf75a598dcb58ebbb1b93"
        },
        "domain": "test.com",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": null,
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "659bf801598dcb58ebbb1b95"
        },
        "domain": "secret-domination-staging-5.myshopify.com",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "wordpress",
        "shopId": "",
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "65aa4d6829dfea231eb61f98"
        },
        "domain": "innonic.com",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "custom",
        "shopId": "",
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "65afa53b8f9cdbb288fbc4e5"
        },
        "domain": "neesh59.myshopify.com",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": "",
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "65b78c8d97028eac27706527"
        },
        "domain": "facebook.com",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": null,
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "6639f01c55be51627ce97eb7"
        },
        "domain": "bathmatedirect.com",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "shopId": null,
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "663a1b8c29212e7626d3004a"
        },
        "domain": "norelie.fi",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "",
        "shopId": null,
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "66d6fc873866fc2511ccb858"
        },
        "domain": "csodakertesz.hu",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "shoprenter",
        "shopId": "vagyaim2",
        "isKlaviyoDetected": false,
        "_id": {
          "$oid": "66e18c6439b7cbeb86136e3b"
        },
        "domain": "www.vagyaim.hu",
        "lastRequestDate": null
      },
      {
        "analytics": {
          "enabled": true
        },
        "inactive": false,
        "platform": "custom",
        "shopId": "",
        "isKlaviyoDetected": false,
        "useNonstandardOrders": false,
        "_id": {
          "$oid": "67095862f9cedf3b066228df"
        },
        "domain": "blendjet.com",
        "lastRequestDate": null,
        "v3LastRequestDate": null
      },
      {
        "domain": "eles-teszt31.myshopify.com",
        "lastRequestDate": null,
        "v3LastRequestDate": null,
        "inactive": false,
        "analytics": {
          "enabled": true
        },
        "shopId": null,
        "isKlaviyoDetected": false,
        "useNonstandardOrders": false,
        "_id": {
          "$oid": "67cec298b7d0e864196f38c2"
        }
      },
      {
        "domain": "farmaplus.com.ar",
        "lastRequestDate": null,
        "v3LastRequestDate": null,
        "inactive": false,
        "analytics": {
          "enabled": true
        },
        "shopId": "",
        "isKlaviyoDetected": false,
        "useNonstandardOrders": false,
        "_id": {
          "$oid": "67d2cdac1a458396c10a95f5"
        },
        "platform": "custom"
      }
