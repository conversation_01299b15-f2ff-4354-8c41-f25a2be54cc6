import { translate } from '../../../../utils';
import { migrateV2 } from '../../../shared/OmProduct';

const SHOP_SH = 'shopify';
const SHOP_SR = 'shoprenter';

const SHOP_REDIRECT = [SHOP_SH, SHOP_SR, 'unas', 'woocommerce'];

export default ({ element, isNew, version }) => {
  const editorStore = () => window.self !== window.parent && window.parent.om.store;
  const store = editorStore();

  if (store) {
    if (store.state.campaign) {
      let storeType = false;
      if (store.state.campaign.domain) {
        if (store.getters.isActiveShopifyDomain(store.state.campaign.domain)) {
          storeType = SHOP_SH;
        }
        if (store.getters.isActiveShoprenterDomain(store.state.campaign.domain)) {
          storeType = SHOP_SR;
        }
        if (store.getters.isActiveUnasDomain(store.state.campaign.domain)) {
          storeType = 'unas';
        }
        if (store.getters.isActiveWooCommerceDomain(store.state.campaign.domain)) {
          storeType = 'woocommerce';
        }
      }

      element.data.storeType = storeType;

      if (element.data.clickAction === null) {
        element.data.clickAction = storeType === SHOP_SR ? 'redirect' : 'add-to-cart';
      }

      if (version === 3) {
        element.data.mode = 'manual';
        if (!SHOP_REDIRECT.includes(storeType)) {
          element.data.clickAction = 'redirect';
        }
      } else if (
        storeType === 'shopify' ||
        storeType === 'shoprenter' ||
        storeType === 'unas' ||
        storeType === 'woocommerce'
      ) {
        if (isNew) {
          element.data.mode = 'last-visited';
          element.data.clickAction = 'add-to-cart';
        }
      } else if (element.data.mode !== 'optimonk-recommender') {
        element.data.mode = 'manual';
        if (!SHOP_REDIRECT.includes(storeType)) {
          element.data.clickAction = 'redirect';
        }
      }

      if (element.data.ctaText === null) {
        element.data.ctaText =
          element.data.clickAction === 'add-to-cart'
            ? translate('productTexts.example.cta.addToCart')
            : translate('productTexts.example.cta.redirect');
      }
    } else {
      element.data.clickAction = 'redirect';
    }
  }

  if (isNew && element.data.mode === 'manual') {
    // OJ-148 - fix incorrect localized strings in freshly created elements caused by elementDefaults merging.
    element.data.name.text = translate('productTexts.example.name');
    element.data.sku.text = translate('productTexts.example.sku');
    element.data.price.text = translate('productTexts.example.price.new');
    element.data.oldPrice.text = translate('productTexts.example.price.old');
    element.data.ctaText =
      element.data.clickAction === 'add-to-cart'
        ? translate('productTexts.example.cta.addToCart')
        : translate('productTexts.example.cta.redirect');
  }

  if (!element.data.version || element.data.version === 1) {
    migrateV2(element);
  }
};
