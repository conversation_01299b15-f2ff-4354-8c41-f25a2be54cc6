const TEXT_ATTRIBUTES_BY_COMPONENT_TYPE = {
  OmText: ['data.text'],
  OmButton: ['data.text'],
  OmInput: ['data.epicPlaceholder', 'data.errorMessage'],
  OmTextarea: ['data.epicPlaceholder', 'data.errorMessage'],
  OmCountdown: [
    'data.countdown.labels.days',
    'data.countdown.labels.hours',
    'data.countdown.labels.minutes',
    'data.countdown.labels.seconds',
  ],
  OmRadio: ['data.form.customSettings.options.$.key', 'data.errorMessage'],
  OmCheckbox: ['data.form.customSettings.options.$.key', 'data.errorMessage'],
  OmSurvey: ['data.form.customSettings.options.$.key'],
  OmDropdown: [
    'data.form.customSettings.options.$.key',
    'data.errorMessage',
    'data.epicPlaceholder',
  ],
  OmCoupon: ['data.coupon.customCopyText'],
  OmProduct: ['data.ctaText', 'data.products.$.name'],
  OmSocial: ['data.socialShare.text'],
};

const languages = {
  de: 'German',
  fr: 'French',
  pt: 'Portuguese',
  es: 'Spanish',
  ro: 'Romanian',
  hu: 'Hungarian',
  // aa: 'Afar',
  // ab: 'Abkhazian',
  // ae: 'Avestan',
  // af: 'Afrikaans',
  // ak: 'Akan',
  // am: 'Amharic',
  // an: 'Aragonese',
  // ar: 'Arabic',
  // as: 'Assamese',
  // av: 'Avaric',
  // ay: 'Aymara',
  // az: 'Azerbaijani',
  // ba: 'Bashkir',
  // be: 'Belarusian',
  // bg: 'Bulgarian',
  // bh: 'Bihari languages',
  // bi: 'Bislama',
  // bm: 'Bambara',
  // bn: 'Bengali',
  // bo: 'Tibetan',
  // br: 'Breton',
  // bs: 'Bosnian',
  // ca: 'Catalan',
  // ce: 'Chechen',
  // ch: 'Chamorro',
  // co: 'Corsican',
  // cr: 'Cree',
  // cs: 'Czech',
  // cu: 'Church Slavic',
  // cv: 'Chuvash',
  // cy: 'Welsh',
  // da: 'Danish',
  // dv: 'Maldivian',
  // dz: 'Dzongkha',
  // ee: 'Ewe',
  // el: 'Greek',
  // en: 'English',
  // eo: 'Esperanto',
  // et: 'Estonian',
  // eu: 'Basque',
  // fa: 'Persian',
  // ff: 'Fulah',
  // fi: 'Finnish',
  // fj: 'Fijian',
  // fo: 'Faroese',
  // fy: 'Western Frisian',
  // ga: 'Irish',
  // gd: 'Gaelic',
  // gl: 'Galician',
  // gn: 'Guarani',
  // gu: 'Gujarati',
  // gv: 'Manx',
  // ha: 'Hausa',
  // he: 'Hebrew',
  // hi: 'Hindi',
  // ho: 'Hiri Motu',
  // hr: 'Croatian',
  // ht: 'Haitian',
  // hy: 'Armenian',
  // hz: 'Herero',
  // ia: 'Interlingua',
  // id: 'Indonesian',
  // ie: 'Interlingue',
  // ig: 'Igbo',
  // ii: 'Sichuan Yi',
  // ik: 'Inupiaq',
  // io: 'Ido',
  // is: 'Icelandic',
  // it: 'Italian',
  // iu: 'Inuktitut',
  // ja: 'Japanese',
  // jv: 'Javanese',
  // ka: 'Georgian',
  // kg: 'Kongo',
  // ki: 'Kikuyu',
  // kj: 'Kuanyama',
  // kk: 'Kazakh',
  // kl: 'Kalaallisut',
  // km: 'Central Khmer',
  // kn: 'Kannada',
  // ko: 'Korean',
  // kr: 'Kanuri',
  // ks: 'Kashmiri',
  // ku: 'Kurdish',
  // kv: 'Komi',
  // kw: 'Cornish',
  // ky: 'Kirghiz',
  // la: 'Latin',
  // lb: 'Luxembourgish',
  // lg: 'Ganda',
  // li: 'Limburgan',
  // ln: 'Lingala',
  // lo: 'Lao',
  // lt: 'Lithuanian',
  // lu: 'Luba-Katanga',
  // lv: 'Latvian',
  // mg: 'Malagasy',
  // mh: 'Marshallese',
  // mi: 'Maori',
  // mk: 'Macedonian',
  // ml: 'Malayalam',
  // mn: 'Mongolian',
  // mr: 'Marathi',
  // ms: 'Malay',
  // mt: 'Maltese',
  // my: 'Burmese',
  // na: 'Nauru',
  // nb: 'Norwegian',
  // nd: 'North Ndebele',
  // ne: 'Nepali',
  // ng: 'Ndonga',
  // nl: 'Dutch',
  // nn: 'Norwegian',
  // no: 'Norwegian',
  // nr: 'South Ndebele',
  // nv: 'Navajo',
  // ny: 'Chichewa',
  // oc: 'Occitan',
  // oj: 'Ojibwa',
  // om: 'Oromo',
  // or: 'Oriya',
  // os: 'Ossetic',
  // pa: 'Panjabi',
  // pi: 'Pali',
  // pl: 'Polish',
  // ps: 'Pushto',
  // qu: 'Quechua',
  // rm: 'Romansh',
  // rn: 'Rundi',
  // ru: 'Russian',
  // rw: 'Kinyarwanda',
  // sa: 'Sanskrit',
  // sc: 'Sardinian',
  // sd: 'Sindhi',
  // se: 'Northern Sami',
  // sg: 'Sango',
  // si: 'Sinhala',
  // sk: 'Slovak',
  // sl: 'Slovenian',
  // sm: 'Samoan',
  // sn: 'Shona',
  // so: 'Somali',
  // sq: 'Albanian',
  // sr: 'Serbian',
  // ss: 'Swati',
  // st: 'Sotho, Southern',
  // su: 'Sundanese',
  // sv: 'Swedish',
  // sw: 'Swahili',
  // ta: 'Tamil',
  // te: 'Telugu',
  // tg: 'Tajik',
  // th: 'Thai',
  // ti: 'Tigrinya',
  // tk: 'Turkmen',
  // tl: 'Tagalog',
  // tn: 'Tswana',
  // to: 'Tonga',
  // tr: 'Turkish',
  // ts: 'Tsonga',
  // tt: 'Tatar',
  // tw: 'Twi',
  // ty: 'Tahitian',
  // ug: 'Uighur',
  // uk: 'Ukrainian',
  // ur: 'Urdu',
  // uz: 'Uzbek',
  // ve: 'Venda',
  // vi: 'Vietnamese',
  // vo: 'Volapük',
  // wa: 'Walloon',
  // wo: 'Wolof',
  // xh: 'Xhosa',
  // yi: 'Yiddish',
  // yo: 'Yoruba',
  // za: 'Zhuang',
  // zh: 'Chinese',
  // zu: 'Zulu',
};

// TemplateTranslator uses
const proofReaderLanguages = {
  ...languages,
  ro: 'Romanian',
};

const goalDetails = {
  capture_email: 'Capture email.',
  collect_email: 'Collect email.',
  collect_feedback: 'Collect feedback',
  collect_messenger: 'Collect social media.',
  collect_phone: 'Collect phone number.',
  faciliate_social_sharing: 'Faciliate social media sharing.',
  gamify: 'Gamification popup.',
  increase_cart_value: 'Increase webshop cart value.',
  increase_form_submission: 'Increase form submission.',
  promote_spec_offers: 'Promote special offers.',
  promote_special_offers: 'Promote special offers.',
  recommend_products: 'Recommend products.',
  reduce_cart_abandonment: 'Reduce webshop cart abandonment.',
};

module.exports = {
  TEXT_ATTRIBUTES_BY_COMPONENT_TYPE,
  languages,
  goalDetails,
  proofReaderLanguages,
};
