const {
  createTablesIfNotExist,
  smartPPOResultsSchema,
  blackListedProductsTableSchema,
  scraperExternalDataSchema,
  getFormattedTableName,
  getEventTableId,
  getProductTableId,
} = require('@om/queries');
const { hash } = require('@om/schema');

const getTableNames = ({ platform, projectId, datasetId, databaseId, providerServiceId, uuid }) => {
  const eventTableId = getFormattedTableName(
    `${projectId}.${getEventTableId(databaseId, providerServiceId)}`,
  );

  const productTableId = getFormattedTableName(
    `${projectId}.${getProductTableId(databaseId, providerServiceId, platform)}`,
  );

  const tableId = `smart_ppo_results_${databaseId}_${hash(providerServiceId)}_${uuid}`;
  const ppoResultsTableId = getFormattedTableName(`${datasetId}.${tableId}`);
  const blackListTableId = `blacklisted_products_${databaseId}_${hash(providerServiceId)}`;
  const blackListedProductsTableId = getFormattedTableName(
    `${projectId}.${datasetId}.${blackListTableId}`,
  );
  const scraperExternalDataTableId = `scraper_external_data_${databaseId}_${hash(
    providerServiceId,
  )}`;
  const scraperExternalDataTable = getFormattedTableName(
    `${projectId}.${datasetId}.${scraperExternalDataTableId}`,
  );

  return {
    tableId,
    blackListTableId,
    scraperExternalDataTableId,
    eventTableId,
    productTableId,
    ppoResultsTableId,
    blackListedProductsTableId,
    scraperExternalDataTable,
  };
};

const getCreateConfigs = ({
  projectId,
  datasetId,
  databaseId,
  providerServiceId,
  platform,
  prompts = [],
}) => {
  const { blackListTableId, scraperExternalDataTableId } = getTableNames({
    projectId,
    datasetId,
    databaseId,
    providerServiceId,
    platform,
  });

  const createConfigs = [
    {
      datasetId,
      tableId: blackListTableId,
      schema: blackListedProductsTableSchema,
    },
    {
      datasetId,
      tableId: scraperExternalDataTableId,
      schema: scraperExternalDataSchema,
      partitionOptions: {
        field: 'timestamp',
        type: 'DAY',
        requirePartitionFilter: true,
      },
    },
  ];

  prompts.forEach((prompt) => {
    const { tableId } = getTableNames({
      projectId,
      datasetId,
      databaseId,
      providerServiceId,
      uuid: prompt.uuid,
    });

    createConfigs.push({
      datasetId,
      tableId,
      schema: smartPPOResultsSchema,
    });
  });

  return createConfigs;
};

const initTables = async ({
  projectId,
  datasetId,
  databaseId,
  providerServiceId,
  platform,
  prompts = [],
  bigQueryClient,
  location = 'US',
}) => {
  const createConfigs = getCreateConfigs({
    projectId,
    datasetId,
    databaseId,
    providerServiceId,
    platform,
    prompts,
  });

  await createTablesIfNotExist({
    bigQueryClient,
    location,
    createConfigs,
  });
};

module.exports = {
  initTables,
  getTableNames,
};
