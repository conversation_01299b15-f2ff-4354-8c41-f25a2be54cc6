const { externalProductPropertiesView, externalPropertiesView } = require('@om/queries');
const {
  viewedProductIdsView,
  mostPopularProductPropertiesView,
  productRawPropertiesView,
} = require('./product');

const { mostPopularPagePropertiesView, viewedPages } = require('./pages');

const promptResultsQuery = (ppoResultsTableIds, blackListedProductsTableId) => {
  return `
    WITH
      blackListedProducts AS (
        SELECT productId
        FROM ${blackListedProductsTableId}
        GROUP BY productId
      ),
      deduplicatedResults AS (
        SELECT
          productId,
          variableName,
          result
        FROM (${ppoResultsTableIds
          .map((tableId) => `SELECT * FROM ${tableId}`)
          .join('\nUNION ALL\n')})
        WHERE
          result IS NOT NULL AND
          LENGTH(result) > 0 AND
          error IS NULL
        QUALIFY ROW_NUMBER() OVER (PARTITION BY variableName, productId ORDER BY timestamp DESC) = 1
      )
      SELECT
        JSON_OBJECT(ARRAY_AGG(variableName), ARRAY_AGG(result)) AS jsonResult, productId
      FROM deduplicatedResults
      LEFT JOIN blackListedProducts USING (productId)
      WHERE blackListedProducts.productId IS NULL
      GROUP BY productId
      LIMIT 1500000
 `;
};

const promptResultsExportQuery = ({
  databaseId,
  providerServiceId,
  eventTableId,
  productTableId,
  resultsWildcardTableId,
  blackListedProductsTableId,
  scraperExternalDataTableId,
  generationUuids,
  variableNames,
  platform,
  locale,
}) => {
  return `
    CREATE TABLE IF NOT EXISTS ${blackListedProductsTableId} (productId STRING);
    WITH
      blackListedProducts AS (
        SELECT productId
        FROM ${blackListedProductsTableId}
        WHERE SAFE_CAST(productId AS FLOAT64) IS NOT NULL
        GROUP BY productId
      ),
      mostPopularProductProps AS (${mostPopularProductPropertiesView(
        `(${viewedProductIdsView(eventTableId, platform)})`,
        `(${productRawPropertiesView(productTableId, {
          platform,
          databaseId,
          providerServiceId,
          locale,
        })})`,
        `(${externalProductPropertiesView(scraperExternalDataTableId)})`,
      )}),
      rawGenerationResults AS (
        SELECT variableName, productId, result, error.message as error
        FROM ${resultsWildcardTableId}
        WHERE
          _TABLE_SUFFIX IN (${generationUuids.map((uuid) => `'${uuid}'`).join(', ')})
          QUALIFY ROW_NUMBER() OVER (PARTITION BY variableName, productId ORDER BY timestamp DESC) = 1
      ),
      generationResultsPerProduct AS (
        SELECT
          productId as id,
          ${variableNames
            .map(
              (variableName, index) => `
          MAX(IF(variableName = "${variableName}", result, NULL)) as \`${variableName}_${index}\``,
            )
            .join(',\n')},
          ${variableNames
            .map(
              (variableName, index) => `
          MAX(IF(variableName = "${variableName}", error, NULL)) as \`error_${variableName}_${index}\``,
            )
            .join(',\n')}
        FROM rawGenerationResults
        GROUP BY productId
      )

    SELECT
      COALESCE(id, blackListedProducts.productId) as id,
      REGEXP_EXTRACT(mostPopularProductProps.url, r'[^?]*') as url,
      IF(blackListedProducts.productId IS NULL, '', 'excluded') as excluded,
      (SELECT propertyValue FROM UNNEST(product) WHERE propertyName = 'title') as name,
      (SELECT propertyValue FROM UNNEST(product) WHERE propertyName = 'description') as description,
      views as pageViews,
      generationResultsPerProduct.* EXCEPT (id),
      product
    FROM
      mostPopularProductProps
    FULL OUTER JOIN blackListedProducts ON (mostPopularProductProps.id = blackListedProducts.productId)
    LEFT JOIN generationResultsPerProduct USING (id)
    ORDER BY views DESC
    LIMIT @numberOfItems
    `;
};

const promptResultsExportExternalQuery = ({
  eventTableId,
  resultsWildcardTableId,
  blackListedProductsTableId,
  scraperExternalDataTableId,
  generationUuids,
  variableNames,
}) => {
  return `
CREATE TABLE IF NOT EXISTS ${blackListedProductsTableId} (productId STRING);
WITH
  blackListedProducts AS (
    SELECT productId
    FROM ${blackListedProductsTableId}
    WHERE SAFE_CAST(productId AS FLOAT64) IS NULL
    GROUP BY productId
  ),
  externalProperties AS (${externalPropertiesView({ scraperExternalDataTableId, prefix: false })}),
  viewedPages AS (${viewedPages(eventTableId)}),
  mostPopularPageProps AS (${mostPopularPagePropertiesView('viewedPages', 'externalProperties')}),
  rawGenerationResults AS (
    SELECT variableName, productId, result, error.message as error
    FROM ${resultsWildcardTableId}
    WHERE
      _TABLE_SUFFIX IN (${generationUuids.map((uuid) => `'${uuid}'`).join(', ')})
      QUALIFY ROW_NUMBER() OVER (PARTITION BY variableName, productId ORDER BY timestamp DESC) = 1
  ),
  generationResultsPerPage AS (
    SELECT
      productId as id,
      ${variableNames
        .map(
          (variableName) => `
      MAX(IF(variableName = "${variableName}", result, NULL)) as \`${variableName}\``,
        )
        .join(',\n')},
      ${variableNames
        .map(
          (variableName) => `
      MAX(IF(variableName = "${variableName}", error, NULL)) as \`error_${variableName}\``,
        )
        .join(',\n')}
    FROM rawGenerationResults
    GROUP BY productId
  )

SELECT
  COALESCE(url, blackListedProducts.productId) as url,
  IF(blackListedProducts.productId IS NULL, '', 'excluded') as excluded,
  views as pageViews,
  generationResultsPerPage.* EXCEPT (id),
  properties
FROM
mostPopularPageProps
FULL OUTER JOIN blackListedProducts ON (mostPopularPageProps.url = blackListedProducts.productId)
LEFT JOIN generationResultsPerPage ON (mostPopularPageProps.url = generationResultsPerPage.id)
ORDER BY views DESC
LIMIT @numberOfItems
`;
};

module.exports = {
  promptResultsQuery,
  promptResultsExportQuery,
  promptResultsExportExternalQuery,
};
