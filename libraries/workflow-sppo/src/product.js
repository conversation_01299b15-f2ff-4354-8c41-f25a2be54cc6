const entities = require('entities');

const cheerio = require('cheerio');
const { logger } = require('@om/logger');
const { externalProductPropertiesView, productRawPropertiesView } = require('@om/queries');
const { InsertableError, getErrorRow } = require('./error');
const { getDataValidationSetting, validateDescription } = require('./dataValidation');
const { UNIFIED_PRODUCTS_PLATFORMS } = require('./isUnifiedProuctBased');

// [product-sync]
const viewedProductIdsView = (eventTableId, platform) => `
SELECT
  SAFE_CAST(REPLACE(${
    ['shopify', ...UNIFIED_PRODUCTS_PLATFORMS].includes(platform) ? 'productId' : 'variantId'
  }, '"', '') AS STRING) as productId,
  COUNT(*) AS views,
  ARRAY_AGG(url)[SAFE_OFFSET(0)] as url
FROM (
  SELECT
    timestamp AS timestamp,
    providerServiceId,
    ${
      ['shopify', ...UNIFIED_PRODUCTS_PLATFORMS].includes(platform)
        ? 'ARRAY(SELECT AS STRUCT pr.value FROM ev.props AS pr WHERE pr.name = "productId")[SAFE_OFFSET(0)].value AS productId,'
        : ''
    }
    ARRAY(SELECT AS STRUCT pr.value FROM ev.props AS pr WHERE pr.name = "variantId")[SAFE_OFFSET(0)].value AS variantId,
    (SELECT ctx.value FROM UNNEST(be.context) as ctx WHERE ctx.name = 'url') as url
  FROM ${eventTableId} be,
    UNNEST(be.addresses) ad,
    UNNEST(be.events) ev
  WHERE
    ad.customerAddress IS NOT NULL
    AND ad.provider = 'optimonk'
    AND DATE(timestamp) >= DATE(@fromDate)
    AND ev.type = "epv"
)
GROUP BY productId
`;

const blackListedProductsView = (blackListedProductsTableId) => `
SELECT productId
FROM ${blackListedProductsTableId}
GROUP BY productId
`;

const mostPopularProductPropertiesView = (
  viewedProductIdsTableId,
  productRawPropertiesTableId,
  externalPropertiesTableId,
) => `
SELECT
  accountId,
  providerServiceId,
  productId as id,
  idType,
  viewedProductIds.url as url,
  views,
  ARRAY_CONCAT(
    ARRAY_AGG(
      STRUCT(
          SAFE_CAST(REPLACE(propertyName, '"', '') AS STRING) as propertyName,
          SAFE_CAST(REPLACE(propertyValue, '"', '') AS STRING) as propertyValue
        )
      ),
      ANY_VALUE(IFNULL(externalProps.properties, []))
    ) as product
FROM
  ${viewedProductIdsTableId} viewedProductIds
INNER JOIN ${productRawPropertiesTableId} rawProperties ON
  SAFE_CAST(REGEXP_EXTRACT(viewedProductIds.productId, r'(\\d+)') AS STRING) = SAFE_CAST(REGEXP_EXTRACT(rawProperties.id, r'(\\d+)') AS STRING)
LEFT JOIN ${externalPropertiesTableId} externalProps ON (viewedProductIds.productId = externalProps.id)
GROUP BY accountId, providerServiceId, id, idType, url, views
ORDER BY MAX(views) DESC
`;

const mostPopularProductsQuery = ({
  databaseId,
  providerServiceId,
  eventTableId,
  productTableId,
  blackListedProductsTableId,
  scraperExternalDataTableId,
  platform,
  locale,
}) => `
WITH
  viewedProductIds AS (${viewedProductIdsView(eventTableId, platform)}),
  rawProperties AS (${productRawPropertiesView(productTableId, {
    platform,
    databaseId,
    providerServiceId,
    locale,
  })}),
  externalProductProperties AS (${externalProductPropertiesView(scraperExternalDataTableId)}),
  blackListedProducts AS (${blackListedProductsView(blackListedProductsTableId)}),
  mostPopularProductPropertiesView AS (${mostPopularProductPropertiesView(
    'viewedProductIds',
    'rawProperties',
    'externalProductProperties',
  )})
  SELECT * EXCEPT (productId, views, url)
  FROM mostPopularProductPropertiesView
  LEFT JOIN blackListedProducts ON (mostPopularProductPropertiesView.id = blackListedProducts.productId)
  WHERE blackListedProducts.productId IS NULL
  ORDER BY views DESC
  LIMIT @numberOfProducts
`;

const successfullyProcessedView = (ppoResultsTableId) => `
WITH
  latestResults AS (
    SELECT
      *
    FROM ${ppoResultsTableId}
    QUALIFY ROW_NUMBER() OVER (PARTITION BY productId, variableName ORDER BY timestamp DESC) = 1
  ),
  erroredProductIds AS (
    SELECT
      *
    FROM latestResults
    WHERE
      error IS NOT NULL OR
      LENGTH(result) = 0
  )

SELECT DISTINCT
  productId
FROM latestResults
EXCEPT DISTINCT SELECT productId FROM erroredProductIds
`;

const currentProcessedProductCountQuery = (ppoResultsTableId, blackListedProductsTableId) => `
WITH
  successfullyProcessedProducts AS (${successfullyProcessedView(ppoResultsTableId)}),
  blackListedProducts AS (${blackListedProductsView(blackListedProductsTableId)})
SELECT
  COUNT(*) as currentResultCount
FROM successfullyProcessedProducts
LEFT JOIN blackListedProducts USING (productId)
WHERE blackListedProducts.productId IS NULL
`;

const missingMostPopularProductsQuery = ({
  databaseId,
  providerServiceId,
  eventTableId,
  productTableId,
  ppoResultsTableId,
  blackListedProductsTableId,
  scraperExternalDataTableId,
  platform,
  locale,
}) => `
WITH
  viewedProductIds AS (${viewedProductIdsView(eventTableId, platform)}),
  rawProperties AS (${productRawPropertiesView(productTableId, {
    platform,
    databaseId,
    providerServiceId,
    locale,
  })}),
  externalProductProperties AS (${externalProductPropertiesView(scraperExternalDataTableId)}),
  successfullyProcessedProducts AS (${successfullyProcessedView(ppoResultsTableId)}),
  blackListedProducts AS (${blackListedProductsView(blackListedProductsTableId)}),
  allowed_products AS (
    SELECT * EXCEPT (productId)
    FROM (${mostPopularProductPropertiesView(
      'viewedProductIds',
      'rawProperties',
      'externalProductProperties',
    )}) mp
    LEFT JOIN blackListedProducts ON (mp.id = blackListedProducts.productId)
    WHERE mp.id IS NOT NULL AND blackListedProducts.productId IS NULL
    ORDER BY views DESC
    LIMIT @numberOfProducts
  )

SELECT
  accountId,
  providerServiceId,
  id,
  idType,
  product
FROM allowed_products ap
LEFT JOIN successfullyProcessedProducts rt
ON ap.id = rt.productId
WHERE rt.productId IS NULL
ORDER BY views DESC;
`;

const mostPopularProductsCountQuery = (
  eventTableId,
  productTableId,
  blackListedProductsTableId,
  scraperExternalDataTableId,
  platform,
  databaseId,
  providerServiceId,
  locale,
) => `
 WITH
  mostPopularProducts AS (${mostPopularProductsQuery({
    databaseId,
    providerServiceId,
    eventTableId,
    productTableId,
    blackListedProductsTableId,
    scraperExternalDataTableId,
    platform,
    locale,
  })})

  SELECT COUNT(*) as count from mostPopularProducts;
`;

const mapMostPopularProduct = (item, shop) => {
  const productObj = {};

  item.product.forEach((prop) => {
    if (prop.propertyName === 'handle' && shop.type === 'shopify') {
      productObj.url = `${shop.live_domain.toLowerCase()}/products/${prop.propertyValue}`;
    } else if (prop.propertyName === 'urlAlias' && shop.type === 'shoprenter') {
      const primaryDomain = shop.domains.find((domain) => domain.primary) ?? shop.domains[0];
      productObj.url = `${primaryDomain.domain}/${prop.propertyValue}`;
    } else if (prop.propertyName === 'title') {
      productObj.name = prop.propertyValue;
    } else {
      productObj[prop.propertyName] = prop.propertyValue;
    }
    const propPattern = /^(shortDescription|description|name)/;
    const isMatch = propPattern.test(prop.propertyName);
    if (isMatch) {
      productObj[prop.propertyName] = prop.propertyValue;
    }
  });

  return {
    id: item.id,
    accountId: item.accountId,
    providerServiceId: item.providerServiceId,
    ...productObj,
  };
};

const cleansePromptVariable = (rawVariable) => {
  return rawVariable.replace(/{|}/g, '');
};

function detectPromptVariables(text) {
  const variableRegex = /\{([a-zA-Z0-9_]*)(\.[a-zA-Z]+)?\}/g;
  const matches = text.match(variableRegex) || [];

  return matches.map((match) => cleansePromptVariable(match));
}

const cleanProduct = (product, { platform } = {}) => {
  const { id, accountId, providerServiceId } = product;
  const variablePattern = /^(shortDescription|description|name)/;
  try {
    const cleanedProduct = {};
    for (let [key, value] of Object.entries(product)) {
      const isMatch = variablePattern.test(key);

      if (isMatch) {
        // extract text from html
        value = cheerio.load(entities.decodeHTML(value)).text().trim();
        // unescape all escaped characters
        value = value.replaceAll('\\r', '\r').replaceAll('\\n', '\n').replaceAll('\\t', '\t');
        // strip ignored characters
        value = value.replaceAll(/(\r|\t)/g, '');
        if (platform === 'shoprenter') {
          // multiple newlines to single newline
          value = value.replace(/\n{2,}/g, '\n');
          // multiple whitespace(including newlines) to a single space
          value.replace(/\s{2,}/g, ' ').trim();
        }
      }

      cleanedProduct[key] = typeof value === 'string' ? value.trim() : value;
    }

    return cleanedProduct;
  } catch (error) {
    logger.error(error, { id, accountId, providerServiceId }, 'Failed to clean product details');

    return product;
  }
};

class UnknownPromptVariableError extends InsertableError {}
const UNKNOWN_PROMPT_VARIABLE = 'UNKNOWN_PROMPT_VARIABLE';

const checkUnknownPromptVariables = (resultRow, { promptText, variableNames, useExternalData }) => {
  const detectedVariables = detectPromptVariables(promptText);

  const unresolvedVariables = detectedVariables.filter(
    (variable) => !Object.keys(resultRow).includes(variable),
  );

  if (unresolvedVariables.length) {
    const errorRows = variableNames.map((variableName) => {
      return getErrorRow(
        {
          ...(useExternalData ? { productId: resultRow.url } : { productId: resultRow.id }),
          variableName,
        },
        {
          code: UNKNOWN_PROMPT_VARIABLE,
          message: `Prompt variable(s) ${unresolvedVariables.join(',')} not detected!`,
        },
      );
    });

    throw new UnknownPromptVariableError(errorRows);
  }
};

class ProductValidationError extends InsertableError {}
const PRODUCT_VALIDATION = 'PRODUCT_VALIDATION';

const validateProduct = (
  product,
  { variableNames, dataValidation, promptText, useExternalData },
) => {
  const validationErrors = [];
  const variablesUsedInPrompt = detectPromptVariables(promptText);

  variableNames.forEach((variableName) => {
    try {
      const dataValidationSetting = getDataValidationSetting(variableName, dataValidation);
      const usedDescriptionVariables = variablesUsedInPrompt.filter(
        (variable) => variable.startsWith('description') || variable.startsWith('shortDescription'),
      );
      if (usedDescriptionVariables.length > 0) {
        validateDescription(product, usedDescriptionVariables, dataValidationSetting);
      }
    } catch (err) {
      validationErrors.push(
        getErrorRow(
          {
            productId: useExternalData ? product.url : product.id,
            variableName,
          },
          {
            code: PRODUCT_VALIDATION,
            message: err.message,
          },
        ),
      );
    }
  });

  if (validationErrors.length) {
    throw new ProductValidationError(validationErrors);
  }
};

const truncatePromptVariableValue = (value, maxLength) => {
  if (value.length > maxLength) {
    return `${value.slice(0, maxLength)}`;
  }

  return value;
};

module.exports = {
  mapMostPopularProduct,
  productRawPropertiesView,
  mostPopularProductsCountQuery,
  viewedProductIdsView,
  checkUnknownPromptVariables,
  mostPopularProductPropertiesView,
  mostPopularProductsQuery,
  cleanProduct,
  missingMostPopularProductsQuery,
  currentProcessedProductCountQuery,
  detectPromptVariables,
  cleansePromptVariable,
  validateProduct,
  UnknownPromptVariableError,
  ProductValidationError,
  truncatePromptVariableValue,
  blackListedProductsView,
  successfullyProcessedView,
};
