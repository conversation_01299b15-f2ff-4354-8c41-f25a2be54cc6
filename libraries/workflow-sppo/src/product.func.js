const moment = require('moment');
const { BigQuery } = require('@google-cloud/bigquery');
const {
  getFormattedTableName,
  mock,
  runQuery,
  eventFactory,
  behaviouralEventsSchema,
  blackListedProductsTableSchema,
  productRawPropertiesSchema,
  scraperExternalDataSchema,
  unifiedProductsTableSchema,
} = require('@om/queries');
const { mostPopularProductsQuery } = require('.');

const projectId = process.env.SECURE_PROJECT_ID;
const bigqueryClient = new BigQuery({ projectId });

const defaultMostPopularQueryParams = {
  params: {
    fromDate: moment().subtract(1, 'year').toDate(),
    numberOfProducts: 100,
  },
};

describe('mostPopularProductsQuery integration tests', () => {
  test('handle empty events', async () => {
    const events = [
      eventFactory({
        timestamp: moment().toISOString(),
        events: [],
      }),
    ];
    const rawProperties = [];
    const blackList = [];
    const externalProperties = [];

    const eventTableId = getFormattedTableName(mock(events, behaviouralEventsSchema));
    const productTableId = getFormattedTableName(mock(rawProperties, productRawPropertiesSchema));
    const blackListedProductsTableId = getFormattedTableName(
      mock(blackList, blackListedProductsTableSchema),
    );
    const scraperExternalDataTableId = getFormattedTableName(
      mock(externalProperties, scraperExternalDataSchema),
    );

    const mostPopularProducts = await runQuery(
      bigqueryClient,
      mostPopularProductsQuery({
        eventTableId,
        productTableId,
        blackListedProductsTableId,
        scraperExternalDataTableId,
        platform: 'shopify',
      }),
      defaultMostPopularQueryParams,
    );

    expect(mostPopularProducts?.length).toBe(0);
  });

  test('handle events and empty raw props', async () => {
    const events = [
      eventFactory({
        timestamp: moment().toISOString(),
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '*************',
              },
            ],
          },
        ],
        context: [{ name: 'url', value: 'url' }],
      }),
    ];

    const rawProperties = [];
    const blackList = [];
    const externalProperties = [];

    const eventTableId = getFormattedTableName(mock(events, behaviouralEventsSchema));
    const productTableId = getFormattedTableName(mock(rawProperties, productRawPropertiesSchema));
    const blackListedProductsTableId = getFormattedTableName(
      mock(blackList, blackListedProductsTableSchema),
    );
    const scraperExternalDataTableId = getFormattedTableName(
      mock(externalProperties, scraperExternalDataSchema),
    );

    const mostPopularProducts = await runQuery(
      bigqueryClient,
      mostPopularProductsQuery({
        eventTableId,
        productTableId,
        blackListedProductsTableId,
        scraperExternalDataTableId,
        platform: 'shopify',
      }),
      defaultMostPopularQueryParams,
    );

    expect(mostPopularProducts?.length).toBe(0);
  });

  test('handle some events and some raw props for shopify', async () => {
    const events = [
      eventFactory({
        timestamp: moment().toISOString(),
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '*************',
              },
            ],
          },
        ],
        context: [{ name: 'url', value: 'url' }],
      }),
    ];

    const rawProperties = [
      {
        timestamp: moment().toISOString(),
        accountId: '138179',
        provider: 'shopify',
        providerServiceId: 'varnish-vine.myshopify.com',
        id: '"gid://shopify/Product/*************"',
        idType: 'product',
        propertyName: 'description',
        propertyValue: '""',
      },
    ];

    const blackList = [];
    const externalProperties = [];

    const eventTableId = getFormattedTableName(mock(events, behaviouralEventsSchema));
    const productTableId = getFormattedTableName(mock(rawProperties, productRawPropertiesSchema));
    const blackListedProductsTableId = getFormattedTableName(
      mock(blackList, blackListedProductsTableSchema),
    );
    const scraperExternalDataTableId = getFormattedTableName(
      mock(externalProperties, scraperExternalDataSchema),
    );

    const mostPopularProducts = await runQuery(
      bigqueryClient,
      mostPopularProductsQuery({
        eventTableId,
        productTableId,
        blackListedProductsTableId,
        scraperExternalDataTableId,
        platform: 'shopify',
      }),
      defaultMostPopularQueryParams,
    );

    expect(mostPopularProducts).toEqual([
      {
        accountId: '138179',
        id: '*************',
        idType: 'product',
        product: [{ propertyName: 'description', propertyValue: '' }],
        providerServiceId: 'varnish-vine.myshopify.com',
      },
    ]);
  });

  test('handle some events and some raw props for shoprenter', async () => {
    const events = [
      eventFactory({
        timestamp: moment().toISOString(),
        addresses: [
          {
            provider: 'optimonk',
            providerServiceId: '123456',
            customerAddress: '123',
            addressType: 'deviceId',
          },
          {
            provider: 'shoprenter',
            providerServiceId: 'reflexshop',
            customerAddress: null,
            addressType: 'customerId',
          },
        ],
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '16686',
              },
              {
                name: 'variantId',
                value: '16685',
              },
            ],
          },
        ],
        context: [{ name: 'url', value: 'url' }],
      }),
    ];

    const rawProperties = [
      {
        timestamp: moment().toISOString(),
        accountId: '60',
        provider: 'shoprenter',
        providerServiceId: 'reflexshop',
        id: '"16685"',
        idType: 'product',
        propertyName: 'description.1',
        propertyValue: '""',
      },
    ];

    const blackList = [];
    const externalProperties = [];

    const eventTableId = getFormattedTableName(mock(events, behaviouralEventsSchema));

    const productTableId = getFormattedTableName(mock(rawProperties, productRawPropertiesSchema));
    const blackListedProductsTableId = getFormattedTableName(
      mock(blackList, blackListedProductsTableSchema),
    );
    const scraperExternalDataTableId = getFormattedTableName(
      mock(externalProperties, scraperExternalDataSchema),
    );

    const mostPopularProducts = await runQuery(
      bigqueryClient,
      mostPopularProductsQuery({
        eventTableId,
        productTableId,
        blackListedProductsTableId,
        scraperExternalDataTableId,
        platform: 'shoprenter',
      }),
      defaultMostPopularQueryParams,
    );

    expect(mostPopularProducts).toEqual([
      {
        accountId: '60',
        id: '16685',
        idType: 'product',
        product: [{ propertyName: 'description.1', propertyValue: '' }],
        providerServiceId: 'reflexshop',
      },
    ]);
  });

  test('handle some events and some raw props for unas (unified products)', async () => {
    const providerServiceId = 'sy.com';
    const databaseId = 123456;

    const events = [
      eventFactory({
        timestamp: moment().toISOString(),
        addresses: [
          {
            provider: 'optimonk',
            providerServiceId: databaseId.toString(),
            customerAddress: '123',
            addressType: 'deviceId',
          },
        ],
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '*************',
              },
            ],
          },
        ],
        context: [{ name: 'url', value: 'url' }],
      }),
    ];

    const rawProperties = [
      {
        timestamp: moment.utc().toISOString(),
        databaseId,
        providerServiceId,
        productId: '*************',
        name: 'X',
        price: 1000,
        originalPrice: 1000,
        currency: 'HUF',
        imageUrl: '',
        description: 'X',
        parentProductId: '',
        locale: 'en',
        status: 'active',
      },
      {
        timestamp: moment.utc().subtract(7, 'day').toISOString(),
        databaseId,
        providerServiceId,
        productId: '*************',
        name: 'X',
        price: 1000,
        originalPrice: 1000,
        currency: 'HUF',
        imageUrl: '',
        description: 'X',
        parentProductId: '',
        locale: 'xyz',
        status: 'active',
      },
      {
        timestamp: moment.utc().subtract(1, 'day').toISOString(),
        databaseId,
        providerServiceId,
        productId: '*************',
        name: 'Test title',
        price: 1000,
        originalPrice: 1000,
        currency: 'HUF',
        imageUrl: '',
        description: 'Test desc',
        parentProductId: '',
        locale: 'xyz',
        status: 'active',
      },
    ];

    const blackList = [];
    const externalProperties = [];

    const eventTableId = getFormattedTableName(mock(events, behaviouralEventsSchema));
    const productTableId = getFormattedTableName(mock(rawProperties, unifiedProductsTableSchema));
    const blackListedProductsTableId = getFormattedTableName(
      mock(blackList, blackListedProductsTableSchema),
    );
    const scraperExternalDataTableId = getFormattedTableName(
      mock(externalProperties, scraperExternalDataSchema),
    );

    const mostPopularProducts = await runQuery(
      bigqueryClient,
      mostPopularProductsQuery({
        databaseId,
        providerServiceId,
        eventTableId,
        productTableId,
        blackListedProductsTableId,
        scraperExternalDataTableId,
        platform: 'unas',
        locale: 'xyz',
      }),
      defaultMostPopularQueryParams,
    );

    expect(mostPopularProducts).toEqual([
      {
        accountId: databaseId.toString(),
        id: '*************',
        idType: 'product',
        product: expect.arrayContaining([
          expect.objectContaining({ propertyName: 'title', propertyValue: 'Test title' }),
          expect.objectContaining({ propertyName: 'description', propertyValue: 'Test desc' }),
        ]),
        providerServiceId,
      },
    ]);
  });

  test('handle complex shoprenter query', async () => {
    const commonEventPart = {
      timestamp: moment().toISOString(),
      addresses: [
        {
          provider: 'optimonk',
          providerServiceId: '123456',
          customerAddress: '123',
          addressType: 'deviceId',
        },
        {
          provider: 'shoprenter',
          providerServiceId: 'reflexshop',
          customerAddress: null,
          addressType: 'customerId',
        },
      ],
    };
    const events = [
      eventFactory({
        ...commonEventPart,
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '16688',
              },
              {
                name: 'variantId',
                value: '16685',
              },
            ],
          },
        ],
        context: [{ name: 'url', value: 'url' }],
      }),
      eventFactory({
        ...commonEventPart,
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '16687',
              },
              {
                name: 'variantId',
                value: '16686',
              },
            ],
          },
        ],
        context: [{ name: 'url', value: 'url' }],
      }),
      eventFactory({
        ...commonEventPart,
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '16688', // make sure 16685 is more popular than 16686
              },
              {
                name: 'variantId',
                value: '16685', // make sure 16685 is more popular than 16686
              },
            ],
          },
        ],
        context: [{ name: 'url', value: 'url' }],
      }),
    ];

    const rawProperties = [
      {
        timestamp: moment().toISOString(),
        accountId: '60',
        provider: 'shoprenter',
        providerServiceId: 'reflexshop',
        id: '"16685"',
        idType: 'product',
        propertyName: 'description.1',
        propertyValue: '""',
      },
      {
        timestamp: moment().toISOString(),
        accountId: '60',
        provider: 'shoprenter',
        providerServiceId: 'reflexshop',
        id: '"16685"',
        idType: 'product',
        propertyName: 'name.1',
        propertyValue: '""',
      },
      {
        timestamp: moment().toISOString(),
        accountId: '60',
        provider: 'shoprenter',
        providerServiceId: 'reflexshop',
        id: '"16686"',
        idType: 'product',
        propertyName: 'description.1',
        propertyValue: '""',
      },
    ];

    const blackList = [];
    const externalProperties = [];

    const eventTableId = getFormattedTableName(mock(events, behaviouralEventsSchema));
    const productTableId = getFormattedTableName(mock(rawProperties, productRawPropertiesSchema));
    const blackListedProductsTableId = getFormattedTableName(
      mock(blackList, blackListedProductsTableSchema),
    );
    const scraperExternalDataTableId = getFormattedTableName(
      mock(externalProperties, scraperExternalDataSchema),
    );

    const mostPopularProducts = await runQuery(
      bigqueryClient,
      mostPopularProductsQuery({
        eventTableId,
        productTableId,
        blackListedProductsTableId,
        scraperExternalDataTableId,
        platform: 'shoprenter',
      }),
      defaultMostPopularQueryParams,
    );

    expect(mostPopularProducts).toEqual([
      {
        accountId: '60',
        id: '16685',
        idType: 'product',
        product: [
          { propertyName: 'description.1', propertyValue: '' },
          { propertyName: 'name.1', propertyValue: '' },
        ],
        providerServiceId: 'reflexshop',
      },
      {
        accountId: '60',
        id: '16686',
        idType: 'product',
        product: [{ propertyName: 'description.1', propertyValue: '' }],
        providerServiceId: 'reflexshop',
      },
    ]);
  });

  test('handle complex shopify query', async () => {
    const events = [
      eventFactory({
        timestamp: moment().toISOString(),
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '*************',
              },
            ],
          },
        ],
        context: [{ name: 'url', value: 'url' }],
      }),
      eventFactory({
        timestamp: moment().toISOString(),
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '*************',
              },
            ],
          },
        ],
        context: [{ name: 'url', value: 'url' }],
      }),
      eventFactory({
        timestamp: moment().toISOString(),
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '*************', // make sure ************* is more popular than *************
              },
            ],
          },
        ],
        context: [{ name: 'url', value: 'url' }],
      }),
      eventFactory({
        timestamp: moment().toISOString(),
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '*************',
              },
            ],
          },
        ],
      }),
      eventFactory({
        timestamp: moment().toISOString(),
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '*************',
              },
            ],
          },
        ],
      }),
      eventFactory({
        timestamp: moment().toISOString(),
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '*************',
              },
            ],
          },
        ],
      }),
      eventFactory({
        timestamp: moment().toISOString(),
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '*************',
              },
            ],
          },
        ],
      }),
    ];

    const rawProperties = [
      {
        timestamp: moment().toISOString(),
        accountId: '138179',
        provider: 'shopify',
        providerServiceId: 'varnish-vine.myshopify.com',
        id: '"gid://shopify/Product/*************"',
        idType: 'product',
        propertyName: 'description',
        propertyValue: '""',
      },
      {
        timestamp: moment().toISOString(),
        accountId: '138179',
        provider: 'shopify',
        providerServiceId: 'varnish-vine.myshopify.com',
        id: '"gid://shopify/Product/*************"',
        idType: 'product',
        propertyName: 'title',
        propertyValue: '""',
      },
      {
        timestamp: moment().toISOString(),
        accountId: '138179',
        provider: 'shopify',
        providerServiceId: 'varnish-vine.myshopify.com',
        id: '"gid://shopify/Product/*************"',
        idType: 'product',
        propertyName: 'description',
        propertyValue: '""',
      },
      {
        timestamp: moment().toISOString(),
        accountId: '138179',
        provider: 'shopify',
        providerServiceId: 'varnish-vine.myshopify.com',
        id: '"gid://shopify/Product/*************"', // this should be the most popular product
        idType: 'product',
        propertyName: 'description',
        propertyValue: '""',
      },
    ];

    const blackList = [];
    const externalProperties = [];

    const eventTableId = getFormattedTableName(mock(events, behaviouralEventsSchema));
    const productTableId = getFormattedTableName(mock(rawProperties, productRawPropertiesSchema));
    const blackListedProductsTableId = getFormattedTableName(
      mock(blackList, blackListedProductsTableSchema),
    );
    const scraperExternalDataTableId = getFormattedTableName(
      mock(externalProperties, scraperExternalDataSchema),
    );

    const mostPopularProducts = await runQuery(
      bigqueryClient,
      mostPopularProductsQuery({
        eventTableId,
        productTableId,
        blackListedProductsTableId,
        scraperExternalDataTableId,
        platform: 'shopify',
      }),
      defaultMostPopularQueryParams,
    );

    expect(mostPopularProducts).toEqual([
      {
        accountId: '138179',
        id: '*************',
        idType: 'product',
        product: [{ propertyName: 'description', propertyValue: '' }],
        providerServiceId: 'varnish-vine.myshopify.com',
      },
      {
        accountId: '138179',
        id: '*************',
        idType: 'product',
        product: [
          { propertyName: 'description', propertyValue: '' },
          { propertyName: 'title', propertyValue: '' },
        ],
        providerServiceId: 'varnish-vine.myshopify.com',
      },
      {
        accountId: '138179',
        id: '*************',
        idType: 'product',
        product: [{ propertyName: 'description', propertyValue: '' }],
        providerServiceId: 'varnish-vine.myshopify.com',
      },
    ]);
  });

  test('handle blacklist for shopify', async () => {
    const events = [
      eventFactory({
        timestamp: moment().toISOString(),
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '*************',
              },
            ],
          },
        ],
        context: [{ name: 'url', value: 'url' }],
      }),
      eventFactory({
        timestamp: moment().toISOString(),
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '*************',
              },
            ],
          },
        ],
        context: [{ name: 'url', value: 'url' }],
      }),
      eventFactory({
        timestamp: moment().toISOString(),
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '*************', // make sure ************* is more popular than *************
              },
            ],
          },
        ],
        context: [{ name: 'url', value: 'url' }],
      }),
    ];

    const rawProperties = [
      {
        timestamp: moment().toISOString(),
        accountId: '138179',
        provider: 'shopify',
        providerServiceId: 'varnish-vine.myshopify.com',
        id: '"gid://shopify/Product/*************"',
        idType: 'product',
        propertyName: 'description',
        propertyValue: '""',
      },
      {
        timestamp: moment().toISOString(),
        accountId: '138179',
        provider: 'shopify',
        providerServiceId: 'varnish-vine.myshopify.com',
        id: '"gid://shopify/Product/*************"',
        idType: 'product',
        propertyName: 'title',
        propertyValue: '""',
      },
      {
        timestamp: moment().toISOString(),
        accountId: '138179',
        provider: 'shopify',
        providerServiceId: 'varnish-vine.myshopify.com',
        id: '"gid://shopify/Product/*************"',
        idType: 'product',
        propertyName: 'description',
        propertyValue: '""',
      },
    ];

    const blackList = [{ productId: '*************' }];
    const externalProperties = [];

    const eventTableId = getFormattedTableName(mock(events, behaviouralEventsSchema));
    const productTableId = getFormattedTableName(mock(rawProperties, productRawPropertiesSchema));
    const blackListedProductsTableId = getFormattedTableName(
      mock(blackList, blackListedProductsTableSchema),
    );
    const scraperExternalDataTableId = getFormattedTableName(
      mock(externalProperties, scraperExternalDataSchema),
    );

    const mostPopularProducts = await runQuery(
      bigqueryClient,
      mostPopularProductsQuery({
        eventTableId,
        productTableId,
        blackListedProductsTableId,
        scraperExternalDataTableId,
        platform: 'shopify',
      }),
      defaultMostPopularQueryParams,
    );

    expect(mostPopularProducts).toEqual([
      {
        accountId: '138179',
        id: '*************',
        idType: 'product',
        product: [
          { propertyName: 'description', propertyValue: '' },
          { propertyName: 'title', propertyValue: '' },
        ],
        providerServiceId: 'varnish-vine.myshopify.com',
      },
    ]);
  });

  test('handle scraped external properties', async () => {
    const events = [
      eventFactory({
        timestamp: moment().toISOString(),
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '*************',
              },
            ],
          },
        ],
        context: [{ name: 'url', value: 'url' }],
      }),
      eventFactory({
        timestamp: moment().toISOString(),
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '*************',
              },
            ],
          },
        ],
        context: [{ name: 'url', value: 'url' }],
      }),
      eventFactory({
        timestamp: moment().toISOString(),
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '*************',
              },
            ],
          },
        ],
        context: [{ name: 'url', value: 'url' }],
      }),
      eventFactory({
        timestamp: moment().toISOString(),
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '*************',
              },
            ],
          },
        ],
        context: [{ name: 'url', value: 'url' }],
      }),
      eventFactory({
        timestamp: moment().toISOString(),
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '*************', // make sure ************* is more popular than *************
              },
            ],
          },
        ],
        context: [{ name: 'url', value: 'url' }],
      }),
      eventFactory({
        timestamp: moment().toISOString(),
        events: [
          {
            type: 'epv',
            props: [
              {
                name: 'productId',
                value: '*************',
              },
            ],
          },
        ],
        context: [{ name: 'url', value: 'url' }],
      }),
    ];

    const rawProperties = [
      {
        timestamp: moment().toISOString(),
        accountId: '138179',
        provider: 'shopify',
        providerServiceId: 'varnish-vine.myshopify.com',
        id: '"gid://shopify/Product/*************"',
        idType: 'product',
        propertyName: 'description',
        propertyValue: '""',
      },
      {
        timestamp: moment().toISOString(),
        accountId: '138179',
        provider: 'shopify',
        providerServiceId: 'varnish-vine.myshopify.com',
        id: '"gid://shopify/Product/*************"',
        idType: 'product',
        propertyName: 'title',
        propertyValue: '""',
      },
      {
        timestamp: moment().toISOString(),
        accountId: '138179',
        provider: 'shopify',
        providerServiceId: 'varnish-vine.myshopify.com',
        id: '"gid://shopify/Product/*************"',
        idType: 'product',
        propertyName: 'description',
        propertyValue: '""',
      },
      {
        timestamp: moment().toISOString(),
        accountId: '138179',
        provider: 'shopify',
        providerServiceId: 'varnish-vine.myshopify.com',
        id: '"gid://shopify/Product/*************"',
        idType: 'product',
        propertyName: 'description',
        propertyValue: '"desc39"',
      },
    ];

    const blackList = [];
    const externalProperties = [
      {
        timestamp: moment().toISOString(),
        url: 'producturl1',
        ids: [{ idType: 'productId', value: '*************' }],
        properties: [
          {
            name: 'review1',
            value: 'bad review',
          },
          {
            name: 'review2',
            value: 'good review',
          },
          { name: 'review3', value: 'superb review' },
        ],
      },
      {
        timestamp: moment().add(1, 'minutes').toISOString(), // this was scraped later
        url: 'producturl1',
        ids: [{ idType: 'productId', value: '*************' }],
        properties: [
          {
            name: 'review1',
            value: 'first review was changed',
          },
          {
            name: 'review2',
            value: 'good review',
          },
          { name: 'review3', value: 'superb review' },
          { name: 'review4', value: 'another review was added' },
        ],
      },
      {
        timestamp: moment().toISOString(),
        ids: [
          { idType: 'productId', value: '*************' },
          { idType: 'productId', value: '*************' },
        ],
        url: 'producturl2',
        properties: [
          {
            name: 'review1',
            value: 'totally recommended',
          },
          {
            name: 'review2',
            value: 'good review',
          },
          { name: 'review3', value: 'superb review' },
          { name: 'review4', value: 'new review' },
        ],
      },
      {
        timestamp: moment().toISOString(),
        ids: [{ idType: 'productId', value: '*************' }],
        url: 'producturl3',
        properties: [
          {
            name: 'reco1',
            value: 'p1',
          },
          {
            name: 'reco2',
            value: 'p2',
          },
          { name: 'reco3', value: 'p3' },
        ],
      },
    ];

    const eventTableId = getFormattedTableName(mock(events, behaviouralEventsSchema));
    const productTableId = getFormattedTableName(mock(rawProperties, productRawPropertiesSchema));
    const blackListedProductsTableId = getFormattedTableName(
      mock(blackList, blackListedProductsTableSchema),
    );
    const scraperExternalDataTableId = getFormattedTableName(
      mock(externalProperties, scraperExternalDataSchema),
    );

    const mostPopularProducts = await runQuery(
      bigqueryClient,
      mostPopularProductsQuery({
        eventTableId,
        productTableId,
        blackListedProductsTableId,
        scraperExternalDataTableId,
        platform: 'shopify',
      }),
      defaultMostPopularQueryParams,
    );

    expect(mostPopularProducts).toEqual([
      {
        accountId: '138179',
        id: '*************',
        idType: 'product',
        product: [
          { propertyName: 'description', propertyValue: '' },
          { propertyName: 'title', propertyValue: '' },
          {
            propertyName: 'ext_review1',
            propertyValue: 'totally recommended',
          },
          {
            propertyName: 'ext_review2',
            propertyValue: 'good review',
          },
          { propertyName: 'ext_review3', propertyValue: 'superb review' },
          { propertyName: 'ext_review4', propertyValue: 'new review' },
        ],
        providerServiceId: 'varnish-vine.myshopify.com',
      },
      {
        accountId: '138179',
        id: '*************',
        idType: 'product',
        product: [
          { propertyName: 'description', propertyValue: '' },
          {
            propertyName: 'ext_review1',
            propertyValue: 'first review was changed',
          },
          {
            propertyName: 'ext_review2',
            propertyValue: 'good review',
          },
          { propertyName: 'ext_review3', propertyValue: 'superb review' },
          { propertyName: 'ext_review4', propertyValue: 'another review was added' },
        ],
        providerServiceId: 'varnish-vine.myshopify.com',
      },
      {
        accountId: '138179',
        id: '*************',
        idType: 'product',
        product: [
          { propertyName: 'description', propertyValue: 'desc39' },
          {
            propertyName: 'ext_review1',
            propertyValue: 'totally recommended',
          },
          {
            propertyName: 'ext_review2',
            propertyValue: 'good review',
          },
          { propertyName: 'ext_review3', propertyValue: 'superb review' },
          { propertyName: 'ext_review4', propertyValue: 'new review' },
          { propertyName: 'ext_reco1', propertyValue: 'p1' },
          { propertyName: 'ext_reco2', propertyValue: 'p2' },
          { propertyName: 'ext_reco3', propertyValue: 'p3' },
        ],
        providerServiceId: 'varnish-vine.myshopify.com',
      },
    ]);
  });
});
