const { hash } = require('@om/schema/src/utils');
const { UNIFIED_PRODUCTS_PLATFORMS } = require('./isUnifiedProuctBased');

const UNIFIED_PRODUCTS_TABLE_ID = 'product_sync.products';

const getFormattedTableName = (tableName) => {
  return tableName.startsWith('(') ? tableName : `\`${tableName}\``;
};
const getProductTableId = (accountId, providerServiceId, platform) => {
  // [product-sync][isUnified]
  if (UNIFIED_PRODUCTS_PLATFORMS.includes(platform)) {
    return UNIFIED_PRODUCTS_TABLE_ID;
  }
  return `products_optimonk.raw_properties_${accountId}_${hash(providerServiceId)}`;
};
const getEventTableId = (accountId, providerServiceId) =>
  `behavioural_events_optimonk.events_${accountId}_${hash(providerServiceId)}`;
const getPPOResultTableId = (accountId, providerServiceId, uuid) =>
  `smart_ppo.smart_ppo_results_${accountId}_${hash(providerServiceId)}_${uuid}`;
const getProductBlackListTableId = (accountId, providerServiceId) =>
  `smart_ppo.blacklisted_products_${accountId}_${hash(providerServiceId)}`;

const getLanguagesTableId = (accountId, providerServiceId) =>
  `languages_optimonk.raw_properties_${accountId}_${hash(providerServiceId)}`;

const getScraperExternalDataTableId = (accountId, providerServiceId) =>
  `smart_ppo.scraper_external_data_${accountId}_${hash(providerServiceId)}`;

const getDescriptionEmbeddingsTableId = (accountId, providerServiceId) =>
  `description_embeddings_${accountId}_${hash(providerServiceId)}`;

const getRecommendationsTempTableId = (accountId, providerServiceId) =>
  `recommendation_temp_result_${accountId}_${hash(providerServiceId)}`;

const getSPRWhiteListTableId = (accountId, providerServiceId) =>
  `recommendation_whitelist_${accountId}_${hash(providerServiceId)}`;

module.exports = {
  UNIFIED_PRODUCTS_TABLE_ID,
  getProductTableId,
  getEventTableId,
  getFormattedTableName,
  getPPOResultTableId,
  getLanguagesTableId,
  getProductBlackListTableId,
  getScraperExternalDataTableId,
  getDescriptionEmbeddingsTableId,
  getRecommendationsTempTableId,
  getSPRWhiteListTableId,
};
