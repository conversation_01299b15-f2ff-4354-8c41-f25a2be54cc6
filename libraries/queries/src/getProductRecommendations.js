const { UNIFIED_PRODUCTS_PLATFORMS } = require('./isUnifiedProuctBased');
const { productRawPropertiesView } = require('./products');

const generateRecommendationsForProducts = ({
  accountId,
  providerServiceId,
  eventTable,
  productTable,
  textEmbeddingTable,
  requestedItems,
  platform = 'shopify',
  titleProperty = 'name.1',
  numberOfRecommendations = 10,
  whiteListedProductsTable,
  locale,
}) => {
  const events = eventTable.startsWith('(') ? eventTable : `\`${eventTable}\``;
  const products = productTable.startsWith('(') ? productTable : `\`${productTable}\``;
  const textEmbeddings = textEmbeddingTable.startsWith('(')
    ? textEmbeddingTable
    : `\`${textEmbeddingTable}\``;
  const whiteListedProducts = whiteListedProductsTable
    ? whiteListedProductsTable.startsWith('(')
      ? whiteListedProductsTable
      : `\`${whiteListedProductsTable}\``
    : null;

  // [product-sync]
  const mostPopularProducts = `
  SELECT
    SAFE_CAST(REPLACE(${
      ['shopify', ...UNIFIED_PRODUCTS_PLATFORMS].includes(platform) ? 'productId' : 'variantId'
    }, '"', '') AS STRING) as productId,
    SAFE_CAST(REPLACE(variantId, '"', '') AS STRING) as variantId,
    COUNT(*) AS views
  FROM (
    SELECT
      timestamp AS timestamp,
      providerServiceId,
      ${
        ['shopify', ...UNIFIED_PRODUCTS_PLATFORMS].includes(platform)
          ? 'ARRAY(SELECT AS STRUCT pr.value FROM ev.props AS pr WHERE pr.name = "productId")[SAFE_OFFSET(0)].value AS productId,'
          : ''
      }
      ARRAY(SELECT AS STRUCT pr.value FROM ev.props AS pr WHERE pr.name = "variantId")[SAFE_OFFSET(0)].value AS variantId,
      (SELECT ctx.value FROM UNNEST(be.context) as ctx WHERE ctx.name = 'url') as url
    FROM ${events} be,
      UNNEST(be.addresses) ad,
      UNNEST(be.events) ev
    WHERE
      ad.customerAddress IS NOT NULL
      AND ad.provider = 'optimonk'
      AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
      AND ev.type = "epv"
  )
  GROUP BY productId, variantId
`;

  const viewedProductAndVariantIds = `
  viewedProductIds AS (
    SELECT *
    FROM (${mostPopularProducts})
    ${
      platform === 'shopify'
        ? 'QUALIFY ROW_NUMBER() OVER(PARTITION BY productId ORDER BY views desc) = 1'
        : ''
    }
  )`;

  return `
  -- Generate recommendations for ${accountId} - ${providerServiceId}

  WITH
   ${viewedProductAndVariantIds}
   , productProperties AS (
    WITH rawProductProperties AS (
      ${productRawPropertiesView(products, {
        platform,
        databaseId: accountId,
        providerServiceId,
        locale,
      })}
    )
    SELECT
      id,
      ARRAY_AGG(
        STRUCT(
            SAFE_CAST(REPLACE(propertyName, '"', '') AS STRING) as propertyName,
            SAFE_CAST(REPLACE(propertyValue, '"', '') AS STRING) as propertyValue
          )
      ) as properties,
    FROM rawProductProperties
    GROUP BY id
  )
  , requiredProductProperties AS (
    SELECT * FROM productProperties pp,
    UNNEST(pp.properties) AS prop
    WHERE prop.propertyName IN ('${titleProperty}')
    AND prop.propertyValue IS NOT NULL AND prop.propertyValue != ""
  )
  , mostPopularProducts AS (
    SELECT
      productId as id,
      ${
        [...UNIFIED_PRODUCTS_PLATFORMS].includes(platform)
          ? 'productId'
          : 'viewedProductIds.variantId'
      } as variantId,
      views
    FROM viewedProductIds
    GROUP BY id, variantId, views
    ORDER BY MAX(views) DESC
    ${requestedItems ? `LIMIT ${requestedItems}` : ''}
  )
  , textEmbeddings AS (
    SELECT * FROM ${textEmbeddings}
  )

  ${
    whiteListedProductsTable
      ? `  , whiteListedTextEmbeddings AS (
    SELECT tE.id as id, * EXCEPT (id) FROM ${textEmbeddings} as tE
    RIGHT JOIN ${whiteListedProducts} as wL ON (tE.id = wL.id)
  )`
      : ''
  }

  , mostPopularProductsWithTextEmbeddings AS (
    SELECT
      mostPopularProducts.id as id,
      mostPopularProducts.variantId as variantId,
      textEmbeddings.textEmbedding as textEmbedding,
      mostPopularProducts.views as views
    FROM mostPopularProducts
    INNER JOIN textEmbeddings ON (mostPopularProducts.id = textEmbeddings.id AND mostPopularProducts.variantId = textEmbeddings.variantId)
    ORDER BY views
  )
  , distances AS (
    SELECT
      sourceProducts.id as id,
      sourceProducts.variantId  as variantId,
      recommendedProducts.id as recomendedProductId,
      recommendedProducts.variantId as recommendedVariantId,
    ML.DISTANCE(sourceProducts.textEmbedding, recommendedProducts.textEmbedding, 'COSINE') as distance,
    FROM mostPopularProductsWithTextEmbeddings sourceProducts
    CROSS JOIN ${
      whiteListedProductsTable ? 'whiteListedTextEmbeddings' : 'textEmbeddings'
    } recommendedProducts
    WHERE sourceProducts.id != recommendedProducts.id
    ORDER BY distance
  )
  , recommendationOrders AS (
    SELECT
      *,
      ROW_NUMBER() over (PARTITION BY id ORDER BY distance) as recommendationOrder
    FROM distances
    ORDER BY id
  )
  , topRecommendationsWithProperties AS (
    SELECT
      recommendationOrders.id as id,
      recommendationOrders.variantId as variantId,
      recommendationOrders.recomendedProductId as recomendedProductId,
      recommendationOrders.recommendedVariantId as recommendedVariantId,
      productProperties.properties as properties,
      recommendationOrders.recommendationOrder as recommendationOrder,
      ROW_NUMBER() OVER (PARTITION BY recommendationOrders.id ORDER BY recommendationOrders.recommendationOrder ASC) AS rank
    FROM recommendationOrders
    INNER JOIN requiredProductProperties productProperties ON
      SAFE_CAST(REGEXP_EXTRACT(recommendationOrders.recomendedProductId, r'(\\d+)') AS STRING) = SAFE_CAST(REGEXP_EXTRACT(productProperties.id, r'(\\d+)') AS STRING)
  )
  , topNRecommendations AS (
    SELECT *
    FROM topRecommendationsWithProperties
    WHERE rank <= ${numberOfRecommendations}
  )
  , renamedProps AS (
    SELECT
      id,
      ANY_VALUE(variantId) as variantId,
      recomendedProductId,
      recommendedVariantId,
      ARRAY_AGG((SELECT AS STRUCT p.propertyName as name,   p.propertyValue as value)) as properties,
      recommendationOrder
    FROM topNRecommendations, UNNEST(properties) as p
    GROUP BY id, recomendedProductId, recommendedVariantId, recommendationOrder
  )

  SELECT
    id,
    variantId,
    ARRAY_AGG((SELECT AS STRUCT recomendedProductId as productId, recommendedVariantId as variantId, properties) ORDER BY recommendationOrder) AS recommendations,
  FROM renamedProps
  GROUP BY id, variantId
  `;
};

const generateRecommendationsForExternalData = ({
  accountId,
  providerServiceId,
  eventTable,
  externalDataTable,
  textEmbeddingTable,
  requestedItems,
  numberOfRecommendations = 10,
  whiteListedProductsTable,
}) => {
  const events = eventTable.startsWith('(') ? eventTable : `\`${eventTable}\``;
  const externalProperties = externalDataTable.startsWith('(')
    ? externalDataTable
    : `\`${externalDataTable}\``;
  const textEmbeddings = textEmbeddingTable.startsWith('(')
    ? textEmbeddingTable
    : `\`${textEmbeddingTable}\``;
  const whiteListedProducts = whiteListedProductsTable
    ? whiteListedProductsTable.startsWith('(')
      ? whiteListedProductsTable
      : `\`${whiteListedProductsTable}\``
    : null;

  return `
    -- Generate recommendations for ${accountId} - ${providerServiceId}

    WITH
    viewedPages AS (
      SELECT
        COUNT(*) AS views,
        REGEXP_REPLACE(COALESCE(canonicalUrl, url), r'/$', '') as url
      FROM (
        SELECT
          timestamp AS timestamp,
          providerServiceId,
          ARRAY(SELECT SPLIT(pr.value, '?')[SAFE_OFFSET(0)] FROM ev.props AS pr WHERE pr.name = "url")[SAFE_OFFSET(0)] AS url,
          ARRAY(SELECT AS STRUCT pr.value FROM ev.props AS pr WHERE pr.name = "canonicalUrl")[SAFE_OFFSET(0)].value AS canonicalUrl,
        FROM ${events} be,
          UNNEST(be.addresses) ad,
          UNNEST(be.events) ev
        WHERE
          ad.customerAddress IS NOT NULL
          AND ad.provider = 'optimonk'
          AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
          AND ev.type = "pageView"
      )
      GROUP BY url
    )
    , mostPopularPages AS (
        SELECT
          url as id,
          views
        FROM viewedPages
        GROUP BY id, views
        ORDER BY MAX(views) DESC
        ${requestedItems ? `LIMIT ${requestedItems}` : ''}
    )
    , textEmbeddings AS (
      SELECT * FROM ${textEmbeddings}
    )

    ${
      whiteListedProductsTable
        ? `  , whiteListedTextEmbeddings AS (
      SELECT tE.id as id, * EXCEPT (id) FROM ${textEmbeddings} as tE
      RIGHT JOIN ${whiteListedProducts} as wL ON (tE.id = wL.id)
      )`
        : ''
    }
    , mostPopularPagesWithTextEmbeddings AS (
      SELECT
        mostPopularPages.id as id,
        textEmbeddings.textEmbedding as textEmbedding,
        mostPopularPages.views as views
      FROM mostPopularPages
      INNER JOIN textEmbeddings ON (mostPopularPages.id = textEmbeddings.id)
      ORDER BY views
    )
    , distances AS (
      SELECT
        sourcePages.id as id,
        recommendedPages.id as recomendedPageId,
      ML.DISTANCE(sourcePages.textEmbedding, recommendedPages.textEmbedding, 'COSINE') as distance,
      FROM mostPopularPagesWithTextEmbeddings sourcePages
      CROSS JOIN ${
        whiteListedProductsTable ? 'whiteListedTextEmbeddings' : 'textEmbeddings'
      } recommendedPages
      WHERE sourcePages.id != recommendedPages.id
      ORDER BY distance
    )
    , recommendationOrders AS (
      SELECT
        *,
        ROW_NUMBER() over (PARTITION BY id ORDER BY distance) as recommendationOrder
      FROM distances
      ORDER BY id
    )
    , externalProperties AS (
      WITH latestProperties AS (
        SELECT *
        FROM ${externalProperties}
        WHERE
            timestamp > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 365 DAY)
        QUALIFY ROW_NUMBER() OVER (PARTITION BY url ORDER BY timestamp DESC) = 1
      )
      SELECT
          lp.timestamp as timestamp,
          lp.url as url,
          lp.properties as properties
        FROM
          latestProperties as lp
        WHERE (
            SELECT COUNT(*)
            FROM UNNEST(lp.properties) AS prop
            WHERE
            prop.name IN ('price', 'imgUrl', 'name', 'currency')
            AND prop.value IS NOT NULL
        ) = 4
    )
    , topRecommendationsWithProperties AS (
      SELECT
        recommendationOrders.id as id,
        recommendationOrders.recomendedPageId as url,
        externalProperties.properties as properties,
        recommendationOrder
      FROM recommendationOrders
      INNER JOIN externalProperties ON (recommendationOrders.recomendedPageId = externalProperties.url)
      WHERE
          recommendationOrder <= ${numberOfRecommendations}
    )

    SELECT
      id,
      null as variantId,
      ARRAY_AGG(
        (SELECT AS STRUCT url as productId, null as variantId, ARRAY_CONCAT(properties, ARRAY(SELECT AS STRUCT 'url' as name, url as value)) as properties) ORDER BY recommendationOrder) AS recommendations,
    FROM topRecommendationsWithProperties
    GROUP BY id
  `;
};

module.exports = {
  generateRecommendationsForProducts,
  generateRecommendationsForExternalData,
};
