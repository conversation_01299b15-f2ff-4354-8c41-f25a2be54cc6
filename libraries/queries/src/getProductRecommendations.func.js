const { BigQuery } = require('@google-cloud/bigquery');
const moment = require('moment');
const {
  descriptionEmbeddingsSchema,
  behaviouralEventsSchema,
  productRawPropertiesSchema,
  scraperExternalDataSchema,
  SPRWhiteListedProductsSchema,
  unifiedProductsTableSchema,
} = require('./schema');
const {
  generateRecommendationsForProducts,
  generateRecommendationsForExternalData,
} = require('./getProductRecommendations');
const testUtils = require('./testUtils');

const projectId = process.env.SECURE_PROJECT_ID;
const bigquery = new BigQuery({ projectId });

const { mock, runQuery, eventFactory } = testUtils;
const testEmbeddings = require('./productRecommendationTestData.json');
const testEmbeddingsForShoprenter = require('./productRecommendationTestDataShoprenter.json');
// text embeddings was generated from the following data
// [
//   ('p1', 'p1', 'very boring description'),
//   ('p2', 'p2', 'less boring description'),
//   ('p3', 'p3', 'exciting description'),
//   ('p4', 'p4', 'no description'),
//   ('p5', 'p5', 'this is something else entirely'),
//   ('p6', 'p6', 'buy me, promise i will make you happy'),
//   ('p7', 'p7', 'best product of the world you cant live without'),
//   ('p8', 'p8', 'not so great product but hey, it is cheap'),
//   ('p8', 'p8', 'inferior product in every possible sense imaginable'),
//   ('p10', 'p10', 'this will make you miserable'),
//   ('p11', 'p11', 'everyone loves this'),
//   ('p12', 'p12', 'it is totally in now, you need this if you want to be cool')
// ]

describe('generate recommendations', () => {
  describe('shopify/shoprenter', () => {
    const run = async ({
      accountId = '123',
      providerServiceId = 'test',
      events = [],
      products = [],
      embeddings = [],
      requestedItems = 3,
      platform,
      titleProperty,
      numberOfRecommendations = 3,
      whiteListedProducts,
    } = {}) => {
      const query = generateRecommendationsForProducts({
        accountId,
        providerServiceId,
        eventTable: mock(events, behaviouralEventsSchema),
        productTable: mock(products, productRawPropertiesSchema),
        textEmbeddingTable: mock(embeddings, descriptionEmbeddingsSchema),
        requestedItems,
        platform,
        titleProperty,
        numberOfRecommendations,
        whiteListedProductsTable: whiteListedProducts
          ? mock(whiteListedProducts, SPRWhiteListedProductsSchema)
          : null,
      });
      return runQuery({
        bigquery,
        query,
      });
    };

    it('generate for shopify', async () => {
      // we are looking for the recommendations of the three most popular products, three recommendations each
      // we have epvs for 5 products and text embeddings for 11
      const result = await run({
        events: [
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '1',
                  },
                  {
                    name: 'variantId',
                    value: 'v1',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '2',
                  },
                  {
                    name: 'variantId',
                    value: 'v2_2',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '2',
                  },
                  {
                    name: 'variantId',
                    value: 'v2',
                  },
                ],
              },
            ],
          }),
          // v2 variant is more popular
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '2',
                  },
                  {
                    name: 'variantId',
                    value: 'v2',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '3',
                  },
                  {
                    name: 'variantId',
                    value: 'v3',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '4',
                  },
                  {
                    name: 'variantId',
                    value: 'v4',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '5',
                  },
                  {
                    name: 'variantId',
                    value: 'v5',
                  },
                ],
              },
            ],
          }),
        ],
        products: [
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            accountId: '123',
            provider: 'shopify',
            providerServiceId: 'test',
            id: '"gid://shopify/Product/1"',
            idType: 'product',
            propertyName: 'title',
            propertyValue: '"some boring title"',
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            accountId: '123',
            provider: 'shopify',
            providerServiceId: 'test',
            id: '"gid://shopify/Product/2"',
            idType: 'product',
            propertyName: 'title',
            propertyValue: '"title2"',
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            accountId: '123',
            provider: 'shopify',
            providerServiceId: 'test',
            id: '"gid://shopify/Product/3"',
            idType: 'product',
            propertyName: 'title',
            propertyValue: '"title3"',
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            accountId: '123',
            provider: 'shopify',
            providerServiceId: 'test',
            id: '"gid://shopify/Product/4"',
            idType: 'product',
            propertyName: 'title',
            propertyValue: '"title4"',
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            accountId: '123',
            provider: 'shopify',
            providerServiceId: 'test',
            id: '"gid://shopify/Product/5"',
            idType: 'product',
            propertyName: 'title',
            propertyValue: '"title5"',
          },
        ],
        embeddings: testEmbeddings.map((embedding) => {
          return {
            ...embedding,
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
          };
        }),
        titleProperty: 'title',
        platform: 'shopify',
      });

      expect(result.length).toEqual(3);
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: '1',
            variantId: 'v1',
            recommendations: [
              {
                productId: '2',
                variantId: 'v2',
                properties: [
                  {
                    name: 'title',
                    value: 'title2',
                  },
                ],
              },
              {
                productId: '4',
                variantId: 'v4',
                properties: [
                  {
                    name: 'title',
                    value: 'title4',
                  },
                ],
              },
              {
                productId: '3',
                variantId: 'v3',
                properties: [
                  {
                    name: 'title',
                    value: 'title3',
                  },
                ],
              },
            ],
          }),
          expect.objectContaining({
            id: '2',
            variantId: 'v2',
            recommendations: [
              {
                productId: '1',
                variantId: 'v1',
                properties: [
                  {
                    name: 'title',
                    value: 'some boring title',
                  },
                ],
              },
              {
                productId: '4',
                variantId: 'v4',
                properties: [
                  {
                    name: 'title',
                    value: 'title4',
                  },
                ],
              },
              {
                productId: '3',
                variantId: 'v3',
                properties: [
                  {
                    name: 'title',
                    value: 'title3',
                  },
                ],
              },
            ],
          }),
          expect.objectContaining({
            id: '3',
            variantId: 'v3',
            recommendations: [
              {
                productId: '1',
                variantId: 'v1',
                properties: [
                  {
                    name: 'title',
                    value: 'some boring title',
                  },
                ],
              },
              {
                productId: '2',
                variantId: 'v2',
                properties: [
                  {
                    name: 'title',
                    value: 'title2',
                  },
                ],
              },
              {
                productId: '4',
                variantId: 'v4',
                properties: [
                  {
                    name: 'title',
                    value: 'title4',
                  },
                ],
              },
            ],
          }),
        ]),
      );
    });

    it('generate for shoprenter', async () => {
      const result = await run({
        events: [
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '32519',
                  },
                  {
                    name: 'variantId',
                    value: '32519',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '32519',
                  },
                  {
                    name: 'variantId',
                    value: '32519',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '32478',
                  },
                  {
                    name: 'variantId',
                    value: '32478',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '27476',
                  },
                  {
                    name: 'variantId',
                    value: '27476',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '9629',
                  },
                  {
                    name: 'variantId',
                    value: '9629',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '30439',
                  },
                  {
                    name: 'variantId',
                    value: '30439',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '30439',
                  },
                  {
                    name: 'variantId',
                    value: '30439',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '25242',
                  },
                  {
                    name: 'variantId',
                    value: '25242',
                  },
                ],
              },
            ],
          }),
        ],
        products: [
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"25242"',
            idType: 'product',
            propertyName: 'name.1',
            propertyValue: '"SAMPON 400 ML UBORKAKIVONATTAL BABA<br />Rendelési kód: 41079"',
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"32519"',
            idType: 'product',
            propertyName: 'name.1',
            propertyValue: '"Sampon 440 ml Syoss Blonde&Silver"',
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"32478"',
            idType: 'product',
            propertyName: 'name.1',
            propertyValue: '"Balzsam 440 ml Syoss Blonde&Silver"',
          },
          // 30439 is missing the name.1 property. it gets recommendations, but it cant be used as a recommendation
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"9629"',
            idType: 'product',
            propertyName: 'name.1',
            propertyValue:
              '"Golyóstoll nyomógombos 0,7mm, dobozban lazac test, Zebra 901, írásszín kék"',
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"27476"',
            idType: 'product',
            propertyName: 'name.1',
            propertyValue:
              '"Golyóstoll nyomógombos 0,7mm, dobozban lazac test, Zebra 901, írásszín kék"',
          },
        ],
        embeddings: testEmbeddingsForShoprenter.map((embedding) => {
          return {
            ...embedding,
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
          };
        }),
        titleProperty: 'name.1',
        platform: 'shoprenter',
      });

      expect(result.length).toEqual(3);
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: '27476',
            variantId: '27476',
            recommendations: expect.arrayContaining([
              {
                productId: '32519',
                variantId: '32519',
                properties: [
                  {
                    name: 'name.1',
                    value: 'Sampon 440 ml Syoss Blonde\u0026Silver',
                  },
                ],
              },
              {
                productId: '25242',
                variantId: '25242',
                properties: [
                  {
                    name: 'name.1',
                    value:
                      'SAMPON 400 ML UBORKAKIVONATTAL BABA\u003cbr /\u003eRendelési kód: 41079',
                  },
                ],
              },
              {
                productId: '9629',
                variantId: '9629',
                properties: [
                  {
                    name: 'name.1',
                    value:
                      'Golyóstoll nyomógombos 0,7mm, dobozban lazac test, Zebra 901, írásszín kék',
                  },
                ],
              },
            ]),
          }),
          expect.objectContaining({
            id: '30439',
            variantId: '30439',
            recommendations: [
              {
                productId: '9629',
                variantId: '9629',
                properties: [
                  {
                    name: 'name.1',
                    value:
                      'Golyóstoll nyomógombos 0,7mm, dobozban lazac test, Zebra 901, írásszín kék',
                  },
                ],
              },
              {
                productId: '25242',
                variantId: '25242',
                properties: [
                  {
                    name: 'name.1',
                    value:
                      'SAMPON 400 ML UBORKAKIVONATTAL BABA\u003cbr /\u003eRendelési kód: 41079',
                  },
                ],
              },
              {
                productId: '32519',
                variantId: '32519',
                properties: [
                  {
                    name: 'name.1',
                    value: 'Sampon 440 ml Syoss Blonde\u0026Silver',
                  },
                ],
              },
            ],
          }),
          expect.objectContaining({
            id: '32519',
            variantId: '32519',
            recommendations: [
              {
                productId: '32478',
                variantId: '32478',
                properties: [
                  {
                    name: 'name.1',
                    value: 'Balzsam 440 ml Syoss Blonde\u0026Silver',
                  },
                ],
              },
              {
                productId: '25242',
                variantId: '25242',
                properties: [
                  {
                    name: 'name.1',
                    value:
                      'SAMPON 400 ML UBORKAKIVONATTAL BABA\u003cbr /\u003eRendelési kód: 41079',
                  },
                ],
              },
              {
                productId: '27476',
                variantId: '27476',
                properties: [
                  {
                    name: 'name.1',
                    value:
                      'Golyóstoll nyomógombos 0,7mm, dobozban lazac test, Zebra 901, írásszín kék',
                  },
                ],
              },
            ],
          }),
        ]),
      );
    });

    it('should generate recommendation only for products in whitelist table if whitelist table name is passed', async () => {
      // we are looking for the recommendations of the three most popular products, three recommendations each
      // we have epvs for 5 products and text embeddings for 11
      const result = await run({
        events: [
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '1',
                  },
                  {
                    name: 'variantId',
                    value: 'v1',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '2',
                  },
                  {
                    name: 'variantId',
                    value: 'v2_2',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '2',
                  },
                  {
                    name: 'variantId',
                    value: 'v2',
                  },
                ],
              },
            ],
          }),
          // v2 variant is more popular
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '2',
                  },
                  {
                    name: 'variantId',
                    value: 'v2',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '3',
                  },
                  {
                    name: 'variantId',
                    value: 'v3',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '4',
                  },
                  {
                    name: 'variantId',
                    value: 'v4',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: '5',
                  },
                  {
                    name: 'variantId',
                    value: 'v5',
                  },
                ],
              },
            ],
          }),
        ],
        products: [
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            accountId: '123',
            provider: 'shopify',
            providerServiceId: 'test',
            id: '"gid://shopify/Product/1"',
            idType: 'product',
            propertyName: 'title',
            propertyValue: '"some boring title"',
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            accountId: '123',
            provider: 'shopify',
            providerServiceId: 'test',
            id: '"gid://shopify/Product/2"',
            idType: 'product',
            propertyName: 'title',
            propertyValue: '"title2"',
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            accountId: '123',
            provider: 'shopify',
            providerServiceId: 'test',
            id: '"gid://shopify/Product/3"',
            idType: 'product',
            propertyName: 'title',
            propertyValue: '"title3"',
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            accountId: '123',
            provider: 'shopify',
            providerServiceId: 'test',
            id: '"gid://shopify/Product/4"',
            idType: 'product',
            propertyName: 'title',
            propertyValue: '"title4"',
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            accountId: '123',
            provider: 'shopify',
            providerServiceId: 'test',
            id: '"gid://shopify/Product/5"',
            idType: 'product',
            propertyName: 'title',
            propertyValue: '"title5"',
          },
        ],
        embeddings: testEmbeddings.map((embedding) => {
          return {
            ...embedding,
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
          };
        }),
        whiteListedProducts: [{ id: '3' }, { id: '4' }, { id: '5' }],
        titleProperty: 'title',
        platform: 'shopify',
      });

      expect(result.length).toEqual(3);
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: '1',
            variantId: 'v1',
            recommendations: [
              {
                productId: '4',
                variantId: 'v4',
                properties: [
                  {
                    name: 'title',
                    value: 'title4',
                  },
                ],
              },
              {
                productId: '3',
                variantId: 'v3',
                properties: [
                  {
                    name: 'title',
                    value: 'title3',
                  },
                ],
              },
              {
                productId: '5',
                variantId: 'v5',
                properties: [
                  {
                    name: 'title',
                    value: 'title5',
                  },
                ],
              },
            ],
          }),
          expect.objectContaining({
            id: '2',
            variantId: 'v2',
            recommendations: [
              {
                productId: '4',
                variantId: 'v4',
                properties: [
                  {
                    name: 'title',
                    value: 'title4',
                  },
                ],
              },
              {
                productId: '3',
                variantId: 'v3',
                properties: [
                  {
                    name: 'title',
                    value: 'title3',
                  },
                ],
              },
              {
                productId: '5',
                variantId: 'v5',
                properties: [
                  {
                    name: 'title',
                    value: 'title5',
                  },
                ],
              },
            ],
          }),
          expect.objectContaining({
            id: '3',
            variantId: 'v3',
            recommendations: [
              {
                productId: '4',
                variantId: 'v4',
                properties: [
                  {
                    name: 'title',
                    value: 'title4',
                  },
                ],
              },
              {
                productId: '5',
                variantId: 'v5',
                properties: [
                  {
                    name: 'title',
                    value: 'title5',
                  },
                ],
              },
            ],
          }),
        ]),
      );
    });
  });

  describe('custom platform', () => {
    const run = async ({
      accountId = '123',
      providerServiceId = 'test',
      events = [],
      externalData = [],
      embeddings = [],
      requestedItems = 3,
      numberOfRecommendations = 3,
      whiteListedProducts,
    } = {}) => {
      const query = generateRecommendationsForExternalData({
        accountId,
        providerServiceId,
        eventTable: mock(events, behaviouralEventsSchema),
        externalDataTable: mock(externalData, scraperExternalDataSchema),
        textEmbeddingTable: mock(embeddings, descriptionEmbeddingsSchema),
        requestedItems,
        numberOfRecommendations,
        whiteListedProductsTable: whiteListedProducts
          ? mock(whiteListedProducts, SPRWhiteListedProductsSchema)
          : null,
      });
      return runQuery({
        bigquery,
        query,
      });
    };

    it('generate for custom platform', async () => {
      // we are looking for the recommendations of the three most popular pages, three recommendations each
      // we have pageViews for 5 pates and text embeddings for 11 pages
      const id1 = 'https://myshop/products/blue-jacket';
      const id2 = 'https://myshop/products/red-book';
      const id3 = 'https://myshop/products/white-jacket';
      const id4 = 'https://myshop/products/no-desc-product';
      const embeddingsForPages = testEmbeddings.map((embedding) => {
        return {
          ...embedding,
          timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
          variantId: null,
        };
      });
      embeddingsForPages[0].id = id1;
      embeddingsForPages[1].id = id2;
      embeddingsForPages[2].id = id3;
      embeddingsForPages[3].id = id4;
      const result = await run({
        events: [
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: id1,
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: 'https://myshop/products/blue-jacket',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: 'https://myshop/products/red-book',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: 'https://myshop/products/red-book',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: 'https://myshop/products/white-jacket',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: 'https://myshop/products/white-jacket',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: 'https://myshop/products/yellow-jacket',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: 'https://myshop/products/black-jacket',
                  },
                ],
              },
            ],
          }),
        ],
        externalData: [
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            url: 'https://myshop/products/red-book',
            ids: [{ idType: 'productId', value: 'p1' }],
            properties: [
              {
                name: 'id',
                value: 'p1',
              },
              {
                name: 'name',
                value: 'book about the meaning of life, the universe and everything ',
              },
              {
                name: 'originalPrice',
                value: '42 HUF',
              },
              {
                name: 'price',
                value: '42',
              },
              {
                name: 'currency',
                value: 'HUF',
              },
              {
                name: 'sku',
                value: 'L123',
              },
              {
                name: 'imgUrl',
                value: 'image-url',
              },
            ],
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            url: 'https://myshop/products/blue-jacket',
            ids: [{ idType: 'productId', value: 'p2' }],
            properties: [
              {
                name: 'id',
                value: 'p2',
              },
              {
                name: 'name',
                value: 'blue jacket, will camouflage if worn in water',
              },
              {
                name: 'originalPrice',
                value: '1000 EUR',
              },
              {
                name: 'price',
                value: '1000',
              },
              {
                name: 'currency',
                value: 'EUR',
              },
              {
                name: 'sku',
                value: 'L456',
              },
              {
                name: 'imgUrl',
                value: 'cool-image-url',
              },
            ],
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            url: 'https://myshop/products/white-jacket',
            ids: [{ idType: 'productId', value: 'p3' }],
            properties: [
              {
                name: 'id',
                value: 'p3',
              },
              {
                name: 'name',
                value: 'jacket worn by chefs',
              },
              {
                name: 'originalPrice',
                value: '10000 HUF',
              },
              {
                name: 'price',
                value: '10000',
              },
              {
                name: 'currency',
                value: 'HUF',
              },
              {
                name: 'sku',
                value: '789',
              },
              {
                name: 'imgUrl',
                value: 'image-url-of-a-jacket',
              },
            ],
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            url: 'https://myshop/products/no-desc-product',
            ids: [{ idType: 'productId', value: 'p4' }],
            properties: [
              {
                name: 'id',
                value: 'p4',
              },
              {
                name: 'name',
                value: 'cool black jacket',
              },
              {
                name: 'originalPrice',
                value: '10000 SEK',
              },
              {
                name: 'price',
                value: '10000',
              },
              {
                name: 'currency',
                value: 'SEK',
              },
              {
                name: 'imgUrl',
                value: 'image-url-of-a-cool-jacket',
              },
            ],
          },
        ],
        embeddings: embeddingsForPages,
      });

      expect(result.length).toEqual(3);
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'https://myshop/products/blue-jacket',
            variantId: null,
            recommendations: expect.arrayContaining([
              {
                productId: 'https://myshop/products/no-desc-product',
                variantId: null,
                properties: expect.arrayContaining([
                  {
                    name: 'id',
                    value: 'p4',
                  },
                  {
                    name: 'name',
                    value: 'cool black jacket',
                  },
                  {
                    name: 'originalPrice',
                    value: '10000 SEK',
                  },
                  {
                    name: 'price',
                    value: '10000',
                  },
                  {
                    name: 'currency',
                    value: 'SEK',
                  },
                  {
                    name: 'imgUrl',
                    value: 'image-url-of-a-cool-jacket',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/no-desc-product',
                  },
                ]),
              },
              {
                productId: 'https://myshop/products/white-jacket',
                variantId: null,
                properties: [
                  {
                    name: 'id',
                    value: 'p3',
                  },
                  {
                    name: 'name',
                    value: 'jacket worn by chefs',
                  },
                  {
                    name: 'originalPrice',
                    value: '10000 HUF',
                  },
                  {
                    name: 'price',
                    value: '10000',
                  },
                  {
                    name: 'currency',
                    value: 'HUF',
                  },
                  {
                    name: 'sku',
                    value: '789',
                  },
                  {
                    name: 'imgUrl',
                    value: 'image-url-of-a-jacket',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/white-jacket',
                  },
                ],
              },
              {
                productId: 'https://myshop/products/red-book',
                variantId: null,
                properties: [
                  {
                    name: 'id',
                    value: 'p1',
                  },
                  {
                    name: 'name',
                    value: 'book about the meaning of life, the universe and everything ',
                  },
                  {
                    name: 'originalPrice',
                    value: '42 HUF',
                  },
                  {
                    name: 'price',
                    value: '42',
                  },
                  {
                    name: 'currency',
                    value: 'HUF',
                  },
                  {
                    name: 'sku',
                    value: 'L123',
                  },
                  {
                    name: 'imgUrl',
                    value: 'image-url',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/red-book',
                  },
                ],
              },
            ]),
          }),
          expect.objectContaining({
            id: 'https://myshop/products/red-book',
            variantId: null,
            recommendations: expect.arrayContaining([
              {
                productId: 'https://myshop/products/blue-jacket',
                variantId: null,
                properties: [
                  {
                    name: 'id',
                    value: 'p2',
                  },
                  {
                    name: 'name',
                    value: 'blue jacket, will camouflage if worn in water',
                  },
                  {
                    name: 'originalPrice',
                    value: '1000 EUR',
                  },
                  {
                    name: 'price',
                    value: '1000',
                  },
                  {
                    name: 'currency',
                    value: 'EUR',
                  },
                  {
                    name: 'sku',
                    value: 'L456',
                  },
                  {
                    name: 'imgUrl',
                    value: 'cool-image-url',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/blue-jacket',
                  },
                ],
              },
              {
                productId: 'https://myshop/products/white-jacket',
                variantId: null,
                properties: [
                  {
                    name: 'id',
                    value: 'p3',
                  },
                  {
                    name: 'name',
                    value: 'jacket worn by chefs',
                  },
                  {
                    name: 'originalPrice',
                    value: '10000 HUF',
                  },
                  {
                    name: 'price',
                    value: '10000',
                  },
                  {
                    name: 'currency',
                    value: 'HUF',
                  },
                  {
                    name: 'sku',
                    value: '789',
                  },
                  {
                    name: 'imgUrl',
                    value: 'image-url-of-a-jacket',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/white-jacket',
                  },
                ],
              },
              {
                productId: 'https://myshop/products/no-desc-product',
                variantId: null,
                properties: [
                  {
                    name: 'id',
                    value: 'p4',
                  },
                  {
                    name: 'name',
                    value: 'cool black jacket',
                  },
                  {
                    name: 'originalPrice',
                    value: '10000 SEK',
                  },
                  {
                    name: 'price',
                    value: '10000',
                  },
                  {
                    name: 'currency',
                    value: 'SEK',
                  },
                  {
                    name: 'imgUrl',
                    value: 'image-url-of-a-cool-jacket',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/no-desc-product',
                  },
                ],
              },
            ]),
          }),
          expect.objectContaining({
            id: 'https://myshop/products/white-jacket',
            variantId: null,
            recommendations: expect.arrayContaining([
              {
                productId: 'https://myshop/products/blue-jacket',
                variantId: null,
                properties: [
                  {
                    name: 'id',
                    value: 'p2',
                  },
                  {
                    name: 'name',
                    value: 'blue jacket, will camouflage if worn in water',
                  },
                  {
                    name: 'originalPrice',
                    value: '1000 EUR',
                  },
                  {
                    name: 'price',
                    value: '1000',
                  },
                  {
                    name: 'currency',
                    value: 'EUR',
                  },
                  {
                    name: 'sku',
                    value: 'L456',
                  },
                  {
                    name: 'imgUrl',
                    value: 'cool-image-url',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/blue-jacket',
                  },
                ],
              },
              {
                productId: 'https://myshop/products/no-desc-product',
                variantId: null,
                properties: [
                  {
                    name: 'id',
                    value: 'p4',
                  },
                  {
                    name: 'name',
                    value: 'cool black jacket',
                  },
                  {
                    name: 'originalPrice',
                    value: '10000 SEK',
                  },
                  {
                    name: 'price',
                    value: '10000',
                  },
                  {
                    name: 'currency',
                    value: 'SEK',
                  },
                  {
                    name: 'imgUrl',
                    value: 'image-url-of-a-cool-jacket',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/no-desc-product',
                  },
                ],
              },
              {
                productId: 'https://myshop/products/red-book',
                variantId: null,
                properties: [
                  {
                    name: 'id',
                    value: 'p1',
                  },
                  {
                    name: 'name',
                    value: 'book about the meaning of life, the universe and everything ',
                  },
                  {
                    name: 'originalPrice',
                    value: '42 HUF',
                  },
                  {
                    name: 'price',
                    value: '42',
                  },
                  {
                    name: 'currency',
                    value: 'HUF',
                  },
                  {
                    name: 'sku',
                    value: 'L123',
                  },
                  {
                    name: 'imgUrl',
                    value: 'image-url',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/red-book',
                  },
                ],
              },
            ]),
          }),
        ]),
      );
    });

    it('should handle trailing slash', async () => {
      const id1 = 'https://myshop/products/blue-jacket';
      const id2 = 'https://myshop/products/red-book';
      const id3 = 'https://myshop/products/white-jacket';
      const id4 = 'https://myshop/products/no-desc-product';
      const embeddingsForPages = testEmbeddings.map((embedding) => {
        return {
          ...embedding,
          timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
          variantId: null,
        };
      });
      embeddingsForPages[0].id = id1;
      embeddingsForPages[1].id = id2;
      embeddingsForPages[2].id = id3;
      embeddingsForPages[3].id = id4;
      const result = await run({
        events: [
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: id1,
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: 'https://myshop/products/blue-jacket',
                  },
                ],
              },
            ],
          }),
        ],
        externalData: [
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            url: 'https://myshop/products/red-book',
            ids: [{ idType: 'productId', value: 'p1' }],
            properties: [
              {
                name: 'id',
                value: 'p1',
              },
              {
                name: 'name',
                value: 'book about the meaning of life, the universe and everything ',
              },
              {
                name: 'originalPrice',
                value: '42 HUF',
              },
              {
                name: 'price',
                value: '42',
              },
              {
                name: 'currency',
                value: 'HUF',
              },
              {
                name: 'sku',
                value: 'L123',
              },
              {
                name: 'imgUrl',
                value: 'image-url',
              },
            ],
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            url: 'https://myshop/products/blue-jacket',
            ids: [{ idType: 'productId', value: 'p2' }],
            properties: [
              {
                name: 'id',
                value: 'p2',
              },
              {
                name: 'name',
                value: 'blue jacket, will camouflage if worn in water',
              },
              {
                name: 'originalPrice',
                value: '1000 EUR',
              },
              {
                name: 'price',
                value: '1000',
              },
              {
                name: 'currency',
                value: 'EUR',
              },
              {
                name: 'sku',
                value: 'L456',
              },
              {
                name: 'imgUrl',
                value: 'cool-image-url',
              },
            ],
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            url: 'https://myshop/products/white-jacket',
            ids: [{ idType: 'productId', value: 'p3' }],
            properties: [
              {
                name: 'id',
                value: 'p3',
              },
              {
                name: 'name',
                value: 'jacket worn by chefs',
              },
              {
                name: 'originalPrice',
                value: '10000 HUF',
              },
              {
                name: 'price',
                value: '10000',
              },
              {
                name: 'currency',
                value: 'HUF',
              },
              {
                name: 'sku',
                value: '789',
              },
              {
                name: 'imgUrl',
                value: 'image-url-of-a-jacket',
              },
            ],
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            url: 'https://myshop/products/no-desc-product',
            ids: [{ idType: 'productId', value: 'p4' }],
            properties: [
              {
                name: 'id',
                value: 'p4',
              },
              {
                name: 'name',
                value: 'cool black jacket',
              },
              {
                name: 'originalPrice',
                value: '10000 SEK',
              },
              {
                name: 'price',
                value: '10000',
              },
              {
                name: 'currency',
                value: 'SEK',
              },
              {
                name: 'imgUrl',
                value: 'image-url-of-a-cool-jacket',
              },
            ],
          },
        ],
        embeddings: embeddingsForPages,
      });

      expect(result.length).toEqual(1);
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'https://myshop/products/blue-jacket',
            variantId: null,
            recommendations: expect.arrayContaining([
              {
                productId: 'https://myshop/products/no-desc-product',
                variantId: null,
                properties: expect.arrayContaining([
                  {
                    name: 'id',
                    value: 'p4',
                  },
                  {
                    name: 'name',
                    value: 'cool black jacket',
                  },
                  {
                    name: 'originalPrice',
                    value: '10000 SEK',
                  },
                  {
                    name: 'price',
                    value: '10000',
                  },
                  {
                    name: 'currency',
                    value: 'SEK',
                  },
                  {
                    name: 'imgUrl',
                    value: 'image-url-of-a-cool-jacket',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/no-desc-product',
                  },
                ]),
              },
              {
                productId: 'https://myshop/products/white-jacket',
                variantId: null,
                properties: [
                  {
                    name: 'id',
                    value: 'p3',
                  },
                  {
                    name: 'name',
                    value: 'jacket worn by chefs',
                  },
                  {
                    name: 'originalPrice',
                    value: '10000 HUF',
                  },
                  {
                    name: 'price',
                    value: '10000',
                  },
                  {
                    name: 'currency',
                    value: 'HUF',
                  },
                  {
                    name: 'sku',
                    value: '789',
                  },
                  {
                    name: 'imgUrl',
                    value: 'image-url-of-a-jacket',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/white-jacket',
                  },
                ],
              },
              {
                productId: 'https://myshop/products/red-book',
                variantId: null,
                properties: [
                  {
                    name: 'id',
                    value: 'p1',
                  },
                  {
                    name: 'name',
                    value: 'book about the meaning of life, the universe and everything ',
                  },
                  {
                    name: 'originalPrice',
                    value: '42 HUF',
                  },
                  {
                    name: 'price',
                    value: '42',
                  },
                  {
                    name: 'currency',
                    value: 'HUF',
                  },
                  {
                    name: 'sku',
                    value: 'L123',
                  },
                  {
                    name: 'imgUrl',
                    value: 'image-url',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/red-book',
                  },
                ],
              },
            ]),
          }),
        ]),
      );
    });

    it('should handle missing required prop correctly', async () => {
      const id1 = 'https://myshop/products/blue-jacket';
      const id2 = 'https://myshop/products/red-book';
      const id3 = 'https://myshop/products/green-skirt';

      const embeddingsForPages = testEmbeddings.map((embedding) => {
        return {
          ...embedding,
          timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
          variantId: null,
        };
      });
      embeddingsForPages[0].id = id1;
      embeddingsForPages[1].id = id2;
      embeddingsForPages[2].id = id3;

      const result = await run({
        events: [
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: id1,
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: id1,
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: id2,
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: id3,
                  },
                ],
              },
            ],
          }),
        ],
        externalData: [
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            url: id1,
            ids: [{ idType: 'productId', value: 'p1' }],
            properties: [
              {
                name: 'id',
                value: 'p1',
              },
              {
                name: 'name',
                value: 'book about the meaning of life, the universe and everything ',
              },
              {
                name: 'originalPrice',
                value: '42 HUF',
              },
              {
                name: 'currency',
                value: 'HUF',
              },
              {
                name: 'sku',
                value: 'L123',
              },
              {
                name: 'imgUrl',
                value: 'image-url',
              },
            ],
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            url: id2,
            ids: [{ idType: 'productId', value: 'p2' }],
            properties: [
              {
                name: 'id',
                value: 'p2',
              },
              {
                name: 'name',
                value: 'blue jacket, will camouflage if worn in water',
              },
              {
                name: 'originalPrice',
                value: '1000 EUR',
              },
              {
                name: 'price',
                value: '1000',
              },
              {
                name: 'currency',
                value: 'EUR',
              },
              {
                name: 'sku',
                value: 'L456',
              },
              {
                name: 'imgUrl',
                value: 'cool-image-url',
              },
            ],
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            url: id3,
            ids: [{ idType: 'productId', value: 'p3' }],
            properties: [
              {
                name: 'id',
                value: 'p3',
              },
              {
                name: 'name',
                value: 'blue jacket, will camouflage if worn in water',
              },
              {
                name: 'originalPrice',
                value: '1000 EUR',
              },
              {
                name: 'price',
                value: '1000',
              },
              {
                name: 'currency',
                value: 'EUR',
              },
              {
                name: 'sku',
                value: 'L456',
              },
              {
                name: 'imgUrl',
                value: 'cool-image-url',
              },
            ],
          },
        ],
        embeddings: embeddingsForPages,
      });

      expect(result.length).toEqual(3);
      expect(result[0].id).toEqual(id1);
      expect(result[0].recommendations.length).toEqual(2);
      expect(result[0].recommendations[0].productId).toEqual(id2);
      expect(result[0].recommendations[1].productId).toEqual(id3);

      expect(result[1].id).toEqual(id3);
      expect(result[1].recommendations.length).toEqual(1);
      expect(result[1].recommendations[0].productId).toEqual(id2);

      expect(result[2].id).toEqual(id2);
      expect(result[2].recommendations.length).toEqual(1);
      expect(result[2].recommendations[0].productId).toEqual(id3);
    });

    it('should handle nullish prop correctly', async () => {
      const id1 = 'https://myshop/products/blue-jacket';
      const id2 = 'https://myshop/products/red-book';
      const id3 = 'https://myshop/products/green-skirt';

      const embeddingsForPages = testEmbeddings.map((embedding) => {
        return {
          ...embedding,
          timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
          variantId: null,
        };
      });
      embeddingsForPages[0].id = id1;
      embeddingsForPages[1].id = id2;
      embeddingsForPages[2].id = id3;

      const result = await run({
        events: [
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: id1,
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: id1,
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: id2,
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: id3,
                  },
                ],
              },
            ],
          }),
        ],
        externalData: [
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            url: id1,
            ids: [{ idType: 'productId', value: 'p1' }],
            properties: [
              {
                name: 'id',
                value: 'p1',
              },
              {
                name: 'name',
                value: 'book about the meaning of life, the universe and everything ',
              },
              {
                name: 'originalPrice',
                value: '42 HUF',
              },
              // nullish price value
              {
                name: 'price',
                value: null,
              },
              {
                name: 'currency',
                value: 'HUF',
              },
              {
                name: 'sku',
                value: 'L123',
              },
              {
                name: 'imgUrl',
                value: 'image-url',
              },
            ],
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            url: id2,
            ids: [{ idType: 'productId', value: 'p2' }],
            properties: [
              {
                name: 'id',
                value: 'p2',
              },
              {
                name: 'name',
                value: 'blue jacket, will camouflage if worn in water',
              },
              // allowed to be null, not required prop
              {
                name: 'originalPrice',
                value: null,
              },
              {
                name: 'price',
                value: '1000',
              },
              {
                name: 'currency',
                value: 'EUR',
              },
              {
                name: 'sku',
                value: 'L456',
              },
              {
                name: 'imgUrl',
                value: 'cool-image-url',
              },
            ],
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            url: id3,
            ids: [{ idType: 'productId', value: 'p3' }],
            properties: [
              {
                name: 'id',
                value: 'p3',
              },
              {
                name: 'name',
                value: 'blue jacket, will camouflage if worn in water',
              },
              {
                name: 'originalPrice',
                value: '1000 EUR',
              },
              {
                name: 'price',
                value: '1000',
              },
              {
                name: 'currency',
                value: 'EUR',
              },
              {
                name: 'sku',
                value: 'L456',
              },
              {
                name: 'imgUrl',
                value: 'cool-image-url',
              },
            ],
          },
        ],
        embeddings: embeddingsForPages,
      });

      expect(result.length).toEqual(3);
      expect(result[0].id).toEqual(id1);
      expect(result[0].recommendations.length).toEqual(2);
      expect(result[0].recommendations[0].productId).toEqual(id2);
      expect(result[0].recommendations[1].productId).toEqual(id3);

      expect(result[1].id).toEqual(id3);
      expect(result[1].recommendations.length).toEqual(1);
      expect(result[1].recommendations[0].productId).toEqual(id2);

      expect(result[2].id).toEqual(id2);
      expect(result[2].recommendations.length).toEqual(1);
      expect(result[2].recommendations[0].productId).toEqual(id3);
    });

    it('handle whitelist if whitelist table name is passed', async () => {
      // we are looking for the recommendations of the three most popular pages, three recommendations each
      // we have pageViews for 5 pates and text embeddings for 11 pages
      const id1 = 'https://myshop/products/blue-jacket';
      const id2 = 'https://myshop/products/red-book';
      const id3 = 'https://myshop/products/white-jacket';
      const id4 = 'https://myshop/products/no-desc-product';
      const id5 = 'https://myshop/products/black-jacket';
      const embeddingsForPages = testEmbeddings.map((embedding) => {
        return {
          ...embedding,
          timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
          variantId: null,
        };
      });
      embeddingsForPages[0].id = id1;
      embeddingsForPages[1].id = id2;
      embeddingsForPages[2].id = id3;
      embeddingsForPages[3].id = id4;
      embeddingsForPages[4].id = id5;
      const result = await run({
        events: [
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: id1,
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: 'https://myshop/products/blue-jacket',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: 'https://myshop/products/red-book',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: 'https://myshop/products/red-book',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: 'https://myshop/products/white-jacket',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: 'https://myshop/products/white-jacket',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: 'https://myshop/products/yellow-jacket',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(6, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: 'https://myshop/products/black-jacket',
                  },
                ],
              },
            ],
          }),
        ],
        externalData: [
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            url: 'https://myshop/products/red-book',
            ids: [{ idType: 'productId', value: 'p1' }],
            properties: [
              {
                name: 'id',
                value: 'p1',
              },
              {
                name: 'name',
                value: 'book about the meaning of life, the universe and everything ',
              },
              {
                name: 'originalPrice',
                value: '42 HUF',
              },
              {
                name: 'price',
                value: '42',
              },
              {
                name: 'currency',
                value: 'HUF',
              },
              {
                name: 'sku',
                value: 'L123',
              },
              {
                name: 'imgUrl',
                value: 'image-url',
              },
            ],
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            url: 'https://myshop/products/blue-jacket',
            ids: [{ idType: 'productId', value: 'p2' }],
            properties: [
              {
                name: 'id',
                value: 'p2',
              },
              {
                name: 'name',
                value: 'blue jacket, will camouflage if worn in water',
              },
              {
                name: 'originalPrice',
                value: '1000 EUR',
              },
              {
                name: 'price',
                value: '1000',
              },
              {
                name: 'currency',
                value: 'EUR',
              },
              {
                name: 'sku',
                value: 'L456',
              },
              {
                name: 'imgUrl',
                value: 'cool-image-url',
              },
            ],
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            url: 'https://myshop/products/white-jacket',
            ids: [{ idType: 'productId', value: 'p3' }],
            properties: [
              {
                name: 'id',
                value: 'p3',
              },
              {
                name: 'name',
                value: 'jacket worn by chefs',
              },
              {
                name: 'originalPrice',
                value: '10000 HUF',
              },
              {
                name: 'price',
                value: '10000',
              },
              {
                name: 'currency',
                value: 'HUF',
              },
              {
                name: 'sku',
                value: '789',
              },
              {
                name: 'imgUrl',
                value: 'image-url-of-a-jacket',
              },
            ],
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            url: 'https://myshop/products/no-desc-product',
            ids: [{ idType: 'productId', value: 'p4' }],
            properties: [
              {
                name: 'id',
                value: 'p4',
              },
              {
                name: 'name',
                value: 'cool black jacket',
              },
              {
                name: 'originalPrice',
                value: '10000 SEK',
              },
              {
                name: 'price',
                value: '10000',
              },
              {
                name: 'currency',
                value: 'SEK',
              },
              {
                name: 'imgUrl',
                value: 'image-url-of-a-cool-jacket',
              },
            ],
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            url: 'https://myshop/products/black-jacket',
            ids: [{ idType: 'productId', value: 'p8' }],
            properties: [
              {
                name: 'id',
                value: 'p8',
              },
              {
                name: 'name',
                value: 'black jacket, will camouflage if worn at night',
              },
              {
                name: 'originalPrice',
                value: '10000 EUR',
              },
              {
                name: 'price',
                value: '1000',
              },
              {
                name: 'currency',
                value: 'EUR',
              },
              {
                name: 'sku',
                value: 'L456',
              },
              {
                name: 'imgUrl',
                value: 'cool-image-url',
              },
            ],
          },
        ],
        embeddings: embeddingsForPages,
        whiteListedProducts: [
          { id: 'https://myshop/products/white-jacket' },
          { id: 'https://myshop/products/blue-jacket' },
          { id: 'https://myshop/products/red-book' },
          { id: 'https://myshop/products/black-jacket' },
        ],
      });

      expect(result.length).toEqual(3);
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'https://myshop/products/blue-jacket',
            variantId: null,
            recommendations: expect.arrayContaining([
              {
                productId: 'https://myshop/products/red-book',
                variantId: null,
                properties: [
                  {
                    name: 'id',
                    value: 'p1',
                  },
                  {
                    name: 'name',
                    value: 'book about the meaning of life, the universe and everything ',
                  },
                  {
                    name: 'originalPrice',
                    value: '42 HUF',
                  },
                  {
                    name: 'price',
                    value: '42',
                  },
                  {
                    name: 'currency',
                    value: 'HUF',
                  },
                  {
                    name: 'sku',
                    value: 'L123',
                  },
                  {
                    name: 'imgUrl',
                    value: 'image-url',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/red-book',
                  },
                ],
              },
              {
                productId: 'https://myshop/products/white-jacket',
                variantId: null,
                properties: [
                  {
                    name: 'id',
                    value: 'p3',
                  },
                  {
                    name: 'name',
                    value: 'jacket worn by chefs',
                  },
                  {
                    name: 'originalPrice',
                    value: '10000 HUF',
                  },
                  {
                    name: 'price',
                    value: '10000',
                  },
                  {
                    name: 'currency',
                    value: 'HUF',
                  },
                  {
                    name: 'sku',
                    value: '789',
                  },
                  {
                    name: 'imgUrl',
                    value: 'image-url-of-a-jacket',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/white-jacket',
                  },
                ],
              },
              {
                productId: 'https://myshop/products/black-jacket',
                variantId: null,
                properties: [
                  {
                    name: 'id',
                    value: 'p8',
                  },
                  {
                    name: 'name',
                    value: 'black jacket, will camouflage if worn at night',
                  },
                  {
                    name: 'originalPrice',
                    value: '10000 EUR',
                  },
                  {
                    name: 'price',
                    value: '1000',
                  },
                  {
                    name: 'currency',
                    value: 'EUR',
                  },
                  {
                    name: 'sku',
                    value: 'L456',
                  },
                  {
                    name: 'imgUrl',
                    value: 'cool-image-url',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/black-jacket',
                  },
                ],
              },
            ]),
          }),
          expect.objectContaining({
            id: 'https://myshop/products/red-book',
            variantId: null,
            recommendations: expect.arrayContaining([
              {
                productId: 'https://myshop/products/blue-jacket',
                variantId: null,
                properties: [
                  {
                    name: 'id',
                    value: 'p2',
                  },
                  {
                    name: 'name',
                    value: 'blue jacket, will camouflage if worn in water',
                  },
                  {
                    name: 'originalPrice',
                    value: '1000 EUR',
                  },
                  {
                    name: 'price',
                    value: '1000',
                  },
                  {
                    name: 'currency',
                    value: 'EUR',
                  },
                  {
                    name: 'sku',
                    value: 'L456',
                  },
                  {
                    name: 'imgUrl',
                    value: 'cool-image-url',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/blue-jacket',
                  },
                ],
              },
              {
                productId: 'https://myshop/products/white-jacket',
                variantId: null,
                properties: [
                  {
                    name: 'id',
                    value: 'p3',
                  },
                  {
                    name: 'name',
                    value: 'jacket worn by chefs',
                  },
                  {
                    name: 'originalPrice',
                    value: '10000 HUF',
                  },
                  {
                    name: 'price',
                    value: '10000',
                  },
                  {
                    name: 'currency',
                    value: 'HUF',
                  },
                  {
                    name: 'sku',
                    value: '789',
                  },
                  {
                    name: 'imgUrl',
                    value: 'image-url-of-a-jacket',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/white-jacket',
                  },
                ],
              },
              {
                productId: 'https://myshop/products/black-jacket',
                variantId: null,
                properties: [
                  {
                    name: 'id',
                    value: 'p8',
                  },
                  {
                    name: 'name',
                    value: 'black jacket, will camouflage if worn at night',
                  },
                  {
                    name: 'originalPrice',
                    value: '10000 EUR',
                  },
                  {
                    name: 'price',
                    value: '1000',
                  },
                  {
                    name: 'currency',
                    value: 'EUR',
                  },
                  {
                    name: 'sku',
                    value: 'L456',
                  },
                  {
                    name: 'imgUrl',
                    value: 'cool-image-url',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/black-jacket',
                  },
                ],
              },
            ]),
          }),
          expect.objectContaining({
            id: 'https://myshop/products/white-jacket',
            variantId: null,
            recommendations: expect.arrayContaining([
              {
                productId: 'https://myshop/products/blue-jacket',
                variantId: null,
                properties: [
                  {
                    name: 'id',
                    value: 'p2',
                  },
                  {
                    name: 'name',
                    value: 'blue jacket, will camouflage if worn in water',
                  },
                  {
                    name: 'originalPrice',
                    value: '1000 EUR',
                  },
                  {
                    name: 'price',
                    value: '1000',
                  },
                  {
                    name: 'currency',
                    value: 'EUR',
                  },
                  {
                    name: 'sku',
                    value: 'L456',
                  },
                  {
                    name: 'imgUrl',
                    value: 'cool-image-url',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/blue-jacket',
                  },
                ],
              },
              {
                productId: 'https://myshop/products/red-book',
                variantId: null,
                properties: [
                  {
                    name: 'id',
                    value: 'p1',
                  },
                  {
                    name: 'name',
                    value: 'book about the meaning of life, the universe and everything ',
                  },
                  {
                    name: 'originalPrice',
                    value: '42 HUF',
                  },
                  {
                    name: 'price',
                    value: '42',
                  },
                  {
                    name: 'currency',
                    value: 'HUF',
                  },
                  {
                    name: 'sku',
                    value: 'L123',
                  },
                  {
                    name: 'imgUrl',
                    value: 'image-url',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/red-book',
                  },
                ],
              },
              {
                productId: 'https://myshop/products/black-jacket',
                variantId: null,
                properties: [
                  {
                    name: 'id',
                    value: 'p8',
                  },
                  {
                    name: 'name',
                    value: 'black jacket, will camouflage if worn at night',
                  },
                  {
                    name: 'originalPrice',
                    value: '10000 EUR',
                  },
                  {
                    name: 'price',
                    value: '1000',
                  },
                  {
                    name: 'currency',
                    value: 'EUR',
                  },
                  {
                    name: 'sku',
                    value: 'L456',
                  },
                  {
                    name: 'imgUrl',
                    value: 'cool-image-url',
                  },
                  {
                    name: 'url',
                    value: 'https://myshop/products/black-jacket',
                  },
                ],
              },
            ]),
          }),
        ]),
      );
    });
  });

  describe('unas unified', () => {
    const run = async ({
      accountId = '123',
      providerServiceId = 'test',
      events = [],
      products = [],
      embeddings = [],
      requestedItems = 3,
      platform = 'unas',
      titleProperty = 'title',
      numberOfRecommendations = 3,
      whiteListedProducts,
    } = {}) => {
      const query = generateRecommendationsForProducts({
        accountId,
        providerServiceId,
        eventTable: mock(events, behaviouralEventsSchema),
        productTable: mock(products, unifiedProductsTableSchema),
        textEmbeddingTable: mock(embeddings, descriptionEmbeddingsSchema),
        requestedItems,
        platform,
        titleProperty,
        numberOfRecommendations,
        whiteListedProductsTable: whiteListedProducts
          ? mock(whiteListedProducts, SPRWhiteListedProductsSchema)
          : null,
      });
      return runQuery({
        bigquery,
        query,
      });
    };

    test('generate', async () => {
      const providerServiceId = 'sy.com';
      const databaseId = 123456;

      const products = [
        {
          timestamp: moment().toISOString(),
          databaseId,
          providerServiceId,
          productId: '*************',
          name: 'Test title 1',
          price: 1000,
          originalPrice: 1000,
          currency: 'HUF',
          imageUrl: '',
          description: 'Test desc 1',
          parentProductId: '',
          locale: 'en',
          status: 'active',
        },
        {
          timestamp: moment().toISOString(),
          databaseId,
          providerServiceId,
          productId: '8908483395888',
          name: 'Test title 2',
          price: 1000,
          originalPrice: 1000,
          currency: 'HUF',
          imageUrl: '',
          description: 'Test desc 2',
          parentProductId: '',
          locale: 'en',
          status: 'active',
        },
        {
          timestamp: moment().toISOString(),
          databaseId,
          providerServiceId,
          productId: '8909862535472',
          name: 'Test title 3',
          price: 1000,
          originalPrice: 1000,
          currency: 'HUF',
          imageUrl: '',
          description: 'Test desc 3',
          parentProductId: '',
          locale: 'en',
          status: 'active',
        },
      ];

      const events = products.map((product) => {
        return eventFactory({
          timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
          events: [
            {
              type: 'epv',
              props: [
                {
                  name: 'productId',
                  value: product.productId,
                },
              ],
            },
          ],
        });
      });

      const result = await run({
        accountId: databaseId,
        providerServiceId,
        products,
        events,
        embeddings: products.map((product) => {
          return {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            id: product.productId,
            variantId: product.productId,
            descriptionHash: 'EB2AKRKmJAaQyUQgPZyB7AABBEka6NzEUesU1Y9HSMA=',
            textEmbedding: ['2.0'],
          };
        }),
      });

      expect(result.length).toEqual(3);
      expect(result).toEqual(
        expect.arrayContaining(
          products.map((product) => {
            return expect.objectContaining({
              id: product.productId,
              variantId: product.productId,
              recommendations: expect.arrayContaining(
                products
                  .filter((productToRec) => productToRec.productId !== product.productId)
                  .map((productToRec) => {
                    return expect.objectContaining({
                      productId: productToRec.productId,
                      variantId: productToRec.productId,
                      properties: expect.arrayContaining([
                        { name: 'title', value: productToRec.name },
                        { name: 'description', value: productToRec.description },
                      ]),
                    });
                  }),
              ),
            });
          }),
        ),
      );
    });
  });
  describe('woo unified', () => {
    const run = async ({
      accountId = '123',
      providerServiceId = 'test',
      events = [],
      products = [],
      embeddings = [],
      requestedItems = 3,
      platform = 'woocommerce',
      titleProperty = 'title',
      numberOfRecommendations = 3,
      whiteListedProducts,
    } = {}) => {
      const query = generateRecommendationsForProducts({
        accountId,
        providerServiceId,
        eventTable: mock(events, behaviouralEventsSchema),
        productTable: mock(products, unifiedProductsTableSchema),
        textEmbeddingTable: mock(embeddings, descriptionEmbeddingsSchema),
        requestedItems,
        platform,
        titleProperty,
        numberOfRecommendations,
        whiteListedProductsTable: whiteListedProducts
          ? mock(whiteListedProducts, SPRWhiteListedProductsSchema)
          : null,
      });
      return runQuery({
        bigquery,
        query,
      });
    };

    test('generate', async () => {
      const providerServiceId = 'sy.com';
      const databaseId = 123456;

      const products = [
        {
          timestamp: moment().toISOString(),
          databaseId,
          providerServiceId,
          productId: '*************',
          name: 'Test title 1',
          price: 1000,
          originalPrice: 1000,
          currency: 'HUF',
          imageUrl: '',
          description: 'Test desc 1',
          parentProductId: '',
          locale: 'en',
          status: 'active',
        },
        {
          timestamp: moment().toISOString(),
          databaseId,
          providerServiceId,
          productId: '8908483395888',
          name: 'Test title 2',
          price: 1000,
          originalPrice: 1000,
          currency: 'HUF',
          imageUrl: '',
          description: 'Test desc 2',
          parentProductId: '',
          locale: 'en',
          status: 'active',
        },
        {
          timestamp: moment().toISOString(),
          databaseId,
          providerServiceId,
          productId: '8909862535472',
          name: 'Test title 3',
          price: 1000,
          originalPrice: 1000,
          currency: 'HUF',
          imageUrl: '',
          description: 'Test desc 3',
          parentProductId: '',
          locale: 'en',
          status: 'active',
        },
      ];

      const events = products.map((product) => {
        return eventFactory({
          timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
          events: [
            {
              type: 'epv',
              props: [
                {
                  name: 'productId',
                  value: product.productId,
                },
              ],
            },
          ],
        });
      });

      const result = await run({
        accountId: databaseId,
        providerServiceId,
        products,
        events,
        embeddings: products.map((product) => {
          return {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            id: product.productId,
            variantId: product.productId,
            descriptionHash: 'EB2AKRKmJAaQyUQgPZyB7AABBEka6NzEUesU1Y9HSMA=',
            textEmbedding: ['2.0'],
          };
        }),
      });

      expect(result.length).toEqual(3);
      expect(result).toEqual(
        expect.arrayContaining(
          products.map((product) => {
            return expect.objectContaining({
              id: product.productId,
              variantId: product.productId,
              recommendations: expect.arrayContaining(
                products
                  .filter((productToRec) => productToRec.productId !== product.productId)
                  .map((productToRec) => {
                    return expect.objectContaining({
                      productId: productToRec.productId,
                      variantId: productToRec.productId,
                      properties: expect.arrayContaining([
                        { name: 'title', value: productToRec.name },
                        { name: 'description', value: productToRec.description },
                      ]),
                    });
                  }),
              ),
            });
          }),
        ),
      );
    });
  });
});
