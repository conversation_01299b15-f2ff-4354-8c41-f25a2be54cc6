const { getProductTableId, getScraperExternalDataTableId } = require('./table');

const DATASET_IDS = {
  PRODUCTS: 'products_optimonk',
  SPPO: 'smart_ppo',
};

const _hasTable = async (client, datasetId, tableId) => {
  const dataset = client.dataset(datasetId);
  const table = dataset.table(`${tableId.replace(`${datasetId}.`, '')}`);
  const [exists] = await table.exists();
  return !!exists;
};

const _hasProductDataForProductTable = ({ client, accountId, providerServiceId, platform }) => {
  const tableId = getProductTableId(accountId, providerServiceId, platform);
  return _hasTable(client, DATASET_IDS.PRODUCTS, tableId);
};

const _hasProductDataForExternalTable = ({ client, accountId, providerServiceId }) => {
  const tableId = getScraperExternalDataTableId(accountId, providerServiceId);
  return _hasTable(client, DATASET_IDS.SPPO, tableId);
};

const hasProductData = ({ client, accountId, providerServiceId, platform }) => {
  if (platform === 'custom') {
    return _hasProductDataForExternalTable({ client, accountId, providerServiceId });
  }
  return _hasProductDataForProductTable({ client, accountId, providerServiceId, platform });
};

module.exports = { hasProductData };
