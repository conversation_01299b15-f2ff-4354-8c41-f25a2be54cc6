const smartABTestReport = require('./smartABTestReport');
const campaignReport = require('./campaignReport');
const extractHeapProps = require('./extractHeapProps');
const testUtils = require('./testUtils');
const table = require('./table');
const query = require('./query');
const schema = require('./schema');
const errors = require('./errors');
const unifiedOrders = require('./unifiedOrders');
const prepareSPPOQueries = require('./prepareSPPOQueries');
const getAnalyticsTopLevelQuery = require('./getAnalyticsTopLevelQuery');
const generateTextEmbeddings = require('./generateTextEmbeddings');
const hasProductData = require('./hasProductData');
const tableExpiration = require('./tableExpiration');
const generateProductRecommendations = require('./getProductRecommendations');
const mergeProductRecommendations = require('./mergeProductRecommendations');
const lang = require('./lang');
const shoprenterELTDefinitions = require('./shoprenterELTDefinitions');
const detectNonStandardOrders = require('./detectNonStandardOrders');
const getExperimentStat = require('./getExperimentStat');
const { productRawPropertiesView } = require('./products');
const unifiedProducts = require('./unifiedProducts');

module.exports = {
  ...smartABTestReport,
  ...campaignReport,
  ...extractHeapProps,
  ...testUtils,
  ...table,
  ...query,
  ...schema,
  ...errors,
  ...unifiedOrders,
  ...prepareSPPOQueries,
  getAnalyticsTopLevelQuery,
  ...generateTextEmbeddings,
  ...hasProductData,
  ...tableExpiration,
  ...lang,
  ...generateProductRecommendations,
  ...mergeProductRecommendations,
  shoprenterELTDefinitions,
  detectNonStandardOrders,
  getExperimentStat,
  productRawPropertiesView,
  unifiedProducts,
};
