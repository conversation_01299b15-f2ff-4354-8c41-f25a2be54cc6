// [product-sync]

const { UNIFIED_PRODUCTS_TABLE_ID } = require('./table');

const STATUSES = { ACTIVE: 'active' };

const _orderByLocale = (locale) => {
  return locale ? `WHEN '${locale}' THEN 1 ELSE 2` : `WHEN 'hu' THEN 1 WHEN 'en' THEN 2 ELSE 3`;
};

const getProductForPPOGenerate = ({ projectId, locale }) => {
  const orderByLocale = _orderByLocale(locale);
  return `
    SELECT
      productId,
      name,
      description
    FROM \`${projectId}.${UNIFIED_PRODUCTS_TABLE_ID}\`
    WHERE
      timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 14 DAY)
      AND databaseId = @databaseId
      AND providerServiceId = @providerServiceId
      AND productId = @productId
    QUALIFY ROW_NUMBER() OVER (
      PARTITION BY productId
      ORDER BY
        CASE locale
          ${orderByLocale}
        END,
        timestamp DESC
    ) = 1
  `;
};

const getProductsForRawPropertiesView = ({
  projectId,
  databaseId,
  providerServiceId,
  productTableId,
  locale,
}) => {
  const table = productTableId ?? `\`${projectId}.${UNIFIED_PRODUCTS_TABLE_ID}\``;
  const fields = { name: 'title', description: 'description' };
  const orderByLocale = _orderByLocale(locale);
  return `
  (
    SELECT
      timestamp,
      CAST(databaseId AS STRING) AS accountId,
      providerServiceId,
      productId AS id,
      'product' AS idType,
      p.propertyName AS propertyName,
      p.propertyValue AS propertyValue
    FROM (
      SELECT
        timestamp,
        databaseId,
        providerServiceId,
        productId,
        locale,
        ${Object.keys(fields)
          .map((field) => `${field}`)
          .join(',\n')},
        ROW_NUMBER() OVER (
          PARTITION BY productId
          ORDER BY
            CASE locale
              ${orderByLocale}
            END,
            timestamp DESC
        ) AS row_num
      FROM ${table}
      WHERE
        timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 14 DAY)
        AND databaseId = ${databaseId}
        AND providerServiceId = '${providerServiceId}'
        AND status = '${STATUSES.ACTIVE}'
    ) AS base
    CROSS JOIN UNNEST([
      ${Object.entries(fields)
        .map(([from, to]) => `STRUCT('${to}' AS propertyName, base.${from} AS propertyValue)`)
        .join(',\n')}
    ]) AS p
    WHERE base.row_num = 1
  ) as data
  `;
};

module.exports = { getProductForPPOGenerate, getProductsForRawPropertiesView };
