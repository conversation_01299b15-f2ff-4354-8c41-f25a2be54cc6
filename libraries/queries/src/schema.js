/** @type {import('@google-cloud/bigquery').TableField[]} */
const smartPPOResultsSchema = [
  { name: 'timestamp', type: 'TIMESTAMP', mode: 'REQUIRED' },
  { name: 'variableName', type: 'STRING', mode: 'REQUIRED' },
  { name: 'productId', type: 'STRING', mode: 'REQUIRED' },
  { name: 'result', type: 'STRING', mode: 'NULLABLE' },
  {
    name: 'error',
    type: 'RECORD',
    mode: 'NULLABLE',
    fields: [
      { name: 'code', type: 'STRING', mode: 'REQUIRED' },
      { name: 'message', type: 'STRING', mode: 'NULLABLE' },
    ],
  },
];

const blackListedProductsTableSchema = [{ name: 'productId', type: 'STRING', mode: 'NULLABLE' }];

const SPRWhiteListedProductsSchema = [{ name: 'id', type: 'STRING', mode: 'NULLABLE' }];

const scraperExternalDataSchema = [
  { name: 'timestamp', type: 'TIMESTAMP', mode: 'REQUIRED' },
  { name: 'url', type: 'STRING', mode: 'REQUIRED' },
  {
    name: 'ids',
    type: 'RECORD',
    mode: 'REPEATED',
    fields: [
      { name: 'idType', type: 'STRING', mode: 'NULLABLE' },
      { name: 'value', type: 'STRING', mode: 'NULLABLE' },
    ],
  },
  {
    name: 'properties',
    type: 'RECORD',
    mode: 'REPEATED',
    fields: [
      { name: 'name', type: 'STRING', mode: 'REQUIRED' },
      { name: 'value', type: 'STRING', mode: 'REQUIRED' },
    ],
  },
];

const ordersSchema = [
  { name: 'timestamp', type: 'TIMESTAMP', mode: 'NULLABLE' },
  { name: 'orderId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'accountId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'providerServiceId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'deviceId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'deviceType', type: 'STRING', mode: 'NULLABLE' },
  { name: 'total', type: 'STRING', mode: 'NULLABLE' },
  { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
  { name: 'itemCount', type: 'STRING', mode: 'NULLABLE' },
  { name: 'visitorInExperiment', type: 'STRING', mode: 'NULLABLE' },
  { name: 'domain', type: 'STRING', mode: 'NULLABLE' },
  { name: 'source', type: 'STRING', mode: 'NULLABLE' },
];

const behaviouralEventsSchema = [
  { name: 'timestamp', type: 'TIMESTAMP', mode: 'REQUIRED' },
  {
    name: 'addresses',
    type: 'RECORD',
    mode: 'REPEATED',
    fields: [
      { name: 'provider', type: 'STRING', mode: 'REQUIRED' },
      { name: 'providerServiceId', type: 'STRING', mode: 'REQUIRED' },
      { name: 'customerAddress', type: 'STRING', mode: 'NULLABLE' },
      { name: 'addressType', type: 'STRING', mode: 'NULLABLE' },
    ],
  },
  {
    name: 'events',
    type: 'RECORD',
    mode: 'REPEATED',
    fields: [
      { name: 'type', type: 'STRING', mode: 'REQUIRED' },
      {
        name: 'props',
        type: 'RECORD',
        mode: 'REPEATED',
        fields: [
          { name: 'name', type: 'STRING', mode: 'REQUIRED' },
          { name: 'value', type: 'STRING', mode: 'NULLABLE' },
        ],
      },
    ],
  },
  {
    name: 'context',
    type: 'RECORD',
    mode: 'REPEATED',
    fields: [
      { name: 'name', type: 'STRING', mode: 'REQUIRED' },
      { name: 'value', type: 'STRING', mode: 'NULLABLE' },
    ],
  },
];

const productRawPropertiesSchema = [
  { name: 'timestamp', type: 'TIMESTAMP', mode: 'NULLABLE' },
  { name: 'accountId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'provider', type: 'STRING', mode: 'NULLABLE' },
  { name: 'providerServiceId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'id', type: 'STRING', mode: 'NULLABLE' },
  { name: 'idType', type: 'STRING', mode: 'NULLABLE' },
  { name: 'propertyName', type: 'STRING', mode: 'NULLABLE' },
  { name: 'propertyValue', type: 'STRING', mode: 'NULLABLE' },
];

const materializedViewSchema = [
  { name: 'timestamp', type: 'TIMESTAMP', mode: 'NULLABLE' },
  { name: 'field', type: 'STRING', mode: 'NULLABLE' },
  { name: 'deviceId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'value', type: 'STRING', mode: 'NULLABLE' },
  {
    name: 'visitorInExperiment',
    type: 'STRING',
    mode: 'NULLABLE',
  },
  { name: 'deviceType', type: 'STRING', mode: 'NULLABLE' },
];

const globalBehaviouralEventsSchema = [
  { name: 'timestamp', type: 'TIMESTAMP', mode: 'REQUIRED' },
  { name: 'accountId', type: 'STRING', mode: 'REQUIRED' },
  { name: 'providerServiceId', type: 'STRING', mode: 'REQUIRED' },
  {
    name: 'addresses',
    type: 'RECORD',
    mode: 'REPEATED',
    fields: [
      { name: 'provider', type: 'STRING', mode: 'REQUIRED' },
      { name: 'providerServiceId', type: 'STRING', mode: 'REQUIRED' },
      { name: 'customerAddress', type: 'STRING', mode: 'NULLABLE' },
      { name: 'addressType', type: 'STRING', mode: 'NULLABLE' },
    ],
  },
  {
    name: 'events',
    type: 'RECORD',
    mode: 'REPEATED',
    fields: [
      { name: 'type', type: 'STRING', mode: 'REQUIRED' },
      {
        name: 'props',
        type: 'RECORD',
        mode: 'REPEATED',
        fields: [
          { name: 'name', type: 'STRING', mode: 'REQUIRED' },
          { name: 'value', type: 'STRING', mode: 'NULLABLE' },
        ],
      },
    ],
  },
  {
    name: 'context',
    type: 'RECORD',
    mode: 'REPEATED',
    fields: [
      { name: 'name', type: 'STRING', mode: 'REQUIRED' },
      { name: 'value', type: 'STRING', mode: 'NULLABLE' },
    ],
  },
];

const descriptionEmbeddingsSchema = [
  { name: 'timestamp', type: 'TIMESTAMP', mode: 'REQUIRED' },
  { name: 'id', type: 'STRING', mode: 'REQUIRED' },
  { name: 'variantId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'descriptionHash', type: 'STRING', mode: 'REQUIRED' },
  { name: 'textEmbedding', type: 'FLOAT64', mode: 'REPEATED' },
];

const recommendationsTempTableSchema = [
  { name: 'id', type: 'STRING', mode: 'REQUIRED' },
  { name: 'variantId', type: 'STRING', mode: 'NULLABLE' },
  {
    name: 'recommendations',
    type: 'RECORD',
    mode: 'REPEATED',
    fields: [
      { name: 'recomendedProductId', type: 'STRING', mode: 'REQUIRED' },
      { name: 'recommendedVariantId', type: 'STRING', mode: 'NULLABLE' },
      {
        name: 'properties',
        type: 'RECORD',
        mode: 'REPEATED',
        fields: [
          { name: 'name', type: 'STRING', mode: 'REQUIRED' },
          { name: 'value', type: 'STRING', mode: 'NULLABLE' },
        ],
      },
    ],
  },
];

const propertiesTableDefinition = {
  schema: [
    { name: 'timestamp', type: 'TIMESTAMP', mode: 'REQUIRED' },
    { name: 'accountId', type: 'STRING', mode: 'REQUIRED' },
    { name: 'provider', type: 'STRING', mode: 'REQUIRED' },
    { name: 'providerServiceId', type: 'STRING', mode: 'REQUIRED' },
    { name: 'id', type: 'STRING', mode: 'REQUIRED' },
    { name: 'idType', type: 'STRING', mode: 'REQUIRED' },
    { name: 'propertyName', type: 'STRING', mode: 'REQUIRED' },
    { name: 'propertyValue', type: 'STRING', mode: 'NULLABLE' },
  ],
  timePartitioning: {
    type: 'DAY',
    field: 'timestamp',
  },
  requirePartitionFilter: true,
};

const connectionsTableDefinition = {
  schema: [
    { name: 'timestamp', type: 'TIMESTAMP', mode: 'REQUIRED' },
    { name: 'accountId', type: 'STRING', mode: 'REQUIRED' },
    { name: 'provider', type: 'STRING', mode: 'REQUIRED' },
    { name: 'providerServiceId', type: 'STRING', mode: 'REQUIRED' },
    { name: 'fromId', type: 'STRING', mode: 'REQUIRED' },
    { name: 'fromIdType', type: 'STRING', mode: 'REQUIRED' },
    { name: 'toId', type: 'STRING', mode: 'REQUIRED' },
    { name: 'toIdType', type: 'STRING', mode: 'REQUIRED' },
    { name: 'deleted', type: 'BOOLEAN', mode: 'NULLABLE' },
  ],
  timePartitioning: {
    type: 'DAY',
    field: 'timestamp',
  },
  requirePartitionFilter: true,
};

const impressionsViewSchema = [
  { name: 'timestamp', type: 'TIMESTAMP', mode: 'NULLABLE' },
  { name: 'accountId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'providerServiceId', type: 'STRING', mode: 'NULLABLE' },
  {
    name: 'addresses',
    type: 'RECORD',
    mode: 'REPEATED',
    fields: [
      { name: 'provider', type: 'STRING', mode: 'NULLABLE' },
      { name: 'providerServiceId', type: 'STRING', mode: 'NULLABLE' },
      { name: 'customerAddress', type: 'STRING', mode: 'NULLABLE' },
      { name: 'addressType', type: 'STRING', mode: 'NULLABLE' },
    ],
  },
  { name: 'deviceId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'shopifyY', type: 'STRING', mode: 'NULLABLE' },
  { name: 'shopifyId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'klaviyoId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'campaignId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'variantId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'deviceType', type: 'STRING', mode: 'NULLABLE' },
  { name: 'domain', type: 'STRING', mode: 'NULLABLE' },
  { name: 'isControlVariant', type: 'STRING', mode: 'NULLABLE' },
];

const conversionsViewSchema = [
  { name: 'timestamp', type: 'TIMESTAMP', mode: 'NULLABLE' },
  { name: 'accountId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'providerServiceId', type: 'STRING', mode: 'NULLABLE' },
  {
    name: 'addresses',
    type: 'RECORD',
    mode: 'REPEATED',
    fields: [
      { name: 'provider', type: 'STRING', mode: 'NULLABLE' },
      { name: 'providerServiceId', type: 'STRING', mode: 'NULLABLE' },
      { name: 'customerAddress', type: 'STRING', mode: 'NULLABLE' },
      { name: 'addressType', type: 'STRING', mode: 'NULLABLE' },
    ],
  },
  { name: 'deviceId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'shopifyY', type: 'STRING', mode: 'NULLABLE' },
  { name: 'shopifyId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'klaviyoId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'campaignId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'variantId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'deviceType', type: 'STRING', mode: 'NULLABLE' },
  { name: 'domain', type: 'STRING', mode: 'NULLABLE' },
];

const cartTokenSchema = [
  { name: 'timestamp', type: 'TIMESTAMP', mode: 'NULLABLE' },
  { name: 'accountId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'providerServiceId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'cartToken', type: 'STRING', mode: 'NULLABLE' },
  { name: 'deviceId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'domain', type: 'STRING', mode: 'NULLABLE' },
  { name: 'visitorInExperiment', type: 'STRING', mode: 'NULLABLE' },
  { name: 'deviceType', type: 'STRING', mode: 'NULLABLE' },
];

const ordersViewSchema = [
  { name: 'timestamp', type: 'TIMESTAMP', mode: 'NULLABLE' },
  { name: 'accountId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'providerServiceId', type: 'STRING', mode: 'NULLABLE' },
  {
    name: 'addresses',
    type: 'RECORD',
    mode: 'REPEATED',
    fields: [
      { name: 'provider', type: 'STRING', mode: 'NULLABLE' },
      { name: 'providerServiceId', type: 'STRING', mode: 'NULLABLE' },
      { name: 'customerAddress', type: 'STRING', mode: 'NULLABLE' },
      { name: 'addressType', type: 'STRING', mode: 'NULLABLE' },
    ],
  },
  { name: 'deviceId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'shopifyY', type: 'STRING', mode: 'NULLABLE' },
  { name: 'shopifyId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'klaviyoId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'orderId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'total', type: 'FLOAT64', mode: 'NULLABLE' },
  { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
  { name: 'deviceType', type: 'STRING', mode: 'NULLABLE' },
  { name: 'domain', type: 'STRING', mode: 'NULLABLE' },
  { name: 'itemCount', type: 'STRING', mode: 'NULLABLE' },
  { name: 'visitorInExperiment', type: 'STRING', mode: 'NULLABLE' },
  { name: 'data', type: 'STRING', mode: 'NULLABLE' },
];

const webhookOrdersViewSchema = [
  { name: 'timestamp', type: 'TIMESTAMP', mode: 'NULLABLE' },
  { name: 'accountId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'providerServiceId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'orderId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'total_price', type: 'STRING', mode: 'NULLABLE' },
  { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
  { name: 'cart_token', type: 'STRING', mode: 'NULLABLE' },
  { name: 'item_count', type: 'STRING', mode: 'NULLABLE' },
];

const ga4OrdersViewSchema = [
  { name: 'timestamp', type: 'TIMESTAMP', mode: 'NULLABLE' },
  { name: 'accountId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'providerServiceId', type: 'STRING', mode: 'NULLABLE' },
  {
    name: 'addresses',
    type: 'RECORD',
    mode: 'REPEATED',
    fields: [
      { name: 'provider', type: 'STRING', mode: 'NULLABLE' },
      { name: 'providerServiceId', type: 'STRING', mode: 'NULLABLE' },
      { name: 'customerAddress', type: 'STRING', mode: 'NULLABLE' },
      { name: 'addressType', type: 'STRING', mode: 'NULLABLE' },
    ],
  },
  { name: 'deviceId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'shopifyY', type: 'STRING', mode: 'NULLABLE' },
  { name: 'shopifyId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'klaviyoId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'orderId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'total', type: 'FLOAT64', mode: 'NULLABLE' },
  { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
  { name: 'platform', type: 'STRING', mode: 'NULLABLE' },
  { name: 'deviceType', type: 'STRING', mode: 'NULLABLE' },
  { name: 'domain', type: 'STRING', mode: 'NULLABLE' },
  { name: 'itemCount', type: 'STRING', mode: 'NULLABLE' },
  { name: 'visitorInExperiment', type: 'STRING', mode: 'NULLABLE' },
];

const webpixelOrdersViewSchema = JSON.parse(JSON.stringify(ga4OrdersViewSchema));

const ga4NonStandardOrdersViewSchema = [
  { name: 'timestamp', type: 'TIMESTAMP', mode: 'NULLABLE' },
  { name: 'accountId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'providerServiceId', type: 'STRING', mode: 'NULLABLE' },
  {
    name: 'addresses',
    type: 'RECORD',
    mode: 'REPEATED',
    fields: [
      { name: 'provider', type: 'STRING', mode: 'NULLABLE' },
      { name: 'providerServiceId', type: 'STRING', mode: 'NULLABLE' },
      { name: 'customerAddress', type: 'STRING', mode: 'NULLABLE' },
      { name: 'addressType', type: 'STRING', mode: 'NULLABLE' },
    ],
  },
  { name: 'provider', type: 'STRING', mode: 'NULLABLE' },
  { name: 'deviceId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'shopifyY', type: 'STRING', mode: 'NULLABLE' },
  { name: 'shopifyId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'klaviyoId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'deviceType', type: 'STRING', mode: 'NULLABLE' },
  { name: 'domain', type: 'STRING', mode: 'NULLABLE' },
  { name: 'visitorInExperiment', type: 'STRING', mode: 'NULLABLE' },
  { name: 'data', type: 'STRING', mode: 'NULLABLE' },
];

const shoprenterImportViewSchema = [
  { name: 'resource', type: 'STRING', mode: 'REQUIRED' },
  { name: 'row', type: 'STRING', mode: 'REQUIRED' },
];

const campaignReportsSchema = [
  { name: 'day', type: 'DATE', mode: 'NULLABLE' },
  { name: 'accountId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'providerServiceId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'groupBy', type: 'STRING', mode: 'NULLABLE' },
  { name: 'groupName', type: 'STRING', mode: 'NULLABLE' },
  {
    name: 'impressionCount',
    type: 'RECORD',
    mode: 'NULLABLE',
    fields: [
      { name: 'mobile', type: 'INT64', mode: 'NULLABLE' },
      { name: 'desktop', type: 'INT64', mode: 'NULLABLE' },
      { name: 'allType', type: 'INT64', mode: 'NULLABLE' },
    ],
  },
  {
    name: 'conversionCount',
    type: 'RECORD',
    mode: 'NULLABLE',
    fields: [
      { name: 'mobile', type: 'INT64', mode: 'NULLABLE' },
      { name: 'desktop', type: 'INT64', mode: 'NULLABLE' },
      { name: 'allType', type: 'INT64', mode: 'NULLABLE' },
    ],
  },
  {
    name: 'visitorCount',
    type: 'RECORD',
    mode: 'NULLABLE',
    fields: [
      { name: 'mobile', type: 'INT64', mode: 'NULLABLE' },
      { name: 'desktop', type: 'INT64', mode: 'NULLABLE' },
      { name: 'allType', type: 'INT64', mode: 'NULLABLE' },
    ],
  },
  {
    name: 'orders',
    type: 'RECORD',
    mode: 'NULLABLE',
    fields: [
      {
        name: 'fiveDaysShown',
        type: 'RECORD',
        mode: 'NULLABLE',
        fields: [
          {
            name: 'mobile',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'desktop',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'allType',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
        ],
      },
      {
        name: 'fiveDaysFilled',
        type: 'RECORD',
        mode: 'NULLABLE',
        fields: [
          {
            name: 'mobile',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'desktop',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'allType',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
        ],
      },
      {
        name: 'inSessionShown',
        type: 'RECORD',
        mode: 'NULLABLE',
        fields: [
          {
            name: 'mobile',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'desktop',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'allType',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
        ],
      },
      {
        name: 'inSessionFilled',
        type: 'RECORD',
        mode: 'NULLABLE',
        fields: [
          {
            name: 'mobile',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'desktop',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'allType',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
        ],
      },
    ],
  },
  {
    name: 'nonstandardOrders',
    type: 'RECORD',
    mode: 'NULLABLE',
    fields: [
      {
        name: 'fiveDaysShown',
        type: 'RECORD',
        mode: 'NULLABLE',
        fields: [
          {
            name: 'mobile',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'desktop',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'allType',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
        ],
      },
      {
        name: 'fiveDaysFilled',
        type: 'RECORD',
        mode: 'NULLABLE',
        fields: [
          {
            name: 'mobile',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'desktop',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'allType',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
        ],
      },
      {
        name: 'inSessionShown',
        type: 'RECORD',
        mode: 'NULLABLE',
        fields: [
          {
            name: 'mobile',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'desktop',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'allType',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
        ],
      },
      {
        name: 'inSessionFilled',
        type: 'RECORD',
        mode: 'NULLABLE',
        fields: [
          {
            name: 'mobile',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'desktop',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'allType',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'INT64', mode: 'NULLABLE' },
            ],
          },
        ],
      },
    ],
  },
  {
    name: 'assistedRevenue',
    type: 'RECORD',
    mode: 'NULLABLE',
    fields: [
      {
        name: 'fiveDaysShown',
        type: 'RECORD',
        mode: 'NULLABLE',
        fields: [
          {
            name: 'mobile',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'FLOAT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'desktop',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'FLOAT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'allType',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'FLOAT64', mode: 'NULLABLE' },
            ],
          },
        ],
      },
      {
        name: 'fiveDaysFilled',
        type: 'RECORD',
        mode: 'NULLABLE',
        fields: [
          {
            name: 'mobile',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'FLOAT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'desktop',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'FLOAT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'allType',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'FLOAT64', mode: 'NULLABLE' },
            ],
          },
        ],
      },
      {
        name: 'inSessionShown',
        type: 'RECORD',
        mode: 'NULLABLE',
        fields: [
          {
            name: 'mobile',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'FLOAT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'desktop',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'FLOAT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'allType',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'FLOAT64', mode: 'NULLABLE' },
            ],
          },
        ],
      },
      {
        name: 'inSessionFilled',
        type: 'RECORD',
        mode: 'NULLABLE',
        fields: [
          {
            name: 'mobile',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'FLOAT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'desktop',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'FLOAT64', mode: 'NULLABLE' },
            ],
          },
          {
            name: 'allType',
            type: 'RECORD',
            mode: 'REPEATED',
            fields: [
              { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
              { name: 'value', type: 'FLOAT64', mode: 'NULLABLE' },
            ],
          },
        ],
      },
    ],
  },
];

// [product-sync]
const unifiedProductsTableSchema = [
  { name: 'timestamp', type: 'TIMESTAMP', mode: 'REQUIRED' },
  { name: 'databaseId', type: 'INT64', mode: 'REQUIRED' },
  { name: 'providerServiceId', type: 'STRING', mode: 'REQUIRED' },
  { name: 'locale', type: 'STRING', mode: 'REQUIRED' },
  { name: 'status', type: 'STRING', mode: 'REQUIRED' },
  { name: 'productId', type: 'STRING', mode: 'REQUIRED' },
  { name: 'name', type: 'STRING', mode: 'NULLABLE' },
  { name: 'price', type: 'FLOAT64', mode: 'NULLABLE' },
  { name: 'originalPrice', type: 'FLOAT64', mode: 'NULLABLE' },
  { name: 'currency', type: 'STRING', mode: 'NULLABLE' },
  { name: 'imageUrl', type: 'STRING', mode: 'NULLABLE' },
  { name: 'description', type: 'STRING', mode: 'NULLABLE' },
  { name: 'parentProductId', type: 'STRING', mode: 'NULLABLE' },
  { name: 'productUrl', type: 'STRING', mode: 'NULLABLE' },
  { name: 'inStock', type: 'STRING', mode: 'NULLABLE' },
  { name: 'categories', type: 'STRING', mode: 'NULLABLE' },
  { name: 'sku', type: 'STRING', mode: 'NULLABLE' },
];

module.exports = {
  behaviouralEventsSchema,
  smartPPOResultsSchema,
  blackListedProductsTableSchema,
  scraperExternalDataSchema,
  ordersSchema,
  productRawPropertiesSchema,
  materializedViewSchema,
  globalBehaviouralEventsSchema,
  descriptionEmbeddingsSchema,
  recommendationsTempTableSchema,
  propertiesTableDefinition,
  connectionsTableDefinition,
  impressionsViewSchema,
  conversionsViewSchema,
  cartTokenSchema,
  ordersViewSchema,
  webhookOrdersViewSchema,
  ga4OrdersViewSchema,
  ga4NonStandardOrdersViewSchema,
  shoprenterImportViewSchema,
  campaignReportsSchema,
  SPRWhiteListedProductsSchema,
  webpixelOrdersViewSchema,
  unifiedProductsTableSchema,
};
