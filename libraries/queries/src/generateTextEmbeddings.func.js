const { BigQuery } = require('@google-cloud/bigquery');
const moment = require('moment');
const {
  descriptionEmbeddingsSchema,
  behaviouralEventsSchema,
  productRawPropertiesSchema,
  scraperExternalDataSchema,
  unifiedProductsTableSchema,
} = require('./schema');
const {
  generateTextEmbeddingsForProducts,
  generateTextEmbeddingsForExternalData,
} = require('./generateTextEmbeddings');
const testUtils = require('./testUtils');

const projectId = process.env.SECURE_PROJECT_ID;
const bigquery = new BigQuery({ projectId });

const { mock, eventFactory } = testUtils;

describe('text embedding generation', () => {
  describe('shopify/shoprenter', () => {
    let querySpy;
    const run = async ({
      accountId = 123,
      providerServiceId = 'test',
      events = [],
      products = [],
      results = [],
      fromDate = '2021-01-01',
      generationProperty = 'description',
      platform = 'shopify',
      maxItems = 3,
      fallbackProperty,
    } = {}) => {
      const targetTempTableId = `description_embeddings_test_result_${Date.now()}`;
      const embeddingGeneratorQuery = generateTextEmbeddingsForProducts({
        accountId,
        providerServiceId,
        eventTable: mock(events, behaviouralEventsSchema),
        productTable: mock(products, productRawPropertiesSchema),
        resultTable: targetTempTableId,
        fromDate,
        dryRun: true,
        maxItems,
        generationProperty,
        platform,
        fallbackProperty,
      });
      const query = `
      -- Test generate text embeddings
      CREATE TEMP TABLE ${targetTempTableId} AS
      ${mock(results, descriptionEmbeddingsSchema)};

      ${embeddingGeneratorQuery};

      SELECT * FROM ${targetTempTableId}
      `;

      return testUtils.runQuery({
        bigquery,
        query,
      });
    };
    beforeEach(() => {
      querySpy = jest.spyOn(testUtils, 'runQuery');
    });
    afterEach(() => {
      jest.clearAllMocks();
    });
    it('empty results', async () => {
      await run({
        events: [],
        products: [],
      });

      const result = await querySpy.mock.results[0]?.value;
      expect(result).toEqual([]);
    });

    it('should update result table with correct data if result table was previously empty (shopify multiple variants)', async () => {
      await run({
        events: [
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p1',
                  },
                  {
                    name: 'variantId',
                    value: 'v',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p2',
                  },
                  {
                    name: 'variantId',
                    value: 'v1',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p2',
                  },
                  {
                    name: 'variantId',
                    value: 'v2',
                  },
                ],
              },
            ],
          }),
          // v2 variant is more popular
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p2',
                  },
                  {
                    name: 'variantId',
                    value: 'v2',
                  },
                ],
              },
            ],
          }),
        ],
        products: [
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            accountId: '123',
            provider: 'shopify',
            providerServiceId: 'test',
            id: '"gid://shopify/Product/p1"',
            idType: 'product',
            propertyName: 'description',
            propertyValue: '"some boring description"',
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            accountId: '123',
            provider: 'shopify',
            providerServiceId: 'test',
            id: '"gid://shopify/Product/p2"',
            idType: 'product',
            propertyName: 'description',
            propertyValue: '"description2"',
          },
        ],
      });

      const result = await querySpy.mock.results[0]?.value;
      expect(result.length).toEqual(2);
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'p1',
            textEmbedding: [2.0],
            descriptionHash: 'FCtyStrC7rQ2zLTVF8IxMkaIK/SyswgJWYfJmuKp9H8=',
          }),
          expect.objectContaining({
            id: 'p2',
            descriptionHash: 'JihX9HAlxq/x+rc0btNaqbKTSbjtVcYSxMSQjOZIDig=',
            textEmbedding: [2.0],
          }),
        ]),
      );
    });

    it('should update result table with correct data if result table was previously NOT empty (shopify multiple variants)', async () => {
      const t1 = moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss');
      const t2 = moment().subtract(2, 'days').format('YYYY-MM-DD HH:mm:ss');
      const t3 = moment().subtract(3, 'days').format('YYYY-MM-DD HH:mm:ss');
      const t4 = moment().subtract(29, 'days').format('YYYY-MM-DD HH:mm:ss');
      const t5 = moment().subtract(20, 'days').format('YYYY-MM-DD HH:mm:ss');
      await run({
        events: [
          eventFactory({
            timestamp: t1,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p1',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t1,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p1',
                  },
                  {
                    name: 'variantId',
                    value: 'v1_1',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t2,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p2',
                  },
                  {
                    name: 'variantId',
                    value: 'v2_1',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t2,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p2',
                  },
                  {
                    name: 'variantId',
                    value: 'v2_2',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t2,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p2',
                  },
                  {
                    name: 'variantId',
                    value: 'v2_2',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t3,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p3',
                  },
                  {
                    name: 'variantId',
                    value: 'v3_1',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t3,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p3',
                  },
                  {
                    name: 'variantId',
                    value: 'v3_1',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t4,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p4',
                  },
                  {
                    name: 'variantId',
                    value: 'v4_1',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t5,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p5',
                  },
                  {
                    name: 'variantId',
                    value: 'v5_1',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t5,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p5',
                  },
                  {
                    name: 'variantId',
                    value: 'v5_1',
                  },
                ],
              },
            ],
          }),
        ],
        products: [
          // p1 was in results already, description did not change, should be left as it is
          {
            timestamp: t1,
            accountId: '123',
            provider: 'shopify',
            providerServiceId: 'test',
            id: '"gid://shopify/Product/p1"',
            idType: 'product',
            propertyName: 'description',
            propertyValue: '"some boring description"',
          },
          // p2 was in results already but description changed since last generation, most popular variant remained the same
          {
            timestamp: t2,
            accountId: '123',
            provider: 'shopify',
            providerServiceId: 'test',
            id: '"gid://shopify/Product/p2"',
            idType: 'product',
            propertyName: 'description',
            propertyValue: '"changed description"',
          },
          // p3 was previously not in results, should be added
          {
            timestamp: t3,
            accountId: '123',
            provider: 'shopify',
            providerServiceId: 'test',
            id: '"gid://shopify/Product/p3"',
            idType: 'product',
            propertyName: 'description',
            propertyValue: '"description3"',
          },
          // p4 is no longer in most popular, should not regenerate, but leave in results
          {
            timestamp: t4,
            accountId: '123',
            provider: 'shopify',
            providerServiceId: 'test',
            id: '"gid://shopify/Product/p4"',
            idType: 'product',
            propertyName: 'description',
            propertyValue: '"description4"',
          },
          // p5 has same description as before, but another variant has become the most popular
          {
            timestamp: t5,
            accountId: '123',
            provider: 'shopify',
            providerServiceId: 'test',
            id: '"gid://shopify/Product/p5"',
            idType: 'product',
            propertyName: 'description',
            propertyValue: '"description5"',
          },
        ],
        results: [
          {
            timestamp: t1,
            id: 'p1',
            variantId: 'v1_1',
            descriptionHash: 'FCtyStrC7rQ2zLTVF8IxMkaIK/SyswgJWYfJmuKp9H8=',
            textEmbedding: ['1.0'],
          },
          {
            timestamp: t2,
            id: 'p2',
            variantId: 'v2_2',
            descriptionHash: 'JihX9HAlxq/x+rc0btNaqbKTSbjtVcYSxMSQjOZIDig=',
            textEmbedding: ['1.0'],
          },
          {
            timestamp: t4,
            id: 'p4',
            variantId: 'v4_1',
            descriptionHash: '0zlQg+wlmNUYOdQspbK+p9PKTLcKwY+9I0mF7x5CWSg=',
            textEmbedding: ['1.0'],
          },
          {
            timestamp: t5,
            id: 'p5',
            variantId: 'v5_2',
            descriptionHash: 'hlUtGhLoA3acSH+UsG+/O26p4fCB7KBrr4+SEkQ1Odo=', // "description5"
            textEmbedding: ['1.0'],
          },
        ],
        maxItems: 4,
      });

      const result = await querySpy.mock.results[0]?.value;

      expect(result.length).toEqual(5);
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'p1',
            variantId: 'v1_1',
            textEmbedding: [1.0],
            descriptionHash: 'FCtyStrC7rQ2zLTVF8IxMkaIK/SyswgJWYfJmuKp9H8=',
          }),
          expect.objectContaining({
            id: 'p2',
            variantId: 'v2_2',
            textEmbedding: [2.0],
            descriptionHash: 'dY1RY7OV5cOLpihhdpMwYXbEsq2HNpBkPOMXmu98wpM=',
          }),
          expect.objectContaining({
            id: 'p3',
            variantId: 'v3_1',
            textEmbedding: [2.0],
            descriptionHash: 'I+E8pWzwzaPu3mTGhsQmqe3xUCszxsFAtzG3REmEFhI=',
          }),
          expect.objectContaining({
            id: 'p4',
            variantId: 'v4_1',
            textEmbedding: [1.0],
            descriptionHash: '0zlQg+wlmNUYOdQspbK+p9PKTLcKwY+9I0mF7x5CWSg=',
          }),
          expect.objectContaining({
            id: 'p5',
            variantId: 'v5_1',
            textEmbedding: [1.0],
            descriptionHash: 'hlUtGhLoA3acSH+UsG+/O26p4fCB7KBrr4+SEkQ1Odo=',
          }),
        ]),
      );
    });

    it('should update result table with correct data if result table was previously NOT empty (shoprenter)', async () => {
      const t1 = moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss');
      const t2 = moment().subtract(2, 'days').format('YYYY-MM-DD HH:mm:ss');
      const t3 = moment().subtract(3, 'days').format('YYYY-MM-DD HH:mm:ss');
      const t4 = moment().subtract(18, 'days').format('YYYY-MM-DD HH:mm:ss');
      const t5 = moment().subtract(20, 'days').format('YYYY-MM-DD HH:mm:ss');
      await run({
        events: [
          eventFactory({
            timestamp: t1,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p1',
                  },
                  {
                    name: 'variantId',
                    value: 'v1',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t1,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p1',
                  },
                  {
                    name: 'variantId',
                    value: 'v1',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t2,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p2',
                  },
                  {
                    name: 'variantId',
                    value: 'v2',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t2,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p2',
                  },
                  {
                    name: 'variantId',
                    value: 'v2',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t3,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p3',
                  },
                  {
                    name: 'variantId',
                    value: 'v3',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t3,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p3',
                  },
                  {
                    name: 'variantId',
                    value: 'v3',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t4,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p4',
                  },
                  {
                    name: 'variantId',
                    value: 'v4',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t5,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'parent_product_id',
                  },
                  {
                    name: 'variantId',
                    value: 'v5',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t5,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'parent_product_id',
                  },
                  {
                    name: 'variantId',
                    value: 'v5',
                  },
                ],
              },
            ],
          }),
        ],
        products: [
          // p1 was in results already, description did not change, should be left as it is
          {
            timestamp: t1,
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"v1"',
            idType: 'product',
            propertyName: 'description',
            propertyValue: '"some boring description"',
          },
          // p2 was in results already but description changed since last generation, most popular variant remained the same
          {
            timestamp: t2,
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"v2"',
            idType: 'product',
            propertyName: 'description',
            propertyValue: '"changed description"',
          },
          // p3 was previously not in results, should be added
          {
            timestamp: t3,
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"v3"',
            idType: 'product',
            propertyName: 'description',
            propertyValue: '"description3"',
          },
          // p4 is no longer in most popular, should not regenerate, but leave in results
          {
            timestamp: t4,
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"v4"',
            idType: 'product',
            propertyName: 'description',
            propertyValue: '"description4"',
          },
          // p5 has parent product
          {
            timestamp: t5,
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"v5"',
            idType: 'product',
            propertyName: 'description',
            propertyValue: '"description5"',
          },
        ],
        results: [
          {
            timestamp: t1,
            id: 'v1',
            variantId: 'v1',
            descriptionHash: 'FCtyStrC7rQ2zLTVF8IxMkaIK/SyswgJWYfJmuKp9H8=',
            textEmbedding: ['1.0'],
          },
          {
            timestamp: t2,
            id: 'v2',
            variantId: 'v2',
            descriptionHash: 'JihX9HAlxq/x+rc0btNaqbKTSbjtVcYSxMSQjOZIDig=',
            textEmbedding: ['1.0'],
          },
          {
            timestamp: t4,
            id: 'v4',
            variantId: 'v4',
            descriptionHash: '0zlQg+wlmNUYOdQspbK+p9PKTLcKwY+9I0mF7x5CWSg=',
            textEmbedding: ['1.0'],
          },
        ],
        maxItems: 4,
        platform: 'shoprenter',
      });

      const result = await querySpy.mock.results[0]?.value;

      expect(result.length).toEqual(5);
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'v1',
            variantId: 'v1',
            textEmbedding: [1.0],
            descriptionHash: 'FCtyStrC7rQ2zLTVF8IxMkaIK/SyswgJWYfJmuKp9H8=',
          }),
          expect.objectContaining({
            id: 'v2',
            variantId: 'v2',
            textEmbedding: [2.0],
            descriptionHash: 'dY1RY7OV5cOLpihhdpMwYXbEsq2HNpBkPOMXmu98wpM=',
          }),
          expect.objectContaining({
            id: 'v3',
            variantId: 'v3',
            textEmbedding: [2.0],
            descriptionHash: 'I+E8pWzwzaPu3mTGhsQmqe3xUCszxsFAtzG3REmEFhI=',
          }),
          expect.objectContaining({
            id: 'v4',
            variantId: 'v4',
            textEmbedding: [1.0],
            descriptionHash: '0zlQg+wlmNUYOdQspbK+p9PKTLcKwY+9I0mF7x5CWSg=',
          }),
          expect.objectContaining({
            id: 'v5',
            variantId: 'v5',
            textEmbedding: [2.0],
            descriptionHash: 'hlUtGhLoA3acSH+UsG+/O26p4fCB7KBrr4+SEkQ1Odo=',
          }),
        ]),
      );
    });

    it('should handle html content as description', async () => {
      await run({
        events: [
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'v1',
                  },
                  {
                    name: 'variantId',
                    value: 'v1',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'v2',
                  },
                  {
                    name: 'variantId',
                    value: 'v2',
                  },
                ],
              },
            ],
          }),
        ],
        products: [
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"v1"',
            idType: 'product',
            propertyName: 'description',
            propertyValue:
              '"&lt;h3 class=&quot;MsoNormal&quot;&gt;Jellemzők:&lt;/h3&gt;\\r\\n\\r\\n&lt;ul&gt;\\r\\n\t&lt;li&gt;A könnyű és gyors szerszámcsere javítja a hatékonyságot és időt takarít meg&lt;/li&gt;\\r\\n\t&lt;li&gt;A gyorstokmánynak köszönhetően pontosabb a munka, még a nehezen elérhető helyeken is&lt;/li&gt;\\r\\n\t&lt;li&gt;A cserélhető sarok és távtartó kiegészítők könnyebbé teszik a hozzáférést és javítják a pontosságot még a nehezen elérhető helyeken is&lt;/li&gt;\\r\\n\t&lt;li&gt;Kompakt és könnyű gép&lt;/li&gt;\\r\\n\t&lt;li&gt;2 sebesség&lt;/li&gt;\\r\\n\t&lt;li&gt;A gép hossza csak 165 mm, illetve 116 mm csatlakoztatott kiegészítők nélkül&lt;/li&gt;\\r\\n\t&lt;li&gt;Ergonomikus felépítés és gumi bevonat a kényelmes használat érdekében&lt;/li&gt;\\r\\n\t&lt;li&gt;13 mm gyorstokmány a tartozékok széles köréhez&lt;/li&gt;\\r\\n\t&lt;li&gt;20 fokozatban állítható nyomaték&lt;/li&gt;\\r\\n\t&lt;li&gt;Ergonomikus puha markolat&lt;/li&gt;\\r\\n&lt;/ul&gt;\\r\\n\\r\\n&lt;h3 class=&quot;MsoNormal&quot;&gt;Leszállított tartozékok:&lt;o:p&gt;&lt;/o:p&gt;&lt;/h3&gt;\\r\\n\\r\\n&lt;ul&gt;\\r\\n\t&lt;li class=&quot;MsoNormal&quot;&gt;2xBSL1820M+3 fej&lt;/li&gt;\\r\\n\t&lt;li class=&quot;MsoNormal&quot;&gt;&lt;o:p&gt;HITBOX&lt;/o:p&gt;&lt;/li&gt;\\r\\n&lt;/ul&gt;\\r\\n\\r\\n&lt;p&gt;Törekszünk a weboldalon megtalálható pontos és hiteles információk közlésére. Olykor, ezek tartalmazhatnak téves információkat: a képek tájékoztató jellegűek és tartalmazhatnak tartozékokat, amelyek nem szerepelnek az alapcsomagban, egyes leírások vagy az árak előzetes értesítés nélkül megváltozhatnak a gyártók által, vagy hibákat tartalmazhatnak.\\r\\n&lt;/p&gt;\\r\\n\\r\\n&lt;p class=&quot;MsoNormal&quot;&gt;Hibát találsz a leírásban vagy az adatlapon? &lt;strong&gt;&lt;a href=&quot;mailto:<EMAIL>?subject=Hibát találtam az oldalon&quot;&gt;Jelezd nekünk!&lt;/a&gt;&lt;/strong&gt;\\r\\n&lt;/p&gt;\\r\\n\\r\\n&lt;p&gt;&lt;a href=&quot;https://www.keziszerszamshop.hu/hikoki-gepek&quot;&gt;Hitachi szerszámok&lt;/a&gt;\\r\\n&lt;/p&gt;"',
          },
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"v2"',
            idType: 'product',
            propertyName: 'description',
            propertyValue:
              '"&lt;p&gt;Törekszünk a weboldalon megtalálható pontos és hiteles információk közlésére. Olykor, ezek tartalmazhatnak téves információkat: a képek tájékoztató jellegűek és tartalmazhatnak tartozékokat, amelyek nem szerepelnek az alapcsomagban, egyes leírások vagy az árak előzetes értesítés nélkül megváltozhatnak a gyártók által, vagy hibákat tartalmazhatnak.\\r\\n&lt;/p&gt;Hibát találsz a leírásban vagy az adatlapon? &lt;strong&gt;&lt;a href=&quot;mailto:<EMAIL>?subject=Hibát találtam az oldalon&quot;&gt;Jelezd nekünk!&lt;/a&gt;&lt;/strong&gt;"',
          },
        ],
      });

      const result = await querySpy.mock.results[0]?.value;
      expect(result.length).toEqual(2);
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'v2',
            textEmbedding: [2.0],
            descriptionHash: 'rQjZv+zyW0jJThv13yssuldB1eRUy64UuTUUXsWYV4Y=',
          }),
          expect.objectContaining({
            id: 'v1',
            descriptionHash: 'LB5i9DFL3mX0a6fFNBBhiqGKl8gZK3g0zHwMSQbMQG4=',
            textEmbedding: [2.0],
          }),
        ]),
      );
    });

    it('should skip products with too short or missing descriptions', async () => {
      const t1 = moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss');
      const t2 = moment().subtract(2, 'days').format('YYYY-MM-DD HH:mm:ss');
      await run({
        events: [
          eventFactory({
            timestamp: t1,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p1',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t2,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p2',
                  },
                ],
              },
            ],
          }),
        ],
        products: [
          // p1 was in results already, new description is too short, leave unchaged
          {
            timestamp: t1,
            accountId: '123',
            provider: 'shopify',
            providerServiceId: 'test',
            id: '"gid://shopify/Product/p1"',
            idType: 'product',
            propertyName: 'description',
            propertyValue: '"desc"',
          },
          // result is not generated, because description is missing
          {
            timestamp: t2,
            accountId: '123',
            provider: 'shopify',
            providerServiceId: 'test',
            id: '"gid://shopify/Product/p2"',
            idType: 'product',
            propertyName: 'description',
            propertyValue: '"',
          },
        ],
        results: [
          {
            timestamp: t1,
            id: 'p1',
            descriptionHash: 'FCtyStrC7rQ2zLTVF8IxMkaIK/SyswgJWYfJmuKp9H8=',
            textEmbedding: ['1.0'],
          },
        ],
      });

      const result = await querySpy.mock.results[0]?.value;

      expect(result.length).toEqual(1);
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'p1',
            textEmbedding: [1.0],
            descriptionHash: 'FCtyStrC7rQ2zLTVF8IxMkaIK/SyswgJWYfJmuKp9H8=',
          }),
        ]),
      );
    });

    it('should fallback to shortDescription if fallback prop passed, skip products with too short or missing descriptions', async () => {
      const t1 = moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss');
      const t2 = moment().subtract(2, 'days').format('YYYY-MM-DD HH:mm:ss');
      const t3 = moment().subtract(3, 'days').format('YYYY-MM-DD HH:mm:ss');
      await run({
        events: [
          eventFactory({
            timestamp: t1,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p1',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t2,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p2',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t3,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p3',
                  },
                ],
              },
            ],
          }),
        ],
        products: [
          // p1 was in results already, new description is too short, leave unchaged
          {
            timestamp: t1,
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"p1"',
            idType: 'product',
            propertyName: 'description.1',
            propertyValue: '"desc"',
          },
          // result is not generated, because description is missing
          {
            timestamp: t2,
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"p2"',
            idType: 'product',
            propertyName: 'description.1',
            propertyValue: '"',
          },
          // result is generated from fallback prop shortDescription
          {
            timestamp: t3,
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"p3"',
            idType: 'product',
            propertyName: 'description.1',
            propertyValue: '"',
          },
          {
            timestamp: t3,
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"p3"',
            idType: 'product',
            propertyName: 'shortDescription.1',
            propertyValue: '"this is the fallback description to be used',
          },
        ],
        results: [
          {
            timestamp: t1,
            id: 'p1',
            descriptionHash: 'FCtyStrC7rQ2zLTVF8IxMkaIK/SyswgJWYfJmuKp9H8=',
            textEmbedding: ['1.0'],
          },
        ],
        generationProperty: 'description.1',
        fallbackProperty: 'shortDescription.1',
      });

      const result = await querySpy.mock.results[0]?.value;

      expect(result.length).toEqual(2);
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'p1',
            textEmbedding: [1.0],
            descriptionHash: 'FCtyStrC7rQ2zLTVF8IxMkaIK/SyswgJWYfJmuKp9H8=',
          }),
          expect.objectContaining({
            id: 'p3',
            textEmbedding: [2.0],
            descriptionHash: 'iwM35VX7JEOQjQpQDLZi/Fbyv4e8zJ/Q2zE/8RoyCHo=',
          }),
        ]),
      );
    });
    it('should not use fallback if fallback prop is not provided', async () => {
      const t1 = moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss');
      const t2 = moment().subtract(2, 'days').format('YYYY-MM-DD HH:mm:ss');
      const t3 = moment().subtract(3, 'days').format('YYYY-MM-DD HH:mm:ss');
      await run({
        events: [
          eventFactory({
            timestamp: t1,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p1',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t2,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p2',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t3,
            events: [
              {
                type: 'epv',
                props: [
                  {
                    name: 'productId',
                    value: 'p3',
                  },
                ],
              },
            ],
          }),
        ],
        products: [
          // p1 was in results already, new description is too short, leave unchaged
          {
            timestamp: t1,
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"p1"',
            idType: 'product',
            propertyName: 'description.1',
            propertyValue: '"desc"',
          },
          // result is not generated, because description is missing
          {
            timestamp: t2,
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"p2"',
            idType: 'product',
            propertyName: 'description.1',
            propertyValue: '"',
          },
          // result is not generated from this data, because fallback prop is not provided(in case of shopify and custom platform will not look for fallback)
          {
            timestamp: t3,
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"p3"',
            idType: 'product',
            propertyName: 'description.1',
            propertyValue: '"',
          },
          {
            timestamp: t3,
            accountId: '123',
            provider: 'shoprenter',
            providerServiceId: 'test',
            id: '"p3"',
            idType: 'product',
            propertyName: 'shortDescription.1',
            propertyValue: '"this is the fallback description to be used',
          },
        ],
        results: [
          {
            timestamp: t1,
            id: 'p1',
            descriptionHash: 'FCtyStrC7rQ2zLTVF8IxMkaIK/SyswgJWYfJmuKp9H8=',
            textEmbedding: ['1.0'],
          },
        ],
        generationProperty: 'description.1',
      });

      const result = await querySpy.mock.results[0]?.value;

      expect(result.length).toEqual(1);
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'p1',
            textEmbedding: [1.0],
            descriptionHash: 'FCtyStrC7rQ2zLTVF8IxMkaIK/SyswgJWYfJmuKp9H8=',
          }),
        ]),
      );
    });
  });
  describe('custom platform', () => {
    let querySpy;
    const run = async ({
      accountId = 123,
      providerServiceId = 'test',
      events = [],
      externalData = [],
      results = [],
      fromDate = '2021-01-01',
      generationProperty = 'description',
    } = {}) => {
      const targetTempTableId = `description_embeddings_test_result_${Date.now()}`;
      const embeddingGeneratorQuery = generateTextEmbeddingsForExternalData({
        accountId,
        providerServiceId,
        eventTable: mock(events, behaviouralEventsSchema),
        externalPropertiesTable: mock(externalData, scraperExternalDataSchema),
        resultTable: targetTempTableId,
        fromDate,
        dryRun: true,
        maxItems: 3,
        generationProperty,
      });
      const query = `
      -- Test generate text embeddings
      CREATE TEMP TABLE ${targetTempTableId} AS
      ${mock(results, descriptionEmbeddingsSchema)};

      ${embeddingGeneratorQuery};

      SELECT * FROM ${targetTempTableId}
      `;

      return testUtils.runQuery({
        bigquery,
        query,
      });
    };
    beforeEach(() => {
      querySpy = jest.spyOn(testUtils, 'runQuery');
    });
    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should update result table with correct data if result table was previously NOT empty', async () => {
      const t1 = moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss');
      const t2 = moment().subtract(2, 'days').format('YYYY-MM-DD HH:mm:ss');
      const t3 = moment().subtract(3, 'days').format('YYYY-MM-DD HH:mm:ss');
      const t4 = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss');
      await run({
        events: [
          eventFactory({
            timestamp: t1,
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: 'https://myshop/products/red-jacket',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t2,
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: 'https://myshop/products/blue-jacket',
                  },
                ],
              },
            ],
          }),
          eventFactory({
            timestamp: t3,
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: 'https://myshop/products/pink-jacket',
                  },
                ],
              },
            ],
          }),
        ],

        externalData: [
          {
            timestamp: t1,
            url: 'https://myshop/products/red-jacket',
            ids: [{ idType: 'productId', value: 'p1' }],
            properties: [
              {
                name: 'description',
                value: 'boring product with boring description',
              },
            ],
          },
          {
            timestamp: t2,
            url: 'https://myshop/products/blue-jacket',
            ids: [{ idType: 'productId', value: 'p2' }],
            properties: [
              {
                name: 'description',
                value: 'new description',
              },
            ],
          },
          {
            timestamp: moment().toISOString(),
            url: 'https://myshop/products/pink-jacket',
            ids: [{ idType: 'productId', value: 'p3' }],
            properties: [
              {
                name: 'description',
                value: 'boring product with boring description',
              },
            ],
          },
          {
            timestamp: t4,
            url: 'https://myshop/products/black-jacket',
            ids: [{ idType: 'productId', value: 'p4' }],
            properties: [
              {
                name: 'description',
                value: 'boring product with boring description',
              },
            ],
          },
        ],
        results: [
          {
            timestamp: t1,
            variantId: null,
            id: 'https://myshop/products/red-jacket',
            descriptionHash: '/mSPTszBosZEG6DWrZeLLI9Wq5QFyvY+xHvqGTVUwAY=',
            textEmbedding: ['1.0'],
          },
          {
            timestamp: t2,
            variantId: null,
            id: 'https://myshop/products/blue-jacket',
            descriptionHash: 'JihX9HAlxq/x+rc0btNaqbKTSbjtVcYSxMSQjOZIDig=',
            textEmbedding: ['1.0'],
          },
          {
            timestamp: t4,
            variantId: null,
            id: 'https://myshop/products/black-jacket',
            descriptionHash: '0zlQg+wlmNUYOdQspbK+p9PKTLcKwY+9I0mF7x5CWSg=',
            textEmbedding: ['1.0'],
          },
        ],
      });

      const result = await querySpy.mock.results[0]?.value;

      expect(result.length).toEqual(4);
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'https://myshop/products/red-jacket',
            textEmbedding: [1.0],
            descriptionHash: '/mSPTszBosZEG6DWrZeLLI9Wq5QFyvY+xHvqGTVUwAY=',
          }),
          expect.objectContaining({
            id: 'https://myshop/products/blue-jacket',
            textEmbedding: [2.0],
            descriptionHash: 'mwkNxfEMamHULHV/voZwKDg58C+3VoVN03M+o5wrpQw=',
          }),
          expect.objectContaining({
            id: 'https://myshop/products/pink-jacket',
            textEmbedding: [2.0],
            descriptionHash: '/mSPTszBosZEG6DWrZeLLI9Wq5QFyvY+xHvqGTVUwAY=',
          }),
          expect.objectContaining({
            id: 'https://myshop/products/black-jacket',
            textEmbedding: [1.0],
            descriptionHash: '0zlQg+wlmNUYOdQspbK+p9PKTLcKwY+9I0mF7x5CWSg=',
          }),
        ]),
      );
    });

    it('should handle trailing slash', async () => {
      const t1 = moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss');
      await run({
        events: [
          eventFactory({
            timestamp: t1,
            events: [
              {
                type: 'pageView',
                props: [
                  {
                    name: 'canonicalUrl',
                    value: 'https://myshop/products/blue-jacket/',
                  },
                ],
              },
            ],
          }),
        ],

        externalData: [
          {
            timestamp: t1,
            url: 'https://myshop/products/blue-jacket',
            ids: [{ idType: 'productId', value: 'p2' }],
            properties: [
              {
                name: 'description',
                value: 'new description',
              },
            ],
          },
        ],
        results: [
          {
            timestamp: t1,
            variantId: null,
            id: 'https://myshop/products/blue-jacket',
            descriptionHash: 'JihX9HAlxq/x+rc0btNaqbKTSbjtVcYSxMSQjOZIDig=',
            textEmbedding: ['1.0'],
          },
        ],
      });

      const result = await querySpy.mock.results[0]?.value;

      expect(result.length).toEqual(1);
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'https://myshop/products/blue-jacket',
            textEmbedding: [2.0],
            descriptionHash: 'mwkNxfEMamHULHV/voZwKDg58C+3VoVN03M+o5wrpQw=',
          }),
        ]),
      );
    });
  });
  describe('unas unified', () => {
    let querySpy;
    const run = async ({
      accountId = 123,
      providerServiceId = 'test',
      events = [],
      products = [],
      results = [],
      fromDate = '2021-01-01',
      generationProperty = 'description',
      platform = 'unas',
      maxItems = 3,
      fallbackProperty,
    } = {}) => {
      const targetTempTableId = `description_embeddings_test_result_${Date.now()}`;
      const embeddingGeneratorQuery = generateTextEmbeddingsForProducts({
        accountId,
        providerServiceId,
        eventTable: mock(events, behaviouralEventsSchema),
        productTable: mock(products, unifiedProductsTableSchema),
        resultTable: targetTempTableId,
        fromDate,
        dryRun: true,
        maxItems,
        generationProperty,
        platform,
        fallbackProperty,
      });
      const query = `
      -- Test generate text embeddings
      CREATE TEMP TABLE ${targetTempTableId} AS
      ${mock(results, descriptionEmbeddingsSchema)};

      ${embeddingGeneratorQuery};

      SELECT * FROM ${targetTempTableId}
      `;

      return testUtils.runQuery({
        bigquery,
        query,
      });
    };
    beforeEach(() => {
      querySpy = jest.spyOn(testUtils, 'runQuery');
    });
    afterEach(() => {
      jest.clearAllMocks();
    });

    test('should update result table with correct data if result table was previously NOT empty', async () => {
      const providerServiceId = 'sy.com';
      const databaseId = 123456;

      const products = [
        {
          timestamp: moment().toISOString(),
          databaseId,
          providerServiceId,
          productId: '*************',
          name: 'Test title 1',
          price: 1000,
          originalPrice: 1000,
          currency: 'HUF',
          imageUrl: '',
          description: 'Test desc 1',
          parentProductId: '',
          locale: 'en',
          status: 'active',
        },
        {
          timestamp: moment().toISOString(),
          databaseId,
          providerServiceId,
          productId: '*************',
          name: 'Test title 2',
          price: 1000,
          originalPrice: 1000,
          currency: 'HUF',
          imageUrl: '',
          description: 'Test desc 2',
          parentProductId: '',
          locale: 'en',
          status: 'active',
        },
      ];

      const events = products.map((product) => {
        return eventFactory({
          timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
          events: [
            {
              type: 'epv',
              props: [
                {
                  name: 'productId',
                  value: product.productId,
                },
              ],
            },
          ],
        });
      });

      await run({
        accountId: databaseId,
        providerServiceId,
        events,
        products,
        results: [
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            id: '111',
            variantId: '111',
            descriptionHash: 'FCtyStrC7rQ2zLTVF8IxMkaIK/SyswgJWYfJmuKp9H8=',
            textEmbedding: ['1.0'],
          },
        ],
      });

      const result = await querySpy.mock.results[0]?.value;
      expect(result.length).toEqual(3);
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: '111',
            variantId: '111',
            descriptionHash: 'FCtyStrC7rQ2zLTVF8IxMkaIK/SyswgJWYfJmuKp9H8=',
            textEmbedding: [1.0],
          }),
          expect.objectContaining({
            id: '*************',
            variantId: '*************',
            descriptionHash: 'EB2AKRKmJAaQyUQgPZyB7AABBEka6NzEUesU1Y9HSMA=',
            textEmbedding: [2.0],
          }),
          expect.objectContaining({
            id: '*************',
            variantId: '*************',
            descriptionHash: 'TKexKp7F3LaGd0znFI34eRi3h345VoOWa/3tDKXVDig=',
            textEmbedding: [2.0],
          }),
        ]),
      );
    });
  });
  describe('woo unified', () => {
    let querySpy;
    const run = async ({
      accountId = 123,
      providerServiceId = 'test',
      events = [],
      products = [],
      results = [],
      fromDate = '2021-01-01',
      generationProperty = 'description',
      platform = 'woocommerce',
      maxItems = 3,
      fallbackProperty,
    } = {}) => {
      const targetTempTableId = `description_embeddings_test_result_${Date.now()}`;
      const embeddingGeneratorQuery = generateTextEmbeddingsForProducts({
        accountId,
        providerServiceId,
        eventTable: mock(events, behaviouralEventsSchema),
        productTable: mock(products, unifiedProductsTableSchema),
        resultTable: targetTempTableId,
        fromDate,
        dryRun: true,
        maxItems,
        generationProperty,
        platform,
        fallbackProperty,
      });
      const query = `
      -- Test generate text embeddings
      CREATE TEMP TABLE ${targetTempTableId} AS
      ${mock(results, descriptionEmbeddingsSchema)};

      ${embeddingGeneratorQuery};

      SELECT * FROM ${targetTempTableId}
      `;

      return testUtils.runQuery({
        bigquery,
        query,
      });
    };
    beforeEach(() => {
      querySpy = jest.spyOn(testUtils, 'runQuery');
    });
    afterEach(() => {
      jest.clearAllMocks();
    });

    test('should update result table with correct data if result table was previously NOT empty', async () => {
      const providerServiceId = 'sy.com';
      const databaseId = 123456;

      const products = [
        {
          timestamp: moment().toISOString(),
          databaseId,
          providerServiceId,
          productId: '*************',
          name: 'Test title 1',
          price: 1000,
          originalPrice: 1000,
          currency: 'HUF',
          imageUrl: '',
          description: 'Test desc 1',
          parentProductId: '',
          locale: 'en',
          status: 'active',
        },
        {
          timestamp: moment().toISOString(),
          databaseId,
          providerServiceId,
          productId: '*************',
          name: 'Test title 2',
          price: 1000,
          originalPrice: 1000,
          currency: 'HUF',
          imageUrl: '',
          description: 'Test desc 2',
          parentProductId: '',
          locale: 'en',
          status: 'active',
        },
      ];

      const events = products.map((product) => {
        return eventFactory({
          timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
          events: [
            {
              type: 'epv',
              props: [
                {
                  name: 'productId',
                  value: product.productId,
                },
              ],
            },
          ],
        });
      });

      await run({
        accountId: databaseId,
        providerServiceId,
        events,
        products,
        results: [
          {
            timestamp: moment().subtract(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
            id: '111',
            variantId: '111',
            descriptionHash: 'FCtyStrC7rQ2zLTVF8IxMkaIK/SyswgJWYfJmuKp9H8=',
            textEmbedding: ['1.0'],
          },
        ],
      });

      const result = await querySpy.mock.results[0]?.value;
      expect(result.length).toEqual(3);
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: '111',
            variantId: '111',
            descriptionHash: 'FCtyStrC7rQ2zLTVF8IxMkaIK/SyswgJWYfJmuKp9H8=',
            textEmbedding: [1.0],
          }),
          expect.objectContaining({
            id: '*************',
            variantId: '*************',
            descriptionHash: 'EB2AKRKmJAaQyUQgPZyB7AABBEka6NzEUesU1Y9HSMA=',
            textEmbedding: [2.0],
          }),
          expect.objectContaining({
            id: '*************',
            variantId: '*************',
            descriptionHash: 'TKexKp7F3LaGd0znFI34eRi3h345VoOWa/3tDKXVDig=',
            textEmbedding: [2.0],
          }),
        ]),
      );
    });
  });
});
