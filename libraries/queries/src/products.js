const unifiedProducts = require('./unifiedProducts');
const { UNIFIED_PRODUCTS_PLATFORMS } = require('./isUnifiedProuctBased');

const productViewByPlatform = (productTableId, settings) => {
  // [product-sync][isUnified]
  if (UNIFIED_PRODUCTS_PLATFORMS.includes(settings?.platform)) {
    return unifiedProducts.getProductsForRawPropertiesView({
      productTableId,
      ...settings,
    });
  }
  return `${productTableId} AS data`;
};

// @argument settings { platform, databaseId, providerServiceId }
const productRawPropertiesView = (productTableId, settings) => {
  return `
SELECT
  agg.data.*
FROM (
  SELECT
    ARRAY_AGG(STRUCT(data) ORDER BY timestamp DESC)[SAFE_OFFSET(0)] agg
  FROM
    ${productViewByPlatform(productTableId, settings)}
  WHERE
    timestamp > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 365 DAY)
  GROUP BY accountId, providerServiceId, id, idType, propertyName
)
WHERE
  agg.data.propertyValue IS NOT NULL
`;
};

module.exports = { productRawPropertiesView };
