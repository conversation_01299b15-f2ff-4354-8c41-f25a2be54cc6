stages:
  - build
  - test
  - analyze
  - deploy
  - k8s-deploy
  - e2e

include:
  - local: 'ops/ci/docker-devops.yml'
  - local: 'ops/ci/gcloud-devops.yml'
  - local: 'ops/ci/build.yml'
  - local: 'ops/ci/test.yml'
  - local: 'ops/ci/analyze.yml'
  - local: 'ops/ci/deploy.yml'
  - local: 'ops/ci/changes.yml'

  - local: 'libraries/**/*.gitlab-ci.yml'

  - local: 'packages/om-backend/.gitlab-ci.yml'
  - local: 'packages/product-sync-service/.gitlab-ci.yml'
  - local: 'packages/om-admin/.gitlab-ci.yml'
  - local: 'packages/om-frontend/.gitlab-ci.yml'
  - local: 'packages/om-backoffice/.gitlab-ci.yml'
  - local: 'packages/om-fakeclient/.gitlab-ci.yml'
  - local: 'packages/om-optimage/.gitlab-ci.yml'
  - local: 'packages/om-public-api/.gitlab-ci.yml'
  - local: 'packages/om-renderer/.gitlab-ci.yml'
  - local: 'packages/om-point-n-click/.gitlab-ci.yml'
  - local: 'packages/staging-oauth-gateway/.gitlab-ci.yml'
  - local: 'packages/shopify-extensions/.gitlab-ci.yml'

deploy live:
  extends: .deploy
  environment:
    name: live
  only:
    refs: [master]

deploy stage1:
  extends: .deploy
  environment:
    name: stage1

deploy stage2:
  extends: .deploy
  environment:
    name: stage2

deploy stage3:
  extends: .deploy
  environment:
    name: stage3

deploy stage4:
  extends: .deploy
  environment:
    name: stage4

deploy stage5:
  extends: .deploy
  environment:
    name: stage5

deploy stage6:
  extends: .deploy
  environment:
    name: stage6

deploy qa1:
  extends: .deploy
  environment:
    name: qa1

build workflow-executors:
  stage: build
  only:
    changes: !reference [.changes, workflow-executors]
  trigger:
    strategy: depend
    include:
      - local: packages/workflow-executors/build.gitlab-ci.yml

test workflow-executors:
  stage: test
  needs: [build workflow-executors]
  only:
    changes: !reference [.changes, workflow-executors]
  trigger:
    strategy: depend
    include:
      - local: packages/workflow-executors/test.gitlab-ci.yml

deploy workflows-live:
  extends: .deploy-workflows
  environment:
    name: prod
  only:
    refs: [master]

deploy workflows-staging:
  extends: .deploy-workflows
  environment:
    name: staging-eu

deploy gcp-staging:
  extends: .deploy-gcp
  environment:
    name: staging-eu

deploy gcp-prod:
  extends: .deploy-gcp
  environment:
    name: prod
#  only:
#    refs: [master]

deploy k8s-production-cluster:
  extends: .deploy-argocd
  needs:
    - build frontend
  environment:
    name: k8s-production-cluster

deploy k8s-staging1-cluster:
  extends: .deploy-argocd
  needs:
    - build frontend
    - build backend
    - build admin
    - build backoffice
    - build renderer
    - build fakeclient
  variables:
    ARGOCD_APP_NAME: optimonk-staging1
  environment:
    name: k8s-staging1-cluster


deploy k8s-staging2-cluster:
  extends: .deploy-argocd
  needs:
    - build frontend
    - build backend
    - build admin
    - build backoffice
    - build renderer
    - build fakeclient
  variables:
    ARGOCD_APP_NAME: optimonk-staging2
  environment:
    name: k8s-staging2-cluster

deploy k8s-staging3-cluster:
  extends: .deploy-argocd
  needs:
    - build frontend
    - build backend
    - build admin
    - build backoffice
    - build renderer
    - build fakeclient
  variables:
    ARGOCD_APP_NAME: optimonk-staging3
  environment:
    name: k8s-staging3-cluster
