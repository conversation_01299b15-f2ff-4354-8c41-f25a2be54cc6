{"eslint.validate": ["javascript", "typescript", "vue", "pug"], "editor.defaultFormatter": "esbenp.prettier-vscode", "gitlab.instanceUrl": "https://gitlab.innonic.com", "typescript.tsdk": "node_modules/typescript/lib", "editor.formatOnSave": true, "cSpell.words": ["semibold"], "files.associations": {".env*": "dotenv"}, "i18n-ally.localesPaths": "./packages/om-admin/src/translations", "i18n-ally.keystyle": "nested", "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.tabSize": 2}