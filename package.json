{"private": true, "workspaces": {"packages": ["packages/*", "libraries/*", "packages/workflow-executors/*"]}, "name": "om-monorepo", "version": "1.0.0", "main": "index.js", "repository": "**********************:optimonk/om-monorepo.git", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "hooks": {"pre-commit": "lint-staged -d"}, "scripts": {"prepare": "husky install", "dev:admin": "./ops/scripts/set-title.sh && yarn workspace om-admin dev", "dev:backend": "./ops/scripts/set-title.sh && yarn workspace om-backend dev", "dev:frontend": "./ops/scripts/set-title.sh && yarn workspace om-frontend dev", "dev:backoffice": "./ops/scripts/set-title.sh && yarn workspace om-backoffice dev", "dev:optimage": "./ops/scripts/set-title.sh && yarn workspace om-optimage dev", "dev:fakeclient": "./ops/scripts/set-title.sh && yarn workspace om-fakeclient dev", "dev:publicapi": "./ops/scripts/set-title.sh && yarn workspace om-public-api dev", "dev:ssr": "./ops/scripts/set-title.sh && yarn workspace om-renderer dev", "dev:pnc": "./ops/scripts/set-title.sh && yarn workspace om-point-n-click serve", "dev:concurrently": "concurrently --prefix-colors=black.bgWhite --names=INGRESS,BACKEND,FRONTEND,FAKECLIENT,OPTIMAGE,PUBLICAPI,SSR  \"yarn run dev:ingress\" \"yarn run dev:backend\" \"yarn run dev:frontend\" \"yarn run dev:fakeclient\" \"yarn run dev:optimage\" \"yarn run dev:publicapi\" \"yarn run dev:ssr\"", "dev:tmux": "./ops/scripts/tmux.sh", "dev:ingress": "./ops/scripts/set-title.sh && cd ops/traefik && yarn traefik --configfile ./traefik.yml", "dev:workflow-executors": "./ops/scripts/dev-workflow-executors.sh", "dev:staging-oauth-gateway": "yarn workspace staging-oauth-gateway dev", "docker:build:admin": "DOCKER_BUILDKIT=1 docker build -t om-admin -f packages/om-admin/Dockerfile .", "docker:build:backend": "DOCKER_BUILDKIT=1 docker build -t om-backend -f packages/om-backend/Dockerfile .", "docker:build:backoffice": "DOCKER_BUILDKIT=1 docker build -t om-backoffice -f packages/om-backoffice/Dockerfile .", "docker:build:frontend": "DOCKER_BUILDKIT=1 docker build -t om-frontend -f packages/om-frontend/Dockerfile .", "docker:build:optimage": "DOCKER_BUILDKIT=1 docker build -t om-optimage -f packages/om-optimage/Dockerfile .", "docker:build:fakeclient": "DOCKER_BUILDKIT=1 docker build -t om-fakeclient -f packages/om-fakeclient/Dockerfile .", "docker:build:publicapi": "DOCKER_BUILDKIT=1 docker build -t om-public-api -f packages/om-public-api/Dockerfile .", "docker:build:ssr": "DOCKER_BUILDKIT=1 docker build -t om-renderer -f packages/om-renderer/Dockerfile .", "generate:doctoc": "doctoc -u --title='## Table of Contents' --gitlab .", "prettier": "prettier \"packages/om-admin/**/*.+(js|scss|css|ts|vue)\"", "check-format": "npm run prettier -- --list-different", "format": "npm run prettier -- --config .prettierrc --write", "lint:admin": "yarn workspace om-admin lint", "lint:backend": "yarn workspace om-backend lint", "lint:backoffice": "yarn workspace om-backoffice lint", "lint:fakeclient": "yarn workspace om-fakeclient lint", "lint:frontend": "yarn workspace om-frontend lint", "lint:optimage": "yarn workspace om-optimage lint", "lint:pointnclick": "yarn workspace om-point-n-click lint", "lint:publicapi": "yarn workspace om-admin lint", "lint": "concurrently --prefix-colors=black.bgWhite --names=ADMIN,BACKEND,BACKOFFICE,FAKECLIENT,FRONTEND,OPTIMAGE,POINTNCLICK,PUBLICAPI \"yarn run lint:admin\" \"yarn run lint:backend\" \"yarn run lint:backoffice\" \"yarn run lint:fakeclient\" \"yarn run lint:frontend\" \"yarn run lint:optimage\"  \"yarn run lint:pointnclick\" \"yarn run lint:publicapi\"", "sync:templates": "yarn workspace om-backend sync:templates"}, "devDependencies": {"@om/traefik": "workspace:^", "@prettier/plugin-pug": "^1.15.3", "concurrently": "^5.3.0", "doctoc": "^2.0.0", "eslint": "^7.28.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.23.4", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-vue": "^7.11.1", "husky": "^6.0.0", "lint-staged": "^11.0.0", "prettier": "^2.3.1"}, "resolutions": {"concurrently": "~5.3.0", "eslint": "7.28.0"}, "packageManager": "yarn@3.1.1", "dependencies": {"lodash.set": "^4.3.2"}}