.deploy-devops: &deploy-devops |
  # Initializing deploy devops tools...
  [[ "$TRACE" ]] && set -x

  function setup_docker() {
    if [[ -n "$DOCKER_SWARM_TLSCA" ]]; then
      echo "Setting up Docker TLS certificates..."
      mkdir -p /root/.docker
      echo "${DOCKER_SWARM_TLSCA}" > /root/.docker/ca.pem
      echo "${DOCKER_SWARM_TLSCERT}" > /root/.docker/cert.pem
      echo "${DOCKER_SWARM_TLSKEY}" > /root/.docker/key.pem
      export DOCKER_TLS_VERIFY=1
    fi

    if [[ -n "$DOCKER_REGISTRY_USERNAME" ]]; then
      docker login -u "$DOCKER_REGISTRY_USERNAME" -p "$DOCKER_REGISTRY_PASSWORD" "https://$DOCKER_REGISTRY_HOST"
    fi
  }

.deploy:
  image: docker:stable-git
  stage: deploy
  tags:
    - lightweight
  needs:
    - build admin
    - build backend
    - build backoffice
    - build fakeclient
    - build frontend
    - build optimage
    - build public-api
    - build renderer
  interruptible: false
  when: manual
  variables:
    DOCKER_OM_BACKEND_IMAGE_TAG: $DOCKER_REGISTRY_HOST/$DOCKER_REGISTRY_REPOSITORY/om-backend:$CI_COMMIT_SHA
    DOCKER_OM_FRONTEND_IMAGE_TAG: $DOCKER_REGISTRY_HOST/$DOCKER_REGISTRY_REPOSITORY/om-frontend:$CI_COMMIT_SHA
    DOCKER_OM_ADMIN_IMAGE_TAG: $DOCKER_REGISTRY_HOST/$DOCKER_REGISTRY_REPOSITORY/om-admin:$CI_COMMIT_SHA
    DOCKER_OM_ADMIN_STORYBOOK_IMAGE_TAG: $DOCKER_REGISTRY_HOST/$DOCKER_REGISTRY_REPOSITORY/om-admin-storybook:$CI_COMMIT_SHA
    DOCKER_OM_BACKOFFICE_IMAGE_TAG: $DOCKER_REGISTRY_HOST/$DOCKER_REGISTRY_REPOSITORY/om-backoffice:$CI_COMMIT_SHA
    DOCKER_OM_FAKECLIENT_IMAGE_TAG: $DOCKER_REGISTRY_HOST/$DOCKER_REGISTRY_REPOSITORY/om-fakeclient:$CI_COMMIT_SHA
    DOCKER_OM_OPTIMAGE_IMAGE_TAG: $DOCKER_REGISTRY_HOST/$DOCKER_REGISTRY_REPOSITORY/om-optimage:$CI_COMMIT_SHA
    DOCKER_OM_PUBLIC_API_IMAGE_TAG: $DOCKER_REGISTRY_HOST/$DOCKER_REGISTRY_REPOSITORY/om-public-api:$CI_COMMIT_SHA
    DOCKER_OM_RENDERER_IMAGE_TAG: $DOCKER_REGISTRY_HOST/$DOCKER_REGISTRY_REPOSITORY/om-renderer:$CI_COMMIT_SHA
    DOCKER_OM_BACKEND_ENV_FILE_PATH: $DOCKER_RUNTIME_CONFIG_PATH/om-backend.env
    DOCKER_OM_FRONTEND_ENV_FILE_PATH: $DOCKER_RUNTIME_CONFIG_PATH/om-frontend.env
    DOCKER_OM_ADMIN_ENV_FILE_PATH: $DOCKER_RUNTIME_CONFIG_PATH/om-admin.env
    DOCKER_OM_BACKOFFICE_ENV_FILE_PATH: $DOCKER_RUNTIME_CONFIG_PATH/om-backoffice.env
    DOCKER_OM_FAKECLIENT_ENV_FILE_PATH: $DOCKER_RUNTIME_CONFIG_PATH/om-fakeclient.env
    DOCKER_OM_OPTIMAGE_ENV_FILE_PATH: $DOCKER_RUNTIME_CONFIG_PATH/om-optimage.env
    DOCKER_OM_PUBLIC_API_ENV_FILE_PATH: $DOCKER_RUNTIME_CONFIG_PATH/om-public-api.env
    DOCKER_OM_RENDERER_ENV_FILE_PATH: $DOCKER_RUNTIME_CONFIG_PATH/om-renderer.env
    DOCKER_HOST: $DOCKER_SWARM_HOST
  before_script:
    - *deploy-devops
    - apk add --no-cache curl
  script:
    - setup_docker
    - ./ops/ci/pull_config.sh $CI_ENVIRONMENT_NAME
    - ./ops/deploy/deploy-service.sh $CI_ENVIRONMENT_NAME om-renderer om-backend om-admin om-frontend om-fakeclient om-backoffice om-public-api om-optimage
  after_script: |
    if [ $CI_ENVIRONMENT_NAME == 'live' ]; then
      if [ $CI_JOB_STATUS == 'success' ]; then
        ./ops/deploy/send-deploy-message.sh "success"
      else
        ./ops/deploy/send-deploy-message.sh "failed"
      fi
    fi

.deploy-workflows:
  image: alpine:3.15
  stage: deploy
  tags:
    - lightweight
  needs:
    - build workflow-executors
  interruptible: false
  when: manual
  variables:
    GIT_STRATEGY: none
  before_script:
    - apk add --no-cache git curl bash
    - curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh"  | bash
    - mv kustomize /usr/local/bin/
  script: |
    set -xv
    git clone https://${GITLAB_DEPLOY_USER}:${GITLAB_DEPLOY_TOKEN}@gitlab.com/optimonk/optimonk-config.git 2> /dev/null || git -C optimonk-config pull
    cd optimonk-config
    for service in etl flow poll query heap ppo-chatgpt ppo-controller ppo-initializer ppo-publisher periodic-query-controller asset-downloader product-feed-controller product-feed-parser spr-text-embedding-controller spr-generation-controller shoprenter-elt-controller shoprenter-extract;
    do
        cd overlays/wf-${service}/$CI_ENVIRONMENT_NAME
        kustomize edit set image executor-image=us-central1-docker.pkg.dev/optimonk-build-4914/om-artifact/workflow-executors/${service}:${CI_COMMIT_SHA}
        cd ../../../
    done
    git config --global user.email "<EMAIL>"
    git config --global user.name "GitLab CI/CD"
    git commit -am "Deploy workflows to $CI_ENVIRONMENT_NAME from Gitlab CI pipeline jobName: ${CI_JOB_NAME} JobId: ${CI_JOB_ID}"
    git push origin master
  only:
    changes: !reference [.changes, workflow-executors]

.deploy-gcp:
  image: alpine:3.15
  stage: deploy
  tags:
    - lightweight
  needs:
    - build product-sync
  interruptible: false
  when: manual
  variables:
    GIT_STRATEGY: none
  before_script:
    - apk add --no-cache git curl bash
    - curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh"  | bash
    - mv kustomize /usr/local/bin/
  script: |
    set -xv
    git clone https://${GITLAB_DEPLOY_USER}:${GITLAB_DEPLOY_TOKEN}@gitlab.com/optimonk/optimonk-config.git 2> /dev/null || git -C optimonk-config pull
    cd optimonk-config
    for service in product-sync-service;
    do
        cd overlays/${service}/$CI_ENVIRONMENT_NAME
        kustomize edit set image executor-image=us-central1-docker.pkg.dev/optimonk-build-4914/om-artifact/${service}:${CI_COMMIT_SHA}
        cd ../../../
    done
    git config --global user.email "<EMAIL>"
    git config --global user.name "GitLab CI/CD"
    git commit -am "Deploy GCP service to $CI_ENVIRONMENT_NAME from GitLab CI pipeline jobName: ${CI_JOB_NAME} JobId: ${CI_JOB_ID}"
    git push origin
  only:
    changes: !reference [.changes, gcp]

.deploy-argocd:
  image: drpsychick/argocd-kubectl:latest
  when: manual
  stage: k8s-deploy
  variables:
    ARGOCD_APP_NAME: optimonk
    DEPLOY_COMMIT_AUTHOR_NAME: $GITLAB_USER_NAME
    DEPLOY_COMMIT_AUTHOR_EMAIL: $GITLAB_USER_EMAIL
    DEPLOY_COMMIT_MESSAGE: 'Deploy optimonk to $CI_ENVIRONMENT_NAME ($CI_COMMIT_SHA)'
    ARGOCD_OPTS: --grpc-web
    GIT_STRATEGY: none
  tags:
    - lightweight
  before_script:
    - |
      # check if the required variables are set
      if [ -z "$DEPLOY_COMMIT_MESSAGE" ]; then echo "DEPLOY_COMMIT_MESSAGE is not set"; exit 1; fi
    - git config --global user.email "${DEPLOY_COMMIT_AUTHOR_EMAIL:-<EMAIL>}"
    - git config --global user.name "${DEPLOY_COMMIT_AUTHOR_NAME:-GitLab CI/CD}"
  script:
    - git clone https://gitlab-ci-token:${GITLAB_DO_ARGOCD_DEPLOYMENTS_TOKEN}@gitlab.com/optimonk/do-argocd-deployments.git
    - cd do-argocd-deployments
    - |
      # Update image tags in the kustomization.yaml file
      set -x
      original_path=$(pwd)

      app_state=$(argocd app get -ojson $ARGOCD_APP_NAME)
      app_path=$(echo $app_state | jq -r '.spec.source.path')
      cd $app_path
      kustomize edit set image \
        "frontend-image=*:${CI_COMMIT_SHA}" \
        "backend-image=*:${CI_COMMIT_SHA}" \
        "admin-image=*:${CI_COMMIT_SHA}" \
        "backoffice-image=*:${CI_COMMIT_SHA}" \
        "renderer-image=*:${CI_COMMIT_SHA}" \
        "fakeclient-image=*:${CI_COMMIT_SHA}"
      cd $original_path

      set +x
    - 'git commit -am "$DEPLOY_COMMIT_MESSAGE" -m "$CI_COMMIT_TITLE" -m "*Job*: <$CI_JOB_URL|$CI_JOB_ID>" -m "*Commit*: <$CI_PROJECT_URL/commit/$CI_COMMIT_SHA|$CI_COMMIT_SHORT_SHA>"'
    - git push origin master
    - argocd app sync $ARGOCD_APP_NAME
    - argocd app wait $ARGOCD_APP_NAME --health --timeout 300
