.test-lib:
  image: node:16.14-slim
  stage: test
  variables:
    CURRENT_LIB: common
    COVERAGE_DIR: coverage
    COVERAGE_PATH: libraries/$CURRENT_LIB/$COVERAGE_DIR
    COVERAGE_SUMMARY_FILE_PATH: $COVERAGE_PATH/coverage-summary.txt
    COBERTURA_COVERAGE_FILE_PATH: $COVERAGE_PATH/cobertura-coverage.xml
    COBERTURA_COVERAGE_SOURCE_PATH: $CI_PROJECT_DIR/libraries/$PACKAGE_NAME
  cache:
    policy: pull-push
    key:
      files:
        - yarn.lock
    paths:
      - .yarn
  only:
    changes:
      - libraries/**/*
      - yarn.lock
      - package.json
      - .gitlab-ci.yml
      - ops/ci/*
  script:
    - yarn workspaces focus @om/${CURRENT_LIB}
    - yarn workspace @om/${CURRENT_LIB} test:unit:ci
  after_script:
    - !reference [.docker-devops]
    - fix_cobertura_source_path $COBERTURA_COVERAGE_FILE_PATH $COBERTURA_COVERAGE_SOURCE_PATH
  coverage: '/^Statements\s*:\s*([^%]+)/'
  artifacts:
    when: always
    reports:
      coverage_report:
        coverage_format: cobertura
        path: $COVERAGE_PATH/cobertura-coverage.xml
      junit: $COVERAGE_PATH/junit.xml
    paths:
      - $COVERAGE_PATH

.test:
  image: jdrouet/docker-with-buildx:0.9
  stage: test
  variables:
    DOCKER_BUILD_TEST_STAGE: export-test-results
    COVERAGE_DIR: coverage
    COVERAGE_SUMMARY_FILE: coverage-summary.txt
    COVERAGE_FILE: cobertura-coverage.xml
    COVERAGE_PATH: packages/$PACKAGE_NAME/$COVERAGE_DIR
    COVERAGE_SUMMARY_FILE_PATH: $COVERAGE_PATH/$COVERAGE_SUMMARY_FILE
    COBERTURA_COVERAGE_FILE_PATH: $COVERAGE_PATH/$COVERAGE_FILE
    COBERTURA_COVERAGE_SOURCE_PATH: $CI_PROJECT_DIR/packages/$PACKAGE_NAME
  before_script:
    - !reference [.docker-devops]
  script:
    - docker login -u "$DOCKER_REGISTRY_USERNAME" -p "$DOCKER_REGISTRY_PASSWORD"
    - test_package --output type=local,dest=$COVERAGE_PATH
  after_script:
    - !reference [.docker-devops]
    - fix_cobertura_source_path $COBERTURA_COVERAGE_FILE_PATH $COBERTURA_COVERAGE_SOURCE_PATH
  coverage: '/^Statements\s*:\s*([^%]+)/'
