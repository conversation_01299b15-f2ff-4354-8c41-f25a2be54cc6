.build-gitlab:
  image: jdrouet/docker-with-buildx:0.9
  stage: build
  before_script:
    - !reference [.docker-devops]
    - !reference [.gcloud-devops]
    - setup_gcloud_sdk_alpine
  script:
    - docker login -u "$DOCKER_REGISTRY_USERNAME" -p "$DOCKER_REGISTRY_PASSWORD"
    - gcloud_setup_docker_auth $GCP_CLOUD_BUILD_SA_KEY
    - build_package --output type=image,\"name=$DOCKER_REGISTRY_REPOSITORY/$PACKAGE_NAME:$CI_COMMIT_SHA,us-central1-docker.pkg.dev/optimonk-build-4914/om-artifact/$PACKAGE_NAME:$CI_COMMIT_SHA\",push=true

.build-workflow-gitlab:
  extends: .build-gitlab
  variables:
    DOCKER_IMAGE_NAME: us-central1-docker.pkg.dev/optimonk-build-4914/om-artifact/workflow-executors/$PACKAGE_NAME
    DOCKER_CACHE_IMAGE_NAME: $DOCKER_REGISTRY_REPOSITORY/workflow-executors
    DOCKER_IMAGE_TAG: $CI_COMMIT_SHA
    # PACKAGE_NAME:
    PACKAGE_PATH: packages/workflow-executors/$PACKAGE_NAME
    DOCKER_FILE_PATH: $PACKAGE_PATH/Dockerfile
  script:
    - docker login -u "$DOCKER_REGISTRY_USERNAME" -p "$DOCKER_REGISTRY_PASSWORD"
    - gcloud_setup_docker_auth $GCP_CLOUD_BUILD_SA_KEY
    - |
      # Build and push the image with buildx
      docker buildx build \
      --cache-from type=registry,ref=${DOCKER_CACHE_IMAGE_NAME}:${PACKAGE_NAME}-master-cache \
      --cache-from type=registry,ref=${DOCKER_CACHE_IMAGE_NAME}:${PACKAGE_NAME}-${CI_COMMIT_REF_SLUG}-cache \
      --cache-to type=registry,ref=${DOCKER_CACHE_IMAGE_NAME}:${PACKAGE_NAME}-${CI_COMMIT_REF_SLUG}-cache,mode=max \
      --output type=image,\"name=${DOCKER_IMAGE_NAME}:${DOCKER_IMAGE_TAG}\",push=true \
      -f ${DOCKER_FILE_PATH} \
      .

.build:
  extends: .build-gitlab
